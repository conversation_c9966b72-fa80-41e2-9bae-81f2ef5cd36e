namespace :smbc_account_inquiry do
  desc "Update inquiry_result by mapping with account_inquired_result"
  task update_staff_account_transfer: :environment do
    StaffAccountTransfer.where(
      "account_inquired_result IS NULL OR (account_inquired_result = -1 AND account_inquired_at IS NULL)"
    ).update_all(inquiry_result: SmbcAccountInquiry.inquiry_results[:unconfirmed])

    StaffAccountTransfer.where(account_inquired_result: -1).where.not(account_inquired_at: nil)
      .update_all(inquiry_result: SmbcAccountInquiry.inquiry_results[:confirming])

    StaffAccountTransfer.where(account_inquired_result: 0)
      (inquiry_result: SmbcAccountInquiry.inquiry_results[:confirmed_ok])

    StaffAccountTransfer.where(account_inquired_result: [1, 2, 9])
      .update_all(inquiry_result: SmbcAccountInquiry.inquiry_results[:confirmed_ng])
  end

  desc "Compare data"
  task compare_data: :environment do
    a_1 = StaffAccountTransfer
      .where("account_inquired_result IS NULL OR (account_inquired_result = -1 AND account_inquired_at IS NULL)")
      .count
    a_2 = StaffAccountTransfer.where(inquiry_result: SmbcAccountInquiry.inquiry_results[:unconfirmed]).count
    puts "Unconfirmed before: #{a_1} - Unconfirmed after: #{a_2}"

    a_ids_1 = StaffAccountTransfer
      .where("account_inquired_result IS NULL OR (account_inquired_result = -1 AND account_inquired_at IS NULL)")
      .pluck(:id)
    a_ids_2 = StaffAccountTransfer.where(inquiry_result: SmbcAccountInquiry.inquiry_results[:unconfirmed]).pluck(:id)
    puts "Unconfirmed differences: #{(a_ids_1 - a_ids_2) + (a_ids_2 - a_ids_1)}"

    # Confirming

    a_1 = StaffAccountTransfer.where(account_inquired_result: -1).where.not(account_inquired_at: nil).count
    a_2 = StaffAccountTransfer.where(inquiry_result: SmbcAccountInquiry.inquiry_results[:confirming]).count
    puts "Confirming before: #{a_1} - Confirming after: #{a_2}"

    a_ids_1 = StaffAccountTransfer.where(account_inquired_result: -1).where.not(account_inquired_at: nil).pluck(:id)
    a_ids_2 = StaffAccountTransfer.where(inquiry_result: SmbcAccountInquiry.inquiry_results[:confirming]).pluck(:id)
    puts "Confirmed differences: #{(a_ids_1 - a_ids_2) + (a_ids_2 - a_ids_1)}"

    # Confirmed OK

    a_1 = StaffAccountTransfer.where(account_inquired_result: 0).count
    a_2 = StaffAccountTransfer.where(inquiry_result: SmbcAccountInquiry.inquiry_results[:confirmed_ok]).count
    puts "Confirmed before: #{a_1} - Confirmed OK after: #{a_2}"

    a_ids_1 = StaffAccountTransfer.where(account_inquired_result: 0).pluck(:id)
    a_ids_2 = StaffAccountTransfer.where(inquiry_result: SmbcAccountInquiry.inquiry_results[:confirmed_ok]).pluck(:id)
    puts "Confirmed differences: #{(a_ids_1 - a_ids_2) + (a_ids_2 - a_ids_1)}"

    # Confirmed NG

    a_1 = StaffAccountTransfer.where(account_inquired_result: [1, 2, 9]).count
    a_2 = StaffAccountTransfer.where(inquiry_result: SmbcAccountInquiry.inquiry_results[:confirmed_ng]).count
    puts "Confirmed NG before: #{a_1} - Confirmed NG after: #{a_2}"

    a_ids_1 = StaffAccountTransfer.where(account_inquired_result: [1, 2, 9]).pluck(:id)
    a_ids_2 = StaffAccountTransfer.where(inquiry_result: SmbcAccountInquiry.inquiry_results[:confirmed_ng]).pluck(:id)
    puts "Confirmed NG differences: #{(a_ids_1 - a_ids_2) + (a_ids_2 - a_ids_1)}"
  end
end
