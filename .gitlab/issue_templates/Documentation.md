### Problem to solve

<!-- Include the following detail as necessary:
* What product or feature(s) affected?
* What docs or doc section affected? Include links or paths.
* Is there a problem with a specific document, or a feature/process that's not addressed sufficiently in docs?
* Any other ideas or requests?
-->

### Further details

<!--
* Any concepts, procedures, reference info we could add to make it easier to successfully use GitLab?
* Include use cases, benefits, and/or goals for this work.
* If adding content: What audience is it intended for? (What roles and scenarios?)
  For ideas, see personas at https://handbook.gitlab.com/handbook/product/personas/ or the persona labels at
  https://gitlab.com/groups/gitlab-org/-/labels?subscribed=&search=persona%3A
-->

### Proposal

<!-- Further specifics for how can we solve the problem. -->

### Who can address the issue

<!-- What if any special expertise is required to resolve this issue? -->

### Other links/references

<!-- E.g. related GitLab issues/MRs -->

/label ~documentation
