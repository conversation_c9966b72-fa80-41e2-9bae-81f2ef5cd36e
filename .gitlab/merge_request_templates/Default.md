## Summary

<!--
Describe in detail what your merge request does and why.

Please keep this description updated with any discussion that takes place so
that reviewers can understand your intent. Keeping the description updated is
especially important if they didn't participate in the discussion.
-->

- Ticket: [ADO-](https://dev.azure.com/ponos-tech/workz/_workitems/edit/ticket_id) _Ticket Title_
- Target branch: %{target_branch}
- Description: _what does this MR do and why?_

## Screenshots/Recordings

_Screenshots are required for UI changes, and strongly recommended for all other merge requests._

<!--
Please include any relevant screenshots or screen recordings that will assist
reviewers and future readers. If you need help visually verifying the change,
please leave a comment and ping a GitLab reviewer, maintainer, or MR coach.
-->

| Before | After  |
| ------ | ------ |
|        |        |

## Local test steps

_Numbered steps to set up and validate the change are strongly suggested._

<!--
Example below:

1. In rails console enable the experiment fully
   ```ruby
   Feature.enable(:member_areas_of_focus)
   ```
2. Visit any group or project member pages such as `http://127.0.0.1:3000/groups/flightjs/-/group_members`
3. Click the `invite members` button.
-->

## MR acceptance checklist

* [ ] Test steps (local)
* [ ] Rubocop   _Screenshots/Logs are strongly suggested._
* [ ] RSpec     _Screenshots/Logs are strongly suggested._

/assign me