---
description: A description of your rule
---

- Unit test need to run 100% code, including happy cases and edge cases
- Using create data via FactoryBot if necessary
- Using described_class instead of model name
- For associations, group it by 'Associations', using shoulda matcher
- For Validations, group it by describe 'Validations', using shoulda matcher
- Always follow example by corporation_spec.rb
- For Acts as paranoid need to test behavior (destroy, restore). In this, test case for destroy to test find_by return nil and with_deleted return record, restore to test find_by return record, nested group is not necessary for acts as paranoid
- If no exists testcase, no need to create describe group
- For enums, group it by describe 'Enums', using shoulda matcher
- For instance methods, group it by describe 'Instance methods'
- For class methods, group it by describe 'Class methods'
- For callbacks, group it by describe 'Callbacks', group it by before_save, before_create, before_update, before_destroy, after_save, after_create, after_update, after_destroy
- For scopes, group it by describe 'Scopes'
- For concerns, group it by describe 'Concerns'
- For constants, group it by describe 'Constants'
- For helpers, group it by describe 'Helpers'
- Not test private methods
- For public methods, test result is expected
- For nested attributes, group it by 'Nested attributes', test behavior is expected
- For delegate, group it by describe 'Delegations', test behavior is expected
- For serializer, group it by describe 'Serializations'
- For uploader, group it by describe 'Uploader'
- Order of describe group is: Constants -> Uploader -> Associations -> Nested attributes -> Serializer ->  Enums -> Validations  -> Delegations -> Callbacks -> Scopes -> Instance methods -> Class methods -> Concerns -> Helpers -> Acts as paranoid
