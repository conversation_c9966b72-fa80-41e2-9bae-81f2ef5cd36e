require 'rails_helper'

RSpec.describe TrainingSchedule, type: :model do
  subject { FactoryBot.build(:training_schedule) }

  describe 'Constants' do
    it 'has UNSPECIFIED_OPTION constant' do
      expect(described_class::UNSPECIFIED_OPTION).to eq('-1')
    end

    it 'has INCLUDE_MODELS constant' do
      expect(described_class::INCLUDE_MODELS).to eq([:location, { person_in_charge: :account }])
    end

    it 'has RESTORE_PARAMS constant' do
      expect(described_class::RESTORE_PARAMS).to eq({
        deleted_by: nil,
        deleted_at: nil,
        is_full: false
      })
    end

    it 'has UNAVAILABLE_RESTORE_SESSION_CODE constant' do
      expect(described_class::UNAVAILABLE_RESTORE_SESSION_CODE).to eq(%w(training_first_round training_second_round))
    end
  end

  describe 'Associations' do
    it { is_expected.to belong_to(:location) }
    it { is_expected.to belong_to(:creator).class_name('Admin').optional }
    it { is_expected.to belong_to(:person_in_charge).class_name('Admin').optional }
    it { is_expected.to belong_to(:training_schedule).class_name('TrainingSchedule').optional }
    it { is_expected.to have_many(:training_schedule_applicants).dependent(:destroy) }
  end

  describe 'Enums' do
    it { is_expected.to define_enum_for(:training_session_code)
      .with_values(training_first_round: 1, training_second_round: 2, single_session: 3) }
  end

  describe 'Validations' do
    let(:admin) { create(:admin, id: subject.person_in_charge_id) }
    let(:location) { create(:location, id: subject.location_id) }
    let(:same_trainer_schedule) do
      create(:training_schedule, start_time: subject.start_time, person_in_charge_id: admin.id)
    end

    let(:same_location_and_session_schedule) do
      create(:training_schedule, start_time: subject.start_time, location_id: location.id,
        training_session_code: subject.training_session_code)
    end

    it { is_expected.to be_valid }

    it 'should validate person_in_charge_id presence on create' do
      subject.person_in_charge_id = nil
      expect(subject).not_to be_valid
      expect(subject.errors[:person_in_charge_id])
        .to include(I18n.t('admin.training_schedule.errors.invalid_person_in_charge'))
    end

    it 'should validate start_time presence on create' do
      subject.start_time = nil
      expect(subject).not_to be_valid
      expect(subject.errors.details[:start_time]).to include(a_hash_including(error: :blank))
    end

    it 'should validate end_time presence on create' do
      subject.end_time = nil
      expect(subject).not_to be_valid
      expect(subject.errors.details[:end_time]).to include(a_hash_including(error: :blank))
    end

    it 'should validate start_time less than end_time on create' do
      subject.start_time = subject.end_time

      expect(subject).not_to be_valid
      expect(subject.errors[:base]).to include(I18n.t('admin.training_schedule.errors.start_time_less_than_end_time'))
    end

    it 'should validate trainer has another schedule that has applicants' do
      allow(TrainingScheduleApplicant).to receive(:not_absent_by_schedule_ids)
        .with([same_trainer_schedule.id])
        .and_return({same_trainer_schedule.id => [build(:training_schedule_applicant)]})

      expect(subject).not_to be_valid
      expect(subject.errors[:base]).to include(I18n.t('admin.training_schedule.errors.same_date_and_trainer'))
    end

    it 'should validate same location and session has applicants' do
      allow(TrainingScheduleApplicant).to receive(:not_absent_by_schedule_ids)
        .with([same_location_and_session_schedule.id])
        .and_return({same_location_and_session_schedule.id => [build(:training_schedule_applicant)]})

      expect(subject).not_to be_valid
      expect(subject.errors[:base]).to include(I18n.t('admin.training_schedule.errors.same_date_location_and_session'))
    end

    context 'when updating person in charge' do
      let(:other_schedule) do
        create(:training_schedule, person_in_charge: create(:admin), start_time: subject.start_time)
      end

      before do
        subject.save!
      end

      it 'should validate trainer has another schedule that has applicants' do
        allow(TrainingScheduleApplicant).to receive(:not_absent_by_schedule_ids)
          .with([other_schedule.id])
          .and_return({other_schedule.id => [build(:training_schedule_applicant)]})

        expect(subject.update(person_in_charge_id: other_schedule.person_in_charge_id)).to be_falsey
        expect(subject.errors[:base]).to include(I18n.t('admin.training_schedule.errors.same_date_and_trainer'))
      end
    end
  end

  describe 'Delegations' do
    let(:location) { build_stubbed(:location, name: 'Location Name') }
    let(:person_in_charge) do
      build_stubbed(:admin, account: build_stubbed(:account,
        name: 'Person In Charge Name',
        email: 'Person In Charge Email'))
    end

    let(:training_schedule) { build_stubbed(:training_schedule, location: location, person_in_charge: person_in_charge) }

    before do
      allow(location).to receive(:formatted_full_address).and_return('Formatted Full Address')
      allow(location).to receive(:prefecture_name).and_return('Prefecture Name')
      allow(location).to receive(:prefecture_id).and_return('Prefecture ID')
      allow(location).to receive(:stations_1_name).and_return('Station Name')
    end

    it { expect(training_schedule.location_name).to eq('Location Name') }
    it { expect(training_schedule.location_formatted_full_address).to eq('Formatted Full Address') }
    it { expect(training_schedule.location_prefecture_name).to eq('Prefecture Name') }
    it { expect(training_schedule.location_prefecture_id).to eq('Prefecture ID') }
    it { expect(training_schedule.location_stations_1_name).to eq('Station Name') }
    it { expect(training_schedule.person_in_charge_email).to eq('Person In Charge Email') }
    it { expect(training_schedule.person_in_charge_name).to eq('Person In Charge Name') }
  end

  describe 'Callbacks' do
    describe 'after_create' do
      it 'creates admin action log' do
        expect(TrainingScheduleLoggingWorker)
          .to receive(:perform_async)
          .with('admin', {'admin_id' => subject.creator_id, 'target_id' => anything, 'action_type' => 'create'})
          .and_call_original

        subject.save!
      end

      it 'does not create admin action log if creator_id is blank' do
        subject.creator_id = nil
        expect(TrainingScheduleLoggingWorker).not_to receive(:perform_async)

        subject.save!
      end
    end
  end

  describe 'Scopes' do
    let!(:training_schedule1) do
      create(:training_schedule, start_time: Time.zone.parse('2025-01-01 10:00:00'), is_full: false)
    end

    let!(:training_schedule2) do
      create(:training_schedule, start_time: Time.zone.parse('2025-01-02 12:00:00'), is_full: true)
    end

    describe '.by_location' do
      it { expect(described_class.by_location(training_schedule1.location_id))
        .to contain_exactly(training_schedule1) }
    end

    describe '.by_person_in_charge' do
      it { expect(described_class.by_person_in_charge(training_schedule1.person_in_charge_id))
        .to contain_exactly(training_schedule1) }
    end

    describe '.start_after' do
      it { expect(described_class.start_after(Time.zone.parse('2025-01-01 12:00:00')))
        .to contain_exactly(training_schedule2) }
    end

    describe '.start_before' do
      it { expect(described_class.start_before(Time.zone.parse('2025-01-01 12:00:00')))
        .to contain_exactly(training_schedule1) }
    end

    describe '.available' do
      it { expect(described_class.available).to contain_exactly(training_schedule1) }
    end

    describe '.by_start_time' do
      it { expect(described_class.by_start_time(Time.zone.parse('2025-01-01 12:00:00')))
        .to contain_exactly(training_schedule2) }
    end

    describe '.between_dates' do
      it { expect(described_class.between_dates(Time.zone.parse('2025-01-01 10:00:00'),
        Time.zone.parse('2025-01-02 11:59:59'))).to contain_exactly(training_schedule1) }
    end

    describe '.deleted_by_schedule' do
      let!(:deleted_schedule) { create(:training_schedule, deleted_by: training_schedule1.id, deleted_at: Time.current) }

      it { expect(described_class.deleted_by_schedule(training_schedule1.id)).to contain_exactly(deleted_schedule) }
      it { expect(described_class.deleted_by_schedule(training_schedule2.id)).to be_empty }
    end
  end

  describe 'Instance methods' do
    let(:training_schedule) { build(:training_schedule) }

    describe '#has_training_time_passed?' do
      it 'returns true if training time has passed' do
        training_schedule.end_time = 1.second.ago
        expect(training_schedule.has_training_time_passed?).to be true
      end

      it 'returns false if training time has not passed' do
        training_schedule.end_time = 1.second.from_now
        expect(training_schedule.has_training_time_passed?).to be false
      end
    end

    describe '#absent_deadline' do
      context 'when the start time is before 12:00' do
        it 'returns the absent deadline is 12:00 of 2 days before the start time' do
          training_schedule.start_time = Time.zone.parse('2025-01-03 10:00:00')

          expect(training_schedule.absent_deadline)
            .to eq(Time.zone.parse('2025-01-01 12:00:00'))
        end
      end

      context 'when the start time is after 12:00' do
        it 'returns the absent deadline is 12:00 of 2 days before the start time' do
          training_schedule.start_time = Time.zone.parse('2025-01-03 16:00:00')

          expect(training_schedule.absent_deadline)
            .to eq(Time.zone.parse('2025-01-01 12:00:00'))
        end
      end
    end

    describe '#able_to_absent?' do
      before do
        allow(ServerTime).to receive(:now).and_return(Time.zone.parse('2025-01-01 12:00:00'))
      end

      it 'should returns true when within absent deadline' do
        allow(training_schedule).to receive(:absent_deadline).and_return(Time.zone.parse('2025-01-01 11:59:59'))

        expect(training_schedule.able_to_absent?).to be false
      end

      it 'should returns false when out of absent deadline' do
        allow(training_schedule).to receive(:absent_deadline).and_return(Time.zone.parse('2025-01-01 12:00:01'))

        expect(training_schedule.able_to_absent?).to be true
      end
    end

    describe '#can_change_person_in_charge?' do
      it 'returns true if person in charge is blank' do
        training_schedule.person_in_charge_id = nil

        expect(training_schedule.can_change_person_in_charge?(anything)).to be true
      end

      it 'returns true if person in charge is current admin' do
        training_schedule.person_in_charge_id = 1

        expect(training_schedule.can_change_person_in_charge?(1)).to be true
      end

      it 'returns false if person in charge is not current admin' do
        training_schedule.person_in_charge_id = 1

        expect(training_schedule.can_change_person_in_charge?(2)).to be false
      end
    end

    describe '#assign_is_full' do
      let(:training_schedule) { build(:training_schedule, is_full: false) }
      let(:training_schedule_applicants) { build_list(:training_schedule_applicant, 2) }

      before do
        allow(training_schedule).to receive_message_chain(:training_schedule_applicants, :not_absent)
          .and_return(training_schedule_applicants)
      end

      it 'assigns is_full to true if valid applicants count is greater than or equal to total portion' do
        training_schedule.total_portion = 2

        expect { training_schedule.assign_is_full }.to change(training_schedule, :is_full).from(false).to(true)
      end

      it 'assigns is_full to false if valid applicants count is less than total portion' do
        training_schedule.total_portion = 3

        expect { training_schedule.assign_is_full }.not_to change(training_schedule, :is_full)
        expect(training_schedule.is_full).to be false
      end
    end

    describe '#applied_staff_ids' do
      let(:training_schedule_applicant) { build(:training_schedule_applicant, staff_id: 1) }

      before do
        allow(training_schedule).to receive_message_chain(:training_schedule_applicants, :booked)
          .and_return([training_schedule_applicant])
      end

      it 'returns booked staff ids through training schedule applicants' do
        expect(training_schedule.applied_staff_ids).to contain_exactly(1)
      end
    end

    describe '#get_related_schedules' do
      it 'returns related schedules' do
        related_schedule = create(:training_schedule, start_time: training_schedule.start_time, person_in_charge_id: training_schedule.person_in_charge_id)
        expect(training_schedule.get_related_schedules).to include(related_schedule)
      end
    end

    describe '#notify_trainer' do
      it 'notifies trainer for booked schedule' do
        training_schedule.update(person_in_charge_email: '<EMAIL>')
        expect(AdminMailer).to receive(:notify_staff_booked_training_schedule).with('<EMAIL>', {})
        training_schedule.notify_trainer(:booked)
      end

      it 'notifies trainer for absent schedule' do
        training_schedule.update(person_in_charge_email: '<EMAIL>')
        expect(AdminMailer).to receive(:notify_staff_cancel_schedule).with('<EMAIL>', {})
        training_schedule.notify_trainer(:absent)
      end

      it 'notifies trainer for delete schedule' do
        training_schedule.update(person_in_charge_email: '<EMAIL>')
        expect(AdminMailer).to receive(:notify_delete_schedule).with('<EMAIL>', training_schedule.delete_mailer_data)
        training_schedule.notify_trainer(:delete)
      end
    end

    describe '#get_deleted_schedules' do
      it 'returns deleted schedules' do
        deleted_schedule = create(:training_schedule, deleted_by: training_schedule.id, deleted_at: Time.current)
        expect(training_schedule.get_deleted_schedules).to include(deleted_schedule.deleted_format)
      end
    end

    describe '#get_histories' do
      it 'returns histories' do
        admin_log = create(:admin_action_log, target: 'TrainingSchedule', target_id: training_schedule.id)
        staff_log = create(:staff_action_log, target: 'TrainingSchedule', target_id: training_schedule.id)
        expect(training_schedule.get_histories).to include(admin_log, staff_log)
      end
    end
  end

  describe 'Class methods' do
    describe '.get_by_search_params' do
      it 'returns training schedules by search params' do
        dates = [1.day.from_now.to_date]
        location_ids = create(:location).id.to_s
        training_session_code = 'single_session'
        person_in_charge_id = create(:admin).id.to_s
        training_schedule = create(:training_schedule, start_time: dates.first, location_id: location_ids, training_session_code: training_session_code, person_in_charge_id: person_in_charge_id)
        result = described_class.get_by_search_params(dates, location_ids, training_session_code, person_in_charge_id)
        expect(result).to have_key(dates.first.to_s)
      end
    end

    describe '.available_schedules_by_location' do
      it 'returns available schedules by location' do
        location = create(:location)
        training_schedule = create(:training_schedule, location_id: location.id, start_time: 1.day.from_now, is_full: false)
        result = described_class.available_schedules_by_location(location.id)
        expect(result[:single_session_schedules]).to include({
          'id' => training_schedule.id,
          'datetime' => training_schedule.formatted_datetime
        })
      end
    end
  end

  describe 'Acts as paranoid' do
    it 'destroys the record' do
      training_schedule = create(:training_schedule)
      training_schedule.destroy
      expect(described_class.find_by(id: training_schedule.id)).to be_nil
      expect(described_class.with_deleted.find_by(id: training_schedule.id)).to eq(training_schedule)
    end

    it 'restores the record' do
      training_schedule = create(:training_schedule)
      training_schedule.destroy
      training_schedule.restore
      expect(described_class.find_by(id: training_schedule.id)).to eq(training_schedule)
    end
  end

  describe 'Concerns' do
    it 'includes TrainingScheduleDecorator' do
      expect(described_class.included_modules).to include(TrainingScheduleDecorator)
    end
  end
end
