require 'rails_helper'

RSpec.describe TrainingScheduleApplicant, type: :model do
  let(:staff) { create(:staff) }
  let(:training_schedule) { create(:training_schedule) }
  let(:training_schedule_applicant) do
    build(:training_schedule_applicant, staff: staff, training_schedule: training_schedule)
  end

  describe 'Constants' do
    it 'has ABSENT_STATUSES constant' do
      expect(described_class::ABSENT_STATUSES).to eq([2, 3])
    end

    it 'has NOT_ABSENT_STATUSES constant' do
      expect(described_class::NOT_ABSENT_STATUSES).to eq([0, 1])
    end
  end

  describe 'Associations' do
    it { is_expected.to belong_to(:staff) }
    it { is_expected.to belong_to(:training_schedule) }
  end

  describe 'Validations' do
    it { is_expected.to validate_presence_of(:staff) }
  end

  describe 'Enums' do
    it do
      is_expected.to define_enum_for(:schedule_status_code)
        .with_values(booked: 0, joined: 1, absent_with_notice: 2, absent_without_notice: 3)
    end
  end

  describe 'Delegations' do
    let(:applicant) { create(:training_schedule_applicant, staff: staff, training_schedule: training_schedule) }

    it 'delegates training_schedule methods' do
      expect(applicant).to respond_to(:formatted_datetime)
      expect(applicant).to respond_to(:training_date_weekday)
      expect(applicant).to respond_to(:location)
      expect(applicant).to respond_to(:formatted_absent_deadline)
      expect(applicant).to respond_to(:able_to_absent?)
      expect(applicant).to respond_to(:location_name)
      expect(applicant).to respond_to(:location_id)
      expect(applicant).to respond_to(:training_session_code)
      expect(applicant).to respond_to(:working_date)
      expect(applicant).to respond_to(:start_time_text)
      expect(applicant).to respond_to(:end_time_text)
    end

    it 'delegates staff methods' do
      expect(applicant).to respond_to(:staff_name)
      expect(applicant).to respond_to(:staff_account_email)
      expect(applicant).to respond_to(:staff_account_name_kana)
      expect(applicant).to respond_to(:staff_total_work_experience)
      expect(applicant).to respond_to(:staff_evaluation)
      expect(applicant).to respond_to(:staff_home_tel)
      expect(applicant).to respond_to(:staff_tel)
      expect(applicant).to respond_to(:staff_email)
      expect(applicant).to respond_to(:staff_age)
      expect(applicant).to respond_to(:staff_uniform_size)
      expect(applicant).to respond_to(:staff_gender_id)
      expect(applicant).to respond_to(:staff_display_workable_time)
      expect(applicant).to respond_to(:staff_is_working_car)
      expect(applicant).to respond_to(:staff_level_up_training)
      expect(applicant).to respond_to(:staff_account_name_kana_to_half_size)
      expect(applicant).to respond_to(:staff_number)
    end
  end

  describe 'Scopes' do
    let!(:staff1) { create(:staff) }
    let!(:staff2) { create(:staff) }
    let!(:applicant1) { create(:training_schedule_applicant, staff: staff1) }
    let!(:applicant2) { create(:training_schedule_applicant, staff: staff2) }

    describe '.by_staff_id' do
      it 'returns applicants for specified staff ids' do
        result = described_class.by_staff_id([staff1.id])
        expect(result).to contain_exactly(applicant1)
      end
    end

    describe '.by_schedule_id' do
      let!(:schedule1) { create(:training_schedule) }
      let!(:schedule2) { create(:training_schedule) }
      let!(:applicant_schedule1) { create(:training_schedule_applicant, training_schedule: schedule1) }
      let!(:applicant_schedule2) { create(:training_schedule_applicant, training_schedule: schedule2) }

      it 'returns applicants for specified schedule ids' do
        result = described_class.by_schedule_id([schedule1.id])
        expect(result).to contain_exactly(applicant_schedule1)
      end
    end

    describe '.first_sessions' do
      let!(:first_round_schedule) { create(:training_first_round) }
      let!(:second_round_schedule) { create(:training_second_round) }
      let!(:first_session_applicant) { create(:training_schedule_applicant, training_schedule: first_round_schedule) }
      let!(:second_session_applicant) { create(:training_schedule_applicant, training_schedule: second_round_schedule) }

      it 'returns applicants for first round training sessions' do
        result = described_class.first_sessions
        expect(result).to contain_exactly(first_session_applicant)
      end
    end

    describe '.second_sessions' do
      let!(:first_round_schedule) { create(:training_first_round) }
      let!(:second_round_schedule) { create(:training_second_round) }
      let!(:first_session_applicant) { create(:training_schedule_applicant, training_schedule: first_round_schedule) }
      let!(:second_session_applicant) { create(:training_schedule_applicant, training_schedule: second_round_schedule) }

      it 'returns applicants for second round training sessions' do
        result = described_class.second_sessions
        expect(result).to contain_exactly(second_session_applicant)
      end
    end

    describe '.single_sessions' do
      let!(:single_session_schedule) { create(:training_single_session) }
      let!(:first_round_schedule) { create(:training_first_round) }
      let!(:single_session_applicant) { create(:training_schedule_applicant, training_schedule: single_session_schedule) }
      let!(:first_session_applicant) { create(:training_schedule_applicant, training_schedule: first_round_schedule) }

      it 'returns applicants for single training sessions' do
        result = described_class.single_sessions
        expect(result).to contain_exactly(single_session_applicant)
      end
    end

    describe '.upcoming_trainings' do
      let(:start_time) { 1.day.from_now }
      let(:end_time) { 3.days.from_now }
      let!(:upcoming_schedule) { create(:training_schedule, start_time: 2.days.from_now) }
      let!(:past_schedule) { create(:training_schedule, start_time: 1.day.ago) }
      let!(:future_schedule) { create(:training_schedule, start_time: 5.days.from_now) }
      let!(:upcoming_applicant) { create(:booked_schedule_applicant, training_schedule: upcoming_schedule) }
      let!(:past_applicant) { create(:booked_schedule_applicant, training_schedule: past_schedule) }
      let!(:future_applicant) { create(:booked_schedule_applicant, training_schedule: future_schedule) }

      it 'returns booked applicants for trainings within the specified time range' do
        result = described_class.upcoming_trainings(start_time, end_time)
        expect(result).to contain_exactly(upcoming_applicant)
      end
    end

    describe '.not_absent' do
      let!(:booked_applicant) { create(:training_schedule_applicant, schedule_status_code: :booked) }
      let!(:joined_applicant) { create(:training_schedule_applicant, schedule_status_code: :joined) }
      let!(:absent_with_notice_applicant) { create(:training_schedule_applicant, schedule_status_code: :absent_with_notice) }
      let!(:absent_without_notice_applicant) { create(:training_schedule_applicant, schedule_status_code: :absent_without_notice) }

      it 'returns applicants who are not absent' do
        result = described_class.not_absent
        expect(result).to contain_exactly(booked_applicant, joined_applicant)
      end
    end
  end

  describe 'Callbacks' do
    let(:applicant) { build(:training_schedule_applicant, staff: staff, training_schedule: training_schedule) }

    describe 'before_update :clear_cancel_reason' do
      it 'clears cancel_reason when schedule_status_code changes and is not unavailable' do
        applicant.schedule_status_code = :absent_with_notice
        applicant.cancel_reason = 'Some reason'
        applicant.save!

        applicant.schedule_status_code = :booked
        applicant.save!

        expect(applicant.cancel_reason).to be_nil
      end

      it 'does not clear cancel_reason when schedule_status_code changes to unavailable status' do
        applicant.schedule_status_code = :booked
        applicant.cancel_reason = 'Some reason'
        applicant.save!

        applicant.schedule_status_code = :absent_with_notice
        applicant.save!

        expect(applicant.cancel_reason).to eq('Some reason')
      end
    end

    describe 'after_save :update_is_full' do
      let(:training_schedule) { create(:training_schedule, total_portion: 2, is_full: false) }

      it 'updates training_schedule is_full to true when total_portion is reached' do
        create(:training_schedule_applicant, training_schedule: training_schedule, schedule_status_code: :booked)
        create(:training_schedule_applicant, training_schedule: training_schedule, schedule_status_code: :booked)

        training_schedule.reload
        expect(training_schedule.is_full).to be true
      end

      it 'updates training_schedule is_full to false when below total_portion' do
        training_schedule.update!(is_full: true)
        create(:training_schedule_applicant, training_schedule: training_schedule, schedule_status_code: :booked)

        training_schedule.reload
        expect(training_schedule.is_full).to be false
      end

      it 'raises error when valid applicants exceed total_portion' do
        training_schedule.update!(total_portion: 1)
        create(:training_schedule_applicant, training_schedule: training_schedule, schedule_status_code: :booked)

        expect {
          create(:training_schedule_applicant, training_schedule: training_schedule, schedule_status_code: :booked)
        }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end
  end

  describe 'Instance Methods' do
    let(:applicant) { create(:training_schedule_applicant, staff: staff, training_schedule: training_schedule) }

    describe '#is_unavailable?' do
      it 'returns true for absent_with_notice status' do
        applicant.schedule_status_code = :absent_with_notice
        expect(applicant.is_unavailable?).to be true
      end

      it 'returns true for absent_without_notice status' do
        applicant.schedule_status_code = :absent_without_notice
        expect(applicant.is_unavailable?).to be true
      end

      it 'returns false for booked status' do
        applicant.schedule_status_code = :booked
        expect(applicant.is_unavailable?).to be false
      end

      it 'returns false for joined status' do
        applicant.schedule_status_code = :joined
        expect(applicant.is_unavailable?).to be false
      end
    end

    describe '#send_new_schedule_email' do
      it 'sends email notification to staff' do
        expect(StaffMailer).to receive(:notify_staff_new_training_schedule)
          .with(
            staff,
            training_schedule.training_time_with_day,
            staff.account_name,
            applicant.location_name,
            training_schedule.location_formatted_full_address
          )
          .and_return(double(deliver_now: true))

        applicant.send_new_schedule_email
      end
    end

    describe '#send_new_schedule_sms' do
      it 'sends SMS notification to staff' do
        expect(MessageSenderService).to receive(:notify_staff_new_training_schedule)
          .with(
            staff.account&.tel,
            datetime: training_schedule.training_time_with_day,
            user_name: staff.account_name,
            location_name: applicant.location_name,
            location_full_address: training_schedule.location&.formatted_full_address
          )

        applicant.send_new_schedule_sms
      end
    end

    describe '#send_fcm_to_staff' do
      let(:admin_id) { 123 }
      let(:notification_type) { :new_training_schedule }

      it 'sends FCM notification with admin creator' do
        expect(AppSendNotificationWorker).to receive(:perform_async)
          .with([{
            staff_id: staff.id,
            creator_type: :admin,
            creator_id: admin_id,
            notification_type: notification_type,
            params: {
              location_name: applicant.location_name,
              job_date_time: applicant.formatted_datetime
            }
          }])
          .and_return('job_id')

        applicant.send_fcm_to_staff(admin_id, notification_type)
      end

      it 'sends FCM notification with system creator when admin_id is blank' do
        expect(AppSendNotificationWorker).to receive(:perform_async)
          .with([{
            staff_id: staff.id,
            creator_type: :by_system,
            notification_type: notification_type,
            params: {
              location_name: applicant.location_name,
              job_date_time: applicant.formatted_datetime
            }
          }])
          .and_return('job_id')

        applicant.send_fcm_to_staff(nil, notification_type)
      end
    end
  end

  describe 'Class Methods' do
    describe '.not_absent_by_schedule_ids' do
      let!(:schedule1) { create(:training_schedule) }
      let!(:schedule2) { create(:training_schedule) }
      let!(:booked_applicant) { create(:training_schedule_applicant, training_schedule: schedule1, schedule_status_code: :booked) }
      let!(:joined_applicant) { create(:training_schedule_applicant, training_schedule: schedule1, schedule_status_code: :joined) }
      let!(:absent_applicant) { create(:training_schedule_applicant, training_schedule: schedule2, schedule_status_code: :absent_with_notice) }

      it 'returns empty hash when schedule_ids is blank' do
        result = described_class.not_absent_by_schedule_ids([])
        expect(result).to eq({})

        result = described_class.not_absent_by_schedule_ids(nil)
        expect(result).to eq({})
      end

      it 'returns grouped applicants by schedule_id excluding absent statuses' do
        result = described_class.not_absent_by_schedule_ids([schedule1.id, schedule2.id])

        expect(result[schedule1.id]).to include(booked_applicant)
        expect(result[schedule1.id]).to include(joined_applicant)
        expect(result[schedule2.id]).to be_nil
      end
    end

    describe '.get_booked_applicant_by_staff_id' do
      let!(:staff1) { create(:staff) }
      let!(:staff2) { create(:staff) }
      let!(:first_round_schedule) { create(:training_first_round, start_time: 2.days.from_now) }
      let!(:second_round_schedule) { create(:training_second_round, start_time: 3.days.from_now) }
      let!(:first_round_applicant) { create(:booked_schedule_applicant, staff: staff1, training_schedule: first_round_schedule) }
      let!(:second_round_applicant) { create(:booked_schedule_applicant, staff: staff2, training_schedule: second_round_schedule) }

      before do
        # Mock Settings.datetime.formats
        allow(Settings).to receive_message_chain(:datetime, :formats).and_return('%Y/%m/%d %H:%M')
      end

      it 'returns hash with first_session and second_session data for booked applicants' do
        result = described_class.get_booked_applicant_by_staff_id([staff1.id, staff2.id])

        expect(result).to have_key(:first_session)
        expect(result).to have_key(:second_session)
        expect(result[:first_session][staff1.id]).to be_present
        expect(result[:second_session][staff2.id]).to be_present
      end

      it 'returns empty sessions when no booked applicants found' do
        result = described_class.get_booked_applicant_by_staff_id([999])

        expect(result[:first_session]).to be_empty
        expect(result[:second_session]).to be_empty
      end

      it 'only includes booked applicants, not other statuses' do
        absent_applicant = create(:absent_schedule_applicant, staff: staff1, training_schedule: first_round_schedule)

        result = described_class.get_booked_applicant_by_staff_id([staff1.id])

        # Should still have the booked applicant data, not affected by absent applicant
        expect(result[:first_session][staff1.id]).to be_present
      end
    end
  end

  describe 'Private Methods' do
    let(:applicant) { build(:training_schedule_applicant, staff: staff, training_schedule: training_schedule) }

    describe '#update_is_full' do
      it 'raises error when training_schedule is nil' do
        applicant.training_schedule = nil
        expect { applicant.save! }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end

    describe '#clear_cancel_reason' do
      it 'does not clear cancel_reason when applicant is unavailable' do
        applicant.schedule_status_code = :absent_with_notice
        applicant.cancel_reason = 'Test reason'
        applicant.save!

        # Simulate the callback being called
        applicant.send(:clear_cancel_reason)

        expect(applicant.cancel_reason).to eq('Test reason')
      end

      it 'clears cancel_reason when applicant is available' do
        applicant.schedule_status_code = :booked
        applicant.cancel_reason = 'Test reason'

        # Simulate the callback being called
        applicant.send(:clear_cancel_reason)

        expect(applicant.cancel_reason).to be_nil
      end
    end
  end
end
