class Location < ApplicationRecord
  extend OrderAsSpecified

  attr_accessor :disable_create_survey, :thumbnail_parameters, :thumbnail_background_parameters

  acts_as_paranoid
  strip_attributes

  include FormatPostalCodeBeforeSave
  include SetLatLongDecorator
  include ImageDecorator
  include LocationDecorator

  TRAINING_CENTERS_CACHED_KEY = "training_center_locs".freeze

  SEARCH_ADDRESS_ATTRS = %w(city street_number building prefecture_name)
  LIMIT_STATIONS = 20
  REQUIRE_ORDER_ATTRS = %i(prefecture_id city street_number tel postal_code)
  BOOLEAN_ATTRS = %i(is_store_parking_area_usable)
  PIC_TYPES = %i(haken_destination claim mandator)
  STATION_NAME_FIELDS = %w(station_1 station_2 station_3 station_4 station_5)
  LOCATION_PIC_TYPES = {haken_destination: 1, claim: 2, mandator: 3}
  DEFAULT_SURVEY_ANSWER = {answer_18: true}
  LOCATION_TYPE = {
    Settings.location_type[0] => :lawson,
    Settings.location_type[1] => :nature_lawson,
    Settings.location_type[2] => :lawson_store_100
  }
  JOB_LOGO_URL = {
    lawson: ActionController::Base.helpers.asset_path("lawson_logo_update.png"),
    nature_lawson: ActionController::Base.helpers.asset_path("natural-lawson.png"),
    lawson_store_100: ActionController::Base.helpers.asset_path("lawson-store.png")
  }
  JOB_THUMBNAL_URL = {
    lawson: ActionController::Base.helpers.asset_path("<EMAIL>"),
    nature_lawson: ActionController::Base.helpers.asset_path("nature_lawson_sm.jpg"),
    lawson_store_100: ActionController::Base.helpers.asset_path("lawson_store.jpg")
  }
  NOT_LAWSON = "not_lawson"
  STATION_KEY_AND_ASSOCIATION = {
    station_1: "stations_1",
    station_2: "stations_2",
    station_3: "stations_3",
    station_4: "stations_4",
    station_5: "stations_5"
  }

  OUTSIDE_LAWSON_ATTRS = %i(job_content personal_things clothes special_note)
  API_BASIC_INFO = %i(id postal_code name job_content tel fax latitude
    longitude is_store_parking_area_usable caution_to_staff personal_things clothes special_note city)
  API_JOB_METHODS = %i(address prefecture_city camelized_type thumbnail_background_path
    stations_info_value_only thumbnail_path logo job_categories_text station_1_info
    station_1_short_info only_name corporation_name prefecture_name job_category_name)
  JOB_SEARCH_ATTRS = [:id, :latitude, :longitude, :city, :is_store_parking_area_usable]
  JOB_SEARCH_METHODS = [:station_1_info, :thumbnail_path, :logo, :job_categories_text,
    :station_1_short_info, :only_name, :corporation_name, :prefecture_name]
  format_postal_code :postal_code

  enum :pos_type_id, {old_pos: 1, new_pos: 2, no_pos: 3}
  enum :station_1_transportation_method_id, [:walking, :bus, :taxi], prefix: true
  enum :station_2_transportation_method_id, [:walking, :bus, :taxi], prefix: true
  enum :station_3_transportation_method_id, [:walking, :bus, :taxi], prefix: true
  enum :station_4_transportation_method_id, [:walking, :bus, :taxi], prefix: true
  enum :station_5_transportation_method_id, [:walking, :bus, :taxi], prefix: true
  enum :default_invoice_target, {lawson_invoice: 0, separate_invoice: 1}

  belongs_to :corporation_group
  belongs_to :organization
  belongs_to :prefecture, optional: true
  belongs_to :stations_1, class_name: "Station", foreign_key: :station_1, optional: true
  belongs_to :stations_2, class_name: "Station", foreign_key: :station_2, optional: true
  belongs_to :stations_3, class_name: "Station", foreign_key: :station_3, optional: true
  belongs_to :stations_4, class_name: "Station", foreign_key: :station_4, optional: true
  belongs_to :stations_5, class_name: "Station", foreign_key: :station_5, optional: true
  has_many :user_locations, dependent: :destroy
  has_many :location_job_categories, dependent: :destroy
  has_many :user_order_case_search_conditions, dependent: :destroy
  has_many :users, through: :user_locations
  has_many :location_pics, dependent: :destroy
  has_many :orders, dependent: :restrict_with_error
  has_many :order_cases, through: :orders
  has_many :staff_apply_order_cases, dependent: :destroy
  has_many :staff_like_locations, dependent: :destroy
  has_many :location_evaluation_summaries, dependent: :destroy
  has_many :location_evaluations, dependent: :destroy
  has_many :staff_complaints, dependent: :destroy
  has_many :staff_messages, dependent: :destroy
  has_many :user_arrange_billing_search_conditions, dependent: :destroy
  has_many :owner_notifications, dependent: :destroy
  has_many :last_evaluation_summaries, ->{LocationEvaluationSummary.last_updated},
    class_name: LocationEvaluationSummary.name, foreign_key: :location_id
  has_many :priority_staffs, dependent: :destroy
  has_many :order_templates, dependent: :destroy
  has_many :location_payment_rates, dependent: :destroy
  has_many :billing_payment_templates, dependent: :destroy
  has_one :location_survey, dependent: :destroy
  has_one :now_base_price, ->{current_price.order(start_date: :desc)}, class_name: "LocationPaymentRate"
  has_one :new_base_price, ->{new_price.order(start_date: :desc)}, class_name: "LocationPaymentRate"
  belongs_to :job_category
  mount_uploader :thumbnail, LocationImageFileUploader
  mount_uploader :thumbnail_background, LocationImageFileUploader
  belongs_to :department, class_name: Department.name, foreign_key: :department_id,
    optional: true

  accepts_nested_attributes_for :location_pics, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :location_survey, reject_if: :can_not_create_survey
  accepts_nested_attributes_for :priority_staffs, allow_destroy: true
  LOCATION_ATTRIBUTE = %i(code location_type name name_kana short_name corporation_group_id
    organization_id note postal_code prefecture_id city street_number building tel fax
    email station_1 station_1_walking_time station_2 station_2_walking_time
    station_3 station_3_walking_time station_4 station_4_walking_time department_id
    station_5 station_5_walking_time is_store_parking_area_usable is_approval_required
    haken_acceptance_started_at closed_at pos_type_id station_1_transportation_method_id
    station_2_transportation_method_id station_3_transportation_method_id
    station_4_transportation_method_id station_5_transportation_method_id is_export_timesheet disable_create_survey
    is_auto_matching default_invoice_target
    caution_to_staff caution_to_staff_mail thumbnail thumbnail_background
    is_jacket is_pants is_shoes job_category_id is_training_center) <<
    OUTSIDE_LAWSON_ATTRS << [location_pics_attributes: %i(id pic_type_id department position name tel fax cellphone
      email note _destroy)] << [priority_staffs_attributes: %i(id location_id staff_id _destroy)] <<
        {location_survey_attributes: LocationSurvey.column_names.map(&:to_sym)}
  ORDER_ATTR_JSON = %i(id name postal_code city street_number building tel fax
    is_store_parking_area_usable overall_started_at organization_id organization_full_name
    default_invoice_target)
  CATEGORY_ATTRS = %i(job_content caution_to_staff personal_things clothes special_note)

  validates :name, :creator_id, :updater_id, :organization_id, :corporation_group_id,
    :postal_code, :prefecture_id, :city, :street_number, :tel, :short_name, presence: true
  validates :job_category_id, presence: true
  validates :name, name_without_special_character: true
  validates :name_kana, format_rule: {rule: :katakana_location_and_account, message: :katakana_name}
  validates :code, format_rule: {rule: :half_size}
  validates :postal_code, format_rule: {rule: :postal_code}
  validates :email, :tel, format_rule: true
  validates :fax, format_rule: {rule: :tel}
  validates :location_type, inclusion: {in: Settings.location_type, allow_blank: true}
  validates :station_1_walking_time, :station_2_walking_time, :station_3_walking_time,
    :station_4_walking_time, :station_5_walking_time,
    numericality: {only_integer: true, greater_than_or_equal_to: 0}, allow_nil: true
  validates :closed_at, valid_date: true
  validates :station_1, :station_2, :station_3, :station_4, :station_5, both_field_exists: true
  validates :corporation_group_id, change_association: {related_models: [LocationPic]}
  validates :corporation_group_id, :organization_id, change_association: {related_models: [Order],
    message: :exists_order}
  validates(*OUTSIDE_LAWSON_ATTRS, length: {maximum: 255})
  validate :exists_order_after_closed_at, on: :update
  validate :uniqueness_of_pritority_staffs, on: [:create, :update]
  validate :must_input_thumbnail, on: [:create, :update]

  delegate :full_name, :violation_day_str, :violation_day, :is_lawson, :thumbnail_path,
    to: :corporation_group, prefix: true, allow_nil: true
  delegate :id, to: :location_survey, prefix: true, allow_nil: true
  delegate :full_name, to: :organization, prefix: true, allow_nil: true
  delegate :name, to: :prefecture, prefix: true, allow_nil: true
  delegate :name, :station_name, to: :stations_1, prefix: true, allow_nil: true
  delegate :name, to: :stations_2, prefix: true, allow_nil: true
  delegate :name, to: :stations_3, prefix: true, allow_nil: true
  delegate :name, to: :stations_4, prefix: true, allow_nil: true
  delegate :name, to: :stations_5, prefix: true, allow_nil: true
  delegate :name, to: :job_category, prefix: true, allow_nil: true
  delegate :name, to: :department, prefix: true, allow_nil: true

  before_save :refresh_cached_content, if: :is_training_center_changed?
  before_save :check_lat_long, :set_outside_lawson_attr

  after_create :create_user_locations_for_owner
  after_update :cancel_order_case_and_arrangement_after_closed_at

  scope :by_corporation_group_id, ->group_id{where(corporation_group_id: group_id)}
  scope :by_corporation_id, ->corporation_id do
    joins(:corporation_group).where(corporation_groups: {corporation_id: corporation_id})
  end
  scope :by_configured_store_com, -> do
    location_ids = LocationPic.valid_step_1_location_ids
    by_ids(location_ids)
      .where.not(is_store_parking_area_usable: nil)
      .joins(:corporation_group)
      .where.not(corporation_groups: {violation_day: nil})
  end
  scope :by_not_configured_store_com, -> do
    where.not(id: by_configured_store_com.pluck(:id))
  end
  scope :search_and_sort_by_ids, ->ids do
    where(id: ids).order(sanitize_sql_for_order([Arel.sql("FIELD(locations.id, ?)"), ids]))
  end
  scope :by_name, ->name{where("locations.name LIKE ?", "%#{sanitize_sql_like(name)}%")}
  scope :by_code, ->code{where(code: code)}
  scope :by_surveyed, ->{where(id: LocationSurvey.pluck(:location_id))}
  scope :by_not_survey, ->{where.not(id: LocationSurvey.pluck(:location_id))}
  scope :by_has_violation_file, -> do
    joins(:corporation_group).where.not(corporation_groups: {violation_file: nil})
  end
  scope :by_addresses, ->addresses do
    group_conditions = RansackKeywordExtractor.or_contains addresses, SEARCH_ADDRESS_ATTRS
    ransack(m: "or", g: group_conditions).result
  end
  scope :by_violation_day, ->violation_start, violation_end do
    start_at = violation_start.present? ? ServerTime.parse(violation_start) : nil
    end_at = violation_end.present? ? ServerTime.parse(violation_end)&.end_of_day : nil
    locations = self.joins(:corporation_group)

    locations = if start_at && end_at
                  locations.where("violation_day >= ? and violation_day <= ?", start_at, end_at)
                elsif start_at
                  locations.where("violation_day >= ?", start_at)
                else
                  locations.where("violation_day <= ?", end_at)
                end
    locations
  end

  scope :search_all_text, ->keyword, _field = [] do
    return if keyword.blank?

    fields = %w(name code) if fields.blank?
    query_condition = {m: "or", g: []}
    fields.map do |field|
      query_condition[:g] << {"#{field}_cont".to_sym => keyword.gsub("　", " ")}
      query_condition[:g] << {"#{field}_cont".to_sym => keyword.gsub(" ", "　")}
    end
    ransack(m: "or", g: [query_condition]).result
  end

  scope :by_order_case_from, ->order_case_from do
    self.joins(orders: :order_cases).where("order_cases.case_started_at >= ?",
      ServerTime.parse(order_case_from).beginning_of_day)
  end

  scope :by_order_case_to, ->order_case_to do
    self.joins(orders: :order_cases).where("order_cases.case_started_at <= ?",
      ServerTime.parse(order_case_to).end_of_day)
  end

  scope :by_organization_id, ->organization_id{where(organization_id: organization_id)}
  scope :search_by, ->keyword, fields = %w(name name_kana short_name) do
    return if keyword.blank?

    group_conditions = RansackKeywordExtractor.or_contains keyword, fields
    ransack(m: "or", g: group_conditions).result
  end
  scope :where_keep_order, ->ids do
    where(id: ids).order(sanitize_sql_for_order([Arel.sql("FIELD(id, ?)"), ids]))
  end
  scope :is_active, -> do
    where("locations.closed_at IS NULL OR locations.closed_at > ?", ServerTime.now)
  end

  scope :by_ids, ->ids do
    where(id: ids)
  end

  scope :closed, -> do
    where("closed_at <= ?", ServerTime.now)
  end

  scope :not_closed_until, ->date do
    where("closed_at IS NULL OR closed_at >= ?", date.in_time_zone)
  end

  scope :by_pic_department_id, ->department_id do
    joins(:corporation_group).where("corporation_groups.pic_department_id = ?", department_id)
  end

  scope :filter_by_payment_rates, ->(is_all, corporation_group_ids = [], location_name = "") do
    locations = joins(:location_payment_rates)
    unless is_all
      locations = locations.by_corporation_group_id(corporation_group_ids)
        .by_name(location_name)
    end
    locations.distinct
  end

  scope :not_auto_matching, ->{where(is_auto_matching: false)}

  scope :training_center_locs, ->{includes(:prefecture).where(is_training_center: true)}

  def prefecture_json_format
    {
      id: id,
      prefecture_name: prefecture_name,
      name: name
    }
  end

  def basic_info
    data = self.stations_info
    data[:postal_code] = self.postal_code
    data[:city] = [self.prefecture_name, self.city].join(" ")
    data[:address] = [self.street_number, self.building].join
    data[:id] = self.id
    data[:get_type] = self.get_type
    data[:thumbnail_path] = self.thumbnail_background_path
    data[:name] = self.name
    data[:job_category_name] = self.job_category_name
    data[:job_content] = self.job_content
    data[:tel] = self.tel
    data[:fax] = self.fax
    data[:latitude] = self.latitude
    data[:longitude] = self.longitude
    data[:is_store_parking_area_usable] = self.is_store_parking_area_usable
    data[:caution_to_staff] = self.caution_to_staff
    data[:personal_things] = self.personal_things
    data[:clothes] = self.clothes
    data[:special_note] = self.special_note
    data
  end

  def prefecture_city
    [self.prefecture_name, self.city].join
  end

  def stations_info
    ids_map = {
      self.station_1 => :station_1,
      self.station_2 => :station_2,
      self.station_3 => :station_3,
      self.station_4 => :station_4,
      self.station_5 => :station_5
    }
    stations_infomation = {}
    Station.where(id: ids_map.keys).includes(:railway_line).find_each do |station|
      station_field = ids_map[station.id]
      trans_time = self["#{station_field}_walking_time"].to_i
      trans_method = self["#{station_field}_transportation_method_id"].to_sym
      trans_name = I18n.t("admin.location.station_transportation_methods")[trans_method]
      minutes = I18n.t("admin.location.location_tab.minute")
      trans_info = [trans_name, trans_time, minutes].join if trans_time != 0
      stations_infomation[station_field] = [station.name_with_railway_line, trans_info].join(" ")
    end
    stations_infomation
  end

  def stations_info_value_only
    self.stations_info.values
  end

  def full_name
    name
  end

  def corporation_name
    self&.corporation_group&.corporation&.full_name.to_s
  end

  def evaluation
    self&.last_evaluation_summaries&.first
  end

  def id_with_leading_zeros
    self.id.to_s.rjust(Settings.num_leading_zeros.order_id, "0")
  end

  def full_address
    [prefecture_name, city, street_number, building].compact.join
  end

  def address
    [street_number, building].compact.join
  end

  def refresh_cached_content
    redis ||= Lawson::RedisConnector.new
    redis.del(TRAINING_CENTERS_CACHED_KEY)
  end

  def check_lat_long
    return unless self.postal_code_changed? || self.prefecture_id_changed? || self.city_changed? ||
      self.street_number_changed? || self.building_changed?

    set_lat_long
  end

  def set_lat_long
    result = set_lat_long_value(full_address, prefecture_name, postal_code)
    self.latitude, self.longitude = result
  end

  def get_coordinates
    [self.latitude, self.longitude]
  end

  STATION_NAME_FIELDS.each do |station_field|
    define_method "#{station_field}_info" do
      return unless self[station_field]

      station_association = STATION_KEY_AND_ASSOCIATION[station_field.to_sym]
      trans_time = self["#{station_field}_walking_time"].to_i
      trans_method = self["#{station_field}_transportation_method_id"].to_sym
      trans_name = I18n.t("admin.location.station_transportation_methods")[trans_method]
      minutes = I18n.t("admin.location.location_tab.minute")
      trans_info = [trans_name, trans_time, minutes].join if trans_time != 0
      [self.send(station_association)&.send(:name_with_railway_line), trans_info].join(" ")
    end
  end

  def station_1_short_info
    trans_time = self.station_1_walking_time.to_i
    trans_method = self.station_1_transportation_method_id.to_sym
    trans_name = I18n.t("admin.location.station_transportation_methods")[trans_method]
    minutes = I18n.t("admin.location.location_tab.minute")
    trans_info = [trans_name, trans_time, minutes].join if trans_time != 0
    [self.stations_1_station_name, trans_info].join(" ")
  end

  def only_name
    self.name.gsub(self.get_type, "").strip
  end

  def is_valid_order_info?
    REQUIRE_ORDER_ATTRS.all?{|attr| self.send(attr).present?} &&
      BOOLEAN_ATTRS.all?{|attr| !self.send(attr).nil?}
  end

  def display_order_violation_day
    return if self.corporation_group_violation_day.blank?

    I18n.l(self.corporation_group_violation_day.to_date, format: Settings.date.day_and_date)
  end

  def days_to_violation_day
    return if self.corporation_group_violation_day.blank?

    ((self.corporation_group_violation_day - ServerTime.now.beginning_of_day) / 1.day).to_i
  end

  def closed_day_error_message
    return if self.closed_at.blank?

    message = nil
    message = I18n.t "activerecord.errors.models.order.attributes.location_id.closed_location" if self.closed_at <= ServerTime.now.beginning_of_day
    message
  end

  def location_not_survey_error_message
    if self.is_show_survey && self.location_survey.nil? && self.closed_at.nil? &&
      !Order.by_locations(self.id).where("created_at <= ? ", Settings.location.survey_date_required).exists? &&
      !corporation_group.corporation.labor?
      message = I18n.t "activerecord.errors.models.order.attributes.location_id.not_survey"
    end
    message
  end

  def valid_order_step_1_info?
    valid_transaction = self.corporation_group.corporation.transaction_error_message.nil?
    is_valid_order_info? && valid_violation_day? && valid_closed_day? && valid_pics_info? &&
      valid_transaction
  end

  def station_info
    STATION_NAME_FIELDS.map do |field|
      station_id = self.send(field)
      next unless station_id

      station = Station.find_by(id: station_id)
      {
        line_name: station&.railway_line&.name,
        station_name: station&.name,
        walking_time: self.send("#{field}_walking_time")
      }
    end.compact
  end

  def total_recruiting_job
    self.order_cases.count(&:recruiting?)
  end

  def get_type
    if corporation_group_is_lawson
      (name.scan Settings.regex.admin.location_type)[0] || Settings.location_type[0]
    else
      NOT_LAWSON
    end
  end

  def camelized_type
    case self.get_type
    when Settings.location_type[0]
      Settings.location_type_camel[0]
    when Settings.location_type[1]
      Settings.location_type_camel[1]
    when Settings.location_type[2]
      Settings.location_type_camel[2]
    when NOT_LAWSON
      Settings.location_type_camel[3]
    else
      Settings.location_type_camel[0]
    end
  end

  def is_active_at_moment? moment
    self.closed_at.blank? || self.closed_at.to_date >= moment.to_date
  end

  def thumbnail_path
    unless corporation_group_is_lawson
      path = thumbnail_url
      path = thumbnail_url(:thumbnail_custom) if thumbnail.thumbnail_custom.file&.exists?
      return path
    end
    type = LOCATION_TYPE[self.get_type]
    return JOB_THUMBNAL_URL[:lawson] unless type

    JOB_THUMBNAL_URL[type]
  end

  def remove_thumbnail
    return unless corporation_group_is_lawson

    self.remove_thumbnail!
    self.save
  end

  def logo
    return corporation_group_thumbnail_path unless corporation_group_is_lawson

    type = LOCATION_TYPE[self.get_type]
    return JOB_LOGO_URL[:lawson] unless type

    JOB_LOGO_URL[type]
  end

  def job_categories_text
    job_category_name || Settings.lawson_job_category
  end

  def new_location_survey
    return build_location_survey(DEFAULT_SURVEY_ANSWER) unless new_record? || location_survey

    location_survey
  end

  def thumbnail_background_path
    return thumbnail_background_url unless thumbnail_background.thumbnail_background_custom.file&.exists?

    thumbnail_background_url(:thumbnail_background_custom)
  end

  def priority_staff_options
    staff_ids = priority_staffs.pluck(:staff_id)
    Staff.includes(:account).where(id: staff_ids).type_staff
      .map{|s| [[s.staff_number, s.account_name].join(" "), s.id]}
  end

  class << self
    def training_center_locs_json_arr
      redis ||= Lawson::RedisConnector.new
      cached_content = redis.get(TRAINING_CENTERS_CACHED_KEY)
      return cached_content if cached_content.present?

      result = sorted_training_center_locs_by_prefecture.values.flatten.map(&:prefecture_json_format).to_json
      redis.set(TRAINING_CENTERS_CACHED_KEY, result, ex: 1.hour)
      result
    end

    def sorted_training_center_locs_by_prefecture
      training_centers_hash = training_center_locs.group_by(&:prefecture_name)

      if Settings.prefectures_sorting_order.present?
        order = Settings.prefectures_sorting_order
        selected_keys_to_sort = training_centers_hash.slice(*order)

        return selected_keys_to_sort.merge(training_centers_hash)
      end

      training_centers_hash
    end

    def order_in_owner_info sort_key, is_descending
      order_type = is_descending ? :desc : :asc
      case sort_key.to_sym
      when :id, :name, :short_name
        order(sort_key.to_sym => order_type)
      when :haken_destination_name
        order_type = order_type.to_s
        order("haken_destination_name #{order_type}")
      when :claim_name
        order_type = order_type.to_s
        order("claim_name #{order_type}")
      when :mandator_name
        order_type = order_type.to_s
        order("mandator_name #{order_type}")
      else
        order(id: :desc)
      end
    end

    def build_select_query
      query = "locations.id, locations.name, locations.short_name,"
      query << LOCATION_PIC_TYPES.map do |type, value|
        build_location_pics_name_query(type, value)
      end.join(",")
      query
    end

    def build_location_pics_name_query type, pic_type_id
      "(SELECT name
        FROM location_pics
        WHERE location_pics.location_id = locations.id
        AND location_pics.pic_type_id = #{pic_type_id}
        ORDER BY updated_at DESC LIMIT 1) AS #{type}_name"
    end

    def options_for_select_with_corporation corporation_id
      self.by_corporation_id(corporation_id).pluck(:name, :id)
    end

    def options_for_select_training_schedule_form
      self.training_center_locs.pluck :name, :id
    end

    def ransackable_attributes _auth_object = nil
      %w(code name name_kana short_name) + SEARCH_ADDRESS_ATTRS
    end
  end

  def self.categories_by_locations location_ids, page = nil, per_page = nil
    job_categories = LocationJobCategory.where(location_id: location_ids).group_by(&:location_id)
    data = []
    if page.blank? || per_page.blank?
      locations = Location.where(id: location_ids)
    else
      locations = Location.search_and_sort_by_ids(location_ids).page(page).per(per_page)
    end
    locations
      .includes(:job_category, :prefecture, stations_1: [:railway_line], corporation_group: [:corporation])
      .as_json(only: Location::JOB_SEARCH_ATTRS, methods: Location::JOB_SEARCH_METHODS)
      .each do |location|
      loc_id = location["id"]
      loc_categories = job_categories[loc_id] || []
      loc = location
      loc["job_category_key"] = "#{loc_id}_0"
      loc["location_job_category_id"] = 0
      data << loc
      loc_categories.each do |loc_category|
        new_loc = loc.dup
        new_loc["location_job_category_id"] = loc_category.id
        new_loc["job_category_key"] = "#{loc_id}_#{loc_category.id}"
        new_loc["job_categories_text"] = loc_category.job_categories_text
        new_loc["thumbnail_path"] = loc_category.thumbnail_path
        data << new_loc
      end
    end
    data
  end

  def name_and_code
    "#{code} #{name}"
  end

  def get_stocon_valid_dates
    account_ids = self.users.pluck(:account_id)
    return [] if account_ids.blank?

    Account.where(id: account_ids, location_code: self.code)
      .pluck(:valid_start_date, :valid_end_date)
  end

  private

  def valid_violation_day?
    days_to_violation_day.to_i > 0
  end

  def valid_closed_day?
    self.closed_at.blank? || self.closed_at > ServerTime.now.beginning_of_day
  end

  def valid_pics_info?
    pics_data = OrderLocationDataService.new(self.id).pics_data
    PIC_TYPES.all?{|attr| pics_data[attr].present?}
  end

  def create_user_locations_for_owner
    owners = self.corporation_group.corporation.users.owner
    user_location_params = owners.map do |owner|
      {
        user_id: owner.id,
        location_id: self.id,
        corporation_group_id: self.corporation_group_id
      }
    end
    UserLocation.import([:user_id, :location_id, :corporation_group_id], user_location_params)
  end

  def exists_order_after_closed_at
    order_exists = self.orders.exist_order_less_than_closed_at(self.closed_at)
    errors.add(:closed_at, :exists_order_after_closed_at) if order_exists.present?
  end

  def cancel_order_case_and_arrangement_after_closed_at
    return unless self.closed_at_changed?

    arrangement_belongs_to_self_ids = Arrangement.belongs_to_location(self.id)
      .after_time(self.closed_at).pluck(:id)
    CancelArrangeAfterClosedAtWorker.perform_async arrangement_belongs_to_self_ids, self.updater_id
  end

  def set_outside_lawson_attr
    return unless corporation_group_is_lawson

    OUTSIDE_LAWSON_ATTRS.each do |attr|
      self[attr] = ""
    end
  end

  def can_not_create_survey
    new_record? || User.by_role_can_survey(self.id).blank? || self.disable_create_survey&.true?
  end

  def uniqueness_of_pritority_staffs
    staff_ids = priority_staffs.reject(&:_destroy).map(&:staff_id)
    return if staff_ids.length == staff_ids.uniq.length

    errors.add :id, :invalid
    overlap_staff_ids = staff_ids.select{|id| staff_ids.count(id) > 1}.uniq
    priority_staffs.each do |ps|
      ps.errors.add :staff_id, :unique if overlap_staff_ids.include?(ps.staff_id)
    end
  end

  def must_input_thumbnail
    return if corporation_group&.is_lawson

    errors.add :thumbnail, :blank if thumbnail.blank?
    errors.add :thumbnail_background if thumbnail_background.blank?
  end
end
