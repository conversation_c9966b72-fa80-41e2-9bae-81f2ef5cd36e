class TrainingScheduleApplicant < ApplicationRecord
  acts_as_paranoid

  include TrainingScheduleApplicantDecorator
  include ExportTrainingScheduleApplicantDecorator

  ABSENT_STATUSES = [2, 3].freeze
  NOT_ABSENT_STATUSES = [0, 1].freeze

  belongs_to :staff, ->{unscope(where: :deleted_at)}
  belongs_to :training_schedule, ->{unscope(where: :deleted_at)}

  validates :staff, presence: true

  enum :schedule_status_code, {booked: 0, joined: 1, absent_with_notice: 2, absent_without_notice: 3}

  delegate :formatted_datetime, :training_date_weekday, :location, :formatted_absent_deadline, :able_to_absent?,
    :location_name, :location_id, :training_session_code, :working_date,
    :start_time_text, :end_time_text, :remove_other_schedules_and_notify_trainer,
    :person_in_charge_email, :person_in_charge_name, :location_prefecture_name, :notify_trainer,
    to: :training_schedule, allow_nil: true
  delegate :name, :account_email, :account_name_kana, :total_work_experience, :evaluation,
    :home_tel, :tel, :email, :age, :uniform_size, :gender_id, :display_workable_time,
    :is_working_car, :level_up_training, :account_name_kana_to_half_size,
    to: :staff, prefix: true
  delegate :staff_number, to: :staff

  scope :by_staff_id, ->staff_ids{where(staff_id: staff_ids)}

  scope :by_schedule_id, ->schedule_ids{where(training_schedule_id: schedule_ids)}

  scope :first_sessions, -> do
    includes(:training_schedule).where(training_schedules: {training_session_code: :training_first_round})
  end

  scope :second_sessions, -> do
    includes(:training_schedule).where(training_schedules: {training_session_code: :training_second_round})
  end

  scope :single_sessions, -> do
    includes(:training_schedule).where(training_schedules: {training_session_code: :single_session})
  end

  scope :upcoming_trainings, ->(start_time, end_time) do
    joins(:training_schedule)
      .booked
      .where("training_schedules.start_time >= ? AND training_schedules.start_time < ?",
        start_time, end_time)
  end

  scope :not_absent, -> do
    where.not(schedule_status_code:
      [schedule_status_codes[:absent_with_notice], schedule_status_codes[:absent_without_notice]])
  end

  # Callbacks ---
  before_update :clear_cancel_reason, if: :schedule_status_code_changed?
  after_save :update_is_full
  # ------

  def is_unavailable?
    schedule_status_code.in? %w(absent_with_notice absent_without_notice)
  end

  def send_new_schedule_email
    StaffMailer.notify_staff_new_training_schedule(
      staff,
      training_schedule.training_time_with_day,
      staff&.account_name,
      location_name,
      training_schedule.location_formatted_full_address
    ).deliver_now
  end

  def send_new_schedule_sms
    MessageSenderService.notify_staff_new_training_schedule(
      staff&.account&.tel,
      datetime: training_schedule.training_time_with_day,
      user_name: staff&.account_name,
      location_name: location_name,
      location_full_address: training_schedule.location&.formatted_full_address
    )
  end

  def send_fcm_to_staff admin_id, notification_type
    app_notification_option = {
      staff_id: staff_id,
      creator_type: :by_system,
      notification_type: notification_type,
      params: {
        location_name: location_name,
        job_date_time: formatted_datetime
      }
    }
    if admin_id.present?
      app_notification_option[:creator_type] = :admin
      app_notification_option[:creator_id] = admin_id
    end
    jid = AppSendNotificationWorker.perform_async([app_notification_option])
    p jid
  end

  class << self
    def not_absent_by_schedule_ids schedule_ids
      return {} if schedule_ids.blank?

      TrainingScheduleApplicant
        .joins(:training_schedule)
        .where("training_schedule_id IN (?) AND schedule_status_code NOT IN (?)",
          schedule_ids, TrainingScheduleApplicant::ABSENT_STATUSES)
        .group_by{|app| app[:training_schedule_id]}
    end

    def get_booked_applicant_by_staff_id staff_ids
      applicants = select("training_schedule_applicants.staff_id as staff_id, " \
        "training_schedules.training_session_code as session_code, " \
        "MAX(training_schedules.start_time) AS start_time")
        .joins("INNER JOIN training_schedules ON " \
          "training_schedule_applicants.training_schedule_id = training_schedules.id")
        .where("training_schedule_applicants.schedule_status_code = ? AND " \
          "training_schedule_applicants.deleted_at IS NULL AND " \
          "training_schedule_applicants.staff_id in (?)",
          TrainingScheduleApplicant.schedule_status_codes["booked"], staff_ids)
        .group("training_schedule_applicants.staff_id , training_schedules.training_session_code")
      applicant_hash = {
        first_session: {},
        second_session: {}
      }
      applicants.each do |applicant|
        training_date = applicant.start_time&.strftime(Settings.datetime.formats)
        if applicant.session_code == TrainingSchedule.training_session_codes["training_first_round"]
          applicant_hash[:first_session][applicant.staff_id] = training_date
        else
          applicant_hash[:second_session][applicant.staff_id] = training_date
        end
      end
      applicant_hash
    end
  end

  private

  def update_is_full
    raise ActiveRecord::RecordInvalid unless training_schedule

    valid_applicants_count = TrainingScheduleApplicant.by_schedule_id(training_schedule_id).not_absent.size

    raise ActiveRecord::RecordInvalid if training_schedule.total_portion < valid_applicants_count

    updated_params = training_schedule.total_portion == valid_applicants_count
    return if training_schedule.is_full == updated_params

    training_schedule.update!(is_full: updated_params)
  end

  def clear_cancel_reason
    return if self.is_unavailable?

    self.cancel_reason = nil
  end
end
