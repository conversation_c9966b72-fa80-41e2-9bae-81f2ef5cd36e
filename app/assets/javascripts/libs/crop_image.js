var cropper;
var _this = this;
var modalPreviewThumbnail = $(".st-work__modal-preview-image");
var previewThumbnail = $(".st-work__modal-preview-image img#preview-thumbnail");
var btnRotateLeft90 = $(".st-work__modal-preview-image .btn-rotate-left-90");
var btnRotateRight90 = $(".st-work__modal-preview-image .btn-rotate-right-90");
var btnCloseModal =  $(".st-work__modal-preview-image .close");
var btnSaveThumbnail = $(".st-work__modal-preview-image .save-thumbnail-cropped");

// Set events for preview image modal
btnRotateLeft90.on("click", function(){
  cropper.cropper("rotate", 90);
});

btnRotateRight90.on("click", function(){
  cropper.cropper("rotate", -90);
});

btnCloseModal.on("click", function(){
  $("#"+_this._input.id).val("");
  modalPreviewThumbnail.modal("hide"); 
});

btnSaveThumbnail.on("click", function(){
  event.preventDefault();
  var imageCropped = previewThumbnail.cropper("getCroppedCanvas");
  var base64encodedImage = imageCropped.toDataURL();
  _this._previewThumbnailAfterSave.attr("src", base64encodedImage);
  modalPreviewThumbnail.modal("hide");
});

function CropImage(_config) {
  this._input = _config.input;
  this._inputName = _config.inputName;
  this._previewThumbnailAfterSave = _config.previewThumbnailAfterSave;
  this._cropper = _config.cropper;
  _this = this;
}

CropImage.prototype.previewImage = function() {
  var reader = new FileReader();
  previewThumbnail.cropper("destroy");
  reader.onload = function(evt) {
    var img = new Image();
    img.src = evt.target.result;
    img.onload = function() {
      _this.openPreviewModal(img.src);
    }
  };
  reader.readAsDataURL(_this._input.files[0]);
}

CropImage.prototype.openPreviewModal = function(src) {
  previewThumbnail.attr("src", src);
  $(".cropper-canvas img, .cropper-view-box img").attr("src", src);
  var aspectRatio = _this._cropper.resizeWidth/_this._cropper.resizeHeight;
  cropper = previewThumbnail.cropper({
    viewMode: 1,
    aspectRatio: aspectRatio,
    minContainerWidth: 570,
    minContainerHeight: 400,
    minCropBoxWidth: 30,
    minCropBoxHeight: 30,
    movable: true,
    rotatable: true,
    responsive: true,
    checkOrientation: false,
    crop: function(event) {
      var parameters = {
        crop_x: event.x,
        crop_y: event.y,
        crop_h: event.height,
        crop_w: event.width,
        rotate: event.rotate,
        resize_width: _this._cropper.resizeWidth,
        resize_height: _this._cropper.resizeHeight
      };
      _.forEach(parameters, function(value, key){
        $("[name="+'"'+_this._inputName+'['+key+']"]').val(value);
      });
    }
  });
  modalPreviewThumbnail.modal("show") 
}