$.ajaxSetup({
  beforeSend: function(xhr) {
    xhr.setRequestHeader('X-CSRF-Token', $('meta[name="csrf-token"]').attr('content'));
  }
});

$.ajaxPrefilter(function( options, originalOptions, jqXHR ) {
  var hostNamePrefix = $("#hostNamePrefix").attr("href");
  var hostNameRegex = new RegExp(hostNamePrefix, "i")
  if (hostNamePrefix && !(options.url.search(hostNameRegex) == 0 || options.url.search(/https?/) == 0)) {
    options.url = hostNamePrefix + encodeURIComponent(options.url);
  };
});

(function($) {
  $.resetFormError = function() {
    $(".has-error").removeClass("has-error");
    $(".custom-error").removeClass("custom-error");
    $("a[data-toggle='tab']").removeClass("error-tab");
    $(".table-cell-error").removeClass("table-cell-error");
    $(".help-block").addClass("hide");
  }

  $.lawsonAjaxStaffEditForm = function(response, extraErrorElements, autoNestedForm, radioErrors, originErrorModels) {
    radioErrors = radioErrors || []
    if(!response.status) {
      if(response.nested_errors) {
        $.resetFormError();

        var nestedErrorMessages = JSON.parse(response.nested_errors);
        var base_keys = _.keys(extraErrorElements);
        var errorMessages = JSON.parse(response.errors);
        var autoNestedForm = autoNestedForm || false;
        $.each(errorMessages, function(key, value) {
          var $inputElement = $("[name$='[" + key + "]']").not("[name*='_attributes']").not("[type='hidden']");
          if (_.isEmpty(value)) return;
          if (_.isUndefined(extraErrorElements[key])) {
            if (_.includes(radioErrors, key))
              addErrorToRadio($inputElement, value[0]);
            else
              addErrorToInput($inputElement, value[0]);
          }
          else
            addErrorToInput($(extraErrorElements[key]), value[0]);
        });

        $.each(nestedErrorMessages, function(modelNested, values) {
          if (autoNestedForm) {
            if (originErrorModels && _.includes(originErrorModels, modelNested)) {
              _.forEach(values, function(value) {
                if (!_.isEmpty(value)) {
                  $("#origin-error-" + modelNested.replace(/_/g, "-")).removeClass("hide");
                  $("#origin-error-" + modelNested.replace(/_/g, "-")).parents(".form-group").addClass("has-error");
                  return false;
                }
              })
            }
            $("." + modelNested.replace(/_/g, "-") + ":not(.ng-hide)").each(function(key, inputElement){
              if (values[key]){
                $.each(values[key], function(field, errors){
                  if(_.isEqual(field, "department")) field = "department_id";
                  var $inputElement = $(inputElement).find("[name$='[" + field + "]']").not("[type='hidden']");
                  if (_.includes(radioErrors, key)) {
                    addErrorToRadio($inputElement, value[0]);
                  } else {
                    var $parentElem = $inputElement.parents(".nested-attr");
                    if ($parentElem.length == 0)
                      addErrorToInput($inputElement, errors[0]);
                    else
                      addErrorToInput($inputElement, errors[0], $parentElem);
                  }
                });
              }
            });
          }

          $.each(values, function(key, errors) {
            if(!_.includes(base_keys, key)) {
              var $inputElement = $("[name$='[" + modelNested +'_attributes][' + key + "]']").not("[type='hidden']");
              if (_.includes(radioErrors, key)) {
                addErrorToRadio($inputElement, errors[0]);
              } else {
                addErrorToInput($inputElement, errors[0]);
                $inputElement.closest(".form-group").find("input.black-input:text").addClass("custom-error");
              }
            }
          });

          extraErrorElements = extraErrorElements || {};
          $.each(extraErrorElements, function(key, selector) {
            var keyAttr = _.isEqual(key.split("_")[0], "base") ? "base" : key;
            var errorElem = "." + keyAttr + "-" + modelNested;
            var errorMsg = nestedErrorMessages[modelNested][keyAttr];

            if(_.isArray(selector)) {
              $.each(selector, function(_, singleSelector) {
                addErrorToSelector(singleSelector, errorMsg, errorElem);
              });
            } else addErrorToSelector(selector, errorMsg, errorElem);
          });
        });
      }
    } else {
      $(location).attr("href", response.redirect_path);
    }
  };

  $.lawsonAjaxOrderForm = function(response) {
    if (!response.status && !_.isEmpty(response.nested_errors)) {
      $(".has-error").removeClass("has-error");
      $(".help-block").remove();

      $.each(response.nested_errors, function(index, errorObject) {
        $.each(errorObject, function(inputName, errors) {
          var selector = response.order.type_id == "individual" ? "#single-order" : "#multi-order";
          var indexOrder = $(selector).find(".order-step-2")[index];
          var inputElement = $(indexOrder).find("[name$='[" + inputName +"\]']")[0];
          if (!_.isUndefined(inputElement)) {
            var $element = $(inputElement).closest('.form-group');
            $element.addClass("has-error");
            $("<span class='help-block'>" + errors[0] + "</span>").insertAfter($element.find(".input-field-icon"));

            $('html, body').animate({scrollTop: $(".help-block").parentsUntil(".order-step-2").parent().offset().top}).finish();
          }
        });
      });
    } else if (response.not_found) {
      $(location).attr("href", response.redirect_path);
    }
  };

  $.lawsonAjaxOrderFormStep1 = function(response, extraErrorElements, autoNestedForm) {

    if (!response.status) {
      $(".error-notice").removeClass("error-notice");
      $("a[data-toggle='tab']").removeClass("error-tab");
      $(".help-block").remove();
      extraErrorElements = extraErrorElements || {};
      autoNestedForm = autoNestedForm || false
      var errorMessages = JSON.parse(response.errors);
      $.each(errorMessages, function(key, value) {
        var $inputElement = $("[name$='[" + key + "]']").not("[name*='_attributes']");
        if (_.isEmpty(value)) return;

        $formGroup = $inputElement.closest(".form-group");
        $formGroup.addClass("form-error");
        if (!_.isUndefined($formGroup.find(".label-textfield")[0])) {
          $("<p class='error-notice'>" + value[0] + "</p>").insertAfter($formGroup.find(".label-textfield"));
        } else {
          $("<p class='error-notice'>" + value[0] + "</p>").insertAfter($formGroup.find(".select-box"));
        }
      });

      $("h2.collapse-title").removeClass("collapsed");
      $("#collapse-assignee").addClass("show");
      $('html, body').animate({scrollTop: $(".error-notice").parent().offset().top}).finish();
    } else if (response.not_found) {
      $(location).attr("href", response.redirect_path);
    }
  };


  $.lawsonAjax = function(response, extraErrorElements, autoNestedForm, multipleFiledsFor) {
    if (!response.status) {
      $(".has-error").removeClass("has-error");
      $("a[data-toggle='tab']").removeClass("error-tab");
      $(".help-block").remove();
      extraErrorElements = extraErrorElements || {};
      autoNestedForm = autoNestedForm || false;
      var errorMessages = typeof(response.errors) == "object" ? response.errors : JSON.parse(response.errors);
      $.each(errorMessages, function(key, value) {
        var $inputElement = $("[name$='[" + key + "]']").not("[name*='_attributes']");
        if ($inputElement.length === 0) {
          $inputElement = $("[name$='[" + key + "][]']").not("[name*='_attributes']");
        }
        if (_.isEmpty(value)) return;
        addErrorToInput($inputElement, value[0]);
      });

      $.each(extraErrorElements, function(key, selector) {
        if (errorMessages[key]) {
          $(selector).removeClass("hide").text(errorMessages[key][0]);
        } else {
          $(selector).addClass("hide");
        }
      });

      if ($(".order-detail-box").length) {
        var offSet = $(".help-block").parentsUntil(".order-detail-box").parent().offset();
        if (!_.isUndefined(offSet)) {
          $("html, body").animate({scrollTop: offSet.top}).finish();
        }
      }

      if (response.nested_errors && multipleFiledsFor) {
        var nestedErrorMessages = JSON.parse(response.nested_errors);
        $.each(nestedErrorMessages, function(modelNested, values){
          if (autoNestedForm) {
            var className = "."+modelNested+"_fields";
            $(className).each(function(key, inputElement){
              if (values[key]) {
                $.each(values[key], function(field, errors){
                  var $selectElement = $(inputElement).find("select[name*=" + field + "]");
                  addErrorToInput($selectElement, errors[0]);
                });
              }
            })
          }
        })
      }

      if (response.nested_errors) {
        var nestedErrorMessages = JSON.parse(response.nested_errors);
        $.each(nestedErrorMessages, function(modelNested, values) {
          if (autoNestedForm) {
            $(".nested-fields").each(function(key, inputElement){
              if (values[key]){
                $.each(values[key], function(field, errors){
                  var $inputElement = $(inputElement).find("input[name*=" + field + "]");
                  addErrorToInput($inputElement, errors[0]);
                });
              }
            });
          }
          if(response.nested_errors_object) {
            var nestedErrorObjectMessages = JSON.parse(response.nested_errors_object);
            if (autoNestedForm) {
              $.each(nestedErrorObjectMessages, function(_, values) {
                $(".nested-errors-object").each(function(_, inputElement){
                  $.each(values, function(field, errors){
                    var $inputElement = $(inputElement).find("textarea[name*=" + field + "]");;
                    addErrorToInput($inputElement, errors[0]);
                  });
                });
              })
            }
          }
          else {
            $.each(values, function(key, errors) {
              $.each(errors, function(field, error) {
                var $inputElement = $("[name$='[" + modelNested +'_attributes]['+ key + '][' + field + "]']");
                addErrorToInput($inputElement, error[0]);
              });
            });
          }
        });
      }

      if (response.payment_rate_errors) {
        var rateErrors = JSON.parse(response.payment_rate_errors);
        $.each(rateErrors, function(key, value) {
          $(".nested-fields").each(function(_, inputElement){
            var $inputElement = $(inputElement).find("input[name*=" + key + "]");
            addErrorToInput($inputElement, value[0]);
          });
        });
      }
    } else {
      $(location).attr("href", response.redirect_path);
    }
  };

  $.lawsonAjaxOrderFormAdminStep2 = function(response) {
    if (!response.status && !_.isEmpty(response.nested_errors)) {
      $(".has-error").removeClass("has-error");
      $(".help-block").remove();

      $.each(response.nested_errors, function(index, errorObject) {
        $.each(errorObject, function(inputName, errors) {
          var selector = response.order.type_id == "individual" ? "#single-order" : "#multi-order";
          var indexOrder = $(selector).find(".order-step-2")[index];
          var inputElement = $(indexOrder).find("[name$='[" + inputName +"\]']")[0];
          if (!_.isUndefined(inputElement)) {
            var $element = $(inputElement).closest(".form-group");
            var $formCustom = $(inputElement).closest(".form-custom");
            $element.find(".control-label:first").addClass("has-error");
            $formCustom.addClass("has-error");
            $("<span class='help-block'>" + errors[0] + "</span>").insertAfter($formCustom.find(".input-field-icon"));

            $("html, body").animate({scrollTop: $(".help-block").parentsUntil(".order-step-2").parent().offset().top}).finish();
          }
        });
      });
    } else if (response.not_found) {
      $(location).attr("href", response.redirect_path);
    }
  };

  $.lawsonAjaxRegularOrderFormAdminStep2 = function(response) {
    if (!response.status && !_.isEmpty(response.nested_errors)) {
      $(".has-error").removeClass("has-error");
      $(".help-block").remove();

      $.each(response.nested_errors, function(index, errorObject) {
        $.each(errorObject, function(inputName, errors) {
          var selector = response.order.type_id == "non_recurring" ? "#single-order" : "#multi-order";
          var indexOrder = $(selector).find(".order-step-2")[index];
          if(response.order.type_id == "non_recurring" && inputName == "started_at"){
            inputName = "selected_dates";
          }
          var inputElement = $(indexOrder).find("[name$='[" + inputName +"\]']")[0];
          if (!_.isUndefined(inputElement)) {
            var $element = $(inputElement).closest(".form-group");
            var $formCustom = $(inputElement).closest(".form-custom");
            $element.find(".control-label:first").addClass("has-error");
            $formCustom.addClass("has-error");
            $("<span class='help-block'>" + errors[0] + "</span>").insertAfter($formCustom.find(".input-field-icon"));

            $("html, body").animate({scrollTop: $(".help-block").parentsUntil(".order-step-2").parent().offset().top}).finish();
          }
        });
      });
    } else if (response.not_found) {
      $(location).attr("href", response.redirect_path);
    }
  };

  $.lawsonAjaxRegularOrderFormAdminStep3 = function(response) {
    if (!response.status) {
      $(".has-error").removeClass("has-error");
      $(".help-block").remove();

      $.each(response.errors, function(inputName, errorMessage) {
        var selector = ".create-order-step3";
        var inputElement = $(selector).find("[name$='[" + inputName +"\]']");
        if (!_.isUndefined(inputElement)) {
          var element = $(inputElement).closest(".form-group");
          var formCustom = $(inputElement).closest(".form-custom");
          element.find(".control-label").addClass("has-error");
          formCustom.addClass("has-error");
          $("<span class='help-block'>" + errorMessage + "</span>").insertAfter(inputElement);

          $("html, body").animate({scrollTop: $(".help-block").parentsUntil(".create-order-step3").parent().offset().top}).finish();
        }
      });
    } else if (response.not_found) {
      $(location).attr("href", response.redirect_path);
    }
  };

  $.lawsonAjaxLocationPic = function(response) {
    $(".has-error").removeClass("has-error");
    $(".help-block").remove();
    if (!response.status) {
      if (response.nested_errors) {
        var nestedErrorMessages = response.nested_errors;
        $.each(nestedErrorMessages, function(modelNested, values) {
          $.each(values, function(key, nestedResponse) {
            var typeError = nestedResponse.type;
            $.each(nestedResponse.error, function(field, error) {
              if (!_.isEmpty(error)) {
                var $inputElement = $("[name*='[" + field + "]'").filter("." + typeError);
                $inputElement.closest(".form-group").addClass("has-error");
                $("<p class='help-block'>" + error[0] + "</p>").insertAfter($inputElement);
              }
            });
          });
        });
      }
    } else if (response.not_found) {
      $(location).attr("href", response.redirect_path);
    }
  };

  $.lawsonAjaxCreateArrangement = function(response) {
    if (!response.status) {
      $(".has-error").removeClass("has-error");
      $(".help-block").remove();

      var selector = "#single-order";

      $.each(response.nested_errors, function(index, errorObject) {
        $.each(errorObject, function(inputName, errors) {
          var selector = "#single-order";
          var indexOrder = $(selector).find(".order-step-2")[index];
          var inputElement = $(indexOrder).find("[name$='[" + inputName +"\]']")[0];
          if (!_.isUndefined(inputElement)) {
            var $element = $(inputElement).closest(".form-group");
            var $formCustom = $(inputElement).closest(".form-custom");
            $element.find(".control-label:first").addClass("has-error");
            $formCustom.addClass("has-error");
            $("<span class='help-block'>" + errors[0] + "</span>").insertAfter($formCustom.find(".input-field-icon"));
          }
        });
      });
      var arrangementErrors = response.arrangement_errors;
      $.each(arrangementErrors, function(index, errors) {
        if (!_.isEmpty(errors)) {
          arrangementError = JSON.parse(errors);
          var errorIndex = index;
          $.each(arrangementError, function(inputName, errors) {
            if (!_.isEmpty(arrangementError) && (inputName === "staff_id")) {
              var $inputElement = $("[name$='[" + inputName + "]']").not("[name*='_attributes']")[errorIndex];
              if (!_.isEmpty(errors)) {
                addErrorToInput($($inputElement), errors[0]);
              }
            }
          })
        }
      });

      var workAchievementErrors = response.work_achievement_errors;
      $.each(workAchievementErrors, function(index, errors) {
        if (!_.isEmpty(errors)) {
          workAchievementError = JSON.parse(errors);
          var errorIndex = index;
          $.each(workAchievementError, function(inputName, errors) {
            if (!_.isEmpty(workAchievementError)) {
              var indexOrder = $(selector).find(".order-step-2")[errorIndex];
              var inputElement = $(indexOrder).find("[name$='[" + inputName + "]']").not("[name*='_attributes']")[0];
              var $inputElement = $(inputElement);
              if (!_.isEmpty(errors)) {
                var $element = $inputElement.closest(".form-group");
                var $formCustom = $inputElement.closest(".form-custom");
                $element.find(".control-label:first").addClass("has-error");
                $formCustom.addClass("has-error");
                $("<span class='help-block'>" + errors[0] + "</span>").insertAfter($formCustom.find(".input-field-icon"));
              }
            }
          })
        }
      });

      if ($(".help-block").length) {
        $("html, body").animate({scrollTop: $(".help-block").parentsUntil(".order-step-2").parent().offset().top}).finish();
      }
    } else if (response.not_found) {
      $(location).attr("href", response.redirect_path);
    }
  };

  $.lawsonMinimumWageAjax = function(response) {
    $(".has-error").removeClass("has-error");
    $(".help-block").remove();

    if (!response.status) {
      $.each(response.errors, function(key, obj) {
        if (!obj.is_valid) {
          var messageObj = obj.error;
          if (!_.isEmpty(messageObj.current_wage)) {
            var $inputElement = $(".wage-input.current-wage-" + obj.id);
            addErrorToInput($inputElement, messageObj.current_wage[0]);
          }
          if (!_.isEmpty(messageObj.start_date)) {
            var $inputElement = $(".start-date.start-date-" + obj.id);
            addErrorToInput($inputElement, messageObj.start_date[0]);
          }
        }
      });
    } else {
      $(location).attr("href", response.redirect_path);
    }

    $(".minimum-wage-table").find("th").removeClass("has-error");
  };

  function addErrorToInput($inputElement, errorMessage, insertAt) {
    $inputElement.closest(".form-group").addClass("has-error");

    if ($inputElement.closest(".input-group").length) {
      $inputElement = $inputElement.closest(".input-group");
    }

    if ($inputElement.closest('.tab-pane').length) {
      var $tabPane = $inputElement.closest('.tab-pane');
      var $aTagTab = $('a[href="#' + $tabPane.attr('id') + '"]');
      $aTagTab.addClass('error-tab');

      if ($aTagTab.parent().closest('.tab-pane').length) {
        var $parentPane = $aTagTab.parent().closest('.tab-pane');
        var $aTagParentTab = $('a[href="#' + $parentPane.attr('id') + '"]');
        $aTagParentTab.addClass('error-tab');
      }
    }

    if ($inputElement.closest('td').length) {
      var $tdElement = $inputElement.closest('td')
      var $thTable = $tdElement.closest('table').find('th').eq($tdElement.index());
      $thTable.addClass("has-error");
    }

    if ($inputElement.hasClass("select2-hidden-accessible")) {
      $inputElement = $inputElement.closest(".form-group").find(".select2.select2-container");
    }

    if(!_.isUndefined(insertAt)) {
      var $parentLabel = $(insertAt).parents(".form-group").find("label.staff-department-label");
      insertAt.append("<span class='help-block error-msg'>" + errorMessage + "</span>");
      $($parentLabel, insertAt).addClass("has-error");
    }
    else {
      $("<span class='help-block'>" + errorMessage + "</span>").insertAfter($inputElement);
    }
  }

  function addErrorToSelector(selector, message, errorElem) {
    if (message && errorElem.replace(/_/g, "-") == selector) {
      addErrorToInput($(selector), message[0]);
      $(selector).closest(".form-group").find("input.black-input:text").addClass("custom-error");
      $(selector).next().addClass("col-md-9 padding-0");
    }
  }

  function addErrorToRadio(inputElement, value) {
    var parentInput = inputElement.parents(".form-group");
    var error = "<span class='help-block error-msg has-error'>" + value + "</span>";
    parentInput.find(".form-group-radio").append(error);
    parentInput.addClass("has-error");
  }
}(jQuery));
