'use strict';

$(document).ready(function() {
  $('.select2').select2({width: '100%'});

  $(".select2-multiple").select2({width: "100%"})
    .on("select2:selecting", function(e) {
      setScrollTop(e);
      updatePositionSelect2Result(this);
    })
    .on("select2:select", function(e) {
      getScrollTop(e);
      updatePositionSelect2Result(this);
    })
    .on("select2:unselect", function(e) {
      getScrollTop(e);
      updatePositionSelect2Result(this);
    })
    .on("select2:unselecting", function(e) {
      $(this).data("state", "unselected");
      setScrollTop(e);
      updatePositionSelect2Result(this);
    })
    .on("select2:open", function(e) {
      if ($(this).data("state") === "unselected") {
        $(this).removeData("state");
        $(this).select2("close");
      }
    });

  $(".select2-limit-data").select2({
    width: "100%",
    ajax: {
      url: "/selectbox_limit_datas/",
      dataType: "json",
      quietMillis: 1000,
      data: function(params) {
        return {
          search_content: params.term,
          selected: $(this).val(),
          class_name: $(this).data("classname")
        }
      },
      processResults: function(data) {
        return {results: data.results};
      }
    },
    templateResult: formatResult,
    templateSelection: formatSelection
  });

  function formatResult(obj) {
    return obj.account_name || obj.name || obj.full_name;
  };

  function formatSelection(obj) {
    return obj.text || obj.account_name || obj.name || obj.full_name;
  };

  function updatePositionSelect2Result(el){
    $(el).data("select2").dropdown._positionDropdown();
  };

  function getScrollTop(e){
    $(".select2-results__options").scrollTop($(e.currentTarget).data("scrolltop"));
  };

  function setScrollTop(e){
    $(e.currentTarget).data("scrolltop", $(".select2-results__options").scrollTop());
  };

  $(".btn-block-direct-submit-form").on("click", function(e) {
    e.preventDefault();
  });
});
