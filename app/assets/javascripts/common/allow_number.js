function allowNumber(parentInput, input) {
  var EXCEPT_KEYS = [46, 8, 9, 27, 13, 110, 190];
  var KEYS_WITH_SHIFTKEY = [65, 67, 88, 86];
  $(parentInput).on("keydown", $(input), function(e) {
    var key = e.which || e.keyCode;
    if ($.inArray(key, EXCEPT_KEYS) !== -1 ||
      ((key === 65 || key === 67 || key === 88 || key === 86) &&
      (e.ctrlKey === true || e.metaKey === true)) ||
      (key >= 35 && key <= 40)) {
      return;
    }
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) &&
      (e.keyCode < 96 || e.keyCode > 105)) {
      e.preventDefault();
    }
  });

  $(parentInput).on('keypress', $(input), function(e) {
    var key = e.which || e.keyCode;
    if ((key < 48 || key > 57) && (e.shiftKey && $.inArray(key, KEYS_WITH_SHIFTKEY) !== -1) &&
      $.inArray(e.keyCode, EXCEPT_KEYS) === -1) {
      e.preventDefault();
    }
  });

  $(parentInput).on('paste', $(input), function(e) {
    var pastedText;
    if (window.clipboardData && window.clipboardData.getData) {
      pastedText = window.clipboardData.getData('Text');
    } else if (e.originalEvent.clipboardData && e.originalEvent.clipboardData.getData) {
      pastedText = e.originalEvent.clipboardData.getData('text/plain');
    }
    var floatVal = parseFloat(pastedText);
    var intVal = parseInt(pastedText);
    if (_.isNaN(floatVal) || (floatVal !== intVal)) {
      e.preventDefault();
    }
  });
}
