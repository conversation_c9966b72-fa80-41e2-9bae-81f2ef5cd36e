$(document).ajaxStart(function() {
  $(".btn-submit-form-ajax").prop("disabled", true);
}).ajaxStop(function() {
  $(".btn-submit-form-ajax").prop("disabled", false);
}).ready(function() {
  $(".btn-open-modal").on("click", function(e) {
    e.preventDefault();
  });

  $(".btn-submit-modal").on("click", function() {
    $("#" + $(this).data("form-id")).submit();
  });

  $("form").submit(function() {
    $(this).find(".btn-submit-form").prop("disabled", true);
  });

  $(".btn-back-to-list").one("click", function() {
    $(this).click(function () { return false; });
  });

  $("#table-with-redirectbtn").on("click", ".redirect-btn", function() {
    $(this).click(function () { return false; });
  });
});
