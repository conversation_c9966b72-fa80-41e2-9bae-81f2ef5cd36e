var DEFAULT_DATE_TIME_FORMAT = "yyyy/mm/dd";
var DATE_HOUR_MINUTE_FORMAT = "YYYY/MM/DD HH:mm";
var DATE_FORMAT_ONLY_MONTH_YEAR = "yyyy/mm";
var ORDER_DATEPICKER_FORMAT = "YYYY/MM/DD";
var DATE_TIME_FORMAT_WITH_DAY_NAME = "YYYY/MM/DD(dd)";
var FULL_DATE_FORMAT = "YYYY/MM/DD";
var FULL_DATE_FORMAT_HYPHEN = "YYYY-MM-DD";
var ENTER_KEY_CODE = 13;
var POSTAL_CODE_SEARCH_NUMBER = 7;
var HIDDEN_PARAM_METHODS = ["POST", "PUT"];
var DATE_PICKER_FORMAT = "YYYY年MMMM";
var TIME_PICKER_FORMAT = "HH:mm";
var DEFAULT_MINUTE_STEP = 1;
var FILE_SIZE_IN_MB = 10;
var DATE_MONTH_YEAR_FORMAT = "YYYY/MM";
var DATE_MONTH_YEAR_VIEW_MODE = "months";
var TIME_PICKER_12_HOUR = "hh:mm A";
var ONLY_MONTH_AND_YEAR = "MMMYYYY年";
var ANGULAR_ITEMS_PER_PAGE = [10, 15, 30, 50];
var DAY_MONTH_FORMAT_WITH_DAY_NAME = "MM/DD(dd)";
var MAX_INTEGER = 2147483647;
var DAY_MONTH_FORMAT = "MMMDo";
var YEAR_W_MONTH_FORMAT = "YY年MMM";
var FULL_YEAR_W_MONTH_FORMAT = "YYYY年MMM";
var NOT_REQUIRED_RESIDENSE_STATUS_ID = 27;
var STAFF_TEL_LENGTH = [10, 11];
var STAFF_TEL_REGEX = /^\d{10,11}$/;
var RESIDENCE_NUMBER_REGEX = /^[A-Za-z]{2}[0-9]{8}[A-Za-z]{2}$/;
var MOBILE_MAX_WIDTH = 768;
var DATE_FORMAT_MOBILE = "MM/DD";
var SPECIAL_OFFER_FEE = [1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000];
var CHECK_DATE_REGEX = /^([1][9][0-9]{2}|[2][0-9]{3})[/]([0][1-9]|[1][0-2])[/]([0][1-9]|[1][0-9]|[2][0-9]|[3][0-1])$/;
var KEY_UP_CTRL_C_CODE = [91, 17, 18, 67];
var REGISTRATION_UPPER_YEAR = 14;
var REGISTRATION_LOWER_YEAR = 100;
var OTP_REGEX = /^[0-9]{6}$/;
var OTP_RESEND_INTERVAL = 45;
