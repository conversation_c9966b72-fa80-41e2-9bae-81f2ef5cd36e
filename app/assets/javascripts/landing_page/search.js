$( document ).ready(function() {
  var $dataForSearch = $('#data-for-search');
  var $jobList = $('#lp-jobs-list');
  var $searchInput = $('#lp-search-input');
  var $searchInputSubmit = $('#lp-search-submit');
  var $showVideoBtn = $('#play-show-video');
  var $videoShow = $('#video-show');
  var $video = $('#lawson-intro');
  var $paginator = $("#paginator");

  var dataCurrentSearch = $dataForSearch.data('currentSearch');
  if (!!dataCurrentSearch) {
    $searchInput.val(dataCurrentSearch);
  }

  $showVideoBtn.click(function() {
    $videoShow.addClass("show");
    $showVideoBtn.addClass("hide");
    $video[0].src += "?rel=0&autoplay=1";
  });
  $searchInputSubmit.click(function() {
    var searchItemTemmlate = $('#search-template').html();
    var searchBy = $searchInput.val();
    var dataAreaId = $dataForSearch.data('areaId');
    $dataForSearch.data('oldSearchContent', searchBy);
    $.ajax({
      type: 'GET',
      data: {'search': searchBy, page: 1, area_id: dataAreaId},
      url: '/search',
      dataType: "json",
      error: function() {
        $jobList.html('<p>An error has occurred</p>');
      },
      success: function(res) {
        $jobList.empty();
        $paginator.empty();
        $dataForSearch.data('currentPage', res.page);
        $dataForSearch.data('totalPage', res.total_page);
        var lp_jobs = res.lp_jobs;
        if (lp_jobs.length === 0) {
          $jobList.html("<div class='col-xs-12 not-found'><p>現在、検索されたエリアには求人がございません</p></div>");
          return false;
        }
        $.each(lp_jobs, function(i, val) {
          var $item = $('<div />');
          $item.html(searchItemTemmlate);
          $item.find('a.search-url').attr('href', '/jobs/' + val.id);
          $item.find('div.department-name').html('<span>' + val.address_region + '</span>');
          $item.find('img.img-responsive').attr('src', val.coporation_logo_url);
          $item.find('div.station-name').html(val.street_address);
          $item.find('div.station-time').html(val.address_locality);
          $item.find('div.order-case-job-description').html('<span>' + val.title + '</span>');
          $item.find('div.order-case-requirements').html('<span>' + val.short_description + '</span>');
          $item.find('div.order-case-salary').html('<span>時給: ' + val.salary + '円</span>');
          $jobList.append($item);
        });
      }
    });
  });

  var lockAjax = false;
  $searchInput.autocomplete({
    source: function( request, response ) {
      var dataSearch = $searchInput.val();
      if (!dataSearch || lockAjax === true) {
        return [];
      }
      var dataAreaId = $dataForSearch.data('areaId');
      lockAjax = true;
      $.ajax({
        type: 'GET',
        url: '/autocomplete',
        data: {'search': dataSearch, area_id: dataAreaId},
        dataType: 'json',
        success: function( data ) {
          setTimeout(function(){
            lockAjax = false;
          },200);
          response( data.search_text );
        }
      });
    }
  });

  $(window).scroll(function() {
    var searchBy = $searchInput.val();
    if (!searchBy) {
      return false;
    }
    var searchItemTemmlate = $('#search-template').html();
    var oldSearch = $dataForSearch.data('oldSearchContent');
    var totalPage = $dataForSearch.data('totalPage');
    if($(window).scrollTop() + $(window).height() >= $(document).height()) {
      var currentPage = $dataForSearch.data('currentPage');
      if (parseInt(currentPage) <= parseInt(totalPage) && searchBy != '' && oldSearch === searchBy) {
        $.ajax({
          type: 'GET',
          data: {'search': oldSearch, page: parseInt(currentPage) + 1},
          url: '/search',
          dataType: 'json',
          error: function() {
            $jobList.html("<div class='col-xs-12 not-found'><p>現在、検索されたエリアには求人がございません</p></div>");
          },
          success: function(res) {
            $dataForSearch.data('currentPage', res.page);
            $dataForSearch.data('totalPage', res.total_page);
            var lp_jobs = res.lp_jobs;
            $.each(lp_jobs, function(i, val) {
              var $item = $('<div />');
              $item.html(searchItemTemmlate);
              $item.find('a.search-url').attr('href', '/jobs/' + val.id);
              $item.find('div.department-name').html('<span>' + val.address_region + '</span>');
              $item.find('img.img-responsive').attr('src', val.coporation_logo_url);
              $item.find('div.station-name').html(val.street_address);
              $item.find('div.station-time').html(val.address_locality);
              $item.find('div.order-case-job-description').html('<span>' + val.title + '</span>');
              $item.find('div.order-case-requirements').html('<span>' + val.short_description + '</span>');
              $item.find('div.order-case-salary').html('<span>時給: ' + val.salary + '円</span>');
              $jobList.append($item);
            });
          }
        });
      }
    }
  });
});