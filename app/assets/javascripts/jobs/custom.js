$("#carousel-modal-welcome").carousel({
  interval: false
})
//- Staff sidebar on mobile
$('.js-st-btn-mobile-toggle').click(function(){
  $(this).children().toggleClass('st-change-icon');
  $('<div class="overlay-mobile"></div>').insertAfter($('#js-staff-main-body')).hide().fadeIn(100);
  $('#js-staff-sidebar').animate({'left': 0}, 100);
  $('.overlay-mobile').click(function(){
    $('.js-st-btn-mobile-toggle').children().toggleClass('st-change-icon');
    $('#js-staff-sidebar').animate({'left': '-100%'}, 100);
    $(this).remove();
  });

  // Hide overlay when click close button
  if ($(this).find('i.st-change-icon').length === 0) {
    $('#js-staff-sidebar').animate({'left': '-100%'}, 300);
    $('.overlay-mobile').remove();
  };
});

$(".js-slider-input").each( function() {
  $(".js-slider-input").slider({});

  $('.slider-min').text($('.js-slider-input').data('slider-value')[0] + '円');
  $('.slider-max').text($('.js-slider-input').data('slider-value')[1] + '円');

  $(".js-slider-input").on("slide", function (event) {
    $('.slider-min').text(event.value[0] + '円');
    $('.slider-max').text(event.value[1] + '円');
  });
});
