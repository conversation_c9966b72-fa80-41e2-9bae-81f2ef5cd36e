// This is a manifest file that'll be compiled into application.js, which will include all the files
// listed below.
//
// Any JavaScript/Coffee file within this directory, lib/assets/javascripts, vendor/assets/javascripts,
// or any plugin's vendor/assets/javascripts directory can be referenced here using a relative path.
//
// It's not advisable to add code directly here, but if you do, it'll appear at the bottom of the
// compiled file. JavaScript code in this file should be added after the last require_* statement.
//
// Read Sprockets README (https://github.com/rails/sprockets#sprockets-directives) for details
// about supported directives.
//
//= require ./corporation_staff_common/main
//= require ./corporation/order
//= require ./corporation/setting_password
//= require ./corporation/users
//= require ./corporation/violation_day
//= require ./corporation/flash_messages
//= require ./corporation/common
//= require ./libs/jquery-ui.min
//= require ./libs/lodash.min
//= require ./libs/lawson-xhr
//= require ./libs/jquery-toggle-password
//= require ./libs/moment-with-locales.min
//= require ./libs/toastr.min
//= require ./libs/jquery.datepick-ja
//= require ./libs/multi-datepicker.js
//= require i18n
//= require i18n.js
//= require i18n/translations
//= require ./common/corp_label_required
//= require ./constants
//= require jquery_ujs
//= require ./libs/jquery.serializejson.min
//= require ./common/round_minutes
//= require ./corporation/order_case
//= require ./corporation/usage_achievement
