$(function() {
  moment.updateLocale('ja', {
    calendar : {
      lastDay : '[前日] HH:mm',
      sameDay : '[当日] HH:mm',
      nextDay : '[翌日] HH:mm',
      sameElse : 'L'
    }
  });

  var now = moment();
  var dateTimeObj = now.clone();
  var arrDate = ['前日', '当日', '翌日'];
  var arrLength = arrDate.length;
  var TIME_FORMAT = "HH:mm";
  var TIME_REGEX = /(([0-1][0-9]|[2][0-3]):[0-5][0-9])/;

  // Render data moment to dropdown datetime

  $('.timepicker-date').text( arrDate[1] );
  $('.timepicker-hour').text( moment().hour() );
  $('.timepicker-minute').text( moment().minutes() );

  $('.js-wa-add-content').each(function () {

    var idOfElement = '#' + $(this).attr('id');
    var currentValue = $(this).find(".datetimeinput-custom").val();
    var timeValuePattern = currentValue.split(/\s|\:/);
    dateTimeObj = moment($(idOfElement).data('initTime'), TIME_FORMAT) || dateTimeObj;

    $(idOfElement).find('.timepicker-date').text(timeValuePattern[0] || arrDate[1]);
    $(idOfElement).find('.timepicker-hour').text(timeValuePattern[1] || moment().format("HH"));
    $(idOfElement).find('.timepicker-minute').text(timeValuePattern[2] || moment().format("MM"));

    // Render value input when change value on dropdown datetime
    function renderInputValue() {
      var timeValue = $(idOfElement).find('.timepicker-hour').text() + ':' + $(idOfElement).find('.timepicker-minute').text();
      var dateTimeValue = $(idOfElement).find('.timepicker-date').text() + ' ' + timeValue;
      $(idOfElement).find('.datetimeinput-custom').val(dateTimeValue);
      dateTimeObj = moment(timeValue, TIME_FORMAT);
    }

    $(idOfElement).find('.datetimepicker-input').on('change', function (e) {
      var matchedTime = $(e.target).val().match(TIME_REGEX);
      if (matchedTime) {
        var timeObj = moment(matchedTime[1], TIME_FORMAT);
        $(idOfElement).find('.timepicker-hour').text(timeObj.format("HH"));
        $(idOfElement).find('.timepicker-minute').text(timeObj.format("mm"));
      }
    });

    // Change date on dropdown when click icon

    $(idOfElement).find('.js-add-date').on('click', function (e) {
        e.preventDefault();
        var index = arrDate.indexOf( $(idOfElement).find('.timepicker-date').text() );
        var next = arrDate[ (index + arrLength + 1) % arrLength ];
        $(idOfElement).find('.timepicker-date').text(next);
        renderInputValue();
    })

    $(idOfElement).find('.js-subtract-date').on('click', function (e) {
      e.preventDefault();
      var index = arrDate.indexOf( $(idOfElement).find('.timepicker-date').text() );
      var previous = arrDate[ (index + arrLength - 1) % arrLength ];
      $(idOfElement).find('.timepicker-date').text(previous);
      renderInputValue();
    })

    // Add/Subtract hour on dropdown datetime when click icon

    $(idOfElement).find('.js-add-hour').on('click', function (e) {
      e.preventDefault();
      $(idOfElement).find('.timepicker-hour').text( dateTimeObj.add(1, 'h').format('HH') );
      renderInputValue();
    })

    $(idOfElement).find('.js-subtract-hour').on('click', function (e) {
      e.preventDefault();
      $(idOfElement).find('.timepicker-hour').text( dateTimeObj.subtract(1, 'h').format('HH') );
      renderInputValue();
    })

    // Add/Subtract minute on dropdown datetime when click icon

    $(idOfElement).find('.js-add-minute').on('click', function (e) {
      e.preventDefault();
      $(idOfElement).find('.timepicker-minute').text( dateTimeObj.add(1, 'm').format('mm') );

      if ( dateTimeObj.minutes() == 59 ) {
        $(idOfElement).find('.timepicker-hour').text( dateTimeObj.add(1, 'h').format('HH'));
      }
      renderInputValue();
    })

    $(idOfElement).find('.js-subtract-minute').on('click', function (e) {
      e.preventDefault();

      if ( dateTimeObj.minute() == 0 ) {
        $(idOfElement).find('.timepicker-hour').text(dateTimeObj.startOf('hour').subtract(1, 'h').format('HH'));
      }

      $(idOfElement).find('.timepicker-minute').text( dateTimeObj.subtract(1, 'm').format('mm') );
      renderInputValue();
    })

    // Click input or icon input show dropdown datetime
    $(idOfElement).find('.datetimeinput-custom, .input-field-icon').on('click', function (e) {
      renderInputValue();
      e.stopPropagation();
      $(idOfElement).find('.dropdown-menu').addClass('show');
    })

    // $(idOfElement).on('focusout', function(){
    //   $(this).find('.dropdown-menu, .timepicker-minutes, .timepicker-hours, .timepicker-days').removeClass('show');
    // });

    // Click to body close dropdown datetime
    $('body').click(function () {
      $(idOfElement).find('.dropdown-menu, .timepicker-minutes, .timepicker-hours, .timepicker-days').removeClass('show');
    });

    // Click to dropdown datetime not hide
    $(idOfElement).find('.dropdown-menu').on('click', function(e) {
      e.stopPropagation();
    })

    // Show table select datetime when click
    $(idOfElement).find('.timepicker-minute').on('click', function(e) {
      $(idOfElement).find('.timepicker-minutes').addClass('show');
    })

    $(idOfElement).find('.timepicker-hour').on('click', function(e) {
      $(idOfElement).find('.timepicker-hours').addClass('show');
    })

    $(idOfElement).find('.timepicker-date').on('click', function(e) {
      $(idOfElement).find('.timepicker-days').addClass('show');
    })

    // Get value of table select datetime show to input
    $(idOfElement).find('.day').on('click', function(e) {
      var value = $(this).text();
      $(idOfElement).find('.timepicker-date').text( value );
      renderInputValue();
      $(idOfElement).find('.timepicker-days').removeClass('show');
    })

    $(idOfElement).find('.hour').on('click', function(e) {
      var value = $(this).text();
      $(idOfElement).find('.timepicker-hour').text( value );
      renderInputValue();
      $(idOfElement).find('.timepicker-hours').removeClass('show');
    })

    $(idOfElement).find('.minute').on('click', function(e) {
      var value = $(this).text();
      $(idOfElement).find('.timepicker-minute').text( value );
      renderInputValue();
      $(idOfElement).find('.timepicker-minutes').removeClass('show');
    })

  })
});
