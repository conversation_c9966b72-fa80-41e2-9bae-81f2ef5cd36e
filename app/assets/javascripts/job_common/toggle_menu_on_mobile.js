//- Toggle Left Sidebar and Logout Dropdown on mobile
$(".js-mobile-partner").click( function (event) {
  
  event.preventDefault();
  if ( $(window).width() <= 768 ) {
    if ( $('body').find('.overlay-mobile').length === 0 ) {
      $('<div class="overlay-mobile" id="js-sidebar-partner"></div>').insertAfter($('.main-body')).hide().fadeIn('200');
      $('#js-sidebar-partner').show().animate({'left': 0}, 500);
    }
    else if ($('.overlay-mobile').attr('id') === 'js-sidebar-partner') {
      $('.overlay-mobile').trigger('click');
    }
    else {
      $('.overlay-mobile').trigger('click');
      $('<div class="overlay-mobile" id="js-sidebar-partner"></div>').insertAfter($('.main-body')).hide().fadeIn('200');
      $('#js-sidebar-partner').show().animate({'left': 0}, 500);
    }

    $('.overlay-mobile').click(function(){
      $('#js-sidebar-partner').animate({'left': '-200%'}, 500);
      $(this).remove();
    });
  }
});

$(".js-dropdown-show").click( function (event) {
  $(this).toggleClass('active-dropdown');
  event.preventDefault();
  if ( $(window).width() <= 768 ) {
    if ( $('body').find('.overlay-mobile').length === 0 ) {
      $('<div class="overlay-mobile" id="js-partner-dropdown"></div>').insertAfter($('.main-body')).hide().fadeIn('200');
      $('.js-partner-dropdown').show().animate({'right': 0}, 500);
    }
    else if ($('.overlay-mobile').attr('id') === 'js-partner-dropdown') {
      $('.overlay-mobile').trigger('click');
    }
    else {
      $('.overlay-mobile').trigger('click');
      $('<div class="overlay-mobile" id="js-partner-dropdown"></div>').insertAfter($('.main-body')).hide().fadeIn('200');
      $('.js-partner-dropdown').show().animate({'right': 0}, 500);
    }

    $('.overlay-mobile').click(function(){
      $('.js-partner-dropdown').animate({'right': '-200%'}, 500);
      $(this).remove();
    });
  }
});