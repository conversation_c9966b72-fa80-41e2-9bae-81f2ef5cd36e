$(document).ready(function() {
  $("#forgot-password #email-address").on("input", function(e) {
    var isEmpty = _.isEmpty($(this).val().trim());

    $("#user-setting-password-button").prop("disabled", isEmpty)
      .toggleClass("button--disabled", isEmpty)
      .toggleClass("button--primary", !isEmpty);
  });

  $("#form-admin-verify-login-id #verify-login-id").on("input", function(e) {
    var isEmpty = _.isEmpty($(this).val().trim());

    $(".verify-login-id-button").prop("disabled", isEmpty)
      .toggleClass("button--disabled", isEmpty)
      .toggleClass("button--primary", !isEmpty);
  });

  $('.toggle-icon').on('click', function() {
    var $newPassword = $('.new-password');
    var elementType = $newPassword.prop('type') === 'password' ? 'text' : 'password';
    $newPassword.prop('type', elementType);
  });
});
