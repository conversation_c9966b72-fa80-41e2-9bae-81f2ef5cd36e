$(function() {
  $("#submit-violation-day").on("click", function() {
    $(".disable-submit-btn").prop("disabled", true);
    $("#form-violation-day").trigger("submit");
  });

  $("#form-violation-day").on("submit", function(e) {
    e.preventDefault();
    var actionURL = $(this).attr("action");
    $.ajax({
      url: actionURL,
      method: "POST",
      dataType: "json",
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $("#modal-confirm").modal("hide");
        $.lawsonAjax(response);
        $(".disable-submit-btn").prop("disabled", false);
      }
    });
  });
});
