$(document).ready(function() {
  $('.btn-block-direct-submit-form').on('click', function(e) {
    e.preventDefault();
  });

  $("body").on("submit", "form", function() {
    $("[type=submit]:not(.no-disabled)").addClass("disabled");
    $(".disable-submit:not(.no-disabled)").addClass("disabled");
  });

  $('body').on('focusout', 'input.form-control', function() {
    var $this = $(this);
    if ($this.val().length === 0) {
      $this.closest('.form-group').find('label').removeClass('label-ontop');
    }
  });

  $('body').on('focusin', 'input.form-control', function() {
    $(this).closest('.form-group').find('label').addClass('label-ontop');
  });

  setTimeout(function() {
    $('.corp-flash').fadeOut();
  }, 3000);

  var currenLocationPathname = location.pathname;
  var $mainMenuItem = $("a[href='" + currenLocationPathname + "'].main-menu__link");
  var $listSettingItem = $("a[href='" + currenLocationPathname + "'].list-setting__link");

  if ($mainMenuItem.length) {
    $mainMenuItem.closest("li.main-menu__item.js-child-item").addClass("main-menu__item--active")
      .closest(".sub-menu").show().closest("li.main-menu__item").addClass("open");
  } else if ($listSettingItem.length) {
    $listSettingItem.closest(".list-setting__item.js-setting-item").addClass("list-setting__item--active")
      .closest(".user-profile__setting.js-dropdown-setting").show()
      .closest(".user-profile.js-profile").addClass("open");
  }

  if ($('#prefecture_allowed').val() === 'false') {
    $('#prefecture-not-allowed-modal').modal({backdrop: 'static', keyboard: false});
  }
});

function filterInputTypeNumber(event) {
  var keyCode = ('which' in event) ? event.which : event.keyCode;
  return !(keyCode == 69 || keyCode == 101);
};

function handlePasteIntoInputTypeNumber(e) {
  var clipboardData, pastedData;
  clipboardData = e.clipboardData || window.clipboardData;
  pastedData = clipboardData.getData('Text').toUpperCase();
  if (pastedData.indexOf('E') > -1) {
    e.stopPropagation();
    e.preventDefault();
  }
};
