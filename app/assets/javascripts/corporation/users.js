$(function() {
  toastr.options = {
    "closeButton": true,
    "positionClass": "toast-top-full-width toast-format",
  }
  /**
   * User edit avatar
   */
  if ($(".corporation-user-edit-avatar").length) {
    $("#user_avatar").on("change", function() {
      var $avatarImgElm = $("img#user-avatar-img");

      if ($(this).val()) {
        previewAvatar(this, $avatarImgElm);
      } else {
        var oldAvatar = $avatarImgElm.data("old") || $avatarImgElm.data("default");
        $avatarImgElm.attr("src", oldAvatar);
      }
    });
  }

  /**
   * User edit email
   */
  if ($(".user-account__change-email").length) {
    $(".user-account__field span.toggle-icon").togglePassword({
      targetElement: "#current-password"
    });
  }

  function previewAvatar(inputContent, previewElm) {
    var reader = new FileReader();

    reader.onload = function(evt) {
      $(previewElm).attr("src", evt.target.result);
    };

    reader.readAsDataURL(inputContent.files[0]);
  }

  $("#user-list, #violation-day-list").on("click", ".sort-data", function(event) {
    var order = $(this).find('.col-sort').hasClass("fa-sort-down") ? "asc" : "desc"
    var classIcon = $(this).find('.col-sort').hasClass("fa-sort-down") ? "fa-sort-up" : "fa-sort-down"
    sortTable($(".sort-data").index(this), order);
    $(".col-sort").removeClass("fa-sort-down fa-sort-up").addClass("fa-sort");
    $(this).find(".col-sort").removeClass("fa-sort").addClass(classIcon);
  });

  $("#user-list").on("click", ".btn-destroy", function(event) {
    var idUser = $(this).attr("data-id");
    var base_url = get_base_url();
    $('.btn-confirm').attr("href", base_url + "/" + idUser);
  });
  getLastSearchCondition(false);

  $("form[id^='edit_user_']").on("submit", function() {
    $("#change-avatar").attr("disabled", true);
    setTimeout(function() {
      $("#user_avatar").attr("disabled", true)
    }, 10);
  });
});

function getLastSearchCondition(isAjax) {
  var base_url = get_base_url();
  var search_params = $("#search").val();
  if(!_.isEmpty(search_params) || (_.isEmpty(search_params) && isAjax)) {
    window.location.url = base_url + "?name=" + search_params;
    history.pushState({}, '', window.location.url);
  }
}

function sortTable(n, order) {
  var rows, switching, i, valAbove, valBelow, shouldSwitch;
  switching = true;
  while (switching) {
    switching = false;
    rows = $("#table-users tr");
    for (i = 1; i < (rows.length - 1); i++) {
      shouldSwitch = false;
      valAbove = rows[i].getElementsByTagName("td")[n].innerHTML.toLowerCase();
      valBelow = rows[i + 1].getElementsByTagName("td")[n].innerHTML.toLowerCase();
      if (order == "asc") {
        if (valAbove > valBelow) {
          shouldSwitch= true;
          break;
        }
      } else if (order == "desc") {
        if (valAbove < valBelow) {
          shouldSwitch = true;
          break;
        }
      }
    }
    if (shouldSwitch) {
      rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
      switching = true;
    }
  }
}

function get_base_url() {
  return window.location.origin + window.location.pathname;
}
