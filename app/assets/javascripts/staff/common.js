$(function(){
  $(".staff-required").each(function() {
    $(this).append("<span>" + I18n.t("common.label.required_txt") + "</span>");
  });

  $("form").submit(function() {
    $(this).find(".btn-submit-form").prop("disabled", true);
  });

  var defaultConfig = {
    locale: 'ja',
    format: "L",
    inline: true
  }

  var currentData = [];

  if($(".staff-header__right").length > 0) {
    $.ajax({
      url: "/get_works?date=" + moment().format(moment.HTML5_FMT.DATE),
      method: 'GET',
      dataType: 'json',
      success: function(response) {
        var extraConfig = formatInputForDatePicker(response);
        $(".js-staff-datepickerinline-desktop").datetimepicker(_.assign(defaultConfig, extraConfig));
        $(".js-staff-datepickerinline-mobile").datetimepicker(_.assign(defaultConfig, extraConfig));
      }
    });

    $(".js-staff-datepickerinline-desktop").on("update_month.datetimepicker", function(e) {
      $.ajax({
        url: "/get_works?date=" + moment(e.date).format(moment.HTML5_FMT.DATE),
        method: 'GET',
        dataType: 'json',
        success: function(response) {
          var extraConfig = formatInputForDatePicker(response);
          $(".js-staff-datepickerinline-desktop").datetimepicker("destroy");
          $(".js-staff-datepickerinline-desktop").datetimepicker(_.assign(defaultConfig, extraConfig));
          var currentMonth = $(".js-staff-datepickerinline-desktop").datetimepicker("date").format("MM-YY");
          if (currentMonth != moment().format("MM-YY")) {
            $(".js-staff-datepickerinline-desktop").find("td.active").removeClass("active");
          }
        }
      });
    });

    $(".js-staff-datepickerinline-mobile").on("update_month.datetimepicker", function(e) {
      $.ajax({
        url: "/get_works?date=" + moment(e.date).format(moment.HTML5_FMT.DATE),
        method: 'GET',
        dataType: 'json',
        success: function(response) {
          var extraConfig = formatInputForDatePicker(response);
          $(".js-staff-datepickerinline-mobile").datetimepicker("destroy");
          $(".js-staff-datepickerinline-mobile").datetimepicker(_.assign(defaultConfig, extraConfig));
          var currentMonth = $(".js-staff-datepickerinline-mobile").datetimepicker("date").format("MM-YY");
          if (currentMonth != moment().format("MM-YY")) {
            $(".js-staff-datepickerinline-mobile").find("td.active").removeClass("active");
          }
        }
      });
    });


    $(".js-staff-datepickerinline-desktop").on("change.datetimepicker", function(e) {
      if (!_.isNull(e.oldDate) && !_.isEmpty(currentData)) {
        var dateSelected = e.date.format("YYYY/MM/DD");
        if (_.includes(currentData["applied"], dateSelected)) {
          location.href = "/" + I18n.locale + "/work_lists#work_list";
        }

        if (_.includes(currentData["arranged"], dateSelected)) {
          location.href = "/" + I18n.locale + "/work_lists#work_list";
        }

        if (_.includes(currentData["worked"], dateSelected)) {
          var queryString = e.date.format("[year=]YYYY[&month=]MM");
          location.href = "/" + I18n.locale + "/work_lists?" + queryString + "#work_history_list";
        }
      }
    });

  }

  function formatInputForDatePicker(data) {
    currentData = data;
    highlightDates = _.concat(data["applied"], data["arranged"], data["worked"]);
    dateExtraClass = [];

    appliedDateClass = _.map(data["applied"], function(date){
      return {
        "date": date,
        "class": "applying"
      };
    });

    arrangedDateClass = _.map(data["arranged"], function(date){
      return {
        "date": date,
        "class": "to-work"
      };
    });

    workedDateClass = _.map(data["worked"], function(date){
      return {
        "date": date,
        "class": "worked"
      };
    });
    dateExtraClass = _.concat(workedDateClass, arrangedDateClass, appliedDateClass);

    return {
      "highlightDates": highlightDates,
      "highlightClass": "highlighted work-status",
      "dateExtraClass": dateExtraClass,
      "defaultDate": data["selected_date"]
    }
  }
});
