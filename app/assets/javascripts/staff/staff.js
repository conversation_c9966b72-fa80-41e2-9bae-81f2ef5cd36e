$(function() {
  if ($(".staff-edit-avatar").length) {

    $(".staff-upload-avatar").on("click", function(){
      $(this).val("");
    });

    $(".staff-upload-avatar").on("change", function() {
      var $avatarImgElm = $("img#staff-avatar-preview");
      if ($(this).val()) {
        previewAvatar(this, $avatarImgElm);
      } else {
        var oldAvatar = $avatarImgElm.data("old") || $avatarImgElm.data("default");
        $avatarImgElm.attr("src", oldAvatar);
      }
    });
  }

  var cropper;

  function previewAvatar(inputContent, previewElm) {
    var reader = new FileReader();
    previewElm.cropper("destroy");
    reader.onload = function(evt) {
      var img = new Image();
      img.src = evt.target.result;
      img.onload = function() {
        openCropImageModal(img.src, previewElm);
      }
    };
    reader.readAsDataURL(inputContent.files[0]);
  }

  function openCropImageModal(src, previewElm) {
    previewElm.attr("src", src);
    $(".cropper-canvas img, .cropper-view-box img").attr("src", src);
    cropper = previewElm.cropper({
      viewMode: 1,
      aspectRatio: 1,
      minContainerWidth: 340,
      minContainerHeight: 340,
      minCropBoxWidth: 50,
      minCropBoxHeight: 50,
      movable: true,
      rotatable: true,
      responsive: true,
      checkOrientation: false
    });
    $("#modal-preview-avatar").modal({backdrop: "static", keyboard: false});
  }

  $(".save-cropper-avatar").on("click", function(event) {
    event.preventDefault();
    var imageCropped = $("img#staff-avatar-preview").cropper("getCroppedCanvas");
    var base64encodedImage = imageCropped.toDataURL();
    $("#staff-avatar-img").attr("src", base64encodedImage);
    $("#modal-preview-avatar").modal("hide");
  });

  $(".btn-rotate-left-90").on("click", function(){
    cropper.cropper("rotate", 90);
  });

  $(".btn-rotate-right-90").on("click", function(){
    cropper.cropper("rotate", -90);
  });

  $("form[id^='edit_staff_']").on("submit", function() {
    $("#change-avatar").attr("disabled", true);
    setTimeout(function() {
      $("#staff_avatar").attr("disabled", true)
    }, 10);
  });

  if ($("textarea.form-control.staff-note-area").length) {
    if ($("textarea.form-control.staff-note-area").val().length > 0) {
      $("label.staff-note-text").addClass("on-top");
    } else {
      $("label.staff-note-text").removeClass("on-top");
    }

    $("textarea.form-control.staff-note-area").on("focusin", function() {
      $("label.staff-note-text").removeClass("label-ontop");
      $("label.staff-note-text").addClass("on-top");
    });

    $("textarea.form-control.staff-note-area").on("focusout", function() {
      if ($("textarea.form-control.staff-note-area").val().length <= 0) {
        $("label.staff-note-text").removeClass("on-top");
      }
    });
  }

  var getFormData = function(form) {
    var $inputs = $("input[type='file']:not([disabled])", form);
    $inputs.each(function(_, input) {
      if (input.files.length > 0) return;
      $(input).prop("disabled", true);
    });
    var formData = new FormData(form);
    $inputs.prop("disabled", false);
    return formData;
  };

  function dataURItoBlob(dataURI) {
    var byteString = atob(dataURI.split(',')[1]);
    var mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0]
    var ab = new ArrayBuffer(byteString.length);
    var ia = new Uint8Array(ab);
    for (var i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    var blob = new Blob([ab], {type: mimeString});
    return blob;
  }

  $("#modal-preview-avatar .modal-close").on("click", function(){
    $(".staff-upload-avatar").val("");
    $(this).modal("hide");
  });
  

  $(".staff-update-avatar").on("submit", function(e){
    e.preventDefault();
    var canvas = $("img#staff-avatar-preview").cropper("getCroppedCanvas");
    var originalAvatar = $(".staff-upload-avatar")[0].files[0];
    if (canvas == undefined || canvas.nodeName !== "CANVAS" || originalAvatar == undefined) {
      return
    }
    var actionURL = $(this).attr("action");
    var formData = getFormData(this);
    var dataURI = canvas.toDataURL(originalAvatar.type);
    var blob = dataURItoBlob(dataURI);
    var imageCropped = new File([blob], originalAvatar.name, {
      type: originalAvatar.type
    });
    formData.append("staff[avatar_cropped]", imageCropped);
    $.ajax({
      url: actionURL,
      method: "POST",
      dataType: "json",
      data: formData,
      processData: false,
      contentType: false,
      success: function(response) {
        $("#change-avatar").attr("disabled", false);
        $("#staff_avatar").attr("disabled", false);
        if(response.status){
          var message = "<p class='alert-success'>"+ response.message +"</p>";
          window.setTimeout(function(){
            location.href = response.redirect_to;
          }, 1000);
        } else {
          var message = "<p class='alert-danger'>"+ response.message +"</p>";
          window.scroll(0,0);
        }
        $(".st-avatar-uploaded-avatar").append(message);
      },
      error: function(){
        location.reload();
      }
    });
  })

  $("#cancel_interview").on("click", function(e){
    var actionURL = $(this).attr("action");
    $.ajax({
      url: actionURL,
      method: "POST",
      dataType: "json",
      data: {},
      success: function(response) {
        $("#modal-confirm-update-interview").modal("hide");
        if (response.status) {
          $("#update-interview-success").modal("show");
        } else {
          $("#update-interview-false").modal("show");
        }
      }
    });
  });
});

function isNumberKey(evt){
  var charCode = (evt.which) ? evt.which : evt.keyCode
  if (charCode > 31 && (charCode < 48 || charCode > 57))
      return false;
  return true;
}
