$(function(){
  $("#form-staff-new-password #new-password-login-id").on("input", function(e) {
    var isEmpty = _.isEmpty($(this).val().trim());

    $("#staff-new-password-button").prop("disabled", isEmpty)
      .toggleClass("button--disabled", isEmpty)
      .toggleClass("button--info", !isEmpty);
  });

  $(document).on('click','#staff-new-password-button',function(e){
    e.preventDefault();
    $('.confirm-forgot-login-id').on('click',function(){
      $('#form-staff-new-password').submit();
    });
  });

  $(".toggle-icon").on("click", function() {
    var $newPassword = $(this).parent().find("input[id*=password]");
    var elementType = $newPassword.prop("type") === "password" ? "text" : "password";
    $newPassword.prop("type", elementType);
  });
});
