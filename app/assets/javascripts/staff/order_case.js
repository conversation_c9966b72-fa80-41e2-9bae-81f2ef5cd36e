$("#carousel-modal-welcome").carousel({
  interval: false
})

$(function() {  
  $(".js-timepicker-24-hour").datetimepicker({
    locale: I18n.locale,
    format: TIME_PICKER_FORMAT,
  });

  $(".js-datepicker-staff").datetimepicker({
    locale: I18n.locale,
    format: ORDER_DATEPICKER_FORMAT,
    minDate: moment().startOf("day")
  });

  $(".disable-after-current-date").on("change.datetimepicker", function () {
    $(".disable-after-current-date").datetimepicker("minDate");
  });

  if ($("#switch-map-input").prop("checked")) {
    $(".st-work-result__map").addClass("show-map", 5000);
    $(".st-work-result__left").addClass("change-width", 5000);
  }

  $("#free-word-location").keypress(function(e) {
    if (e.keyCode == 13) {
      e.preventDefault();
    }
  });

  $(".js-timepicker-24-hour, .js-datepicker-staff").on("change.datetimepicker", function(e) {
    var $inputElm = $(this).find("input");

    if (e.date && $inputElm.val() !== e.date._i) {
      $inputElm.trigger("change");
    }
  });

  $(".js-btn-filter").each(function() {
    $(".js-btn-filter").on("click", function() {
      if ($(".js-btn-filter").hasClass("change-size")) {
        $(".custom-span-st-work-filter__datepicke").addClass("hide-datepicker");
      } else {
        $(".custom-span-st-work-filter__datepicke").removeClass("hide-datepicker");
      }
    });
  });

  $(".js-close-filter-mobile, .js-close-filter").each(function() {
    $(this).on("click", function(event) {
      event.preventDefault();
      $(".custom-span-st-work-filter__datepicke").removeClass("hide-datepicker");
    });
  });

  $(document).mouseup(function(e) {
    var container = $(".st-work-filter__button, .js-work-filter");
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      $(".custom-span-st-work-filter__datepicke").removeClass("hide-datepicker");
    }
  });

  if ($("#st-work-filter-datepicker-start").length) {
    var displayDatePlaceHolder = screen.width < MOBILE_MAX_WIDTH ? DATE_FORMAT_MOBILE.toLowerCase() : FULL_DATE_FORMAT.toLowerCase();
    $("input#st-work-filter-datepicker-end").attr("placeholder", displayDatePlaceHolder);
    $("input#st-work-filter-datepicker-start").attr("placeholder", displayDatePlaceHolder);
  }
});
