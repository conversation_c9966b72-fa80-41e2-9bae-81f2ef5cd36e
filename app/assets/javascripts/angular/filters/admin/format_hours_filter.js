angular.module("adminApp").filter("formatHours", function () {
  return function(startTime, endTime) {
    if (_.isNull(startTime)) return;
    var nextDay = "";
    if (moment.parseZone(startTime).isBefore(endTime, "day")) {
      nextDay = I18n.t("staff.order_cases.next_day");
    };
    var formattedRestTime = formatTime(startTime) + "~" + nextDay + formatTime(endTime);
    return formattedRestTime;
  };

  function formatTime(time) {
    return moment.parseZone(time).format(TIME_PICKER_FORMAT);
  };
});
