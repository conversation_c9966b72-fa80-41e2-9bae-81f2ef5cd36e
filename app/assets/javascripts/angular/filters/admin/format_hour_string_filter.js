angular.module("adminApp").filter("formatHourString", function () {
  return function(minutes) {
    if (!minutes) {
      return 0 + I18n.t("common.time.hour");
    };
    var hour = _.toInteger(minutes / 60);
    var minutes = minutes % 60;
    var time = hour + I18n.t("common.time.hour");
    if (minutes != 0) {
      time += minutes + I18n.t("common.time.minute");
    };
    return time;
  };
});
