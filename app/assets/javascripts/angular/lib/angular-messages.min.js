/*
 AngularJS v1.6.9
 (c) 2010-2018 Google, Inc. http://angularjs.org
 License: MIT
*/
(function(y,l){'use strict';function w(){return["$animate",function(u){return{restrict:"AE",transclude:"element",priority:1,terminal:!0,require:"^^ngMessages",link:function(p,n,a,c,f){var e=n[0],d,r=a.ngMessage||a.when;a=a.ngMessageExp||a.whenExp;var k=function(a){d=a?s(a)?a:a.split(/[\s,]+/):null;c.reRender()};a?(k(p.$eval(a)),p.$watchCollection(a,k)):k(r);var g,t;c.register(e,t={test:function(a){var m=d;a=m?s(m)?0<=m.indexOf(a):m.hasOwnProperty(a):void 0;return a},attach:function(){g||f(function(a,
m){u.enter(a,null,n);g=a;var d=g.$$attachId=c.getAttachId();g.on("$destroy",function(){g&&g.$$attachId===d&&(c.deregister(e),t.detach());m.$destroy()})})},detach:function(){if(g){var a=g;g=null;u.leave(a)}}});p.$on("$destroy",function(){c.deregister(e)})}}}]}var v,s,q,x;l.module("ngMessages",[],function(){v=l.forEach;s=l.isArray;q=l.isString;x=l.element}).info({angularVersion:"1.6.9"}).directive("ngMessages",["$animate",function(u){function p(a,c){return q(c)&&0===c.length||n(a.$eval(c))}function n(a){return q(a)?
a.length:!!a}return{require:"ngMessages",restrict:"AE",controller:["$element","$scope","$attrs",function(a,c,f){function e(a,c){for(var b=c,d=[];b&&b!==a;){var h=b.$$ngMessageNode;if(h&&h.length)return g[h];b.childNodes.length&&-1===d.indexOf(b)?(d.push(b),b=b.childNodes[b.childNodes.length-1]):b.previousSibling?b=b.previousSibling:(b=b.parentNode,d.push(b))}}var d=this,r=0,k=0;this.getAttachId=function(){return k++};var g=this.messages={},t,l;this.render=function(m){m=m||{};t=!1;l=m;for(var g=p(c,
f.ngMessagesMultiple)||p(c,f.multiple),b=[],e={},h=d.head,r=!1,k=0;null!=h;){k++;var q=h.message,s=!1;r||v(m,function(a,b){!s&&n(a)&&q.test(b)&&!e[b]&&(s=e[b]=!0,q.attach())});s?r=!g:b.push(q);h=h.next}v(b,function(a){a.detach()});b.length!==k?u.setClass(a,"ng-active","ng-inactive"):u.setClass(a,"ng-inactive","ng-active")};c.$watchCollection(f.ngMessages||f["for"],d.render);this.reRender=function(){t||(t=!0,c.$evalAsync(function(){t&&l&&d.render(l)}))};this.register=function(c,f){var b=r.toString();
g[b]={message:f};var k=a[0],h=g[b];d.head?(k=e(k,c))?(h.next=k.next,k.next=h):(h.next=d.head,d.head=h):d.head=h;c.$$ngMessageNode=b;r++;d.reRender()};this.deregister=function(c){var f=c.$$ngMessageNode;delete c.$$ngMessageNode;var b=g[f];b&&((c=e(a[0],c))?c.next=b.next:d.head=b.next);delete g[f];d.reRender()}}]}}]).directive("ngMessagesInclude",["$templateRequest","$document","$compile",function(l,p,n){function a(a,f){var e=n.$$createComment?n.$$createComment("ngMessagesInclude",f):p[0].createComment(" ngMessagesInclude: "+
f+" "),e=x(e);a.after(e);a.remove()}return{restrict:"AE",require:"^^ngMessages",link:function(c,f,e){var d=e.ngMessagesInclude||e.src;l(d).then(function(e){c.$$destroyed||(q(e)&&!e.trim()?a(f,d):n(e)(c,function(c){f.after(c);a(f,d)}))})}}}]).directive("ngMessage",w()).directive("ngMessageExp",w())})(window,window.angular);
//# sourceMappingURL=angular-messages.min.js.map
