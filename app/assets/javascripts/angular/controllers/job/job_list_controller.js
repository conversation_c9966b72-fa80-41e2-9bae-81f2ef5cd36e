"use strict";

angular.module("jobApp")
  .controller("jobListController", jobListController);
jobListController.$inject =  ["$location", "$scope", "jobListService", "$window"];

function jobListController($location, $scope, jobListService, $window) {
  var vm = this;
  vm.$scope = $scope;

  vm.otherOrder = {};
  vm.currencyLabel = I18n.t("common.currency");
  vm.I18nStatuses = I18n.t("job.block_order");
  vm.I18nMinute = I18n.t("common.time.minute");
  vm.I18nMonth = I18n.t("common.time.month");
  vm.weekScrollPositions = [];
  var DATA_HOLDER_IDS = ["day_options", "group_day_options", "day_names",
    "location_id", "server_day", "data_prefectures",
    "last_conditions", "initial_prefecture_id"];
  var LOCATION_ID = "location_id";
  var IDENTIFY_KEY = "job_category_key";
  var COMPARE_OPTIONS = ["day", "month", "year"];
  var RANGE_HOUR = [0, 24];
  var MAP_URL = "https://www.google.com/maps/search/?api=1&query=";
  var MAX_NUMBER_INTEGER_10 = 10;
  var SEARCH_CONDITION_TYPE = "job_search_conditions";

  var $slider = angular.element(".js-slider-time");
  var $dateScrollBar = angular.element("#dateBarDatesID");
  var tooltipTimeFadeOut = null;
  var $timeTooltips = $("#time-tooltips");
  var $tooltipInnards = $("#time-tooltips .tooltipInnards");
  var $timePreview = $("#time-preview");
  var $monthLabel = $("#js-month-label");
  var $body = $("body");
  var $prefecturesPopup = $("#prefectures-popup");
  var $updatePrefecturesBtn = $("#update-prefectures-btn");
  var $updateSearchConditionsBtn = $("#update-search-conditions-btn");
  var $searchConditionsPopup = $("#search-conditions-popup");
  var $prefectureSelect = $("#prefecture-select");
  var $loader = $("#angular-ng-repeat-loader-jobs");
  var $nextDayJobsBtn = $(".next-day-jobs");
  var $searchList = $("#searchList");
  var windowHeight =  window.innerHeight;
  var loadMonthLabel = 0;
  var AVAILABLE_PREFECTURE_IDS = [1, 4, 9, 11, 12, 13, 14, 16, 23, 26, 27, 28, 34, 38, 40, 47];

  vm.prefectureText = $prefectureSelect.text();
  vm.params = {};
  vm.params.remained_page = 1;
  vm.params.regular_order = false;
  vm.params.is_time_changable = false;
  vm.params.is_open = false;
  vm.params.search_by_prefecture = false;
  vm.hasJobs = false;
  vm.hasFilteredJobs = false;
  vm.totalFilteredJobs = 0;
  vm.sort_type = "default";
  vm.last_sort_type = "default";
  vm.location_sort_type = "started_at";
  vm.defaultJobs = [];
  vm.arrangedJobs = [];
  vm.appliedJobs = [];
  vm.favoritedJobs = [];
  vm.endedJobs = [];
  vm.normalJobs = [];
  vm.filterAppliedJobs = {};
  vm.filterArrangedJobs = {};
  vm.filterFavoritedJobs = {};
  vm.filterEndedJobs = {};
  vm.filterNormalJobs = {};
  vm.prefectureIdsArr = [];
  vm.locationJobs = [];
  vm.locations = [];
  vm.likedLocationIds = [];
  vm.locationGroups = {};
  vm.prefectures = {};
  vm.jobForMap = {};
  vm.nextDateJob = {};
  vm.displayJobs = {};
  vm.filteredLocationJobs = {};
  vm.hasFilteredStaffJobs = false;
  vm.hasArrangedJobs = false;
  vm.hasAppliedJobs = false;
  vm.hasFavoritedJobs = false;
  vm.hasEndedJobs = false;
  vm.hasNormalJobs = false;
  vm.hasStaffJobs = false;
  vm.hasFutureJobs = false;
  vm.hasLocationJobs = false;
  vm.loaderJobsDone = false;
  vm.oldCondition = false;
  vm.hasFilteredLocationJobs = false;
  vm.loaderLocationJobsDone = false;
  vm.alreadyShowNextDateBtn = false;
  vm.initSuccess = false;
  vm.initLocationSuccess = false;
  vm.loadedAllJobs = false;
  vm.data = {};

  $dateScrollBar.on("scroll", function() {
    var indexPosition = Math.ceil($dateScrollBar.scrollLeft() / ($(window).width() / (vm.day_names.length + 1)));
    var month = vm.day_options[indexPosition].month;
    if (!month) return;
    if (loadMonthLabel == 0) {
      loadMonthLabel = loadMonthLabel + 1;
    } else {
      $monthLabel.html(month + vm.I18nMonth);
    }
  });

  vm.init = function(staff_number, sign_in_count, staff_signed_in) {
    vm.data.sign_in_count = sign_in_count;
    vm.data.staff_number = staff_number;
    vm.staff_signed_in = staff_signed_in;
    if (!vm.staff_signed_in) {
      localStorage.removeItem("current_visited_job");
    }
    vm.displayWelcomeModal();
    $loader.removeClass("ng-hide");
    vm.initParams();
    vm.day_selected_option = {year: vm.started_at.split("/")[0], month: vm.started_at.split("/")[1], day: vm.started_at.split("/")[2]};
    vm.today_option = {year: vm.server_day.split("/")[0], month: vm.server_day.split("/")[1], day: vm.server_day.split("/")[2]};
    setWeekScrollPosition();
    initSlider($slider);
    vm.initPrefectures();
    vm.changeTimeRange(false);
    vm.searchUpdate(true, false);
    vm.scrollToDaySelected(vm.started_at == vm.server_day, 'smooth');
  };

  window.onpageshow = function(event) {
    if (event.persisted) {
      if (!vm.currentSelectWeek || !vm.weekScrollPositions) return;
      fixScrollDate();
    }
  };

  function fixScrollDate() {
    var scroll_options = {left: vm.weekScrollPositions[vm.currentSelectWeek]};
    $dateScrollBar[0].scroll(scroll_options);
  }

  vm.initParams = function() {
    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });
    vm.params = $location.search();
    var searchParams = {};
    if (_.isEmpty(vm.params.location_prefecture_id) && _.isEmpty(vm.last_conditions) && !!vm.initial_prefecture_id) {
      if (AVAILABLE_PREFECTURE_IDS.includes(vm.initial_prefecture_id)) {
        searchParams = {
          location_prefecture_id: JSON.stringify(vm.initial_prefecture_id),
          search_by_prefecture: true
        };
      } else {
        searchParams = {
          search_by_prefecture: false
        };
      }
      vm.params = searchParams;
    }
    if (_.isEmpty(vm.params) && !_.isEmpty(vm.last_conditions)) {
      searchParams = {
        start_date_from: vm.last_conditions.condition.start_date_from,
        sort_custom: vm.last_conditions.condition.sort_custom,
        location_prefecture_id: vm.last_conditions.condition.location_prefecture_id,
        search_by_prefecture: vm.last_conditions.condition.search_by_prefecture,
        is_open: vm.last_conditions.condition.is_open,
        is_time_changable: vm.last_conditions.condition.is_time_changable,
        regular_order: vm.last_conditions.condition.regular_order
      };
      vm.oldCondition = true;
      vm.params = searchParams;
    }
    if (!_.isEmpty(vm.last_conditions)) {
      vm.search_time_range = Array.from(vm.last_conditions.time_range.split(","), Number);
      vm.sort_type = vm.last_conditions.sort_type;
    }
    vm.params.search_by_prefecture = booleanFormat(vm.params.search_by_prefecture);
    vm.params.sort_custom = booleanFormat(vm.params.sort_custom);
    vm.params.is_open = booleanFormat(vm.params.is_open);
    vm.params.is_time_changable = booleanFormat(vm.params.is_time_changable);
    vm.params.regular_order = booleanFormat(vm.params.regular_order);
    if (!!vm.params.prefecture_name) {
      vm.params.prefecture_name = vm.params.prefecture_name;
    }
    vm.params.order_key = "created_datetime";
    vm.params.order_type = "desc";
    if (!!vm.params.start_date_from) {
      var todaysDate = new Date(vm.server_day);
      vm.started_at = vm.params.start_date_from;
      var inputDate = new Date(vm.params.start_date_from);
      if (inputDate <= todaysDate) {vm.started_at = vm.server_day;}
    } else if (!!vm.params.is_tomorrow) {
      vm.started_at = moment(vm.server_day).add(1,'days').format(ORDER_DATEPICKER_FORMAT);;
    } else {
      vm.started_at = vm.server_day;
    }
  };

  vm.initPrefectures = function() {
    if (vm.params.location_prefecture_id && !!vm.params.search_by_prefecture) {
      var locationPrefectureId = vm.params.location_prefecture_id.split(",");
       vm.prefectureIdsArr = locationPrefectureId.map(function(id) {
        return parseInt(id);
      });
    }
    vm.prefectures = {
      "関東": [
        vm.prefectureOption(13),
        vm.prefectureOption(11),
        vm.prefectureOption(12),
        vm.prefectureOption(14),
        vm.prefectureOption(9)
      ],
      "中国・四国": [
        vm.prefectureOption(34),
        vm.prefectureOption(38)
      ],
      "九州・沖縄": [
        vm.prefectureOption(40),
        vm.prefectureOption(47)
      ],
      "東北": [
        vm.prefectureOption(4)
      ],
      "北海道": [
        vm.prefectureOption(1)
      ],
      "近畿": [
        vm.prefectureOption(27),
        vm.prefectureOption(26),
        vm.prefectureOption(28)
      ],
      "中部": [
        vm.prefectureOption(23),
        vm.prefectureOption(16)
      ]
    };
  };

  vm.prefectureOption = function(prefectureID) {
    var labelName = getPrefectureLabel(prefectureID);
    var isSelected = vm.prefectureIdsArr.includes(prefectureID) || (!!vm.params.prefecture_name && vm.params.prefecture_name == labelName);
    return {
      "value": prefectureID,
      "label": labelName,
      "selected": isSelected
    };
  }

  function initSlider(slider) {
    slider.slider();
    slider.data("slider").options.min = RANGE_HOUR[0];
    slider.data("slider").options.max = RANGE_HOUR[1];
    slider.data("slider").options.value = RANGE_HOUR;
    var range_hour = RANGE_HOUR;
    if (!vm.initSuccess && vm.search_time_range) {
      range_hour = vm.search_time_range;
    }
    slider.slider("setValue", angular.copy(range_hour), false);
  }

  $slider.on("slideStop", function() {
    $scope.$apply(function(){
      vm.changeTimeRange();
      vm.searchUpdate(false, true);
    });
  });

  $slider.on("change", function() {
    vm.changeTimeRange(true, $slider);
  });

  vm.changeSortType = function() {
    vm.searchUpdate(false, true);
  };

  vm.closeSearchConditionsPopup = function() {
    $searchConditionsPopup.hide();
    $body.removeClass("modal-open");
  };

  vm.openSearchConditionsPopup = function() {
    $searchConditionsPopup.show();
    $body.addClass("modal-open");
  };

  vm.closePrefecturesPopup = function() {
    $prefecturesPopup.hide();
    $body.removeClass("modal-open");
  };

  vm.openPrefecturesPopup = function() {
    $prefecturesPopup.show();
    $body.addClass("modal-open");
  };

  $updateSearchConditionsBtn.on("click", function() {
    vm.openSearchConditionsPopup();
  });

  $updatePrefecturesBtn.on("click", function() {
    vm.openPrefecturesPopup();
  });

  function setWeekScrollPosition() {
    angular.element(document).ready(function() {
      vm.currentSelectWeek = $(".selectedDay").data("week");
      vm.currentDayWeek = $(".isToday").data("week");
      vm.currentWeek = angular.copy(vm.currentSelectWeek);
      vm.currentDateWeek = angular.copy(vm.currentDayWeek);
      _.forEach(vm.group_day_options, function(_, week) {
        vm.weekScrollPositions[week] = $("#week-" + week).children().first().offset().left;
      });
    });
  }

  vm.scrollToDaySelected = function(isToday, behavior) {
    angular.element(document).ready(function() {
      var scroll_options;
      if(isToday) {
        vm.selectedDay(vm.currentDateWeek, vm.today_option);
        scroll_options = {left: vm.weekScrollPositions[vm.currentDayWeek]};
      } else {
        vm.selectedDay(vm.currentWeek, vm.day_selected_option);
        scroll_options = {left: vm.weekScrollPositions[vm.currentSelectWeek]};
      }
      if (behavior) scroll_options.behavior = behavior;
      $dateScrollBar[0].scroll(scroll_options);
    });
  };

  vm.changeTimeRange = function(isSkipFadeOutTooltip, slider) {
    slider = slider ? slider : $slider;
    window.clearTimeout(tooltipTimeFadeOut);
    var values = slider.slider("getValue");
    vm.times = values;
    values[0] = values[0] == RANGE_HOUR[0] ? 0 : values[0];
    var startTime = values[0] < MAX_NUMBER_INTEGER_10 ? '0' + values[0] + ":00" : values[0] + ":00";
    var formatEndTime = values[1] < MAX_NUMBER_INTEGER_10 ? '0' + values[1] + ":00" : values[1] + ":00";
    var endTime = values[1] == RANGE_HOUR[1] ? I18n.t("date.next_day") : formatEndTime;
    if (_.isEmpty(_.xor(vm.time_range, values))) {
      tooltipTimeFadeOut = setTimeout(function() {
        $timeTooltips.fadeOut("smoth");
      }, 1000);
      return;
    }
    vm.time_range = values;
    $timePreview.html(startTime + "~<br>" + endTime);
    $tooltipInnards.text(startTime + " ~ " + endTime);
    if (vm.initSuccess) {
      $timeTooltips.fadeIn(0);
      if (!isSkipFadeOutTooltip) {
        tooltipTimeFadeOut = setTimeout(function() {
          $timeTooltips.fadeOut("smoth");
        }, 1000);
      }
    }
  };

  vm.selectedDay = function(week, day_selected, skip_set_month) {
    if(!skip_set_month) $monthLabel.html(parseInt(day_selected.month) + vm.I18nMonth);
    if (day_selected.is_disable && !vm.oldCondition) return;
    vm.group_day_options[vm.currentSelectWeek].forEach(function(option) {
      option.selected = false;
    });
    vm.group_day_options[week].forEach(function(option) {
      option.selected = isEqualOption(option, day_selected);
    });
    vm.currentSelectWeek = week;
    vm.started_at = [day_selected.year, day_selected.month, day_selected.day].join("/");
    if (!vm.oldCondition) {
      vm.searchUpdate(true, vm.initSuccess);
    }
    vm.oldCondition = false;
  };

  vm.searchUpdate = function(isChangeDate, saveCondition) {
    vm.search(isChangeDate, saveCondition);
  };

  vm.search = function(isChangeDate, saveCondition, isNotResetBtnNext) {
    $loader.removeClass("ng-hide");
    vm.formatParamsBeforeSearch();
    if (saveCondition && vm.staff_signed_in) vm.saveSearchCondition();
    if (!isChangeDate && vm.initSuccess) {
      if (!vm.params.sort_custom) {
        vm.last_sort_type = vm.sort_type;
        vm.sort_type = "default";
      } else {
        if (vm.sort_type == "default") {
          if (vm.last_sort_type != "default") {
            vm.sort_type = vm.last_sort_type;
          } else {
            vm.sort_type = "display_unit_price";
          }
        }
      }
      vm.sort();
      vm.filter();
      if (!isNotResetBtnNext) vm.searchNomalJobs(false, false);
      $("html").animate({scrollTop: 0}, 300);
      $loader.addClass("ng-hide");
      return ;
    }
    if (!isNotResetBtnNext) {
      vm.nextDateJob = {};
      vm.hasFutureJobs = false;
      vm.alreadyShowNextDateBtn = false;
      vm.hasFilteredStaffJobs = false;
    }
    vm.closeSearchConditionsPopup();
    vm.closePrefecturesPopup();
    vm.formatParamsBeforeSearch();
    vm.params = _.omit(vm.params, 'is_tomorrow');
    jobListService.getJobs({search: vm.params}).then(function getJobsSuccess(res) {
      if (res.status == 200) {
        angular.extend(vm, res.data);
        $location.search(vm.params).replace();
        vm.likedLocationIds = vm.data.liked_location_ids;
        vm.excludeOcIds = vm.data.all_ids.join(",");
        vm.defaultJobs = angular.copy(vm.data.order_cases);
        vm.locations = _.concat(vm.locations, vm.data.locations);
        var locations = vm.locations.filter(Boolean);
        vm.locationGroups = filterObjectByKey(locations, IDENTIFY_KEY);
        vm.hasStaffJobs = !_.isEmpty(vm.defaultJobs);
        vm.hasJobs = vm.hasStaffJobs;
        vm.hasFilteredJobs = vm.hasJobs;
        if (!isNotResetBtnNext) vm.searchNomalJobs(false, true);
        vm.displayJobs = {};
        vm.arrangedJobs = getJobsByIds(vm.defaultJobs, vm.data.arranged_oc_ids);
        vm.appliedJobs = getJobsByIds(vm.defaultJobs, vm.data.applied_oc_ids);
        vm.favoritedJobs = getJobsByIds(vm.defaultJobs, vm.data.liked_location_oc_ids);
        vm.endedJobs = getJobsByIds(vm.defaultJobs, vm.data.ended_oc_ids);
        vm.sort();
        vm.filter();
        vm.initSuccess = true;
        $loader.addClass("ng-hide");
        $("html").animate({scrollTop: 0}, 300);
      }
    });
  };

  vm.searchNomalJobs = function(isLoadMore, isReset) {
    if (vm.params.sort_custom && vm.sort_type == "default") {
      vm.params.sort_field = "display_unit_price";
    } else {
      vm.params.sort_field = vm.sort_type;
    }
    if (!vm.params.sort_custom) vm.params.sort_field = "case_started_at";
    vm.params.remained_page = isLoadMore ? vm.params.remained_page + 1 : 1;

    vm.params.from_hour = vm.time_range[0];
    vm.params.to_hour = vm.time_range[1];
    if(!!isReset){
      vm.loaderNormalJobsDone = false;
      vm.loadedAllJobs = false;
    }
    vm.searchNomaljobsDone = false;
    var params = {
      search: vm.params,
      exclude_oc_ids: vm.excludeOcIds,
      exclude_location_ids: vm.likedLocationIds.join(",")
    };
    jobListService.getRemainedJobs(params).then(function getRemainedJobsSuccess(res) {
      if (res.status == 200) {
        if (isLoadMore) {
          vm.normalJobs = _.concat(vm.normalJobs, res.data.order_cases);
        } else {
          vm.normalJobs = res.data.order_cases;
        }
        vm.hasNormalJobs = !_.isEmpty(vm.normalJobs);
        if (vm.hasNormalJobs) {
          vm.filterNormalJobs = filterObjectByKey(vm.normalJobs, IDENTIFY_KEY);
          vm.locations = _.concat(vm.locations, res.data.locations);
          var locations = vm.locations.filter(Boolean);
          vm.locationGroups = filterObjectByKey(locations, IDENTIFY_KEY);
        }
        vm.hasJobs = vm.hasStaffJobs || vm.hasNormalJobs;
        vm.hasFilteredJobs = vm.hasFilteredStaffJobs || vm.hasNormalJobs;
        vm.totalNormalJobs = res.data.total_count;
        vm.searchNomaljobsDone = true;
        vm.loaderNormalJobsDone = true;
        vm.loadedAllJobs = vm.normalJobs.length == vm.totalNormalJobs;
        if (vm.loadedAllJobs && !vm.alreadyShowNextDateBtn) vm.getNextDateHasJob();
      }
    });
  };

  vm.getNextDateHasJob = function() {
    vm.alreadyShowNextDateBtn = true;
    vm.hasFutureJobs = false;
    jobListService.getNextDateHasJob({search: vm.params}).then(function getNextDateSuccess(res) {
      if (res.status == 200) {
        vm.nextDateJob = res.data;
      } else {
        vm.nextDateJob = {};
        vm.alreadyShowNextDateBtn = false;
      }
      vm.hasFutureJobs = Object.keys(vm.nextDateJob).length > 0;
    });
  };

  $searchList.on('DOMSubtreeModified', $searchList, function(){
    if ($searchList.height() < windowHeight - 280) {
      $nextDayJobsBtn.addClass("fixed-bottom");
    } else {
      $nextDayJobsBtn.removeClass("fixed-bottom");
    }
  });

  vm.saveSearchCondition = function() {
    if (!vm.staff_signed_in) {
      return;
    }
    if (vm.initSuccess) {
      var searchCondition = {
        condition: vm.params,
        time_range: vm.time_range.join(),
        sort_type: vm.sort_type,
      };
      $location.search(vm.params).replace();
      jobListService.createSearchCondition({search: searchCondition, search_condition: SEARCH_CONDITION_TYPE});
    }
  };

  vm.filter = function() {
    var from_hour = vm.time_range[0];
    var to_hour = vm.time_range[1];
    var filterArrangedJobs = getJobsInTimeRange(vm.arrangedJobs, from_hour, to_hour);
    var filterAppliedJobs = getJobsInTimeRange(vm.appliedJobs, from_hour, to_hour);
    var filterFavoritedJobs = getJobsInTimeRange(vm.favoritedJobs, from_hour, to_hour);
    var filterEndedJobs = getJobsInTimeRange(vm.endedJobs, from_hour, to_hour);
    vm.filterArrangedJobs = filterObjectByKey(filterArrangedJobs, IDENTIFY_KEY);
    vm.hasArrangedJobs = !_.isEmpty(vm.filterArrangedJobs);
    vm.filterAppliedJobs = filterObjectByKey(filterAppliedJobs, IDENTIFY_KEY);
    vm.hasAppliedJobs = !_.isEmpty(vm.filterAppliedJobs);
    vm.filterFavoritedJobs = filterObjectByKey(filterFavoritedJobs, IDENTIFY_KEY);
    vm.hasFavoritedJobs = !_.isEmpty(vm.filterFavoritedJobs);
    vm.filterEndedJobs = filterObjectByKey(filterEndedJobs, IDENTIFY_KEY);
    vm.hasEndedJobs = !_.isEmpty(vm.filterEndedJobs);
    var filterJobs = getJobsInTimeRange(vm.defaultJobs, from_hour, to_hour);
    vm.hasFilteredStaffJobs = !_.isEmpty(filterJobs);
    vm.hasFilteredJobs = vm.hasFilteredStaffJobs || vm.hasNormalJobs;
    vm.totalFilteredJobs = filterJobs.length;
  };

  vm.sort = function() {
    vm.arrangedJobs = sortJobsByKey(vm.arrangedJobs, vm.sort_type);
    vm.appliedJobs = sortJobsByKey(vm.appliedJobs, vm.sort_type);
    vm.favoritedJobs = sortJobsByKey(vm.favoritedJobs, vm.sort_type);
    vm.endedJobs = sortJobsByKey(vm.endedJobs, vm.sort_type);
  };

  vm.locationInfo = function(jobs) {
    var key = jobs[0][IDENTIFY_KEY];
    return vm.locationGroups[key][0];
  };

  vm.likeLocation = function(locationId, event) {
    if (event) event.stopPropagation();
    if (!vm.staff_signed_in) {
      location.href = "/" + I18n.locale + "/login";
      return;
    }
    if (!vm.isLikeLocation(parseInt(locationId))) {
      var oldFilterNormalJobs = vm.filterNormalJobs;
      _.each(Object.keys(vm.filterNormalJobs), function(key) {
        if (_.includes(key, "" + locationId)) {delete vm.filterNormalJobs[key];};
      });
      jobListService.createStaffLikeLocation({location_id: locationId}).then(function createLikeSuccess(res) {
        if (res.status == 200 && res.data.status === true) {
          vm.search(true, false, true);
          vm.normalJobs = vm.normalJobs.filter(function(job){
            return job.location_id != locationId;
          });
          vm.hasNormalJobs = !_.isEmpty(vm.normalJobs);
          vm.hasJobs = vm.hasStaffJobs || vm.hasNormalJobs;
          vm.hasFilteredJobs = vm.hasFilteredStaffJobs || vm.hasNormalJobs;
        } else {
          vm.filterNormalJobs = oldFilterNormalJobs;
          var index = vm.likedLocationIds.indexOf(locationId);
          if (index > -1) vm.likedLocationIds.splice(index, 1);
        }
      });
    } else {
      jobListService.removeStaffLikeLocation(locationId).then(function(res) {
        if (res.status == 200 && !!res.data.status) {
          var index = vm.likedLocationIds.indexOf(locationId);
          if (index > -1) vm.likedLocationIds.splice(index, 1);
          var unlikeJobs = vm.favoritedJobs.filter(function(job) {return job.location_id == locationId;});
          if (unlikeJobs.length == 0) return;
          var filterUnlikeJobs = getJobsInTimeRange(unlikeJobs, vm.time_range[0], vm.time_range[1]);
          vm.favoritedJobs = vm.favoritedJobs.filter(function(job){return job.location_id != locationId;});
          _.each(Object.keys(vm.filterFavoritedJobs), function(key) {
            if (_.includes(key, "" + locationId)) {delete vm.filterFavoritedJobs[key];};
          });
          vm.hasFavoritedJobs = !_.isEmpty(vm.filterFavoritedJobs);
          vm.normalJobs = _.concat(vm.normalJobs, filterUnlikeJobs);
          vm.normalJobs = sortJobsByKey(vm.normalJobs, vm.sort_type);
          vm.hasNormalJobs = !_.isEmpty(vm.normalJobs);
          vm.filterNormalJobs = filterObjectByKey(vm.normalJobs, IDENTIFY_KEY);
          vm.hasFilteredStaffJobs = vm.hasEndedJobs || vm.hasAppliedJobs || vm.hasArrangedJobs || vm.hasFavoritedJobs;
          vm.hasJobs = vm.hasStaffJobs || vm.hasNormalJobs;
          vm.hasFilteredJobs = vm.hasFilteredStaffJobs || vm.hasNormalJobs;
        }
      });
    }
  };

  vm.isLikeLocation = function(locationId) {
    return vm.likedLocationIds.includes(parseInt(locationId));
  };

  vm.formatParamsBeforeSearch = function() {
    vm.params.start_date_from = vm.started_at;
    vm.params.start_date_to = vm.started_at;
    formatPrefecturesParams();
  };

  vm.updateNotificationFlag  = function(toFlag) {
    vm.data.is_notification_on = toFlag;
  };

  vm.togglePrefectureChkbox = function(prefecture) {
    if (prefecture.selected) {
      vm.prefectureIdsArr.push(prefecture.value);
    } else {
      _.pull(vm.prefectureIdsArr, prefecture.value);
    }
    formatPrefecturesParams();
  };

  vm.getWorkingTime = function(job) {
    return formmatWorkTime(job.predetermined_time, job.break_time);
  };

  vm.redirectToJobDetail = function(job, event) {
    if (event) event.stopPropagation();
    var url = "/order_cases/" + job.id;
    location.href = url;
  };

  vm.redirectToGoogleMap = function(jobs, event) {
    if (event) event.stopPropagation();
    var location = vm.locationInfo(jobs);
    var url = MAP_URL + location.latitude + "," + location.longitude;
    window.open(url, "_blank");
  };

  vm.displayJobClass = function(category, job, index, jobsLength) {
    var lastJobClass = index == jobsLength - 1 ? "last-jobs" : "";
    if ((job.timeout_apply_before_2_hour || vm.registrationStaffApplyTimeout(job)) &&
      (category == "normal-jobs" || category == "favorited-jobs")) {
      if (lastJobClass == ""){ return "finish_recruiting"; }
      return "finish_recruiting" + " " + lastJobClass;
    }
    return category + " " + lastJobClass;
  };

  vm.registrationStaffApplyTimeout = function(job) {
    if (vm.data.no_limit_registration_apply) { return false; }
    var workingStart = moment(job.started_at.substring(0, 10) + " " + job.working_time.split("~")[0]);
    var currentTime = moment(moment().format(DATE_HOUR_MINUTE_FORMAT));
    var difference = moment.duration(workingStart.diff(currentTime)).asHours();
    return difference < 10;
  };

  vm.isDisplayJob = function(jobs, index, category, locationId) {
    if (jobs.length < 5 || !!vm.displayJobs[category + locationId]) {
      if (vm.time_range[1] == 24 && vm.time_range[0] == 0){
        vm.displayJobs[category + locationId] = true;
      }
      return true;
    }
    return index < 3;
  };

  vm.isShowLoadAll = function(jobs, category, locationId) {
    return !vm.displayJobs[category + locationId] && jobs.length >= 5;
  };

  vm.clickShowAll = function(category, locationId) {
    vm.displayJobs[category + locationId] = true;
  };

  vm.redirectToLocationJobCategory  = function(locationId, locationJobCategoryId) {
    location.href = "/" + I18n.locale + "/locations/" + locationId + "/jobs?location_job_category_id=" + locationJobCategoryId;
  };

  vm.isFirstSection = function(category) {
    var categories = {
      "arranged": vm.hasArrangedJobs,
      "applied": vm.hasAppliedJobs,
      "favorited": vm.hasFavoritedJobs,
      "normal": vm.hasNormalJobs,
      "ended": vm.hasEndedJobs
    };
    var trueCategories = [];
    Object.keys(categories).forEach(function(key) {
      if (categories[key]) {trueCategories.push(key);}
    });
    return category == trueCategories[0];
  };

  vm.formatSalary = function(price) {
    if (price == null || price == undefined) price = 0;
    price = Math.round(price).toLocaleString();
    return price + vm.currencyLabel;
  };

  vm.nextDayHasJobs = function() {
    if (!vm.hasFutureJobs) {
      return;
    }
    var weekIdx = vm.nextDateJob.week_idx;
    var dayOptions = {
      "year": vm.nextDateJob.year,
      "month": vm.nextDateJob.month,
      "day": vm.nextDateJob.day
    };
    vm.selectedDay(weekIdx, dayOptions);
    var scrollOptions = {left: vm.weekScrollPositions[weekIdx]};
    $dateScrollBar[0].scroll(scrollOptions);
  };

  vm.isDisplayAveragePrice = function() {
    return vm.sort_type == "average_price";
  };

  vm.displayWelcomeModal = function() {
    if (vm.staff_signed_in) {
      return;
    }
    var isRead = JSON.parse(localStorage.getItem("read_welcome_modal"));
    if (!isRead){
      $("#modal-welcome-workz").show();
      $body.addClass("modal-open");
    }
  }

  vm.closeWelcomeModal = function() {
    var staffNumber = vm.data.staff_number;
    localStorage.setItem("read_welcome_modal", true);
    $("#modal-welcome-workz").hide();
    $body.removeClass("modal-open");
  }

  function formatPrefecturesParams() {
    if (_.isEmpty(vm.prefectureIdsArr)) {
      vm.params.location_prefecture_id = "";
      vm.params.search_by_prefecture = false;
    } else {
      vm.params.location_prefecture_id = vm.prefectureIdsArr.join();
      vm.params.search_by_prefecture = true;
    }
  }

  function fixScrollDateInSafary() {
    var scroll_options = {left: vm.weekScrollPositions[vm.currentSelectWeek]};
    $dateScrollBar[0].scroll(scroll_options);
  }

  function filterObjectByKey(data, key) {
    var newData = data.reduce(function (r, a) {
        var new_key = a[key];
        r[new_key] = r[new_key] || [];
        r[new_key].push(a);
        return r;
    }, Object.create(null));
    return newData;
  }

  function getJobsByIds(jobs, ids) {
    return jobs.filter(function(job) {
      return ids.includes(job.id);
    });
  }

  function getJobIdsOfLocationId(jobs, locationId) {
    var jobIds = [];
    jobs.forEach(function(job){
      if (job.location_id == locationId) {jobIds.push(job.id);}
    });
    return jobIds;
  }

  function getJobsInTimeRange(jobs, fromHour, toHour) {
    return jobs.filter(function(job) {
      var case_started_at = timeToSecond(job.working_time.split("~")[0]);
      var case_ended = job.working_time.split("~")[1];
      if (case_ended.length == 6) case_ended = case_ended.substring(1, 6);
      var case_ended_at = timeToSecond(case_ended);
      if (toHour == 24) {
        if (fromHour == 0) {
          return true;
        } else {
          return case_started_at >= timeToSecond(fromHour);
        }
      }
      return case_started_at >= timeToSecond(fromHour) && case_ended_at <= timeToSecond(toHour) && case_started_at <= case_ended_at;
    });
  }

  function timeToSecond(formatTime) {
    var dataTime = (formatTime + "").split(":");
    var timeSecond = parseInt(dataTime[0]) * 3600;
    if (!!dataTime[1]) {
      timeSecond = timeSecond + parseInt(dataTime[1]) * 60;
    }
    return timeSecond;
  }

  function sortJobsByKey(jobs, key) {
    if (key == "default") {
      return sortJobsDefault(jobs);
    }
    jobs.sort(function(jobA, jobB) {
      var keyA = jobA[key];
      var keyB = jobB[key];
      if (keyA > keyB) return -1;
      if (keyA < keyB) return 1;
      return 0;
    });
    return jobs;
  }

  function sortJobsDefault(jobs) {
    jobs.sort(function(jobA, jobB) {
      var caseStartedjobA = timeToSecond(jobA.working_time.split("~")[0]);
      var caseStartedjobB = timeToSecond(jobB.working_time.split("~")[0]);
      if (caseStartedjobA < caseStartedjobB) return -1;
      if (caseStartedjobA > caseStartedjobB) return 1;
      return 0;
    });
    return jobs;
  }

  function isEqualOption(option1, option2) {
    var status = true;
    COMPARE_OPTIONS.forEach(function(key) {
      if (status) status = option1[key] == option2[key];
    });
    return status;
  }

  function subtractArray(a1, a2) {
    var subtractIds = [];
    a1.forEach(function(data){
      if (!a2.includes(data)) {
        subtractIds.push(data);
      }
    });
    return subtractIds;
  }

  function formmatWorkTime(predeterminedTime, breakTime) {
    var time = (predeterminedTime + breakTime) / 60;
    if (time % 1 === 0)
      return time;
    else {
      var times = time.toString().split(".");
      return times[0] + "<span class='smallDecimal'>." + times[1].substring(2, 0) + "</span>";
    }
  }

  function getPrefectureLabel(PrefectureId) {
    var prefecture_name = "";
    vm.data_prefectures.forEach(function(prefecture) {
      if (prefecture.id == PrefectureId) prefecture_name = prefecture.name;
    });
    return prefecture_name;
  }

  function booleanFormat(data) {
    return data === true || data === "true";
  }

  $(window).scroll(function () {
    if (vm.normalJobs.length < vm.totalNormalJobs) {
      var screenHeight =  $(window).height();
      var isLoadMore = ($(document).height() -  screenHeight / 2) <= $(window).scrollTop() + screenHeight;
      if (isLoadMore && vm.loaderNormalJobsDone && vm.initSuccess && vm.searchNomaljobsDone) vm.searchNomalJobs(true, false);
    } else {
      if (vm.loadedAllJobs && vm.initSuccess && !vm.alreadyShowNextDateBtn) vm.getNextDateHasJob();
    }
  });
}
