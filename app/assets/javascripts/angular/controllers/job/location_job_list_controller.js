"use strict";

angular.module("jobApp")
  .controller("locationJobListController", locationJobListController);
locationJobListController.$inject =  ["$location", "$scope", "jobListService", "$window"];

function locationJobListController($location, $scope, jobListService, $window) {
  var vm = this;
  vm.$scope = $scope;

  vm.otherOrder = {};
  vm.currencyLabel = I18n.t("common.currency");
  vm.I18nStatuses = I18n.t("job.block_order");
  vm.I18nMinute = I18n.t("common.time.minute");
  vm.I18nMonth = I18n.t("common.time.month");
  var GROUP_LOCATION_STARTED_AT = "started_at";
  var COMPARE_OPTIONS = ["day", "month", "year"];
  var RANGE_HOUR = [0, 24];
  var MAP_URL = "https://www.google.com/maps/search/?api=1&query=";
  var MAX_NUMBER_INTEGER_10 = 10;
  var tooltipTimeFadeOut = null;
  var $body = $("body");
  var $slider = angular.element(".js-slider-time-location");
  var $timeTooltips = $("#time-location-tooltips");
  var $timePreview = $("#time-location-preview");
  var $locationTooltipInnards = $("#time-location-tooltips .locationTooltipInnards");
  var $loader = $("#angular-ng-repeat-loader-jobs");
  var $searchList = $("#locationSearchList");
  var windowHeight =  window.innerHeight;

  vm.initSuccess = false;
  vm.params = {};
  vm.defaultJobs = [];
  vm.locationJobs = [];
  vm.locationArrangedJobIds = [];
  vm.locationAppliedJobIds = [];
  vm.locationEndedJobIds = [];
  vm.hasJobs = false;
  vm.loaderJobsDone = false;
  vm.sort_type = "default";
  vm.params.sort_field = "case_started_at";
  vm.last_sort_type = "default";

  vm.init = function(staff_signed_in, location_data, is_liked) {
    vm.staff_signed_in = staff_signed_in;
    vm.location_data = location_data;
    vm.location_id = vm.location_data["id"];
    vm.location_job_category_id = vm.location_data["location_job_category_id"];
    vm.is_liked = is_liked;
    $loader.removeClass("ng-hide");
    vm.loadCurrentVisitedJob();
    vm.params = $location.search();
    vm.params.page = 1;
    initSlider($slider);
    vm.changeTimeRange(false, $slider);
    vm.search(false);
  };

  vm.loadCurrentVisitedJob = function() {
    if (!vm.staff_signed_in) {
      localStorage.removeItem("current_visited_job");
    }
  }

  vm.changeTimeRange = function(isSkipFadeOutTooltip, slider) {
    slider = slider ? slider : $slider;
    window.clearTimeout(tooltipTimeFadeOut);
    var values = slider.slider("getValue");
    vm.times = values;
    values[0] = values[0] == RANGE_HOUR[0] ? 0 : values[0];
    var startTime = values[0] < MAX_NUMBER_INTEGER_10 ? '0' + values[0] + ":00" : values[0] + ":00";
    var formatEndTime = values[1] < MAX_NUMBER_INTEGER_10 ? '0' + values[1] + ":00" : values[1] + ":00";
    var endTime = values[1] == RANGE_HOUR[1] ? I18n.t("date.next_day") : formatEndTime;
    if (_.isEmpty(_.xor(vm.time_range, values))) {
      tooltipTimeFadeOut = setTimeout(function() {
        $timeTooltips.fadeOut("smoth");
      }, 1000);
      return;
    }
    vm.time_range = values;
    $timePreview.html(startTime + "~<br>" + endTime);
    $locationTooltipInnards.text(startTime + " ~ " + endTime);
    if (vm.initSuccess) {
      $timeTooltips.fadeIn(0);
      if (!isSkipFadeOutTooltip) {
        tooltipTimeFadeOut = setTimeout(function() {
          $timeTooltips.fadeOut("smoth");
        }, 1000);
      }
    }
  };

  vm.search = function(isloadMore) {
    if (vm.initSuccess && isloadMore && !vm.loaderJobsDone) return;
    vm.formatParams();
    vm.params.page = isloadMore ? vm.params.page + 1 : 1;
    if (!isloadMore) $loader.removeClass("ng-hide");
    vm.loaderJobsDone = false;
    var reqParams = {search: vm.params};
    if (vm.location_data.location_job_category_id != undefined) {
      reqParams["location_job_category_id"] = vm.location_data.location_job_category_id;
    }
    if (!vm.initSuccess) {
      jobListService.getLocationJobStatuses(vm.location_id, reqParams).then(function mySuccess(res) {
        vm.locationArrangedJobIds = res.data.data.arranged_oc_ids;
        vm.locationAppliedJobIds = res.data.data.applied_oc_ids;
        vm.locationEndedJobIds = res.data.data.ended_oc_ids;
        vm.no_limit_registration_apply = res.data.data.no_limit_registration_apply;
      });
    }
    jobListService.getLocationJobs(vm.location_id, reqParams).then(function mySuccess(res) {
      if (res.status == 200) {
        if (isloadMore) {
          vm.defaultJobs = _.concat(vm.defaultJobs, res.data.data.order_cases);
        } else {
          vm.defaultJobs = res.data.data.order_cases;
        }
        vm.locationJobs = angular.copy(vm.defaultJobs);
        vm.dateGroupJobs = filterObjectByKey(vm.locationJobs, GROUP_LOCATION_STARTED_AT);
        vm.totalJobs = res.data.data.total_items;
        vm.no_limit_registration_apply = res.data.data.no_limit_registration_apply;
        vm.hasJobs = !_.isEmpty(vm.locationJobs);
        vm.initSuccess = true;
        vm.loaderJobsDone = true;
        $loader.addClass("ng-hide");
        if (!isloadMore) {
          $("html").animate({scrollTop: 0}, 300);
        }
      }
    });
  };

  vm.formatParams = function() {
    vm.params.location_department_id = vm.location_id;
    vm.params.per_page = 10;
    if (vm.sort_custom && vm.sort_type == "default") {
      vm.params.sort_field = "display_unit_price";
    } else {
      vm.params.sort_field = vm.sort_type;
    }
    if (!vm.sort_custom) vm.params.sort_field = "case_started_at";
    vm.params.from_hour = vm.time_range[0];
    vm.params.to_hour = vm.time_range[1];
  };

  vm.closeLocationView = function() {
    window.history.go(-1);
  };

  $slider.on("slideStop", function() {
    $scope.$apply(function(){
      vm.changeTimeRange(false, $slider);
      vm.search(false);
    });
  });

  $slider.on("change", function() {
    vm.changeTimeRange(true, $slider);
  });

  vm.likeLocation = function(event) {
    if (!vm.staff_signed_in) {
      location.href = "/" + I18n.locale + "/login";
      return;
    }
    if (event) event.stopPropagation();
    if (!vm.is_liked) {
      jobListService.createStaffLikeLocation({location_id: vm.location_id}).then(function mySuccess(res) {
        if (res.status == 200) {vm.is_liked = true;}
      });
    } else {
      jobListService.removeStaffLikeLocation(vm.location_id).then(function(res) {
        if (res.status == 200) {vm.is_liked = false;}
      });
    }
  };

  vm.getWorkingTime = function(job) {
    return formatWorkTime(job.predetermined_time, job.break_time);
  };

  vm.redirectToJobDetail = function(job, event) {
    if (event) event.stopPropagation();
    var url = "/order_cases/" + job.id;
    location.href = url;
  };

  vm.redirectToGoogleMap = function(location, event) {
    if (event) event.stopPropagation();
    var url = MAP_URL + vm.location_data.latitude + "," + vm.locaiton_data.longitude;
    window.open(url, "_blank");
  };

  vm.displayJobClass = function(category, job, index, jobsLength) {
    var lastJobClass = index == jobsLength - 1 ? "last-jobs" : "";
    if (vm.locationArrangedJobIds.includes(job.id)) {return "arranged-jobs" + " " + lastJobClass;}
    if (vm.locationAppliedJobIds.includes(job.id)) {return "applied-jobs" + " " + lastJobClass;}
    if (vm.locationEndedJobIds.includes(job.id)) {return "ended-jobs" + " " + lastJobClass;}
    if (job.timeout_apply_before_2_hour || vm.registrationStaffApplyTimeout(job)) {return "finish_recruiting" + " " + lastJobClass;}
    return job.status_id + " " + lastJobClass;
  };

  vm.registrationStaffApplyTimeout = function(job) {
    if (vm.no_limit_registration_apply) { return false; }
    var workingStart = moment(job.started_at.substring(0, 10) + " " + job.working_time.split("~")[0]);
    var currentTime = moment(moment().format(DATE_HOUR_MINUTE_FORMAT));
    var difference = moment.duration(workingStart.diff(currentTime)).asHours();;
    return difference < 10;
  };

  vm.isDisplayJob = function(jobs, index, category, locationId) {
    return category == 'location-jobs';
  };

  vm.isShowLoadAll = function(jobs, category, locationId) {
    return false;
  };

  vm.formatSalary = function(price) {
    if (price == null || price == undefined) price = 0;
    price = Math.round(price).toLocaleString();
    return price + vm.currencyLabel;
  };

  vm.isDisplayAveragePrice = function() {
    return vm.sort_type == "average_price";
  };

  function filterObjectByKey(data, key) {
    var newData = data.reduce(function (r, a) {
        r[a[key]] = r[a[key]] || [];
        r[a[key]].push(a);
        return r;
    }, Object.create(null));
    return newData;
  }

  function timeToSecond(formatTime) {
    var dataTime = (formatTime + "").split(":");
    var timeSecond = parseInt(dataTime[0]) * 3600;
    if (!!dataTime[1]) {
      timeSecond = timeSecond + parseInt(dataTime[1]) * 60;
    }
    return timeSecond;
  }

  function formatWorkTime(predeterminedTime, breakTime) {
    var time = (predeterminedTime + breakTime) / 60;
    if (time % 1 === 0)
      return time;
    else {
      var times = time.toString().split(".");
      return times[0] + "<span class='smallDecimal'>." + times[1].substring(2, 0) + "</span>";
    }
  }

  function initSlider(slider) {
    slider.slider();
    slider.data("slider").options.min = RANGE_HOUR[0];
    slider.data("slider").options.max = RANGE_HOUR[1];
    slider.data("slider").options.value = RANGE_HOUR;
    slider.slider("setValue", angular.copy(RANGE_HOUR), false);
  }

  $searchList.on('DOMSubtreeModified', $searchList, function(){
    if ($searchList.height() < windowHeight - 280 && vm.initSuccess) {
      if ( vm.locationJobs.length < vm.totalJobs && vm.loaderJobsDone) {
        vm.search(true);
      } else {
        $(".space-end-1").addClass("d-none");
      }
    }
  });

  $(window).scroll(function () {
    if (vm.locationJobs.length < vm.totalJobs) {
      var screenHeight =  $(window).height();
      if (($(document).height() -  screenHeight / 2) <= $(window).scrollTop() + screenHeight) {
        if (vm.loaderJobsDone) vm.search(true);
      }
    }
  });
}
