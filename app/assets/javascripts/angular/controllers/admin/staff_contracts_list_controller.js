"use strict";

angular.module("adminApp")
  .controller("StaffContractsListController", StaffContractsListController);
StaffContractsListController.$inject = ["$location", "staffContractService", "toaster"];

function StaffContractsListController($location, staffContractService, toaster) {
  var vm = this;
  var staffContractEnumLbs = I18n.t("enum_label.staff_contract");
  var staffSalaryEnumLbs = I18n.t("enum_label.staff_salary");
  var DATETIME_FIELDS = ["created_at_start", "created_at_end"];
  var SEARCH_CONDITION_TYPE = "admin_staff_contract_search_conditions";

  vm.hasRecord = false;
  vm.toasterTimeout = 6200;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.params = $location.search();
  vm.selectAll = false;
  vm.request_statuses = [];
  vm.workConditions = staffContractEnumLbs.work_conditions;
  vm.ssInsuranceSubsectionTypes = staffSalaryEnumLbs.insurance_subsection_types;
  vm.ssEmploymentInsuranceTypes = staffSalaryEnumLbs.employment_insurance_types;
  vm.requestStatuses = staffContractEnumLbs.request_statuses;
  vm.update_params = {};
  vm.hasError = false;
  vm.isDisabledButtonUpdate = false;

  vm.changePerPage = function() {
    vm.refresh();
  }

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !vm.params.desc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !!vm.params.desc
    }
  }

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
    }
    vm.refresh(false);
  }

  vm.formatDate = function(date) {
    if (!_.isEmpty(date)) return date.replace(/-/g, "/");
  }

  vm.enableButtonUpdate = function() {
    var contractSelected = _.filter(vm.staff_contracts, function(staff_contract) {
      return !!staff_contract.select;
    });
    vm.isDisabledButtonUpdate = _.isEmpty(contractSelected);
  }

  function initChkSearch() {
    vm.chk_search = {};
    if (_.isEmpty(vm.params.request_statuses)) return;
    var arrRequestStatus = vm.params.request_statuses.split(",");
    _.forEach(vm.request_statuses, function(value, key) {
      if (_.includes(arrRequestStatus, value.toString())) vm.chk_search[key] = true;
    });
  }

  function setParamsCheckboxSearch() {
    var ids = [];
    _.forEach(vm.request_statuses, function(value, key) {
      if (vm.chk_search[key]) ids.push(value);
    });
    vm.params.request_statuses = ids.join();
  }

  vm.bulkUpdateByIds = function(status) {
    $(".disable-submit-btn").prop("disabled", true);
    var ids = [];
    angular.forEach(vm.staff_contracts, function(staff_contract) {
      if (staff_contract.select == true) ids.push(staff_contract.id);
    });
    vm.update_params.ids = ids;
    vm.update_params.status = status;
    staffContractService.bulkUpdateContractStatus(vm.update_params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasError = !vm.status;
      if (vm.status){
        toaster.pop("success", "", vm.message);
        vm.closeModal();
        vm.selectAll = false;
        vm.refresh(false);
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  }

  vm.resetModal = function() {
    vm.update_params = {};
    vm.hasError = false;
    vm.message = null;
  }

  vm.setStatusAll = function(allField, field) {
    if (vm.chk_search[field]) return;
    vm["search_all_" + allField] = false;
  }

  vm.closeModal = function() {
    $("#modal-confirm-approve").modal('hide');
    $("#modal-confirm-reject").modal('hide');
  }

  vm.isProcessed = function(staff_contract) {
    return staff_contract.request_status != 'requesting';
  }

  vm.checkAllStaffContracts = function() {
    angular.forEach(vm.staff_contracts, function(staff_contract) {
      if (vm.isProcessed(staff_contract)){
        staff_contract.select = false;
      }
      else {
        staff_contract.select = vm.selectAll;
      }
    });
  }

  vm.checkAllChkSearch = function(allField) {
    _.forEach(vm[allField], function(value, key) {
      vm.chk_search[key] = vm["search_all_" + allField];
    });
  }

  function fetchParams() {
    $("#department_id").val(vm.params.department_id).trigger('change.select2');
    DATETIME_FIELDS.forEach(function(attr) {
      if (vm.params[attr]) {
        $("[name='" + attr + "']").datepicker("setDate", vm.params[attr]);
      };
    });
    if (_.isEmpty(vm.params.page)) vm.params.page = 1;
    if (_.isEmpty(vm.params.per_page)) vm.params.per_page = vm.perPageSettings[0];
  }

  vm.init = function(){
    vm.request_statuses = angular.element("#request_statuses").data("infos");
    if (_.isEmpty($location.search())) {
      staffContractService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
        if (res.data.status) {
          vm.params = JSON.parse(res.data.last_conditions);
        } else {
          vm.params = {page: 1, per_page: 10};
        }
        initChkSearch();
        vm.refresh(false);
        fetchParams();
      });
    } else {
      initChkSearch();
      vm.refresh(false, true);
      fetchParams();
    }
  }

  vm.refresh = function(resetPage, isSave) {
    setParamsCheckboxSearch();
    if (resetPage) vm.params.page = 1;
    if (isSave) {
      staffContractService.createSearchCondition({search: _.omit(vm.params, "page", "limit"), search_condition: SEARCH_CONDITION_TYPE});
    }
    if (!vm.params.per_page) vm.params.per_page = 10;

    staffContractService.getStaffContracts(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.staff_contracts.length);
      $location.search(vm.params).replace();
      vm.currentParams = angular.copy(vm.params);
      vm.enableButtonUpdate();
    }, function(error) {
      vm.staff_contracts = [];
    });
  }
}
