'use strict';

angular.module('adminApp').controller('LocationSearchController', LocationSearchController);
LocationSearchController.$inject = ['$location', 'locationService', 'checkValidDateFunction'];

function LocationSearchController($location, locationService, checkValidDateFunction) {
  var vm = this;
  var requestParams = $location.search();
  var searchParamKeys = ['corporation_group_id', 'name', 'code', 'addresses', 'violation_start', 'violation_end', 'surveyed', 'not_survey',
    "configured_store_com", "not_configured_store_com", "lawson_invoice", "separate_invoice"];
  vm.hasRecord = vm.noRecord = false;
  vm.params = {};
  vm.params.page = requestParams.page;
  var SELECT_FIELDS = ["corporation_group_id"];
  var SEARCH_CONDITION_TYPE = "admin_location_search_conditions";
  var DATETIME_FIELDS = ["violation_start", "violation_end"];
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  _.forEach(searchParamKeys, function(searchParamKey) {
    vm.params[searchParamKey] = requestParams[searchParamKey];
  });

  vm.search = function(resetPage, isSave) {
    if (resetPage) {
      vm.params.page = 1;
    };
    vm.params.per_page = vm.params.per_page || vm.perPageSettings[0];
    if (isSave) {
      var searchConditionParams = _.omit(vm.params, "page", "limit");
      locationService.createSearchCondition({search: searchConditionParams, search_condition: SEARCH_CONDITION_TYPE});
    }

    locationService.searchLocation(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.locations.length);
      vm.noRecord = !vm.hasRecord;
      $location.search(vm.params).replace();
    }, function(error) {
    });
  };

  vm.init = function() {
    locationService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
      }

      SELECT_FIELDS.forEach(function(field) {
        $("#" + field).val(vm.params[field]).trigger("change.select2");
      });
      DATETIME_FIELDS.forEach(function(attr) {
        $("[name='" + attr + "']").datepicker("setDate", new Date(vm.params[attr]));
      });
      vm.search(true);
    });
  }

  vm.changePerPage = function() {
    vm.search(false, true);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".location-list-result").offset().top
    }, 1000);
  };

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  };
}
