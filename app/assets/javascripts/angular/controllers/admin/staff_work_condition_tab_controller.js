"use strict";

angular.module("adminApp").controller("StaffWorkConditionTabController", StaffWorkConditionTabController);
StaffWorkConditionTabController.$inject = ["staffListService"];

function StaffWorkConditionTabController(staffListService) {
  var vm = this;
  var STAFF_ID = angular.element("#staff_id").data("infos");
  var LAST_ISSUE_DATE = angular.element("#last-issue-date").data("infos");
  var $issueConfirmationModal = angular.element("#modal-issue-confirmation");
  var $btnDownload = angular.element("#btnDownload");
  vm.params = {};

  vm.showIssueConfirmModal = function(){
    $issueConfirmationModal.modal("show");
  };

  vm.createWorkConditionPDF = function(e){
    e.preventDefault();
    vm.errorExport = null;
    staffListService.validateExportWorkCondition({id: STAFF_ID}).then(function(res) {
      if (res.data.status) {
        $issueConfirmationModal.modal("hide");
        vm.params.id = STAFF_ID;
        $.asyncDownload(e, "staff_work_conditions", vm.params, "pdf");
      } else {
        vm.errorExport = res.data.errors;
      }
    });
  };

  vm.init = function(){
    getLastIssueDate();
  };

  function getLastIssueDate(){
    vm.lastIssueDate = LAST_ISSUE_DATE || I18n.t("admin.staff.work_condition_tab.no_issue");
    if (_.isEmpty(LAST_ISSUE_DATE)) return;
    $btnDownload.removeClass("hidden");
  };
}
