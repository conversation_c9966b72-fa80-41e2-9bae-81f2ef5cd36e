"use strict";

angular.module("adminApp").controller("AdminNotificationOwnersController", AdminNotificationOwnersController);
AdminNotificationOwnersController.$inject = ["$location", "AdminNotificationOwnerService", "checkValidDateFunction", "toaster"];

function AdminNotificationOwnersController($location, AdminNotificationOwnerService, checkValidDateFunction, toaster) {
  var $listResult = $("#notification-list-result");
  var $fromDateInput = $("#sent-date-from-date");
  var $toDateInput = $("#sent-date-to-date");
  var $modalRemoveNotification = $("#modal-remove-notification");

  var vm = this;
  var requestParams = $location.search();
  vm.hasRecord = false;
  vm.params = {};
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.notifications = [];
  vm.total_items = 0;
  var formatDatePicker = {
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true
  };
  $fromDateInput.datepicker(formatDatePicker);
  $toDateInput.datepicker(formatDatePicker);
  vm.params.department_ids = [];
  vm.params.sent_status = [true, false];
  vm.status_send = false;
  vm.status_unsent = false;
  if (!!requestParams.department_ids) {
    vm.params.department_ids = requestParams.department_ids.split(",");
  }
  if (!!requestParams.sent_status) {
    vm.params.sent_status = requestParams.sent_status.split(",");
  }
  if (vm.params.sent_status.includes(true) || vm.params.sent_status.includes("true")) {
    vm.status_send = true;
  }
  if (vm.params.sent_status.includes(false) || vm.params.sent_status.includes("false")) {
    vm.status_unsent = true;
  }
  vm.params.page = requestParams.page || 1;
  vm.params.per_page = requestParams.page || vm.perPageSettings[0];
  vm.params.from_date = requestParams.from_date || "";
  vm.params.to_date = requestParams.to_date || "";

  $modalRemoveNotification.on("hidden.bs.modal", function (e) {
    vm.notificationRemove = undefined;
  });

  vm.init = function() {
    vm.search();
  };

  vm.updateStatusParams = function() {
    var sent_status = [];
    if (vm.status_send) {
      sent_status.push(true);
    }
    if (vm.status_unsent) {
      sent_status.push(false);
    }
    if (!vm.status_unsent && !vm.status_send) {
      sent_status = [true, false];
    }
    vm.params.sent_status = sent_status;
  };

  vm.search = function(isResetPage) {
    if (!!isResetPage) {
      vm.params.page = 1;
    }
    var searchParams = {
      search: vm.params,
      page: vm.params.page,
      per_page: vm.params.per_page
    };
    AdminNotificationOwnerService.searchNotification(searchParams).then(function(res) {
      if (res.status === 200) {
        angular.extend(vm, res.data);
        vm.params.page = res.data.page;
      } else {
        vm.notifications = [];
        vm.total_items = 0;
      }
      if (vm.notifications.length > 0) {
        vm.hasRecord = true;
      } else {
        vm.hasRecord = false;
      }
      vm.copyParams = angular.copy(vm.params);
      vm.copyParams.department_ids = vm.params.department_ids.joins(",");
      vm.copyParams.sent_status = vm.params.sent_status.joins(",");
      $location.search(vm.copyParams).replace();
    });
  };

  vm.removeNotification = function(notificationId) {
    var searchParams = {
      search: vm.params,
      page: vm.params.page,
      per_page: vm.params.per_page
    };
    searchParams.search.department_ids = vm.params.department_ids.split(",");
    searchParams.search.sent_status = vm.vm.params.sent_status.split(",");
    return searchParams;
  };

  vm.removeNotification = function(notificationId) {
    vm.removeNotificationId = notificationId;
    $modalRemoveNotification.modal({backdrop: "static", keyboard: false});
  };

  vm.confirmRemoveNotification = function() {
    if (_.isUndefined(vm.removeNotificationId)) return;
    AdminNotificationOwnerService.deleteNotification(vm.removeNotificationId).then(function mySuccess(res) {
      if (res.data.status) {
        $modalRemoveNotification.modal("hide");
        _.remove(vm.notifications, function(notification) {
          return _.isEqual(notification.id, vm.removeNotificationId);
        });
        toaster.pop("success", "", res.data.message);
        vm.search();
      } else {
        toaster.pop("error", "", res.data.message);
      }
    });
  };

  vm.checkValidDataDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  };

  vm.changePerPage = function() {
    vm.search();
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $listResult.offset().top
    }, 1000);
  };
}
