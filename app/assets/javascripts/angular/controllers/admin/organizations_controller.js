"use strict";

angular.module("adminApp").controller("OrganizationsController", OrganizationsController);
OrganizationsController.$inject = ["organizationService"];

function OrganizationsController(organizationService) {
  var vm = this;
  vm.params = {page: 1, per_page: ANGULAR_ITEMS_PER_PAGE[0]};
  vm.params.id = angular.element("#organization_id").data("infos");

  vm.init = function() {
    vm.refresh();
  }

  vm.refresh = function() {
    organizationService.loadStaffs(vm.params).then(function mySuccess(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.staff_violations.length);
    });
  }

  vm.getDateJoinOrganization = function(staff_id) {
    var staff = _.find(vm.staff_organizations, function(staff) {
      return staff_id === staff.id;
    });
    if(_.isUndefined(staff)) return "";
    return staff.date_join_organization;
  }
}
