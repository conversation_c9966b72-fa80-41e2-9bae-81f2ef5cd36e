"use strict";

angular.module("adminApp")
  .controller("StaffDetailsController", StaffDetailsController);
StaffDetailsController.$inject = ["$location", "$scope", "staffDetailsService", "postalCodeService", "imageService"];

function StaffDetailsController($location, $scope, staffDetailsService, postalCodeService, imageService) {
  var vm = this;
  vm.$scope = $scope;
  var DATA_HOLDER_IDS = ["is_enabled_pic_approve", "is_enabled_op_confirm",
    "is_enabled_send_auth_mail", "status_id",
    "is_enabled_send_modification_request", "staff_email", "specific_activities",
    "staff_id", "is_identity_other", "is_working_other_place", "is_license",
    "nationalities", "prefectures", "registration_histories", "relationships", "banks", "staff_edited",
    "residence_statuses", "residence_permissions", "japan_id", "other_nationality"];
  var ADDRESS_ATTRS = ["postal_code", "prefecture_id", "city", "street_number", "house_number", "building", "tel"];
  var FOREIGN_ATTRS = ["residence_status", "residence_validity", "residence_expiration_date", "residence_permission",
    "residence_permission_validity", "residence_card_name", "other_nationality", "residence_number"];
  var FOREIGN_REMOVE_IMG = ["remove_residence_img_back", "remove_residence_img_front", "remove_certificate_img"];
  var RESIDENCE_DATETIME_FIELDS = ["residence_validity", "residence_expiration_date", "residence_permission_validity"];
  var CHECK_ITEM_FOREIGN_ATTRS = ["is_residence_status", "is_residence_period", "is_permission",
    "is_check_address", "is_request_seal", "japanese_level", "workable_time"];
  var JAPANESE_SOCIAL_ATTRS_FIRST_ID = "#social-attribute-1";
  var FOREIGNER_SOCIAL_ATTRS_FIRST_ID = "#social-attribute-14";

  var $residenceExpDate = angular.element("label[for=staff_residence_expiration_date]");

  vm.hakenTypes = ["equality", "labor"];

  if(checkDateLicense()) {
    toggleValidLicenseClass("addClass");
  }

  DATA_HOLDER_IDS.forEach(function(id) {
    vm[id] = angular.element("#" + id).data("infos");
  });
  RESIDENCE_DATETIME_FIELDS.forEach(function(attr) {
    vm.staff_edited[attr] = formatDate(vm.staff_edited[attr], ORDER_DATEPICKER_FORMAT);
  });
  vm.staff = {staff_account_transfer: {}, staff_employment_history: {}, staff_expectation: {}, staff_check_item: {}};
  vm.staff = angular.copy(vm.staff_edited);
  vm.submitBtn = I18n.t("common.buttons.send");
  vm.initSuccess = true;
  vm.is_working_other_place = vm.is_working_other_place.toString();

  vm.openSendCorrectionRequestModal = function() {
    staffDetailsService.checkSendMailEntry({id: vm.staff_id}).then(function(res) {
      if(!_.isUndefined(res.data.success) && !res.data.success) {
        location.reload();
      } else {
        vm.actionType = "send-mail";
        initModalConfirm();
        angular.element("#comment-confirm-modal").modal("show");
      }
    });
  };

  vm.init = function() {
    setSocialAttributes();
  }

  vm.openApproveModal = function(status) {
    vm.status = status;
    vm.actionType = vm.status == "3" ? "op-approve" : "pic-approve";
    vm.$scope.$watch(vm.status, function() {
      vm.currentStep = "confirm-step";
      $("#admins-edit-staff").submit();
    });
  };

  vm.checkForApproval = function() {
    vm.currentStep = "update-step";
    $("#admins-edit-staff").submit();
    angular.element("#comment-confirm-modal").modal("hide");
  }

  vm.checkForApprovalOrSendMail = function() {
    if(_.includes(vm.actionType, 'approve')) {
      vm.checkForApproval();
    } else if (vm.actionType == 'send-mail') {
      vm.sendMailEntry(vm["staff_id"]);
    }
  }

  function resetModal() {
    vm.modalTitle = "";
    vm.modalEmailAddr = "";
    vm.modalCommentLabel = "";
  }

  function setSocialAttributes() {
    vm.isJapanese = vm.staff.nationality == vm.japan_id;
    vm.nationalityChecked = vm.staff.nationality;
    vm.socialAttributeElementSelected = $("input[name='staff[social_attribute]']:checked");
  }

  vm.rejectStaff = function(staffId) {
    staffDetailsService.rejectStaff({
      id: staffId
    }).then(function(response){
      location.reload();
    });
  }

  vm.sendAuthenticateMail = function(staffId){
    staffDetailsService.sendAuthenticateMail({id: staffId}).then(function(res) {
      location.reload();
    });
  }

  vm.sendMailEntry = function(staffId){
    if (_.isEmpty(vm.entryComment)){
      vm.commentError = true
    }
    else {
      staffDetailsService.sendMailEntry({
        id: staffId,
        comment: vm.entryComment
      }).then(function(res) {
        location.reload();
      });
    }
  }

  $("#admins-edit-staff").on("submit", function(e) {
    e.preventDefault();
    var actionURL;
    if(vm.currentStep == "confirm-step") actionURL = "/entries/check_update_staff/" + vm["staff_id"];
    else actionURL = "/entries/" + vm["staff_id"];

    $.ajax({
      url: actionURL,
      method: 'PUT',
      dataType: 'json',
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        var extraErrorElements = {
          base: ".base-staff-check-item",
          japanese_level: ".japanese-level-staff-check-item",
          working_other_place: ".working-other-place-staff-check-item",
          working_time: ".working-time-staff-check-item",
          is_request_seal: ".is-request-seal-staff-check-item",
          base_staff_expectation: ".base-staff-expectation"
        };
        $.lawsonAjaxStaffEditForm(response, extraErrorElements, true);
      }, complete: function(res) {
        if(res.responseJSON.status && _.isEqual(vm.currentStep, "confirm-step")) {
          $.resetFormError();
          initModalConfirm();
          vm.$scope.$apply(function() {
            angular.element("#comment-confirm-modal").modal("show");
          });
        } else if(!_.isUndefined(res.responseJSON.success) && !res.responseJSON.success) {
          location.reload();
        }
      }
    })
  });

  function initModalConfirm() {
    resetModal();
    if(vm.actionType == 'send-mail') {
      vm.modalTitle = I18n.t("admin.staff.details.modal.modification_request");
      vm.modalCommentLabel = I18n.t("admin.staff.details.modal.comment");
      vm.submitBtn = I18n.t("common.buttons.send");
      vm.modalEmailAddr = vm.staff_email;
    } else {
      vm.modalTitle = I18n.t("admin.staff.details.modal.approval");
      vm.modalCommentLabel = I18n.t("admin.staff.details.modal.evaluation_comment");
      if(vm.actionType == "op-approve") vm.submitBtn = I18n.t("common.buttons.save");
    }

    vm.commentError = false;
    vm.entryComment = "";
  }

  $("#entries-checklist-license-depend").change(function(){
    if(checkDateLicense()) {
      toggleValidLicenseClass("addClass");
    } else {
      toggleValidLicenseClass("removeClass");
    }
  });

  function toggleValidLicenseClass(type){
    $("#check-entries-valid-license-label")[type]('valid-license-label');
    $("#entries-checklist-license-depend")[type]('valid-license-date');
    $("#check-entries-valid-license-date")[type]('valid-license-date');
  }

  function checkDateLicense() {
    var pickedDate = moment($("#entries-checklist-license-depend").val());
    var currentDate = moment(moment().format(FULL_DATE_FORMAT));
    if (currentDate.diff(pickedDate, 'day') > 0)
      return true;
    return false;
  }

  vm.$scope.$watch("vm.staff.staff_account_transfer.bank_id", function() {
    if (vm.staff.staff_account_transfer != undefined &&
        vm.staff.staff_account_transfer.bank_id) {
      vm.getBankBranches(vm.staff.staff_account_transfer.bank_id);
    } else {
      vm.bank_branches = [];
    }
  });

  vm.$scope.$watch("vm.staff.prefecture_id", function() {
    staffDetailsService.getStations({prefecture_id: vm.staff.prefecture_id}).then(function(res) {
      vm.homeStations = res.data.stations;
    });
  });

  vm.getBankBranches = function(bankId){
    staffDetailsService.getBankBranches({bank_id: bankId}).then(function(res) {
      vm.bank_branches = res.data.bank_branches;
    });
  }

  vm.initPostalCode = function(e) {
    $(".open-modal-postal-code").data("modal-active", 0);
    $(e.target).data("modal-active", 1);
    vm.currentPage = 1;

    vm.params = {
      page: 1,
      postal_code: ""
    };

    vm.refresh();
  }

  vm.refresh = function() {
    vm.params.page = vm.currentPage;
    vm.params.postal_code = vm.postalCode;
    postalCodeService.getPostalCode(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.postal_codes.length);
      vm.noRecord = !vm.hasRecord;
      vm.params.limit = res.data.pagination_limit;
    }, function(error) {
    });
  }

  vm.fileChanged = function(event) {
    var previewElm = event.target.getAttribute("target-preview");

    if (!event.target.files[0]) {
      var oldImage = $(previewElm).attr("data-old-image");

      $(previewElm).attr("src", oldImage);
    } else {
      var reader = new FileReader();
      reader.onload = function(evt) {
        $(previewElm).attr("src", evt.target.result);
      }
      reader.readAsDataURL(event.target.files[0]);
    }
  }

  vm.$scope.$watchGroup(["vm.staff.postal_code", "vm.staff.prefecture_id", "vm.staff.city",
    "vm.staff.street_number", "vm.staff.house_number", "vm.staff.building",
    "vm.staff.is_emergency_address_differ_current_address", "vm.staff.tel"], function() {
    if (!vm.staff.is_emergency_address_differ_current_address) {
      ADDRESS_ATTRS.forEach(function(attr) {
        if (_.isNaN(vm.staff[attr])) vm.staff[attr] = "";
        if (_.isUndefined(vm.staff["emergency_" + attr])){
          vm.staff["emergency_" + attr] = angular.copy(vm.staff[attr]);
        }
      });
    }
  });

  vm.$scope.$watch("vm.staff.is_emergency_address_differ_current_address", function() {
    resetAddressEmergency();
  });

  function resetAddressEmergency() {
    if (!vm.staff.is_emergency_address_differ_current_address) {
      ADDRESS_ATTRS.forEach(function(attr) {
        if (_.isUndefined(vm.staff["emergency_" + attr])){
          vm.staff["emergency_" + attr] = angular.copy(vm.staff[attr]);
        }
      });
    } else {
      ADDRESS_ATTRS.forEach(function(attr) {
        vm.staff["emergency_" + attr] = angular.copy(vm.staff_edited["emergency_" + attr]);
      });
    }
  }

  vm.changeNationality = function() {
    vm.isJapanese = vm.staff.nationality == vm.japan_id;
    if (vm.isJapanese) {
      if ($('.nav-tabs a[href="#foreign"]').parents("li").hasClass("active"))
        $('.nav-tabs a[href="#basic_info"]').tab('show');
      _.forEach(FOREIGN_ATTRS, function(attr) {
        vm.staff[attr] = "";
      });
      _.forEach(FOREIGN_REMOVE_IMG, function(imgName) {
        $("[name$='staff[" + imgName + "]']").val("0");
      });
      _.forEach(CHECK_ITEM_FOREIGN_ATTRS, function(attr) {
        vm.staff.staff_check_item[attr] = "";
      });
      vm.is_working_other_place = "false";
      vm.staff.residence_permission = "not_selected";
    } else {
      _.forEach(FOREIGN_ATTRS, function(attr) {
        vm.staff[attr] = vm.staff_edited[attr];
      });
      _.forEach(FOREIGN_REMOVE_IMG, function(imgName) {
        $("[name$='staff[" + imgName + "]']").val("0");
      });
      _.forEach(CHECK_ITEM_FOREIGN_ATTRS, function(attr) {
        vm.staff.staff_check_item[attr] = angular.copy(vm.staff_edited.staff_check_item[attr]);
      });
      vm.is_working_other_place = angular.element("#is_working_other_place").data("infos").toString();
    }
    setCheckedForSocialAttributes(vm.isJapanese);
  }

  vm.getBankCode = function(bank_id) {
    var bank = _.find(vm.banks, function(bank) {return bank.id == bank_id});
    if (_.isUndefined(bank)) return "";
    return bank.code;
  }

  vm.getBankBranchCode = function(branch_id) {
    var branch = _.find(vm.bank_branches, function(branch) {return branch.id == branch_id});
    if (_.isUndefined(branch)) return "";
    return branch.code;
  }

  vm.showTabForeign = function() {
    return (!_.isNaN(vm.staff.nationality) && vm.staff.nationality != vm.japan_id);
  }

  vm.isRequiredCertificateImg = function() {
    return _.includes(vm.specific_activities, vm.staff.residence_status);
  }

  function formatDate(date, formatType) {
    if (_.isEmpty(date)) return "";
    return moment.parseZone(date).format(formatType);
  };

  function setCheckedForSocialAttributes(isJapanese) {
    if (vm.staff.nationality == vm.nationalityChecked) {
      vm.socialAttributeElementSelected.prop("checked", true);
    } else {
      if (isJapanese) {
        $(JAPANESE_SOCIAL_ATTRS_FIRST_ID).prop("checked", true);
      } else {
        $(FOREIGNER_SOCIAL_ATTRS_FIRST_ID).prop("checked", true);
      }
    }
  }

  vm.getImage = function(id, modelName, field) {
    var params = {model_name: modelName, field: field}
    imageService.getImage(id, params).then(function(res) {
      $scope.$applyAsync(function() {
        vm.modal_image_url = res.data.src;
      });
    })
    $("#modal-image").modal("show");
  };

  angular.element(document).ready(function () {
    vm.checkResidenseRequired();
  });

  vm.checkResidenseRequired = function(){
    if (vm.staff.residence_status != NOT_REQUIRED_RESIDENSE_STATUS_ID){
      toggleResidenseRequired($residenceExpDate, true);
    }else{
      toggleResidenseRequired($residenceExpDate, false);
    }
  };

  function toggleResidenseRequired(el, isRequired){
    if (isRequired){
      el.text(I18n.t("activerecord.attributes.staff.residence_expiration_date").concat(I18n.t("common.label.required")));
      el.addClass('required');
    }else {
      el.text(I18n.t("activerecord.attributes.staff.residence_expiration_date"));
      el.removeClass('required');
    };
  };

  vm.hakenTypeText = function(type) {
    return I18n.t("admin.staff.haken_type." + type);
  };
}
