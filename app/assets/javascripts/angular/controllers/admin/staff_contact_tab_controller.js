"use strict";

angular.module("adminApp").controller("StaffContactTabController", StaffContactTabController);

StaffContactTabController.$inject = ["staffDetailsService", "$scope", "orderCaseService"];

function StaffContactTabController(staffDetailsService, $scope, orderCaseService) {
  var vm = this;
  var DATE_FIELDS = ["contact_date"];
  var TIME_FIELDS = ["contact_time"];
  var TRAINING_DATE_PREFIX = ["start", "end"];
  var $searchOrderFormModal = angular.element("#modal-search-form");
  vm.staff_contacts = angular.element("#contact-histories").data("infos");
  vm.staff_trainings = angular.element("#staff-trainings").data("infos");
  vm.staff_careers = angular.element("#staff-careers").data("infos");
  vm.wayToContacts = angular.element("#way-to-contacts-options").data("infos");
  vm.contactTypes = angular.element("#contact-type-options").data("infos");
  vm.staff_stable_employments = angular.element("#staff-stable-employments").data("infos")
  vm.staff_complaints = angular.element("#staff-complaints").data("infos");
  vm.staff_mail_histories = angular.element("#staff-mail-histories").data("infos");
  vm.staff_id = angular.element("#staff_id").data("infos");
  vm.inputOrderId = "";
  vm.currentMailHistoriesPage = 1;
  vm.showLoadMoreMailHistoriesButton = true;
  var datepicker_field = {
    contact: ["#contact-date-picker"],
    training: ["#training-start-date-picker", "#training-end-date-picker"],
    career: ["#career-start-date-picker", "#career-end-date-picker"],
    stable_employment: ["#stable-employment-date-picker"],
    complaint: ["#complaint-date-picker"]
  }
  var datepicker_match = {
    contact: {contact_date: "#contact-date-picker"},
    training: {start_date_only: "#training-start-date-picker", end_date_only: "#training-end-date-picker"},
    career: {start_date_only: "#career-start-date-picker", end_date_only: "#career-end-date-picker"},
    stableEmployment: {employment_date: "#stable-employment-date-picker"},
    complaint: {complaint_date: "#complaint-date-picker"}
  }

  vm.contactNew = {
    id: null,
    contact_date: "",
    contact_time: "",
    way_to_contact: "",
    contact_type: "",
    contact_pic_name: "",
    contact_content: "",
    handle_content: ""
  };
  vm.trainingNew = {
    id: null,
    start_date: "",
    start_date_only: "",
    start_time_only: "",
    end_date: "",
    end_date_only: "",
    end_time_only: "",
    training_content: ""
  };
  vm.careerNew = {
    id: null,
    start_date: "",
    start_date_only: "",
    start_time_only: "",
    end_date: "",
    end_date_only: "",
    end_time_only: "",
    career_content: ""
  };
  vm.stableEmploymentNew = {
    id: null,
    employment_date_only: "",
    employment_time_only: "",
    employment_content: ""
  };
  vm.complaintNew = {
    id: null,
    complaint_date: null,
    order_id: null,
    order_case_id: null,
    corporation_id: null,
    location_id: null,
    complaint_content: null,
    handle_content: null,
    staff_id: vm.staff_id
  };

  var searchComplaintTemplate = {
    search_by_choose_date: true,
    from_start_date: null,
    to_start_date: null,
    corporation_id: null,
    location_id: null,
    order_id: null,
    page: 0,
    per_page: 10
  };

  vm.$scope = $scope;

  vm.$scope.$watch("vm.searchOrder.corporation_id", function() {
    if (vm.searchOrder && vm.searchOrder.corporation_id) {
      vm.searchOrder.location_id = null;
      $("#select2-locations").val("").trigger("change.select2");
    }
  });

  vm.toDelete = {};
  vm.isEditing = false;
  vm.isDisabledBtn = false;

  function formatTimeTraining() {
    TRAINING_DATE_PREFIX.forEach(function(attr) {
      _.forEach(vm.staff_trainings, function(training) {
        training[attr +"_date_only"] = moment(training[attr +"_date"]).format(FULL_DATE_FORMAT);
        training[attr +"_time_only"] = moment(training[attr +"_date"]).format(TIME_PICKER_FORMAT);
      });

      _.forEach(vm.staff_careers, function(career) {
        career[attr +"_date_only"] = moment(career[attr +"_date"]).format(FULL_DATE_FORMAT);
        career[attr +"_time_only"] = moment(career[attr +"_date"]).format(TIME_PICKER_FORMAT);
      });
    });
  }

  function formatTime(time) {
    if(_.isUndefined(time)) return;
    var hour = time.split(":")[0];
    var minute = time.split(":")[1];
    var newTime = new Date();
    newTime.setHours(hour);
    newTime.setMinutes(minute);
    return moment(newTime).parseZone(time).format(TIME_PICKER_FORMAT);
  }

  function setTrainingDateTime(obj) {
    TRAINING_DATE_PREFIX.forEach(function(attr) {
      var time = moment(obj[attr +"_date_only"]).toDate();
      var hour = obj[attr +"_time_only"].split(":")[0];
      var minute = obj[attr +"_time_only"].split(":")[1];
      time.setHours(hour);
      time.setMinutes(minute);
      obj[attr +"_date"] = moment(time).format();
    });
  }

  vm.hasGroupError = function(model, arr) {
    var isErr = false;
    if (!vm[model + "Errors"]) return;
    arr.forEach(function(attr) {
      if (!!vm[model + "Errors"][attr]){
        isErr = true;
      }
    });
    return isErr ? 'has-error' : '';
  };

  vm.hasError = function(model, field) {
    if (!vm[_.camelCase(model) + "Errors"]) return;
    return !!vm[_.camelCase(model) + "Errors"][field] ? 'has-error' : '';
  };

  vm.finish = function() {
    _.forEach(vm.staff_contacts, function(field) {
      DATE_FIELDS.forEach(function(attr) {
        field[attr] = field[attr].replace(/-/g,"/");
      });
      TIME_FIELDS.forEach(function(attr) {
        if (moment(field[attr])._isValid){
          field[attr] = moment.parseZone(field[attr]).format(TIME_PICKER_FORMAT);
        } else {
          field[attr] = formatTime(field[attr]);
        };
      })
    })
  };

  vm.confirmDelete = function(obj) {
    obj._destroy = true;
    $("#modal-delete-contact-tab").modal("hide");
  };

  function resetDateFields(type) {
    _.forEach(datepicker_field[type], function(element) {
      $(element).datepicker("setDate", null);
    })
  }

  function setTriggerDatepicker(type) {
    _.forEach(datepicker_match[type], function(val, key) {
      $(val).datepicker("setDate", new Date(vm[type + "Obj"][key])).trigger("change");
    });
  }

  vm.objDetail = function(type, obj, index) {
    vm[type +"Obj"] = angular.copy(obj);
    vm.isEditing = true;
    vm.currentIndex = index;
    vm[type +"Errors"] = [];
    $("#modal-form-"+ type).modal("show");
    setTriggerDatepicker(type);
  };

  vm.rmObj = function(type, obj) {
    $("#modal-delete-contact-tab").modal("show");
    vm.deleting = 'staff_' + type;
    vm.toDelete = obj;
  };

  vm.addObj =function(obj) {
    vm[_.camelCase(obj) +"Errors"] = [];
    $("#modal-form-"+ obj.replace("_", "-")).modal("show");
    vm.isEditing = false;
    vm[_.camelCase(obj) +"Obj"] = angular.copy(vm[_.camelCase(obj) +"New"]);
    resetDateFields(obj);
  };

  vm.init = function() {
    formatTimeTraining();
    formatTimeStableEmployment();
    formatTimeComplaint();
  };

  vm.saveContact = function() {
    vm.isDisabledBtn = true;
    staffDetailsService.validateContact({staff_contact_history: vm.contactObj}).then(function(res) {
      saveObj("contact", res);
      vm.isDisabledBtn = false;
    });
  };

  vm.saveTraining = function() {
    vm.isDisabledBtn = true;
    setTrainingDateTime(vm.trainingObj);
    staffDetailsService.validateStaffTraining({staff_training: vm.trainingObj}).then(function(res) {
      saveObj("training", res);
      vm.isDisabledBtn = false;
    });
  };

  vm.saveCareer = function() {
    vm.isDisabledBtn = true;
    setTrainingDateTime(vm.careerObj);
    staffDetailsService.validateStaffCareer({staff_career: vm.careerObj}).then(function(res) {
      saveObj("career", res);
      vm.isDisabledBtn = false;
    });
  };

  vm.saveComplaint = function() {
    vm.isDisabledBtn = true;
    staffDetailsService.validateStaffComplaint({staff_complaint: vm.complaintObj}).then(function(res) {
      saveObj("complaint", res);
      vm.isDisabledBtn = false;
    });
  };

  function saveObj(obj, res) {
    if (res.data.status){
      if (!vm.isEditing) {
        vm["staff_"+ obj +"s"].push(angular.copy(vm[_.camelCase(obj) +"Obj"]));
      } else{
        vm["staff_" + obj + "s"].splice(vm.currentIndex, 1, angular.copy(vm[_.camelCase(obj) + "Obj"]));
      }
      $("#modal-form-"+ obj.replace("_", "-")).modal("hide");
      vm[_.camelCase(obj) +"Obj"] = {};
      vm[_.camelCase(obj) +"Errors"] = [];
    } else {
      vm[_.camelCase(obj) +"Errors"] = res.data.errors;
    }
  }

  vm.saveStableEmployment = function() {
    vm.isDisabledBtn = true;
    setEmploymentDate();

    staffDetailsService.validateStraffStableEmployment({staff_stable_employment: vm.stableEmploymentObj}).then(function(res) {
      saveObj("stable_employment", res);
      vm.isDisabledBtn = false;
    });
  };

  vm.stableEmploymentDetail = function(stable_employment, index) {
    vm.stableEmploymentObj = angular.copy(stable_employment);
    vm.isEditing = true;
    vm.currentIndex = index;
    vm.stableEmploymentErrors = [];
    $("#modal-form-stable-employment").modal("show");
    setTriggerDatepicker("stableEmployment");
  };

  vm.openSearchOrder = function() {
    $searchOrderFormModal.modal("show");
    vm.searchOrder = angular.copy(searchComplaintTemplate);
    $(".staff-complaint-select").val("").trigger("change.select2");
    vm.order_cases = [];
    vm.isSearched = false;
    vm.choseOrder = null;
  };

  vm.searchOrderCase = function() {
    vm.searchOrder.order_id = vm.searchOrder.order_id ? vm.searchOrder.order_id.toString() : "";
    orderCaseService.getOrderCases({search: vm.searchOrder}).then(function(res) {
      vm.isSearched = true;
      vm.order_cases = res.data.order_cases;
      vm.searchOrder.total_items = res.data.total_items;
    });
  };

  vm.markAsChose = function(orderCase) {
    vm.choseOrder = angular.copy(orderCase);
  };

  vm.chooseOrder = function() {
    var orderCase = angular.copy(vm.choseOrder);
    vm.complaintObj.order_id = orderCase.id;
    vm.complaintObj.corporation_full_name = orderCase.corporation_full_name;
    vm.complaintObj.location_name = orderCase.location_name;
    vm.complaintObj.location_id = orderCase.location_id;
    vm.complaintObj.corporation_id = orderCase.corporation_id;
    $searchOrderFormModal.modal("hide");
  };

  vm.openMailHistoryDetail = function(id) {
    $("#modal-detail-mail-history").modal("show");
    staffDetailsService.mailHistoryDetail(vm.staff_id, {mail_history_id: id}).then(function(res) {
      if (!!res.data) {
        appendDataMailHistory(res.data.staffMailHistory);
      }
    });
  }

  vm.loadMoreMailHistories = function() {
    var nextPage = vm.currentMailHistoriesPage + 1;
    staffDetailsService.moreMailHistories(vm.staff_id, {page: nextPage}).then(function(res) {
      if (!!res.data) {
        if (res.data.length > 0) {
          vm.staff_mail_histories = vm.staff_mail_histories.concat(res.data);
          vm.currentMailHistoriesPage = nextPage;
        } else {
          vm.showLoadMoreMailHistoriesButton = false;
        }
      }
    });
  }

  function appendDataMailHistory(staffMailHistory) {
    var content = I18n.t("admin.staff.contact.send_time") + ": " + staffMailHistory.send_datetime_format + "</br>" +
      I18n.t("admin.staff.contact.mail_staff") + ": " + staffMailHistory.email_target + "</br>" +
      I18n.t("admin.staff.contact.subject") + ": " + staffMailHistory.mail_subject + "</br>" +
      I18n.t("admin.staff.contact.content") + ": " + staffMailHistory.mail_content + "</br>";
    $("#modal-detail-mail-history .message-content").html(content);
  }

  function formatTimeStableEmployment() {
    _.forEach(vm.staff_stable_employments, function(stable_employment) {
      stable_employment.employment_date_only = moment(stable_employment.employment_date).format(FULL_DATE_FORMAT);
      stable_employment.employment_time_only = moment(stable_employment.employment_date).format(TIME_PICKER_FORMAT);
    })
  }

  function setEmploymentDate() {
    var time = moment(vm.stableEmploymentObj["employment_date_only"]).toDate();
    var hour = vm.stableEmploymentObj["employment_time_only"].split(":")[0];
    var minute = vm.stableEmploymentObj["employment_time_only"].split(":")[1];
    time.setHours(hour);
    time.setMinutes(minute);
    vm.stableEmploymentObj["employment_date"] = moment(time).format();
  }

  function formatTimeComplaint() {
    _.forEach(vm.staff_complaints, function(complaint) {
      complaint.complaint_date = moment(complaint.complaint_date).format(FULL_DATE_FORMAT);
    });
  }

  vm.checkInputOrderId = function(){
    var numberOfOrderId = parseInt(vm.inputOrderId);
    vm.searchOrder.order_id = vm.inputOrderId = numberOfOrderId;
  };
}
