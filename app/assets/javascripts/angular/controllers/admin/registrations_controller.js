"use strict";

angular.module("adminApp")
  .controller("RegistrationsController", RegistrationsController);
RegistrationsController.$inject = ["$location", "registrationListService", "toaster", "checkValidDateFunction"];

function RegistrationsController($location, registrationListService, toaster, checkValidDateFunction) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  vm.currentPage = 1;
  vm.staffSelect = {};
  vm.params = $location.search();
  vm.perPageSettings = [10, 15, 30, 50];
  var SEARCH_CONDITION_TYPE = "admin_staff_search_conditions";
  var REGISTRATION = "registration";
  var STATUS_IDS = {
    "registered": "registered",
    "recruited": "recruited",
    "entry": "entry",
    "op_confirm": "op_confirm",
    "rejected": "rejected"
  };
  var DEVICE_IDS = {
    "web": "web",
    "app": "app",
  };
  var CSV_TYPE = "csv";
  vm.verify_password_error = false;
  vm.authenticated = false;

  vm.init = function() {
    registrationListService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE, search_type: REGISTRATION})
      .then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
        var needJoinToArray = ["current_department_id"];
        _.forEach(needJoinToArray, function(attr) {
          if(!vm.params[attr]) vm.params[attr] = "";
          vm.params[attr] = vm.params[attr].split(",");
        });
        if (!!vm.params.signup_device && vm.params.signup_device != "") {
          var dataSignupDevice = vm.params.signup_device.toString().split(",");
          _.forEach(dataSignupDevice, function(device) {
            vm[device] = true;
          });
        }
        if (!!vm.params.has_applied && vm.params.has_applied != "") {
          var dataHasApplied = vm.params.has_applied.toString().split(",");
          vm.has_apply_job = dataHasApplied.includes("true");
          vm.no_apply_job = dataHasApplied.includes("false");
        }
        if (!!vm.params.has_experience  && vm.params.has_experience != "") {
          var dataHasExperience = vm.params.has_experience.toString().split(",");
          vm.has_exp = dataHasExperience.includes("true");
          vm.no_exp = dataHasExperience.includes("false");
        }
      };
      vm.refresh();
    });
  };

  vm.refresh = function(resetPage) {
    if (_.isEmpty(vm.params)) {
      vm.registered = true;
      vm.params.status_id = STATUS_IDS["registered"];
      vm.params.per_page = 10;
    } else {
      _.forEach(vm.params.status_id.split(","), function(status) {
        vm[status] = true;
      });
    }
    if (resetPage) {
      vm.params.page = 1;
    }
    vm.selectAllStaff = false;
    var search_params = formatParamsBeforeSearch(angular.copy(vm.params));
    registrationListService.getStaff({search: search_params, staff_type: vm.staff_type}).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.staffs.length);
      vm.noRecord = !vm.hasRecord;
      $location.search(search_params).replace();
    }, function(error) {
    });
  };

  vm.search = function(resetPage) {
    var search_condition_params = formatParamsBeforeSearch(angular.copy(_.omit(vm.params, "page", "limit")));
    registrationListService.createSearchCondition({search: search_condition_params, search_condition: SEARCH_CONDITION_TYPE, search_type: REGISTRATION});
    vm.refresh(resetPage);
  };

  function formatParamsBeforeSearch(params) {
    params.current_department_id = (params.current_department_id || []).join(",");
    return params;
  };

  vm.changePerPage = function() {
    vm.search();
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".staff-search-result").offset().top
    }, 1000);
  };

  vm.changeStatusId = function() {
    var validStatus = [];
    vm.status = STATUS_IDS;
    _.forEach(vm.status, function(_, statusID) {
      if (vm[statusID]) {
        validStatus.push(vm.status[statusID]);
      }
    });
    vm.params.status_id = validStatus.toString();
  }

  vm.changeSignUpDevices = function() {
    var validDevice = [];
    _.forEach(DEVICE_IDS, function(_, Device) {
      if (vm[Device]) {
        validDevice.push(DEVICE_IDS[Device]);
      }
    });
    vm.params.signup_device = validDevice.toString();
  }

  vm.changeHasAppliedJob = function() {
    var validSearchConditions = [];
    if (vm.has_apply_job) {
      validSearchConditions.push(true);
    }
    if (vm.no_apply_job) {
      validSearchConditions.push(false);
    }
    vm.params.has_applied = validSearchConditions.toString();
  }

  vm.changeHasExp = function() {
    var validSearchConditions = [];
    if (vm.has_exp) {
      validSearchConditions.push(true);
    }
    if (vm.no_exp) {
      validSearchConditions.push(false);
    }
    vm.params.has_experience = validSearchConditions.toString();
  }

  vm.displayStatusId = function(statusID) {
    return I18n.t("admin.registration.main_search_form.status_ids")[statusID]
  };

  vm.displaySignupDevice = function(signupDevice) {
    return I18n.t("admin.registration.main_search_form.signup_devices")[signupDevice]
  };

  vm.displayRegistrationType = function(registrationType){
    return I18n.t("admin.registration.main_search_form.registration_types")[registrationType]
  }

  vm.displayRegistrationAnswerAnswer4 = function(answer) {
    if (!answer) {return;}
    return I18n.t("admin.registration.main_search_form.registration_answer_answer_4")["option_" + answer]
  };

  vm.displayRegistrationAnswerAnswer5 = function(answer) {
    if (!answer) {return;}
    return I18n.t("admin.registration.main_search_form.registration_answer_answer_5")["option_" + answer]
  };

  vm.clearSearchCondition = function() {
    vm.params = angular.copy({
      per_page: vm.perPageSettings[0],
      status_id: ""
    });
    var statuses = vm.status || {};
    Object.keys(statuses).forEach(function(status) {
      vm[status] = false;
    });
    _.forEach(DEVICE_IDS, function(device) {
      vm[device] = false;
    });
     vm.has_apply_job = false;
    vm.no_apply_job = false;
    vm.has_exp = false;
    vm.no_exp = false;
  }

  vm.openDestroyStaffModal = function(id) {
    angular.element(".btn-delete-staff").attr("staff-id", id);
    if (id) {
      vm.confirmMessage = I18n.t("admin.staff.operate_item.confirm_manual_delete");
      angular.element(".delete-staff-modal").modal("show");
    }
  };

  vm.deleteStaff = function() {
    $(".disable-submit-btn").prop("disabled", true);
    var destroyParams = [];
    var deletedStaffId = angular.element(".btn-delete-staff").attr("staff-id");
    if (deletedStaffId) {
      destroyParams.push(deletedStaffId);
    }
    registrationListService.deleteStaff({staff_ids: destroyParams.join(",")}).then(function(res) {
      angular.element(".delete-staff-modal").modal("hide");
      vm.refresh(true);
      if(res.data.status == "success") {
        toaster.pop("success", "", I18n.t("admin.staff.action_status.delete_success"));
      } else {
        toaster.pop("error", "", I18n.t("admin.staff.action_status.delete_registration_false"));
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  };

  vm.verifyAdminPassword = function() {
    $("#verify-admin-password-modal").modal("show");
  }

  vm.viewStaffInfos = function(e) {
    var submitParams = {
      password: vm.verify_admin_password
    }
    $("#spinner").removeClass("ng-hide");
    registrationListService.checkAdminPassword(submitParams).then(function(res) {
      var res = res.data;
      if (res.status) {
        $("#verify-admin-password-modal").modal("hide");
        vm.verify_admin_password = "";
        vm.exportRegistrations(e);
      } else {
        vm.verify_password_error = true;
        vm.verify_password_error_class = "has-error";
        vm.authenticated = true;
      }
      $("#spinner").addClass("ng-hide");
    });
  }

  vm.exportRegistrations = function(e) {
    var searchParams = formatParamsBeforeSearch(angular.copy(vm.params));
    $.asyncDownload(e, "registrations", JSON.stringify(searchParams), CSV_TYPE);
  }

  vm.handleInputPassword = function(e) {
    var keyCode = e.keyCode || e.which;
    if (keyCode === 13) {
      vm.viewStaffInfos(e);
      return false;
    }
  }

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  }
}
