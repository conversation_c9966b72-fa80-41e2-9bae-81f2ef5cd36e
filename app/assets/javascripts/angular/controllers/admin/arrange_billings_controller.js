"use strict";

angular.module("adminApp").controller("ArrangeBillingsController", ArrangeBillingsController);
ArrangeBillingsController.$inject = ["$scope", "arrangeBillingService", "toaster"];

function ArrangeBillingsController($scope, arrangeBillingService, toaster) {
  var vm = this;
  vm.$scope = $scope;

  var ODD_MONTHS = [1, 3, 5, 7, 8, 10, 12];
  var EVEN_MONTHS = [4, 6, 9 , 11];
  var LEAP_MONTHS = [2];
  var LAST_DAYS_TXT = "月末";
  var BILLING_CLOSED_GROUP_IDS = {
    "5": 1128,
    "10": 1129,
    "15": 1130,
    "20": 1131,
    "25": 1132,
    "月末": 1133
  };
  vm.toasterTimeout = 6200;
  var currentYear = moment().year();
  var $statusElm = $("#admin-export");
  $scope.years = _.range(2018, currentYear + 1);
  $scope.months = _.range(1, 13);
  $scope.registrations = [
    {key: 9, text: I18n.t("admin.arrange_billings.registration_history.history_9")}
  ]
  vm.params = {
    year: currentYear,
    month: moment().month() + 1,
    day: 1,
    corporation_type: "is_lawson",
    corporation_ids: []
  };
  vm.registrationHistory = 9;
  vm.noRecord = vm.hasRecord = false;
  vm.days = [];
  vm.billingType = BILLING_CLOSED_GROUP_IDS[vm.params.day.toString()];

  vm.init = function() {
    vm.getDaysOfMonth(vm.params.year, vm.params.month);
    vm.searchMonthBilling();
  };

  vm.downloadBilling = function(e) {
    vm.isExportingBillings = true;
    var daySelected = getDays();
    var billingDate = [vm.params.year, vm.params.month, daySelected].join("-");
    var isLawson = vm.params.corporation_type == "is_lawson";
    var paramsData = {
      billing_date: billingDate,
      is_lawson: isLawson,
      corporation_ids: vm.params.corporation_ids.join(",")
    };
    arrangeBillingService.searchArrangeBilling(paramsData).then(function(res) {
      var data = res.data;
      if (data.status) {
        $.asyncDownload(e, "arrange_billings", JSON.stringify(paramsData), "xlsx");
      } else {
        toaster.pop("error", "", data.message);
        vm.isExportingBillings = false;
      };
    });
  };

  $statusElm.on("hiddenProgress", function() {
    $scope.$apply(function() {
      if (vm.isExportingBillings) vm.isExportingBillings = false;
    })
  });

  vm.downloadBillingRegist = function(e) {
    var selectedItems = _.filter(vm.month_billings, {select: true});
    var isLawson = vm.params.corporation_type == "is_lawson";
    var paramsData = {
      corporation_ids: _.map(selectedItems, "corporation_id"),
      billing_date: selectedItems[0]["billing_date"],
      registration_history: vm.registrationHistory,
      is_lawson: isLawson
    };
    arrangeBillingService.searchArrangeBillingStaff(paramsData).then(function(res) {
      if (res.data.status) {
        $.asyncDownload(e, "arrange_billings", JSON.stringify(paramsData), "xlsx");
      } else {
        toaster.pop("error", "", I18n.t("admin.arrange_billings.no_registration_data"));
      };
    });
  };

  vm.confirmBillings = function() {
    var selectedItems = _.filter(vm.month_billings, {select: true});
    var isLawson = vm.params.corporation_type == "is_lawson";
    var paramsData = {
      corporation_ids: _.map(selectedItems, "corporation_id"),
      billing_date: selectedItems[0]["billing_date"],
      is_lawson: isLawson
    };
    arrangeBillingService.confirmBillings(paramsData).then(function(res) {
      if (res.data.status) {
        toaster.pop("success", "", I18n.t("admin.arrange_billings.confirm_billing.billing_confirmed"));
      } else {
        toaster.pop("error", "", I18n.t("admin.arrange_billings.confirm_billing.billing_not_confirmed"));
      };
    });
  };

  vm.checkAll = function() {
     _.forEach(vm.month_billings, function(monthBilling) {
       monthBilling.select = vm.selectAll;
     });
     vm.checkSelected(true);
   };

  vm.checkSelected = function(isChecked) {
    var selectedItems = _.find(vm.month_billings, {select: true});
    vm.isSelected = selectedItems ? true : false;
    if (vm.selectAll && !isChecked) {
      vm.selectAll = false;
    };
  };

  vm.cancelBilling = function() {
    var selectedItems = _.filter(vm.month_billings, {select: true});
    var monthBillingIds = _.map(selectedItems, "id");
    var isLawson = vm.params.corporation_type == "is_lawson";
    var cancelParams = {
      corporation_ids: _.map(selectedItems, "corporation_id"),
      billing_date: selectedItems[0]["billing_date"],
      month_billing_ids: monthBillingIds,
      is_lawson: isLawson
    }
    arrangeBillingService.cancelBilling(cancelParams).then(function(res) {
      if (res.data.status) {
        _.remove(vm.month_billings, function(monthBilling) {
          return _.includes(monthBillingIds, monthBilling.id)
        });
        vm.checkSelected(true);
        if (vm.month_billings.length === 0) {
          vm.selectAll = false;
        };
        toaster.pop("success", "", I18n.t("admin.arrange_billings.cancel_success"));
      };
    });
  };

  vm.isInvalidMonth = function() {
    var daySelected = getDays();
    var currentDateSearch = [vm.params.year, vm.params.month, daySelected].join("-");
    if (vm.params.corporation_type == "is_lawson") {
      var date = moment(currentDateSearch).add(1, "month").format("YYYY-MM-DD");
    } else {
      var date = moment(currentDateSearch).add(1, "day").format("YYYY-MM-DD");
    }
    return moment().isBefore(date); 
  };

  vm.searchMonthBilling = function() {
    var daySelected = getDays();
    var billingDate = [vm.params.year, vm.params.month, daySelected].join("-");
    var isLawson = vm.params.corporation_type == "is_lawson";
    var paramData = {
      billing_date: billingDate,
      is_lawson: isLawson,
      corporation_ids: vm.params.corporation_ids.join(",")
    };
    arrangeBillingService.getMonthBillings(paramData).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.month_billings.length);
      vm.noRecord = !vm.hasRecord;
      vm.selectAll = false;
      vm.isSelected = false;
      _.forEach(vm.month_billings, function(monthBilling) {
        monthBilling.select = false;
      });
    });
  };

  vm.getDaysOfMonth = function(year, month) {
    var lastDaysTxt = [LAST_DAYS_TXT];
    vm.lastDays = 28;
    var isLeapYear = new Date(year, 1, 29).getDate() === 29;
    if (_.includes(LEAP_MONTHS, month) && isLeapYear) {
      vm.lastDays = 29;
    }
    if (_.includes(EVEN_MONTHS, month)) {
      vm.lastDays = 30;
    }
    if (_.includes(ODD_MONTHS, month)) {
      vm.lastDays = 31;
    }
    vm.days = _.range(5, 26, 5).concat(lastDaysTxt);
  };

  vm.$scope.$watch("vm.params.corporation_type", function() {
    if (vm.params.corporation_type == "is_lawson") {
      vm.params.day = 1;
    } else {
      vm.params.day = 5;
    }
    vm.resetCorporationsList();
  });

  function getDays() {
    var temp = vm.params.day == LAST_DAYS_TXT ? vm.lastDays : vm.params.day;
    return temp;
  }

  vm.resetCorporationsList = function() {
    setTimeout(function() {
      angular.element("select.select2, select.select2-multiple").trigger("change");
    }, 10);
    vm.params.corporation_ids = [];
    vm.billingType = BILLING_CLOSED_GROUP_IDS[vm.params.day.toString()];
  };
}
