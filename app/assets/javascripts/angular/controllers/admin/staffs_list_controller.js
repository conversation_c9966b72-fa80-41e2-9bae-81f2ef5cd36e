"use strict";

angular.module("adminApp")
  .controller("StaffsListController", StaffsListController);
StaffsListController.$inject = ["$location", "staffListService", "adminRegistrationCodeService", "toaster"];

function StaffsListController($location, staffListService, adminRegistrationCodeService, toaster) {
  var vm = this;
  var XLSX_TYPE = "xlsx";
  vm.contractSendMailStatus = {
    unsent: I18n.t("common.mail_status.unsent"),
    sent: I18n.t("common.mail_status.sent")
  };

  vm.selectSendMail = false;

  var SEARCH_CONDITION_TYPE = "admin_staff_search_conditions";
  var DATETIME_FIELDS = ["contract_start_date", "contract_end_date", "from_retirement_day", "to_retirement_day",
    "from_violation_day", "to_violation_day", "from_debut_date", "to_debut_date"];
  var SELECT_FIELDS = ["from_evaluation", "to_evaluation"]
  var STAFF = "staff";
  var DATA_HOLDER_IDS = ["evalutations", "contract_statuses"];
  vm.hasRecord = false;
  vm.perPageSettings = [10, 15, 30, 50];
  vm.params = {};
  vm.selectAllStaff = false;
  vm.verify_password_error = false;
  vm.authenticated = false;
  vm.isSubmitting = false;
  var NUMBER_OF_MONTHS = [1, 6];
  vm.numberOfMonths = [
    { 
      name: "2".concat(I18n.t("admin.staff.modal_create_contract.month")),
      value: 1
    },
    { 
      name: "6".concat(I18n.t("admin.staff.modal_create_contract.month")),
      value: 6
    }
  ];
  vm.selectedMonth = vm.numberOfMonths[0].value;
  DATA_HOLDER_IDS.forEach(function(id) {
    vm[id] = angular.element("#" + id).data("infos");
  });

  vm.openStaffOtpPopup = function(){
    vm.contactAddress = "";
    vm.currentOtp = "";
    $("#staff-otp-popup").modal("show");
  }

  vm.getOtp = function(){
    var params = {"contact_address": vm.contactAddress};
    vm.currentOtp = ""
    staffListService.getStaffOtp(params).then(function(res) {
      if (res.data) {vm.currentOtp = res.data.otp;}
    });
  }

  vm.init = function() {
    vm.occupation = angular.element(".seach-details__occupations").data("occupations");
    vm.staff_status = angular.element(".seach-details__staff_statuses").data("statuses");
    vm.contract_status = angular.element(".seach-details__contract_statuses").data("statuses");
    vm.level_up_training_format = {level_up_training_1: "1", level_up_training_0: "0"};

    staffListService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE, search_type: STAFF}).then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
        var needJoinToArray = ["current_department_id"];
        _.forEach(needJoinToArray, function(attr) {
          if(!vm.params[attr]) vm.params[attr] = "";
          if(!_.isArray(vm.params[attr])) vm.params[attr] = vm.params[attr].split(",");
        });
      } else {
        vm.params.type = "staff";
        vm.params.per_page = vm.perPageSettings[0];
      }
      angular.element(document).ready(function() {
        $("#current_department_id").trigger("change");
      });
      vm.refresh(true, false, true);
      initCheckBox(vm.params.occupation, vm.occupation, "chkAllOccupation");
      initCheckBox(vm.params.staff_status, vm.staff_status, "chkAllStaffStatus");
      initCheckBox(vm.params.contract_status, vm.contract_status, "chkAllStaffStatus");
      initCheckBox(vm.params.level_up_training_format, vm.level_up_training_format, "", "level_up_training");
      DATETIME_FIELDS.forEach(function(attr) {
        if (vm.params[attr]) {
          $("[name='" + attr + "']").datepicker("setDate", vm.params[attr]);
        };
      });
      vm.params.desc = vm.params.desc == "false" ? false : true;
      vm.params.page = 1;
      vm.currentParams = angular.copy(vm.params);
      vm.contractStartDate = "";
    });
  };

  vm.refresh = function(resetPage, isSave, isUseSearchCondition) {
    var paramSearch = isUseSearchCondition ? angular.copy(vm.params) : angular.copy(formatPerPageParam(vm.currentParams));
    paramSearch = formatParamsBeforeSearch(paramSearch);
    if (resetPage) {
      paramSearch.page = 1;
    }
    if (!vm.params.per_page) paramSearch.per_page = 10;
    if (isSave) {
      staffListService.createSearchCondition({search: _.omit(paramSearch, "page", "limit"), search_condition: SEARCH_CONDITION_TYPE, search_type: STAFF}).then(function(res) {});
    }
    triggerChangeSelect2();

    staffListService.getStaff({search: paramSearch}).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.staffs.length);
      $location.search(paramSearch).replace();
      vm.currentParams = angular.copy(paramSearch);
    }, function(error) {
      vm.staffs = [];
    });
  };

  function formatParamsBeforeSearch(params) {
    if(!_.isEmpty(params.current_department_id) && _.isArray(params.current_department_id)) {
      params.current_department_id = _.join(_.flatten(_.compact(params.current_department_id)), ',');
    }
    return params;
  }

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !vm.params.desc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !!vm.params.desc
    }
  };

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
      vm.currentParams.desc = !vm.currentParams.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
      vm.currentParams.desc = true;
      vm.currentParams.order_key = column;
    }
    vm.refresh(false, true);
  };

  vm.verifyAdmin = function() {
    $("#verify-admin-password-modal").modal("show");
  }

  vm.viewStaffInfos = function(e) {
    var submitParams = {
      password: vm.verify_admin_password
    }
    $("#spinner").removeClass("ng-hide");
    staffListService.checkAdminPassword(submitParams).then(function(res) {
      var res = res.data;
      if (res.status) {
        $("#verify-admin-password-modal").modal("hide");
        vm.verify_admin_password = "";
        vm.exportMasterStaff(e);
      } else {
        vm.verify_password_error = true;
        vm.verify_password_error_class = "has-error";
        vm.authenticated = true;
      }
      $("#spinner").addClass("ng-hide");
    });
  }

  vm.handleInputPassword = function(e) {
    var keyCode = e.keyCode || e.which;
    if (keyCode === 13) {
      vm.viewStaffInfos(e);
      return false;
    }
  }

  vm.exportMasterStaff = function(e) {
    var paramSearch = angular.copy(vm.params);
    paramSearch = formatParamsBeforeSearch(paramSearch);
    if ($(".admin-staff-search").length) {
      $.asyncDownload(e, "staffs", JSON.stringify(paramSearch), XLSX_TYPE, {}, "export_xlsx");
    };
  }

  vm.toggleAllStatusChkboxes = function(fieldName, chkAll) {
    _.forEach(vm[fieldName], function(value, key) {
      vm[key] = vm[chkAll];
    });
    vm.changeStatusCheckbox(fieldName, chkAll);
  };

  vm.changeStatusCheckbox = function(fieldName, isUseKey, chkAll) {
    var validStatus = [];
    _.forEach(vm[fieldName], function(value, key) {
      if (vm[key]) {
        if (isUseKey) {validStatus.push(key);}
        else {validStatus.push(value);}
      } else if (!_.isUndefined(chkAll)) {
        vm[chkAll] = false;
      }
    });
    vm.params[fieldName] = validStatus.toString();
  };

  function initCheckBox(paramValues, fullValues, chkAll, keyAttr) {
    if (!_.isUndefined(paramValues)) {
      var arrParamValues = paramValues.split(",");
      _.forEach(arrParamValues, function(key){
        if (_.isUndefined(keyAttr)) {vm[key] = true;}
        else {vm[keyAttr + "_" + key] = true;}
      });
      if (!_.isEmpty(chkAll) && _.isEqual(Object.keys(fullValues).length, arrParamValues.length)) {
        vm[chkAll] = true;
      }
    }
  }

  function triggerChangeSelect2() {
    SELECT_FIELDS.forEach(function(field) {
      $("#" + field).val(vm.params[field]).trigger("change.select2");
    });
  }

  function formatPerPageParam(currentParams) {
    currentParams.per_page = vm.params.per_page;
    currentParams.page = vm.params.page;
    return currentParams;
  };

  function formatNumberOfMonths(){
    var months = NUMBER_OF_MONTHS.map(function(month){
      return { 
        name: month.toString().concat(I18n.t("admin.staff.modal_create_contract.month")),
        value: month
      }
    })
    return months;
  }

  vm.changePerPage = function() {
    vm.refresh(true, true, true);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".staff-search-result").offset().top
    }, 1000);
  };

  vm.checkAll = function() {
    vm.selectAllStaff = !vm.selectAllStaff;
    angular.forEach(vm.staffs, function(staff) {
      staff.select = vm.selectAllStaff;
    });
    vm.checkSelected(true);
  };

  vm.checkSelected = function(isChecked) {
    var selectedItems = _.find(vm.staffs, {select: true});
    if (selectedItems) {
      vm.isSelected = true;
    } else {
      vm.isSelected = false;
    };
    if (vm.selectAllStaff && !isChecked) {
      vm.selectAllStaff = false;
    };
  };

  $(".auto-search").keydown(function(event) {
    if(event.keyCode == 13) {
      event.preventDefault();
      vm.refresh(true, true, true);
    }
  });

  vm.updateContractStatus = function(){
    var staffIds = [];
    angular.forEach(vm.staffs, function(staff) {
      if(staff.select){
        staffIds.push(staff.id);
      }
    });
    var authenToken = $("#authenticity_token").val();
    var params = {authenticity_token: authenToken, ids: JSON.stringify(staffIds),
      contract_status: vm.contractStatusUpdated, request_send_mail: vm.selectSendMail};
    staffListService.updateContractStatus(params).then(function() {
      location.reload();
    });
  };

  vm.sendVerificationEmail = function() {
    var authenToken = $("#authenticity_token").val();
    var selectedItems = _.filter(vm.staffs, {select: true});
    var paramsData = {
      authenticity_token: authenToken,
      staff_ids: _.map(selectedItems, "id"),
    };
    staffListService.sendVerificationEmail(paramsData).then(function() {
      location.reload();
    });
  };

  vm.sendRemindLoginMail = function() {
    $("#confirmation-modal").modal("show");
  }

  vm.confirmedSubmitDetail = function() {
    var authenToken = $("#authenticity_token").val();
    var selectedItems = _.filter(vm.staffs, {select: true});
    var paramsData = {
      authenticity_token: authenToken,
      staff_ids: _.map(selectedItems, "id"),
    };
    vm.isSubmitting = true;
    staffListService.sendRemindLoginMail(paramsData).then(function() {
      location.reload();
    });
  };

  vm.createStaffContract = function() {
    var selectedItems = _.filter(vm.staffs, {select: true});
    var params = {
      start_date: vm.contractStartDate,
      number_of_months: vm.selectedMonth,
      ids: _.map(selectedItems, "id")
    };
    $("#spinner").removeClass("ng-hide");
    staffListService.createStaffContractHistory(params).then(function() {
      $("#spinner").addClass("ng-hide");
      location.reload();
    });
  }

  vm.clearSearchCondition = function() {
    vm.params = angular.copy({
      per_page: vm.perPageSettings[0]
    });
    vm.chkAllOccupation = false;
    vm.chkAllStaffStatus = false;
    vm.chkAllContractStatus = false;
    vm.level_up_training_0 = false;
    vm.level_up_training_1 = false;

    vm.toggleAllStatusChkboxes('occupation', 'chkAllOccupation');
    vm.toggleAllStatusChkboxes('staff_status', 'chkAllStaffStatus');
    vm.toggleAllStatusChkboxes('contract_status', 'chkAllContractStatus');
    vm.changeStatusCheckbox('level_up_training_format');

    setTimeout(function() {
      angular.element("select.select2, select.select2-multiple").trigger("change");
    }, 10);
  };
}
