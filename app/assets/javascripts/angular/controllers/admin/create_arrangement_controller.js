"use strict";

angular.module("adminApp").controller("CreateArrangementController", CreateArrangementController);
CreateArrangementController.$inject = ["$scope", "$sce", "checkValidDateFunction", "orderService",
  "postalCodeService", "arrangementService"];

function CreateArrangementController($scope, $sce, checkValidDateFunction, orderService,
  postalCodeService, arrangementService) {
  var VIOLATION_DAYS = 60;
  var MAX_REST_TIMES = 3;
  var MIN_REST_TIMES = 1;
  var REST_TIMES = [1, 2, 3];
  var DEFAULT_ORDER_SEGMENT_ID = "haken";
  var vm = this;
  var picsData = ["claim", "haken_destination", "mandator", "order",
    "is_valid_order_info", "violation_day", "days_to_violation_day", "order_pic_tel",
    "prefecture_id", "is_approval_required", "closed_day_error_message", "transaction_error_message",
    "location_not_survey_error_message"
  ];
  var paramsStep1 = ["definition_id", "corporation_id", "corporation_group_id", "organization_id",
    "location_id", "haken_destination_pic_id", "haken_destination_pic_position", "haken_destination_pic_tel",
    "mandator_id", "mandator_position", "mandator_tel", "claim_pic_id", "claim_pic_position",
    "claim_pic_tel", "order_pic_id", "order_pic_email", "order_pic_tel", "note", "order_segment_id",
    "is_fixed_term_project", "is_limited_day"];
  var CORPORATION_BILLING_ATTRS = ["billing_name", "billing_postal_code",
    "billing_organization_id", "billing_prefecture_id", "billing_city", "billing_street_number",
    "billing_building", "billing_tel", "billing_fax", "is_use_current_billing_address", "billing_organizations"];
  var BREAK_TIME_ATTRS = ["break_time", "rest1_started_at", "rest1_ended_at", "rest2_started_at", "rest2_ended_at",
    "rest3_started_at", "rest3_ended_at"];
  var ORDER_BRANCH_DATA = ["started_at", "working_start_time", "working_end_time", "staff_count",
    "is_time_changable", "is_urgent", "is_special_offer", "special_offer_fee", "special_offer_note"];
  var STAFF_SEARCH_FIELDS = ["staff_number", "address_kana", "account_email", "tel", "current_department_id",
    "account_name_kana", "account_name_lower"];
  var ERROR_KEYS = ["nested_errors", "arrangement_errors", "work_achievement_errors"];
  var TEMPLATE_ATTRS = ["billing_basic_unit_price", "billing_night_unit_price", "area_allowance", "short_allowance",
    "absence_discount", "tax_exemption", "payment_basic_unit_price", "payment_night_unit_price", "transportation_fee"];
  var SEGMENT_TRAINING = "training";
  var TRAINING_NOT_APPLICABLE = "training_not_applicable";
  var TRAINING_NOT_SCHEDULED = "training_not_scheduled";

  vm.$scope = $scope;
  vm.originLocationData = {};
  vm.dayNames = I18n.t("date.abbr_day_names");
  vm.params = {};
  vm.selectedLocation = {};
  vm.params.is_used_existing_pic = false;
  vm.params.is_use_current_billing_address = true;
  vm.params.is_fixed_term_project = false;
  vm.params.is_limited_day = false;
  vm.params.under_minimum_wage = false;
  vm.selectedType = "individual_order";
  vm.totalIndividualOrder = 0;
  vm.steps = {
    step1: true,
    step2: false
  };
  vm.location = [];
  vm.selectedLocation = {isValidLocation: true, haken_destination: [], claim: [], mandator: [], order: []}
  _.forEach(paramsStep1, function(attr) {
      vm.params[attr] = "";
      if (attr === "order_segment_id") {
        vm.params[attr] = DEFAULT_ORDER_SEGMENT_ID;
      }
  });
  vm.orderData = {};
  vm.templates = {};

  vm.params.individual_order = [
    {
      order_number: 1,
      break_time: 0,
      is_special_offer: false,
      rest_times: [1],
      staff_count: 1
    }
  ];
  vm.params.bill_paym_temp = {};

  vm.breakTimeOptions = [0, 15, 30, 45, 60, 75, 90, 99];
  vm.durationOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
  vm.specialOfferFeeOptions = SPECIAL_OFFER_FEE;
  vm.estimateData = [];
  vm.totalEstimation = {individual_order: []};
  vm.canSubmitStep1 = true;
  vm.canSubmitStep3 = true;
  vm.isValidLocation = true;
  vm.selectedCorporationGroup = {};
  vm.corporations = angular.element(".corporation-options").data("corporation-options");
  vm.billingPrefectures = angular.element(".billing-prefectures").data("billing-prefectures");
  vm.staff_search_params = {};
  vm.simpleSearchStaff = {};
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  vm.init = function (orderData) {
    vm.params.is_used_existing_pic = false;

    if (!_.isUndefined(vm.organizationOptions) && vm.organizationOptions[0]) {
      vm.params.organization_id = vm.organizationOptions[0][1].toString();
      vm.changeLocationOptions();
    };

    vm.initSaveOrderValues(false);
  };


  vm.loadLocations = function() {
    if (_.isNaN(vm.params.corporation_id)) {
      vm.locations = {};
      vm.selectedLocation = {isValidLocation: true};
      vm.initLocationPicData();
      vm.canSubmitStep1 = true;
      return;
    }
    orderService.loadLocations({
      corporation_id: vm.params.corporation_id
    }).then(function(res) {
      angular.extend(vm, res.data);
      vm.selectedLocation = {};
      vm.selectedLocation.isValidLocation = true;
      vm.canSubmitStep1 = true;
    }, function(error) {
    });
  };

  vm.changeLocation = function() {
    vm.loadPicOptions();
    vm.loadBillingData();
    vm.loadLocationTemplates()
    vm.initInvoiceTarget();
  }


  vm.initInvoiceTarget = function() {
    if (!vm.location) {return;}
    vm.invoice_target = vm.location.default_invoice_target;
  }

  vm.initLocationPicData = function(order) {
    var types = ["haken_destination", "claim"];

    if (order) {
      vm.params["order_pic_id"] = order.order_pic_id;
      vm.params["order_pic_email"] = order.order_pic_email;
    } else if (vm.isCopy) {
      vm.params["order_pic_id"] = getSelectedLocationProperty("order", "id");
      vm.params["order_pic_email"] = getSelectedLocationProperty("order", "email");
    } else {
      vm.params["order_pic_id"] = vm.params["order_pic_email"] = "";
    };

    _.forEach(types, function(type) {
      if (order) {
        vm.params[type + "_pic_id"] = order[type + "_pic_id"];
        vm.params[type + "_pic_position"] = order[type + "_pic_position"];
        vm.params[type + "_pic_email"] = order[type + "_pic_email"];
        vm.params[type + "_pic_tel"] = order[type + "_pic_tel"];
      } else {
        vm.params[type + "_pic_id"] = getSelectedLocationProperty(type, "id");
        vm.params[type + "_pic_position"] = getSelectedLocationProperty(type, "position");
        vm.params[type + "_pic_tel"] = getSelectedLocationProperty(type, "tel");
      }
    });
    vm.initMandator(order);

    var selectedType = vm.selectedType;
    if (!_.isUndefined(vm.params[selectedType][0]["started_at"])) {
      var targetTab = selectedType == "individual_order" ? "#single-order" : "#multi-order";

      $.each($(targetTab).find("[name*='started_at']"), function(key, element) {
        var elementIndex = $(element).scope().$index;
        vm.estimateOrderPrice(selectedType, vm.params[selectedType][elementIndex]);
      });
    }
  };

  function getSelectedLocationProperty(type, key) {
    if (!_.isEmpty(vm.selectedLocation) && vm.selectedLocation.isValidLocation && vm.selectedLocation[type] &&
      vm.selectedLocation[type][0][key]) {
      return vm.selectedLocation[type][0][key];
    }
    return "";
  };

  vm.initMandator = function(order) {
    if (order) {
      vm.params.mandator_id = order["mandator_id"];
      vm.params.mandator_position = order["mandator_position"];
      vm.params.mandator_tel = order["mandator_tel"];
      vm.params.order_pic_tel = _.isEmpty(vm.selectedLocation) ? "" : vm.selectedLocation.order_pic_tel;
    } else {
      vm.params.mandator_id = getSelectedLocationProperty("mandator", "id");
      vm.params.mandator_position = getSelectedLocationProperty("mandator", "position");
      vm.params.mandator_tel = getSelectedLocationProperty("mandator", "tel");
      vm.params.order_pic_tel = _.isEmpty(vm.selectedLocation) ? "" : vm.selectedLocation.order_pic_tel;
    }
  };

  vm.nextToStep2 = function() {
    vm.enableStep(2);
  };

  vm.backToStep1 = function() {
    vm.enableStep(1);
  };

  vm.backToStep2 = function() {
    vm.enableStep(2);
  }

  vm.enableStep = function(step) {
    angular.forEach([1, 2], function(index) {
      vm.steps["step" + index] = (step === index);
    });
    window.scrollTo(0, 0);
  };

  vm.loadPicOptions = function() {
    if (_.isEmpty(vm.params.location_id.toString())) {
      vm.selectedLocation = {isValidLocation: true};
      vm.initLocationPicData();
      vm.canSubmitStep1 = true;
      return vm.selectedLocation;
    }
    loadLocationData();
    if (!_.isEmpty(vm.originLocationData) && vm.location.id === vm.originLocationData.id) {
      vm.location = vm.originLocationData;
      vm.originLocationData = {};
    };
    orderService.loadPicOptions({location_id: vm.params.location_id, id: vm.params.id}).then(function(res) {
      var results = res.data;
      _.forEach(picsData, function(pic_data) {
        vm.selectedLocation[pic_data] = results[pic_data];
      });
      vm.checkLocationIsValid();
      vm.checkViolationDay();
      vm.checkSubmitStep1Condition();
      vm.params.organization_id = results.organization_id;
      vm.params.organization_full_name = results.organization_full_name;
      vm.loadBillingData();
      if (vm.selectedLocation.isValidLocation) {
        vm.initLocationPicData();
      }
    }, function(error) {
    });
  };

  vm.checkLocationIsValid = function() {
    vm.selectedLocation.isValidLocation = true;
    var requiresInfo = [vm.selectedLocation.claim, vm.selectedLocation.haken_destination,
      vm.selectedLocation.mandator];
    if (!_.isEmpty(vm.params.location_id)) {
      _.forEach(requiresInfo, function(locationPic) {
        if (_.isUndefined(locationPic) || !vm.selectedLocation.is_valid_order_info) {
          vm.params.order_pic_tel = "";
          vm.selectedLocation.isValidLocation = false;
        }
      });
    }
  };

  vm.checkViolationDay = function() {
    var days = vm.selectedLocation.days_to_violation_day;
    var message = "";

    if (!_.isInteger(days)) {
      message = I18n.t("corporation.order.step1.violation_day.blank");
    } else if (days <= 0) {
      message = I18n.t("corporation.order.step1.violation_day.error_message");
    } else if (days <= 30) {
      message = I18n.t("corporation.order.step1.violation_day.notice_message_day_30", {days: days});
    } else if (days >= 31 && days <= VIOLATION_DAYS) {
      message = I18n.t("corporation.order.step1.violation_day.notice_message_day_31", {days: days});
    };
    vm.selectedLocation.violation_day_error_message = $sce.trustAsHtml(message);
  };

  vm.showMessageError = function() {
    vm.canSubmitStep3 = vm.canSubmitStep1 && isValidStartDate();
    var error = [];
    if (!vm.isValidLocation) {
      error.push(I18n.t("corporation.order.messages.invalid_location"));
    };
    if (!isValidStartDate()) {
      error.push(I18n.t("corporation.order.messages.invalid_start_date"))
    };
    error.push(vm.selectedLocation.transaction_error_message);
    return $sce.trustAsHtml(error.join("<br>"));
  };

  function isValidStartDate() {
    return moment().isSameOrBefore(vm.orderData.overall_started_at, "day");
  };

  vm.showLocationErrorMessage = function() {
    return I18n.t("corporation.order.step1.location_notice");
  };

  vm.submitCreateOrderStep1 = function() {
    $(".disable-submit-btn").prop("disabled", true);
    $('.has-error').removeClass('has-error');
    $('.help-block').remove();

    orderService.createOrderStep1({order: vm.params, id: vm.params.id}).then(function(res) {
      $.lawsonAjax(res.data);
      if (res.data.status) {
        vm.enableStep(2);
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  };

  vm.submitCreateOrderStep2 = function(confirmWarning) {
    vm.params.under_minimum_wage = false;

    $(".disable-submit-btn").prop("disabled", true);
    $('.has-error').removeClass('has-error');
    $('.help-block').remove();
    $("#confirm-warning-arrange-staff-modal").modal("hide");

    var $form = $("form#form-create-arrangement");
    var orderParams = $form.serializeJSON();
    var formatedParam = formatParamsBeforeSubmit(orderParams);
    formatedParam.confirm_warning = confirmWarning;
    arrangementService.createArrangement(formatedParam).then(function(res) {
      vm.step2Response = res.data;
      $.lawsonAjaxCreateArrangement(res.data);
      if (res.data.status) {
        $(location).attr("href", "/arrangements");
      } else {
        vm.params.under_minimum_wage = res.data.under_minimum_wage;
        $.each(res.data.nested_errors, function(index, value) {
          if (value.ended_at && value.predetermined_time && Object.keys(value).length == 2) {
            $("#ended-at-error" + index).append("<span class='ended-at-error'>&nbsp" +
              I18n.t("corporation.order.step2.cant_create") + "</span>");
          }
        });

        var isValidAllArrangement = true;
        _.forEach(ERROR_KEYS, function(key) {
          var errors = vm.step2Response[key];
          _.forEach(errors, function(val) {
            if (!_.isEmpty(val)) {
              isValidAllArrangement = false;
              return;
            }
          });
        });

        if (isValidAllArrangement) {
          $("#confirm-warning-arrange-staff-modal").modal("show");
        } else {
          vm.enableStep(2);
        }
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  };

  vm.estimateOrderPrice = function(type, order) {
    if(invalidEstimateParams(order)) {
      return;
    }

    var submitParams = vm.estimateOrderParams(type, order);
    submitParams.id = vm.params.id;
    submitParams.corporation_id = vm.params.corporation_id;
    submitParams.bill_paym_template_id = vm.params.bill_paym_temp.id;
    submitParams.type = "individual_order";
    orderService.estimateOrderPrice(submitParams).then(function(res) {
      var orderIndex = vm.params[type].indexOf(order);
      var response_data = res.data;
      vm.params[type][orderIndex]["estimate_data"] = response_data;
      vm.estimateData[orderIndex] = {data: order.estimate_data};
      vm.selectedType = type;
      vm.totalIndividualOrder = _.sumBy(vm.estimateData, "data[summary]");
    }, function(_) {
    });
  };

  function invalidEstimateParams(order) {
    var dateRegex = /^([0-9]{2})\:([0-9]{2})$/;

    return _.isEmpty(order.started_at) || _.isEmpty(order.working_start_time) || _.isEmpty(order.working_end_time)
    || (!_.isEmpty(order.rest1_started_at) && (!moment(order.rest1_started_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest1_started_at)))
    || (!_.isEmpty(order.rest2_started_at) && (!moment(order.rest2_started_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest2_started_at)))
    || (!_.isEmpty(order.rest3_started_at) && (!moment(order.rest3_started_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest3_started_at)))
    || (!_.isEmpty(order.rest1_ended_at) && (!moment(order.rest1_ended_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest1_ended_at)))
    || (!_.isEmpty(order.rest2_ended_at) && (!moment(order.rest2_ended_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest2_ended_at)))
    || (!_.isEmpty(order.rest3_ended_at) && (!moment(order.rest3_ended_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest3_ended_at)))
    || (!_.isEmpty(order.started_at) && (!moment(order.started_at).isValid()
    || !CHECK_DATE_REGEX.test(order.started_at)))
    || (!_.isEmpty(order.working_start_time) && (!moment(order.working_start_time, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.working_start_time)))
    || (!_.isEmpty(order.working_end_time) && (!moment(order.working_end_time, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.working_end_time)))
  }

  function loadLocationData() {
    vm.location = _.find(vm.locations, function(lo) {
      return lo.id === parseInt(vm.params.location_id);
    }) || [];
  };

  vm.estimateOrderParams = function(type, order) {
    var orderBranchData = {};
    var data = {
      type: type,
      prefecture_id: vm.selectedLocation.prefecture_id,
      corporation_id: vm.corporationId,
      location_id: vm.params.location_id
    };
    _.forEach(_.concat(ORDER_BRANCH_DATA, BREAK_TIME_ATTRS), function(key) {
      data[key] = order[key];
    });
    data.create_by_arrangement = true;
    if (type === "individual_order" && order["started_at"] && order["working_start_time"]
      && order["working_end_time"] && order["staff_count"]) {
      orderBranchData = _.extend({}, data);
    }
    return orderBranchData;
  };

  vm.isDisplayPriceColumn = function(orderData, field) {
    return _.sumBy(orderData, field) !== 0;
  }

  vm.isDisplaySpeacialCharge = function(orderData, field) {
    return _.sumBy(orderData, field) !== 0;
  }

  vm.changePosition = function(type) {
    var picID = vm.params[type + "_pic_id"];
    var locationPic = {};
    if (_.isNaN(picID)) {
      locationPic = {
        position: "",
        email: "",
        tel: ""
      }
    } else {
      var currentPics = vm.selectedLocation[type];
      locationPic = _.find(currentPics, function(pic) {return pic.id === parseInt(picID)});
    }
    changePicValue(type, locationPic, false);
  };

  vm.getViolationDay = function() {
    var violationDay = vm.selectedLocation.violation_day;
    if (violationDay) {
      return violationDay.split("(")[0];
    };
  };

  function changePicValue(type, locationPic, isMadator) {
    if (_.isUndefined(locationPic)) {
      return;
    }
    var validLocation = vm.selectedLocation.isValidLocation;
    if (isMadator) {
      vm.params[type + "_position"] = validLocation ? locationPic.position : "";
      vm.params[type + "_tel"] = validLocation ? locationPic.tel : "";
    } else {
      vm.params[type + "_pic_position"] = validLocation ? locationPic.position : "";
      vm.params[type + "_pic_email"] = validLocation ? locationPic.email : "";
      if (type === "order") {
        vm.params.user_locations_pic_tel = validLocation ? locationPic.tel : "";
      } else {
        vm.params[type + "_pic_tel"] = validLocation ? locationPic.tel : "";
      }
    }
  };

  vm.changePositionMandator = function() {
    var picID = vm.params.mandator_id;
    var locationPic = {};
    if (_.isNaN(picID)) {
      locationPic = {
        position: "",
        tel: ""
      }
    } else {
      locationPic = _.find(vm.selectedLocation.mandator, function(pic) {return pic.id === parseInt(picID)});
    }
    changePicValue("mandator", locationPic, true);
  };

  vm.selectOrderBranchDays = function(day, order) {
    order[vm.orderBranchDays[day]] = !order[vm.orderBranchDays[day]];
    vm.estimateOrderPrice('collective_order', order);
  };

  vm.getObjectName = function(typeData, typeId) {
    var object = _.find(vm.selectedLocation[typeData], function(o) {
      return o.id === vm.params[typeId];
    });
    if (object) {
      return object.name;
    }
  };

  vm.addNewOrder = function(orderType) {
    var orders = vm.params[orderType];
    var orderNumber = orders.length + 1;
    orders.push({order_number: orderNumber, break_time: 0, is_special_offer: false,
      rest_times: [1], staff_count: 1});
  };

  vm.removeOrder = function(type, index) {
    var orderBranchId = vm.params[type][index].id;
    if (orderBranchId) {
      var orderBranch = "order[order_branches_attributes][" + 1000 + orderBranchId + "]";
      var destroyElement = "<input type='hidden' name=" + orderBranch + "[id] value=" + orderBranchId + " /> \
        <input type='hidden' name=" + orderBranch + "['_destroy'] value=true />";
      angular.element(".hidden-order-nested").append(destroyElement);
    };
    vm.params[type].splice(index, 1);
    vm.estimateData.splice(index, 1);
    vm.totalIndividualOrder = _.sumBy(vm.estimateData, "data[summary]");
  };

  vm.duplicateOrder = function(type, order) {
    var newOrder = _.extend({}, order);
    newOrder.id = "";
    newOrder.staff = {};
    newOrder.order_number = vm.params[type].length + 1;
    if (!order.is_special_offer) {
      newOrder.special_offer_fee = "";
      newOrder.special_offer_note = "";
    }
    vm.params[type].push(newOrder);
    vm.estimateOrderPrice(type, newOrder);
  };

  vm.getLocationPic = function(typeData) {
    var type = typeData + "_pic";
    if (typeData === "mandator") {
      type = typeData;
    };
    var picName = vm.getObjectName(typeData, type + "_id");
    var picPosition = type === "order_pic" ? vm.params[type + "_email"] : vm.params[type + "_position"];
    return [picName, picPosition, vm.params[type + "_tel"]].join(" ");
  };

  function formatDate(date, formatType) {
    if (_.isEmpty(date)) return;
    return moment.parseZone(date).format(formatType);
  };

  vm.checkSubmitStep1Condition = function() {
    var days = vm.selectedLocation.days_to_violation_day;
    var isValidClosedDay = _.isEmpty(vm.selectedLocation.closed_day_error_message);
    var isLocationNotSurvey = _.isEmpty(vm.selectedLocation.location_not_survey_error_message);
    vm.isValidLocation = vm.selectedLocation.isValidLocation && days > 0 && isValidClosedDay && isLocationNotSurvey;
    vm.canSubmitStep1 = vm.isValidLocation && _.isEmpty(vm.selectedLocation.transaction_error_message);
  };

  vm.loadOrganizations = function() {
    if (!_.isNumber(parseInt(vm.params.corporation_id))) {
      vm.organizations = {};
      vm.is_lawson_staff = false;
      return ;
    }
    orderService.loadOrganizations({corporation_id: vm.params.corporation_id, id: vm.params.id}).then(function(res) {
      angular.extend(vm, res.data);
      if (_.isNull(vm.is_lawson_staff)) {
        vm.is_lawson_staff = false;
      }
    }, function(_) {
    });
  };

  vm.searchPostalCode = function() {
    vm.params.page = vm.currentPage;
    vm.params.postal_code = vm.postalCode;
    postalCodeService.getPostalCode(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.postal_codes.length);
      vm.noRecord = !vm.hasRecord;
      vm.params.limit = res.data.pagination_limit;
    }, function(error) {
    });
  };

  vm.loadBillingData = function() {
    if (_.isEmpty(vm.params.organization_id.toString()) || vm.is_lawson_staff) {
      vm.selectedCorporationGroup = {};
      vm.initBillingData();
      return;
    };
    var submitParams = {organization_id: vm.params.organization_id, id: vm.params.id};
    orderService.loadCorporationGroupData(submitParams).then(function(res) {
      _.forEach(CORPORATION_BILLING_ATTRS, function(attr) {
        vm.selectedCorporationGroup[attr] = res.data.corporation_group[attr];
      });
      if (vm.params.is_use_current_billing_address) {
        vm.initBillingData();
      } else {
        vm.params.billing_organizations = vm.selectedCorporationGroup.billing_organizations;
      }
    }, function(_) {
    });
  };

  vm.initBillingData = function() {
    _.forEach(CORPORATION_BILLING_ATTRS, function(attr) {
      vm.params[attr] = vm.selectedCorporationGroup[attr];
    });
    if (vm.is_lawson_staff) return;
    if (_.isUndefined(vm.selectedCorporationGroup.billing_organization_id)) {
      vm.params.billing_organization_id = vm.params.organization_id;
    }
    vm.params.is_use_current_billing_address = true;
  };

  vm.submitCreateOrder = function(status) {
    vm.buttonsDisabled = true;
    var orderParams = $("form#form-create-order").serializeJSON();
    var formatedParam = formatParamsBeforeSubmit(orderParams);
    _.extend(formatedParam.order, {status_id: status});
    orderService.createOrder(formatedParam).then(function(res) {
      $.lawsonAjax(res.data);
    });
  };

  function formatParamsBeforeSubmit(orderParams) {
    var resultsParam = orderParams.order.order_branches_attributes;
    Object.keys(resultsParam).forEach(function(key) {
      if (!resultsParam[key].is_special_offer) {
        delete resultsParam[key].special_offer_fee;
        delete resultsParam[key].special_offer_note;
      }
      if (resultsParam[key].order_branch_type !== vm.selectedType) {
        delete resultsParam[key];
      }
    });
    orderParams.order.order_branches_attributes = resultsParam;
    orderParams.order.bill_paym_temp = vm.params.bill_paym_temp;
    orderParams.order.training_session_code = vm.params.training_session_code;
    orderParams.order.training_schedule_code = vm.params.training_schedule_code;
    orderParams.prefecture_id = vm.selectedLocation.prefecture_id;
    orderParams["invoice_target"] = vm.invoice_target;
    return orderParams;
  }

  vm.setDefaultSpecialOfferFee = function(type, order) {
    var orderIndex = vm.params[type].indexOf(order);
    if (order.is_special_offer) {
      if (!_.includes(vm.specialOfferFeeOptions, order.special_offer_fee)) {
        vm.params[type][orderIndex]["special_offer_fee"] = SPECIAL_OFFER_FEE[0];
      }
    } else {
      vm.params[type][orderIndex]["is_special_offer"] = false;
      vm.params[type][orderIndex]["special_offer_fee"] = "";
      vm.params[type][orderIndex]["special_offer_note"] = "";
    }
  }

  vm.addRestTime = function(type, order) {
    var orderIndex = vm.params[type].indexOf(order);
    var restTimes = vm.params[type][orderIndex]["rest_times"].slice();
    var restTimeCount = restTimes.length;
    if (restTimeCount === MAX_REST_TIMES) return;
    restTimes.push(restTimeCount + 1);
    vm.params[type][orderIndex]["rest_times"] = restTimes;
  }

  vm.removeRestTime = function(type, order, restTime) {
    var orderIndex = vm.params[type].indexOf(order);
    var restTimes = vm.params[type][orderIndex]["rest_times"].slice();
    var restTimeCount = restTimes.length;
    if (restTimeCount === MIN_REST_TIMES) return;
    if (restTime === 2) {
      vm.params[type][orderIndex]["rest2_started_at"] = vm.params[type][orderIndex]["rest3_started_at"];
      vm.params[type][orderIndex]["rest2_ended_at"] = vm.params[type][orderIndex]["rest3_ended_at"];
    }
    vm.params[type][orderIndex]["rest3_started_at"] = vm.params[type][orderIndex]["rest3_ended_at"] = "";
    restTimes.pop();
    vm.params[type][orderIndex]["rest_times"] = restTimes;
  }

  vm.countRestTime = function(type, order, rest_time) {
    var startTime = order["rest" + rest_time + "_started_at"];
    var endTime = order["rest" + rest_time + "_ended_at"];
    if (_.isEmpty(startTime) || _.isEmpty(endTime)) return;
    var start = moment(startTime, TIME_PICKER_FORMAT);
    var end = moment(endTime, TIME_PICKER_FORMAT);
    if (end.isBefore(start)) end.add(1, "day");
    return I18n.t("corporation.order.step2.total_minutes", {minutes: end.diff(start, "minutes")});
  }

  vm.initOrderBranchRestTime = function(order_branch) {
    var rest_times = [];
    _.forEach(REST_TIMES, function(rest_time) {
      var startTime = order_branch["rest" + rest_time + "_started_at"];
      var endTime = order_branch["rest" + rest_time + "_ended_at"];
      if (_.isEmpty(order_branch["rest" + rest_time + "_started_at"])) return;
      order_branch["rest" + rest_time + "_started_at"] = formatDate(startTime, TIME_PICKER_FORMAT);
      order_branch["rest" + rest_time + "_ended_at"] = formatDate(endTime, TIME_PICKER_FORMAT);
      rest_times.push(rest_time);
    });
    order_branch["rest_times"] = rest_times;
    return order_branch;
  }

  vm.disableSearchStaff = function(orderIndex) {
    var order = vm.params.individual_order[orderIndex];
    return _.isEmpty(order.started_at) || _.isEmpty(order.working_start_time) || _.isEmpty(order.working_end_time);
  }

  vm.openModalSearchStaff = function(orderIndex) {
    if (vm.disableSearchStaff(orderIndex)) {
      $("#modal-notice-select-staff").modal("show");
      return;
    }
    vm.staff_search_params.order_index = orderIndex;
    vm.staff_search_params.desc = vm.staff_search_params.desc == "false" ? false : true;
    vm.staff_search_params.page = 1;
    vm.staff_search_params.per_page = vm.perPageSettings[0];
    vm.simpleSearchStaff.isCloseAllModal = false;
    _.forEach(STAFF_SEARCH_FIELDS, function(field) {
      vm.staff_search_params[field] = "";
    });
    $("#current_department_id").val(vm.staff_search_params.current_department_id).trigger("change.select2");
    vm.currentStaffParams = angular.copy(vm.staff_search_params);
    vm.refresh_staff();
    $("#modal-search-staff").modal("show");
  }

  vm.selectStaff = function(idStaff) {
    vm.idStaffSelected = idStaff;
  }

  vm.refresh_staff = function(resetPage, isUseSearchCondition) {
    var paramSearch = isUseSearchCondition ? vm.staff_search_params : formatPerPageParam(vm.currentStaffParams);
    if (resetPage) {
      paramSearch.page = 1;
    }
    var orderData = vm.params.individual_order[vm.staff_search_params.order_index];
    var orderCaseParams = {
      case_started_at: orderData["started_at"] + " " + orderData["working_start_time"],
      case_ended_at: orderData["started_at"] + " " + orderData["working_end_time"],
      total_portion: 1,
      segment_id: vm.params.order_segment_id
    }
    if (!vm.staff_search_params.per_page) paramSearch.per_page = 10;

    paramSearch.corporation_id = vm.params.corporation_id;
    paramSearch.location_id = vm.params.location_id;
    arrangementService.seachStaffForCreateArrange({search: paramSearch, order_params: orderCaseParams}).then(function(res) {
      vm.idStaffSelected = "";
      vm.staffs = res.data.staffs;
      vm.total_staff_items = res.data.total_items;
      vm.simpleSearchStaff.hasRecord = Boolean(vm.staffs.length);
      vm.currentStaffParams = angular.copy(paramSearch);
    }, function(error) {
      vm.staffs = [];
    });
  };

  vm.confirmArrangeStaff = function(ignoreWarning) {
    var staff = _.find(vm.staffs, function(staff){ return staff.id == vm.idStaffSelected; });
    if(_.isEmpty(staff)) return;
    var orderIndex = vm.staff_search_params.order_index;
    vm.params.individual_order[orderIndex].staff = staff;
    vm.params.individual_order[orderIndex].arrange_rest_times = [1];
    vm.params.individual_order[orderIndex].arrangement = [];
    vm.params.individual_order[orderIndex].has_break = true;
    $("#modal-search-staff").modal("hide");
  }

  vm.initWorkingTime = function(index, workingField) {
    var oderArrangement = vm.params.individual_order[index].arrangement;
    if (_.isEmpty(oderArrangement[workingField])) {
      oderArrangement[workingField] = moment({h: 0, m: 0}).format(TIME_PICKER_FORMAT);
    }
  }

  vm.checkValidStaffTime = function(index, workingField) {
    var oderArrangement = vm.params.individual_order[index].arrangement;
    if (!_.isEmpty(oderArrangement[workingField]) &&
      !moment(oderArrangement[workingField], TIME_PICKER_FORMAT).isValid()) {
      oderArrangement[workingField] = moment().format(TIME_PICKER_FORMAT);
    }
  }

  vm.checkValidTime = function(index, type, order, field) {
    vm.params[type][index][field] = checkValidDateFunction.checkValidTime(vm.params[type][index][field]) ||
      vm.params[type][index][field];
    vm.estimateOrderPrice(type, order);
  }

  vm.isSelectedStaff = function(index) {
    return !_.isEmpty(vm.params.individual_order[index].staff);
  }

  vm.addArrangementRestTime = function(order) {
    var orderIndex = vm.params.individual_order.indexOf(order);
    var restTimes = vm.params.individual_order[orderIndex]["arrange_rest_times"].slice();
    var restTimeCount = restTimes.length;
    if (restTimeCount === MAX_REST_TIMES) return;
    restTimes.push(restTimeCount + 1);
    vm.params.individual_order[orderIndex]["arrange_rest_times"] = restTimes;
  }

  vm.removeArrangementRestTime = function(order, restTime) {
    var orderIndex = vm.params.individual_order.indexOf(order);
    var restTimes = vm.params.individual_order[orderIndex]["arrange_rest_times"].slice();
    var restTimeCount = restTimes.length;
    if (restTimeCount === MIN_REST_TIMES) return;
    if (restTime === 2) {
      vm.params.individual_order[orderIndex].arrangement["rest2_started_at"] = vm.params.individual_order[orderIndex].arrangement["rest3_started_at"];
      vm.params.individual_order[orderIndex].arrangement["rest2_ended_at"] = vm.params.individual_order[orderIndex].arrangement["rest3_ended_at"];
    }
    vm.params.individual_order[orderIndex].arrangement["rest3_started_at"] = vm.params.individual_order[orderIndex].arrangement["rest3_ended_at"] = "";
    restTimes.pop();
    vm.params.individual_order[orderIndex]["arrange_rest_times"] = restTimes;
  }

  function formatPerPageParam(currentStaffParams) {
    currentStaffParams.per_page = vm.staff_search_params.per_page;
    currentStaffParams.page = vm.staff_search_params.page;
    return currentStaffParams;
  };

  vm.displayWarningMessage = function(message) {
    return $sce.trustAsHtml(message);
  };

  vm.checkValidDate = function(index, type, order) {
    vm.params[type][index]["started_at"] = checkValidDateFunction.checkValidDate(vm.params[type][index]["started_at"]) ||
      vm.params[type][index]["started_at"]
    vm.estimateOrderPrice(type, order);
  };

  vm.showBillingPaymentValue = function(field) {
    var normalTxt = I18n.t("admin.order.order_form.new_step3.normal_calculation");
    return !!vm.params.bill_paym_temp[field] || vm.params.bill_paym_temp[field] == 0 ? vm.params.bill_paym_temp[field] : normalTxt;
  }

  vm.loadLocationTemplates = function() {
    if (_.isEmpty(vm.params.location_id.toString())) return;
    orderService.loadLocationTemplates({location_id: vm.params.location_id}).then(function(res) {
      if (!!res.data.templates) {
        vm.templates = angular.copy(res.data.templates);
      }
    });
  }

  vm.changeTemplate = function() {
    var template = _.find(vm.templates, function(template) {
      return template.id == vm.selectedTemplate;
    });
    if (_.isUndefined(template)) {
      resetBillingPaymentValues();
      return;
    }
    vm.selectedTemplateName = template.name;
    vm.params.bill_paym_temp.id = template.id;
    _.each(TEMPLATE_ATTRS, function(fieldName) {
      vm.params.bill_paym_temp[fieldName] = template[fieldName];
    });
  }

  function resetBillingPaymentValues() {
    vm.params.bill_paym_temp = {};
  }

  vm.$scope.$watch("vm.params.order_segment_id", function() {
    if(vm.params.order_segment_id != SEGMENT_TRAINING) {
      vm.params.training_session_code = TRAINING_NOT_APPLICABLE;
      vm.params.training_schedule_code = TRAINING_NOT_SCHEDULED;
    }
  });
}
