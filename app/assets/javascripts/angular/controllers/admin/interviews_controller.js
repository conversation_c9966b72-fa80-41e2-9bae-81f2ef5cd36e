"use strict";

angular.module("adminApp")
  .controller("InterviewsController", InterviewsController);
InterviewsController.$inject = ["interviewService", "$sce", "toaster"];
function InterviewsController(interviewService, $sce, toaster) {
  var vm = this;
  vm.height = 40;
  vm.system_days_of_week = [];
  vm.days_of_week = [];
  vm.str_days_of_week = I18n.t("date.day_names");
  vm.times = [];
  vm.year = "";
  vm.month = "";
  vm.week = "";
  vm.hasRecord = false;
  vm.currentDate = "";
  vm.current = "";
  vm.enableCall = true;
  vm.interview_type = false;
  vm.timeOptions = [];
  vm.fromSelected = "";
  vm.toSelected = "";
  vm.check = true;
  vm.weekOptions = [1,2,3,4,5,6,7,8,9,10,11,12];
  vm.weekSelected = 1;
  vm.excludingHoliday = false;
  vm.roomOptions = [1];
  vm.roomSelected = 1;
  vm.candidateOptions = [1,2,3,4,5,6,7,8,9,10];
  vm.candidateSelected = 1;
  vm.durationOptions = [10,20,30,40,50,60];
  vm.durationSelected = 10;
  vm.daySelected = ["","","","","","",""];
  vm.interviews = {};
  vm.currentId = null;
  vm.interview = {};
  vm.saveOption = 1;
  vm.tableHeight = 400;
  vm.change = 0;
  vm.multiTime = false;
  vm.adminSelected = [];
  vm.currentStaff = {};
  vm.filter_type = 2;
  vm.interviews_applied = [];
  vm.toasterTimeout = 5000;

  vm.init = function() {
    vm.callData(vm.current, vm.month, vm.week);
  };

  vm.setTableStyle = function() {
    return "height: " + vm.tableHeight + "px;";
  };

  vm.reset = function() {
    vm.fromSelected = vm.times[0];
    vm.toSelected = vm.times[0];
    vm.check = true;
    vm.daySelected = ["","","","","","",""];
    vm.weekSelected = 1;
    vm.excludingHoliday = false;
    vm.candidateSelected = 1;
    vm.roomSelected = 1;
    vm.durationSelected = 10;
    $(".day").removeClass("selected");
    $(".day").next().val("");
  };

  vm.params = function() {
    var params = {
      interview_type: vm.interview_type,
      interview_date: vm.current,
      from: vm.fromSelected,
      to: vm.toSelected,
      quantity_room: vm.roomSelected,
      max_candidate: vm.candidateSelected,
      duration: vm.durationSelected,
      days: vm.daySelected,
      weeks: vm.weekSelected,
      include_holiday: !vm.excludingHoliday,
      multi: vm.check,
      multi_time: vm.multiTime
    };

    return params;
  };

  vm.updateParams = function() {
    var params = {
      interview_date: vm.current,
      from: vm.fromSelected,
      to: vm.toSelected,
      interviews_rooms: vm.interview.interviews_rooms,
      option: vm.saveOption
    };

    return params;
  };

  vm.callData = function(current, month, week) {
    if (vm.enableCall) {
      vm.enableCall = false;
      interviewService.getInterviews({
        current: current,
        month: month,
        week: week,
        interview_type: vm.interview_type,
        change: vm.change,
        filter_type: vm.filter_type
      }).then(function(res) {
        if (res.status == 200) {
          vm.change = 0;
          vm.system_days_of_week = res.data.days_of_week;
          vm.days_of_week = formatDaysOfWeek(vm.system_days_of_week)
          vm.times = res.data.times;
          vm.year = res.data.year;
          vm.month = res.data.month;
          vm.current = res.data.current;
          vm.week = res.data.week;
          vm.rooms = JSON.parse(res.data.rooms);
          vm.interviews = res.data.interviews;
          vm.hasRecord = true;
          vm.enableCall = true;
          vm.timeOptions = vm.times.slice();
          vm.roomOptions = Array.apply(null, {length: vm.rooms.length}).map(function(_v,k){return k+1;});
          vm.tableHeight = $(".wrapper").height() - 490;
          if (vm.currentDate == "") {
            vm.currentDate = vm.current;
          }
        }
      });
    }
  };

  vm.setOptions = function(from, to) {
    vm.fromSelected = from;
    vm.toSelected = to;
  };

  vm.setCurrent = function(date) {
    return (vm.year + "/" + date) == vm.currentDate ? "current" : "";
  };

  vm.next = function() {
    vm.change = 1;
    vm.callData(vm.current, vm.month, vm.week + 1);
  };

  vm.prev = function() {
    vm.change = 1;
    vm.callData(vm.current, vm.month, vm.week - 1);
  };

  vm.nextYear = function() {
    vm.change = 1;
    vm.callData(vm.current, vm.month + 1, vm.week);
  };

  vm.prevYear = function() {
    vm.change = 1;
    vm.callData(vm.current, vm.month - 1, vm.week);
  };

  vm.setType = function(type) {
    vm.interview_type = type == 1;
    vm.callData(vm.current, vm.month, vm.week);
  };

  vm.setStyle = function(interview) {
    return "height: " + interview.height*vm.height + "px;";
  };

  vm.setMaxHeight = function(interview) {
    return "max-height: " + (interview.height*vm.height - vm.height + 5) + "px;";
  };
  vm.setStaffName = function(room, index) {
    return room.staff_apply_interviews.length > index;
  };

  vm.addOne = function(keyDate, from) {
    vm.multiTime = false;
    vm.add(keyDate, from, vm.timeOptions[vm.timeOptions.length-1]);
  };

  vm.addMulti = function(keyDate) {
    vm.multiTime = true;
    vm.add(keyDate, vm.timeOptions[0], vm.timeOptions[vm.timeOptions.length-1]);
  };

  vm.add = function(keyDate, from, to) {
    vm.current = vm.system_days_of_week[keyDate];
    vm.setOptions(from, to);
    $("#form-modal").modal("show");
  };

  vm.edit = function(interview, keyDate) {
    vm.currentId = interview.id;
    vm.current = vm.system_days_of_week[keyDate];
    vm.setOptions(interview.start_time, interview.end_time);
    vm.show();
    $("#details-modal").modal("show");
  };

  vm.selectDays = function($event, index) {
    var element = $($event.currentTarget);
    var input = element.next();
    if (input.val() == "") {
      input.val(index);
      element.addClass("selected");
      vm.daySelected[index] = index;
    } else {
      input.val("");
      element.removeClass("selected");
      vm.daySelected[index] = "";
    }
  };

  vm.setClass = function(count, interview, index) {
    var column = 12;
    var columnR = 12 - 1;
    var offset = index * Math.floor(columnR/count);
    if(count > 0){
      column = columnR -  offset;
    }
    column = column + " " + interview.interview_type + " col-md-offset-" + offset;
    return "col-md-" + column;
  };

  vm.showTime = function(time){
    var show = time.indexOf(":00") >= 0;
    return show;
  };

  vm.create = function() {
    if (vm.enableCall) {
      vm.enableCall = false;
      interviewService.create(vm.params()).then(function(res) {
        if (res.status == 200 && res.data.status) {
          vm.reset();
          $("#form-modal").modal("hide");
          vm.enableCall = true;
          vm.callData(vm.current, vm.month, vm.week);
        }
      });
    }
  };

  vm.update = function() {
    if (vm.enableCall) {
      vm.enableCall = false;
      interviewService.update(vm.currentId, vm.updateParams()).then(function(res) {
        if (res.status == 200 && res.data.status) {
          $("#details-modal").modal("hide");
          vm.enableCall = true;
          vm.callData(vm.current, vm.month, vm.week);
          vm.saveOption = 1;
          return
        }
        vm.enableCall = true
        toaster.pop("error", "", res.data.errors);
      });
    }
  };

  vm.checkUpdate = function() {
    if (vm.saveOption == 1) {
      vm.update();
    } else {
      if (vm.enableCall) {
        vm.enableCall = false;
        interviewService.checkDestroy(vm.currentId, {option: vm.saveOption, interviews_rooms: vm.interview.interviews_rooms}).then(function(res) {
          if (res.status == 200) {
            vm.enableCall = true;
            vm.interviews_applied = res.data.interviews;
            if (vm.interviews_applied.length > 0) {
              $("#modal-confirm-delete-rooms").modal("show");
            } else {
              vm.update();
            }
          }
        });
      }
    }
  };


  vm.destroy = function() {
    if (vm.enableCall) {
      vm.enableCall = false;
      interviewService.destroy(vm.currentId, {option: vm.saveOption}).then(function(res) {
        if (res.status == 200 && res.data.status) {
          $("#details-modal").modal("hide");
          vm.enableCall = true;
          vm.callData(vm.current, vm.month, vm.week);
          vm.saveOption = 1;
        }
      });
    }
  };

  vm.checkDestroy = function() {
    if (vm.enableCall) {
      vm.enableCall = false;
      interviewService.checkDestroy(vm.currentId, {option: vm.saveOption}).then(function(res) {
        if (res.status == 200) {
          vm.enableCall = true;
          vm.interviews_applied = res.data.interviews;
          if (vm.interviews_applied.length > 0) {
            $("#modal-confirm-delete-options").modal("show");
          } else {
            vm.destroy();
          }
        }
      });
    }
  };

  vm.show = function() {
    if (vm.enableCall) {
      vm.enableCall = false;
      interviewService.show(vm.currentId).then(function(res) {
        if (res.status == 200) {
          vm.enableCall = true;
          vm.interview = res.data;
          vm.setAdmin();
        }
      });
    }
  };

  vm.changeAdmin = function(index) {
    if (vm.enableCall) {
      vm.enableCall = false;
      if (vm.adminSelected[index]) {
        vm.interview.interviews_rooms[index].admin = vm.interview.admin;
      } else {
        delete vm.interview.interviews_rooms[index].admin;
      }
      interviewService.updateAdmin(vm.currentId, vm.interview.interviews_rooms[index]).then(function(res) {
        if (res.status == 200 && res.data.status) {
          vm.enableCall = true;
          vm.setAdmin();
          vm.callData(vm.current, vm.month, vm.week);
        }
      });
    }
  };

  vm.setAdmin = function() {
    vm.adminSelected = vm.interview.interviews_rooms.map(function(interviews_room){
      return !!interviews_room.admin;
    });
  };

  vm.hasAdmin = function(interview) {
    var hasAdmin = false;
    hasAdmin = interview.interviews_rooms.some(function(interviews_room){
      if (!!interviews_room.admin && interviews_room.admin.id == interview.admin.id) {
        return true;
      }
    });
    return hasAdmin;
  };

  vm.missingAdmin = function(interview) {
    var missingAdmin = false;
    missingAdmin = interview.interviews_rooms.some(function(interviews_room){
      if (!interviews_room.admin && interviews_room.staff_apply_interviews_count > 0) {
        return true;
      }
    });
    return missingAdmin;
  };

  vm.appliedsData = function(interview) {
    var applieds_names = []
    var admin_names = []
    interview.interviews_rooms.some(function(interviews_room){
      if (interviews_room.staff_apply_interviews_count > 0) {
       applieds_names.push(interviews_room.staff_and_prefecture_names);
      }
      if (!!interviews_room.admin) {
        admin_names.push(interviews_room.admin.name);
      }
    });
    var names = []
    var applieds = applieds_names.join(", <br>");
    if (!!applieds){names.push(applieds);}
    var admins = admin_names.join(", <br>");
    if (!!admins){names.push(admins);}
    return $sce.trustAsHtml(names.join(", <br>"));
  }

  vm.checkDisabled = function(room) {
    if (!!room.admin) {
      if(room.admin.id == vm.interview.admin.id) {
        return false;
      }
      else{
        return true;
      }
    }

    return vm.hasAdmin(vm.interview);
  };

  vm.destroyCandidate = function(id) {
    var room = vm.interview.interviews_rooms[id];
    if(room.max_candidate > 1 && room.max_candidate > room.staff_apply_interviews.length) {
      room.max_candidate = room.max_candidate - 1;
    }
  };

  vm.addCandidate = function(id) {
    var room = vm.interview.interviews_rooms[id];
    if(room.max_candidate < 10) {
      room.max_candidate = room.max_candidate + 1;
    }
  };

  vm.confirmUpdate = function() {
    if (vm.enableCall) {
      $("#save-options-modal").modal("show");
    }
  };

  vm.confirmDelete = function() {
    if (vm.enableCall) {
      $("#delete-options-modal").modal("show");
    }
  };

  vm.showStaff = function(room, index) {
    vm.currentRoom = room;
    vm.currentStaff = room.staff_apply_interviews[index].staff;
    vm.currentStaffApplyInterviewIdx = index;
    $("#staff-details-modal").modal("show");
  };

  vm.addRoom = function() {
    for(var i=0; i < vm.interview.interviews_rooms.length; i++){
      if (!!vm.interview.interviews_rooms[i].destroy) {
        vm.interview.interviews_rooms[i].destroy = false;
        return;
      }
    }
    var roomIds = vm.interview.interviews_rooms.map(function(room){return room.room_id;});
    for(var i=0; i < vm.interview.rooms.length; i++){
      if(roomIds.indexOf(vm.interview.rooms[i].id) < 0){
        var room = {
          max_candidate: 1,
          room_id: vm.interview.rooms[i].id,
          room_name: vm.interview.rooms[i].name,
          room_url: vm.interview.rooms[i].url,
          staff_apply_interviews: []
        };
        vm.interview.interviews_rooms[vm.interview.interviews_rooms.length] = room;
        break;
      }
      
    }
  };

  vm.destroyRoom = function(index) {
    var id = vm.interview.interviews_rooms[index].id;
    if (!!id) {
      vm.interview.interviews_rooms[index].destroy = true;
    } else {
      vm.interview.interviews_rooms.splice(index, 1);
    }
  };

  vm.cancelStaff = function() {
    var params = {
      room_id: vm.currentRoom.id,
      staff_id: vm.currentStaff.id,
    }
    interviewService.cancelStaff(params).then(function(res) {
      if (res.status == 200 && res.data.status) {
        _.forEach(vm.interview.interviews_rooms, function(room) {
          if (room.id == vm.currentRoom.id) {
            room.staff_apply_interviews.splice(vm.currentStaffApplyInterviewIdx, 1);
          }
        });
        vm.callData(vm.current, vm.month, vm.week);
      }
    });
  }

  function formatDaysOfWeek(originalDates) {
    var formattedDates = originalDates.map(function (date) {
      return formatDateAndMonth(date);
    });

    return formattedDates;
  }

  function formatDateAndMonth(dateString) {
    var dateComponent = dateString.split('/');
    var month = dateComponent[1];
    var day = dateComponent[2];

    return month + '/' + day;
  }
}