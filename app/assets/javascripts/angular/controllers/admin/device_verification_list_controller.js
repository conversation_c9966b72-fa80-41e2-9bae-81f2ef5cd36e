"use strict";

angular.module("adminApp")
  .controller("DeviceVerificationListController", DeviceVerificationListController);
DeviceVerificationListController.$inject = ["$location", "deviceVerificationService", "toaster"];

function DeviceVerificationListController($location, deviceVerificationService, toaster) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  vm.params = $location.search();
  vm.toasterTimeout = 6200;
  var SEARCH_CONDITION_TYPE = "admin_device_verification_search_conditions";
  vm.statusParam = {id: "", status: ""};
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  vm.init = function() {
    var lastCondition = angular.element(".device-verification-wrap").data("last-condition");
    var existLastCondition = false;

    if (!_.isEmpty(lastCondition)) {
      vm.params = lastCondition;
      existLastCondition = true;
    }
    vm.params.per_page = _.toInteger(vm.params.per_page) || vm.perPageSettings[0];
    vm.currentParams = vm.params;
    vm.refresh();
  }

  vm.refresh = function(isSaveCondition) {
    vm.params = isSaveCondition ? vm.currentParams : vm.params;
    deviceVerificationService.getDeviceList(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.device_verifications.length);
      vm.noRecord = !vm.hasRecord;
      if (isSaveCondition) {
        var searchConditionParams = _.omit(vm.params, "page", "limit");
        deviceVerificationService.createSearchCondition({search: searchConditionParams, search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
        });
      }
      $location.search(vm.params).replace();
    }, function (error) {
    });
  };

  vm.changePerPage = function() {
    vm.refresh(true);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".device-verification-list-result").offset().top
    }, 1000);
  };

  vm.changeStatusDevice = function() {
    deviceVerificationService.updateStatus(vm.statusParam.id, {status_id: vm.statusParam.status}).then(function(res) {
      if (res.data.status) {
        toaster.pop("success", "", res.data.message);
      } else {
        toaster.pop("error", "", res.data.message);
      }
      vm.refresh();
    });
  }
}
