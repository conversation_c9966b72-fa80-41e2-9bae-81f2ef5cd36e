"use strict";

angular.module("adminApp").controller("DashboardController", DashboardController);
DashboardController.$inject = ["dashboardService", "$location"];

function DashboardController(dashboardService, $location) {
  var vm = this;
  vm.params = $location.search();
  vm.hasRecord = false;
  vm.search_dispatch = true;
  vm.search_contract = true;

  moment.locale(I18n.locale);

  vm.init = function() {
    setCurrentDate();
    vm.params.report_date = moment(vm.current_report_date).format(FULL_DATE_FORMAT_HYPHEN);
    vm.params.current_department_id = vm.params.current_department_id ? vm.params.current_department_id : "";
    _.merge(vm.params, {report_date: vm.params.report_date, current_department_id: vm.params.current_department_id.split(",")});
    vm.search();
  };

  vm.search = function() {
    vm.params.report_date = moment(vm.current_report_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = formatSearchParams(angular.copy(vm.params));
    dashboardService.getDataSumReport(params).then(function(res) {
      angular.extend(vm, res.data);
      vm.reportMonth = vm.report_month + "月";
      $location.search(params).replace();
      if (vm.department_sums.length > 0) {
        vm.hasRecord = true;
      }
    });
  };

  var setCurrentDate = function() {
    var reportDate = vm.params.report_date;
    var currentDate = moment(reportDate);
    if (isNaN(Date.parse(reportDate)) || !reportDate){
      currentDate = moment(new Date());
    }
    var year = currentDate.format("YYYY");
    var month = currentDate.format("MM");
    vm.current_report_date = year + "/" + month;
    $("#report-date").datepicker("setDate", vm.current_report_date).trigger("change");
  };

  vm.blurDashboardDate = function() {
    if (!moment(vm.current_report_date).isValid()) {
      setCurrentDate();
    }
  };

  function formatSearchParams(params) {
    params.report_date = moment(vm.current_report_date).format(FULL_DATE_FORMAT_HYPHEN);
    params.current_department_id = params.current_department_id.join(",");
    return params;
  };

  vm.formatDate = function(date, isWeekDay) {
    if (!moment(date).isValid()) return;
    var displayDate = moment(date);
    if (isWeekDay) return displayDate.format("ddd");
    return moment(date).format("D");
  };

  vm.displayData = function(value, isPercent, isHide) {
    if (typeof value === "undefined" || value == null || isHide) return "-";
    if (isPercent) return value + "%";
    return value;
  };

  vm.displayTotalData = function(dispatch, contract, isPercent, isHide) {
    var dispatchEmpty = typeof dispatch === "undefined" || dispatch == null;
    var contractEmpty = typeof contract === "undefined" || contract == null;
    if ((dispatchEmpty && contractEmpty) || isHide) return "-";

    var dispatchValue = dispatchEmpty ? 0 : dispatch;
    var contractValue = contractEmpty ? 0 : contract;

    if (isPercent) {
      return round((dispatchValue + contractValue) / 2) + "%";
    } else {
      return round(dispatchValue + contractValue);
    }
  };

  vm.isDateToday = function(date) {
    if (typeof date === "undefined" || !moment(date).isValid()) return;
    var comparedDate = moment(date).startOf("day");
    var now = moment().startOf("day");
    return now.diff(comparedDate, "days") == 0;
  };

  vm.exportDataSum = function(e) {
    vm.params.report_date = moment(vm.current_report_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = formatSearchParams(angular.copy(vm.params));
    if (vm.search_dispatch && !vm.search_contract) {
      params["type"] = "dispatch";
    } else if (!vm.search_dispatch && vm.search_contract) {
      params["type"] = "contract";
    } else {
      params["type"] = "all";
    }
    var reportDate = angular.copy(vm.params.report_date);
    $.asyncDownload(e, "dashboards", JSON.stringify(params), "xlsx", {report_date: reportDate});
  };

  function round(num) {
    return Math.round(num * 100) / 100;
  };
}
