'use strict';

angular.module('adminApp').controller('LocationTabController', LocationTabController);
LocationTabController.$inject = ['$location', 'locationService'];

function LocationTabController($location, locationService) {
  var vm = this;
  var $tabLocation = angular.element('#tab-location');
  var corporationGroupId = $tabLocation.data('corporation-group-id');
  vm.hasRecord = vm.noRecord = false;
  vm.currentPage = 1;

  vm.params = {
    corporation_id: $tabLocation.data('corporation-id'),
    page: 1
  };

  if (_.isNumber(corporationGroupId)) {
    _.merge(vm.params, {corporation_group_id: corporationGroupId});
  }

  vm.refresh = function() {
    vm.params.page = vm.currentPage;
    locationService.getLocation(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.locations.length);
      vm.noRecord = !vm.hasRecord;
      vm.params.limit = res.data.pagination_limit;
    }, function(error) {
    });
  };
}
