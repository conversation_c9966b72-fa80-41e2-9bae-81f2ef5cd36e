"use strict";

angular.module("adminApp")
  .controller("CorporationGroupTagController", CorporationGroupTagController);
CorporationGroupTagController.$inject = ["$location", "corporationGroupService", "adminService", "toaster"];

function CorporationGroupTagController($location, corporationGroupService, adminService, toaster) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  vm.currentPage = 1;
  vm.params = {};
  vm.toasterTimeout = 6200;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  var SEARCH_CONDITION_TYPE = "admin_corporation_group_tag_search_conditions";

  vm.initCorporationGroup = function(groupTagId) {
    vm.groupTagId = groupTagId;
    vm.refresh();
  };

  vm.search = function(isResetPage, isSaveCondition) {
    if (isResetPage) {
      vm.currentPage = 1;
    };
    vm.params.per_page = vm.params.per_page || vm.perPageSettings[0];
    vm.params.page = vm.currentPage;
    corporationGroupService.getListCorporationGroupTag(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.corporation_group_tags.length);
      vm.noRecord = !vm.hasRecord;
      if (isSaveCondition) {
        adminService.createSearchCondition({search: _.omit(vm.params, "page"), search_condition: SEARCH_CONDITION_TYPE});
      };
    });
  };

  vm.changePerPage = function() {
    vm.search(false, true);
    vm.scrollToResult("group-tag-list-result");
  };

  vm.changePerPageCorporationGroup = function() {
    vm.refresh(false);
    vm.scrollToResult("corporation-group-list-result");
  };

  vm.scrollToResult = function(className) {
    $("html, body").animate({
      scrollTop: $("." + className).offset().top
    }, 1000);
  };

  vm.deleteCorporationGroupTag = function(corporationGroupTagId) {
    corporationGroupService.deleteCorporationGroupTag(corporationGroupTagId).then(function(res) {
      var data = res.data;
      if (data.status) {
        toaster.pop("success", "", data.message);
        vm.search(false);
      } else {
        toaster.pop("error", "", data.message);
      };
    });
  };

  vm.refresh = function(isResetPage) {
    vm.params.group_tag_id = vm.groupTagId;
    vm.params.corporation_id = angular.element("#corporation-group").data("corporation-id");
    if (isResetPage) {
      vm.currentPage = 1;
    };
    vm.params.per_page = vm.params.per_page || vm.perPageSettings[0];
    vm.params.page = vm.currentPage;
    corporationGroupService.searchCorporationGroup(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.corporation_groups.length);
      vm.noRecord = !vm.hasRecord;
    }, function(error) {
    });
  };

  vm.init = function() {
    adminService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
      };
      vm.search(false);
    });
  };

  vm.initDestroyData = function(groupTag) {
    vm.corporation_group_tag_id = groupTag.id;
    vm.hasGroup = groupTag.display_corporation_group_name !== I18n.t("admin.corporation_group_tags.index.no_corporation_group");
  };
}
