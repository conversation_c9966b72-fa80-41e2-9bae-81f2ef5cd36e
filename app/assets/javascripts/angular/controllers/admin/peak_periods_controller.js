'use strict';

angular.module('adminApp')
  .controller('PeakPeriodsController', PeakPeriodsController);
PeakPeriodsController.$inject = ['peakPeriodsService'];

function PeakPeriodsController(peakPeriodsService) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  vm.init = function () {
    vm.peak_periods = [];
    vm.page = 1;
    vm.per_page = 10;
    vm.search();
  }
  var createModal = $("#add-new-peak-period")

  vm.search = function() {
    vm.params = {
      page: vm.page,
      per_page: vm.per_page,
      year: vm.peak_period_year
    };

    peakPeriodsService.getPeakPeriods(vm.params).then(function(res) {
      vm.peak_periods = res.data.data;
      vm.total_count = res.data.total_count;
      vm.hasRecord = Boolean(vm.peak_periods.length);
    });
  }

  vm.createPeakPeriod = function () {
    if (!vm.new_peak_period.name || !vm.new_peak_period.target_date) {
      return;
    }

    showSpinner();
    peakPeriodsService.createPeakPeriod(vm.new_peak_period).then(function(res) {
      if (res.data.status) {
        toastr.success(res.data.message);
        createModal.modal('hide');
        vm.search();
      }
      else {
        vm.errors = res.data.message;
      }
    }, function(error) {
      createModal.modal('hide');
      toastr.error(error.statusText);
    }).then(function() {
      hideSpinner();
    });
  }

  vm.destroyPeakPeriod = function (peakPeriodId) {
    showSpinner();
    peakPeriodsService.destroyPeakPeriod(peakPeriodId).then(function (res) {
      if (res.data.status) {
        toastr.success(res.data.message);
        vm.search();
        $("#modal-delete-peak-period").modal("hide");
      }
      else {
        toastr.error(res.data.message);
      }
    }, function (error) {
      toastr.error(error.statusText);
    }).then(function() {
      hideSpinner();
    });
  }

  createModal.on('hidden.bs.modal', function () {
    vm.new_peak_period = {};
    vm.errors = {};
  });

  $('#peak-period-year').datepicker({
    format: "yyyy",
    viewMode: "years",
    minViewMode: "years",
    autoclose: true
  });

  $('#target-date-datepicker').datepicker({
    format: "yyyy/mm/dd",
    autoclose: true
  });

  vm.confirmDelete = function (peakPeriodId) {
    vm.selectedPeakPeriodId = peakPeriodId;
    $("#modal-delete-peak-period").modal("show");
  };

  vm.openCreateModal = function () {
    createModal.modal("show");
  }

  function showSpinner() {
    $("#spinner").removeClass("ng-hide");
  };

  function hideSpinner() {
    $("#spinner").addClass("ng-hide");
  }
}