"use strict";

angular.module("adminApp")
  .controller("EntriesListController", EntriesListController);
EntriesListController.$inject = ["$location", "entryListService", "checkValidDateFunction",
  "adminRegistrationCodeService", "toaster"];

function EntriesListController($location, entryListService, checkValidDateFunction,
  adminRegistrationCodeService, toaster) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  vm.currentPage = 1;
  vm.statusIds = angular.element(".staff-list-table").data("status-ids");
  vm.staffSelect = {};
  vm.params = $location.search();
  vm.admins = angular.element(".wrap-content-staff").data("admins") ;
  vm.qrCodeSize = 120;
  vm.toasterTimeout = 6200;
  vm.isSelected = false;
  vm.perPageSettings = [10, 15, 30, 50];
  var SELECT_FIELDS = ["admin_id"];
  var SEARCH_CONDITION_TYPE = "admin_staff_search_conditions";
  var DATETIME_FIELDS = ["started_register", "ended_register"];
  var ENTRY = "entry";

  vm.init = function() {
    entryListService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE, search_type: ENTRY})
      .then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
        var needJoinToArray = ["current_department_id"];
        _.forEach(needJoinToArray, function(attr) {
          if(!vm.params[attr]) vm.params[attr] = "";
          vm.params[attr] = vm.params[attr].split(",");
        });
      };

      angular.element(document).ready(function() {
        $("#current_department_id").trigger("change");
      });

      DATETIME_FIELDS.forEach(function(attr) {
        if (vm.params[attr]) {
          $("[name='" + attr + "']").datepicker("setDate", vm.params[attr]);
        };
      });
      vm.refresh();
    });
  };

  vm.refresh = function(resetPage) {
    if (_.isEmpty(vm.params)) {
      vm.entry_status = true;
      vm.params.status_id = "entry";
      vm.params.admin_id = angular.element(".wrap-content-staff").data("admin-id").toString();
      vm.params.per_page = 10;
    } else {
      var statuses = {
        "entry": "entry_status",
        "pic_confirm": "pic_confirm_status",
        "export": "export_status",
        "op_confirm": "op_confirm_status",
        "rejected": "rejected_status"
      }
      _.forEach(vm.params.status_id.split(","), function(status) {
        var statusName = statuses[status];
        vm[statusName] = true;
      });
    }
    if (resetPage) {
      vm.params.page = 1;
    }
    vm.selectAllStaff = false;
    triggerChangeSelect2();
    var search_params = formatParamsBeforeSearch(angular.copy(vm.params));
    entryListService.getStaff({search: search_params}).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.staffs.length);
      vm.noRecord = !vm.hasRecord;
      $location.search(search_params).replace();
    }, function(error) {
    });
  };

  vm.search = function(resetPage) {
    var search_condition_params = formatParamsBeforeSearch(angular.copy(_.omit(vm.params, "page", "limit")));
    entryListService.createSearchCondition({search: search_condition_params, search_condition: SEARCH_CONDITION_TYPE, search_type: ENTRY});
    vm.refresh(resetPage);
  };

  function formatParamsBeforeSearch(params) {
    params.current_department_id = (params.current_department_id || []).join(",");
    return params;
  };

  vm.changePerPage = function() {
    vm.search();
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".staff-search-result").offset().top
    }, 1000);
  };

  vm.changeStatusId = function() {
    var validStatus = [];
    vm.status = {
      "entry_status": "entry",
      "pic_confirm_status": "pic_confirm",
      "export_status": "export",
      "op_confirm_status": "op_confirm",
      "rejected_status": "rejected"
    }
    _.forEach(vm.status, function(key, value) {
      if (vm[value]) {
        validStatus.push(vm.status[value]);
      }
    });
    vm.params.status_id = validStatus.toString();
  }

  vm.checkAll = function() {
    vm.selectAllStaff = !vm.selectAllStaff;
    angular.forEach(vm.staffs, function(staff) {
      staff.select = vm.selectAllStaff;
    });
    vm.checkSelected(true);
  };

  vm.checkSelected = function(isChecked) {
    var selectedItems = _.find(vm.staffs, {select: true});
    if (selectedItems) {
      vm.isSelected = true;
    } else {
      vm.isSelected = false;
    };
    if (vm.selectAllStaff && !isChecked) {
      vm.selectAllStaff = false;
    };
  };

  vm.openDestroyStaffModal = function(id) {
    var selectedStaffs = vm.staffs.filter(function(staff) {
      return staff.select;
    });
    var staffIds = selectedStaffs.map(function(staff) {
      return staff.id;
    });
    angular.element(".btn-delete-staff").attr("staff-id", id);
    if (id) {
      vm.confirmMessage = I18n.t("admin.staff.operate_item.confirm_manual_delete");
    } else {
      id = staffIds;
      vm.confirmMessage = I18n.t("admin.staff.operate_item.confirm_batch_delete");
    }
    angular.element(".delete-staff-modal").modal("show");
  }

  vm.deleteStaff = function() {
    $(".disable-submit-btn").prop("disabled", true);
    var destroyParams = [];
    var idDeleteStaff = angular.element(".btn-delete-staff").attr("staff-id");
    if (idDeleteStaff) {
      destroyParams.push(idDeleteStaff);
    } else {
      angular.forEach(vm.staffs, function(staff) {
        if (staff.select) {
          destroyParams.push(staff.id);
        }
      });
      vm.isSelected = false;
    }

    entryListService.deleteStaff({staff_ids: destroyParams.join(",")}).then(function(res) {
      angular.element(".delete-staff-modal").modal("hide");
      vm.refresh(true);
      toaster.pop("success", "", I18n.t("admin.staff.action_status.delete_success"));
      $(".disable-submit-btn").prop("disabled", false);
    });
  }

  vm.changeStatus = function() {
    var statusParams = [];
    angular.forEach(vm.staffs, function(staff) {
      if (staff.select) {
        var statusID = angular.element(".staff-status-" + staff.id).val();
        statusParams.push({id: staff.id, status_id: statusID});
      }
    });

    entryListService.changeStatus({status: statusParams}).then(function(res) {
      angular.element(".delete-staff-modal").modal("hide");
      vm.refresh(true);
      toaster.pop("success", "", I18n.t("admin.staff.action_status.update_success"));
    });
  }

  vm.showURLRegister = function() {
    $("#show-url-register").modal("show");
  }

  vm.printRegistrationCode = function() {
    $("#registrationQrCode canvas").attr("id", "registration-code-canvas");
    var txtContent = $("#show-url-register .modal-body .txt-content").html();
    var title = $("#show-url-register .modal-title").text().trim();
    var canvasElm = document.getElementById("registration-code-canvas");
    var imgSrc = !!canvasElm ? canvasElm.toDataURL() : "";
    adminRegistrationCodeService.printRegistrationCode(imgSrc, txtContent, title);
  };

  vm.displayStatusId = function(staff) {
    var staffStatus = _.find(vm.statusIds, function(status) {
      return status.key === staff.status_id;
    });
    return _.isUndefined(staffStatus) ? "" : staffStatus.name;
  };

  function triggerChangeSelect2() {
    SELECT_FIELDS.forEach(function(field) {
      $("#" + field).val(vm.params[field]).trigger("change.select2");
    });
  };

  vm.clearSearchCondition = function() {
    // Reset params
    vm.params = angular.copy({
      per_page: vm.perPageSettings[0],
      status_id: ""
    });

    // Reset statuses
    var statuses = vm.status || {};
    Object.keys(statuses).forEach(function(status) {
      vm[status] = false;
    });

    // Reset Select2 state
    setTimeout(function() {
      angular.element("select.select2, select.select2-multiple").trigger("change");
    }, 10);

    // Refresh result
    vm.search(true);
    vm.scrollToResult();
  }

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  }
}
