"use strict";

angular.module("adminApp").controller("ImportLocationBranchController", ImportLocationBranchController);
ImportLocationBranchController.$inject = ["$scope", "$window"];

function ImportLocationBranchController($scope, $window) {
  var vm = this;
  vm.$scope = $scope;
  vm.isImporting = false;
  vm.isImported = false;
  vm.hasError = false;

  vm.fileChanged = function(event) {
    vm.$scope.$apply(function() {
      vm.isImporting = false;
      vm.isImported = false;
      vm.message = "";
      vm.errors = "";
      vm.hasError = false;
      vm.isChoseFile = !_.isNil(event.target.files[0]);
    });
  };

  vm.import = function() {
    vm.isImporting = true;
    $("#spinner").removeClass("ng-hide");
    vm.errors = "";
    vm.message = "";
    vm.hasError = false;
    $.ajax({
      url: "/locations/import_branches",
      method: "POST",
      dataType: "json",
      data: new FormData($("#import-location-branch")[0]),
      processData: false,
      contentType: false,
      success: function(response) {
        $("#imported-message").removeClass("d-none");
        $("#spinner").addClass("ng-hide");
        vm.$scope.$apply(function() {
          vm.isImported = true;
          vm.isImporting = false;
          vm.hasError = !response.status;
          vm.message = response.messages[0];
          vm.errors = response.errors;
        });
      }
    })
  };
}
