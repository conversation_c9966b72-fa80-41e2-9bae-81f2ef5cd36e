"use strict";

angular.module("adminApp").controller("DashboardDetailController", DashboardDetailController);
DashboardDetailController.$inject = ["dashboardService", "$location", "toaster", "$scope"];

function DashboardDetailController(dashboardService, $location, toaster, $scope) {
  var vm = this;
  vm.params = $location.search();
  vm.hasRecord = false;

  moment.locale(I18n.locale);

  vm.init = function() {
    vm.params.report_date = vm.params.report_date ? moment(vm.params.report_date) : moment(new Date());
    vm.params.department_id = vm.params.department_id ? vm.params.department_id : "";
    _.merge(vm.params, {report_date: vm.params.report_date, department_id: vm.params.department_id.split(",")});
    vm.search();
  };

  vm.search = function() {
    var params = formatSearchParams(angular.copy(vm.params));
    dashboardService.getDataSumDetail(params).then(function(res) {
      angular.extend(vm, res.data);
      vm.reportMonth = vm.report_month + "月";
      $location.search(params).replace();
      if (vm.data_sums.length > 0) {
        vm.hasRecord = true;
      }
    });
  };

  function formatSearchParams(params) {
    params.report_date = moment(vm.params.report_date).format(FULL_DATE_FORMAT_HYPHEN);
    params.department_id = params.department_id.join(",");
    return params;
  }

  vm.formatDate = function(date, isWeekDay) {
    if (!moment(date).isValid()) return;
    var displayDate = moment(date);
    if (isWeekDay) return displayDate.format("ddd");
    return moment(date).format("D");
  }

  vm.displayData = function(value, isPercent, isHide) {
    if (typeof value === "undefined" || value == null || isHide) return "-";
    if (isPercent) return value + "%";
    return value;
  }

  vm.isDateToday = function(date) {
    if (typeof date === "undefined" || !moment(date).isValid()) return;
    var comparedDate = moment(date).startOf("day");
    var now = moment().startOf("day");
    return now.diff(comparedDate, "days") == 0;
  }
}
