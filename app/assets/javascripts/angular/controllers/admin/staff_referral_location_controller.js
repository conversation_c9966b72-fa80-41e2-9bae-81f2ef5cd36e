'use strict';

angular.module('adminApp').controller('StaffReferralLocationController', StaffReferralLocationController);
StaffReferralLocationController.$inject = ['$location', 'staffReferralLocationService', 'checkValidDateFunction', 'toaster', "$filter"];

function StaffReferralLocationController($location, staffReferralLocationService, checkValidDateFunction, toaster, $filter) {
  var vm = this;
  var requestParams = $location.search();
  var searchParamKeys = ["staff_id", "location_id", "effected_date_from", "effected_date_to", "joining_date_from", "joining_date_to"];
  vm.hasRecord = vm.noRecord = false;
  vm.params = {};
  vm.create_params = {};
  vm.create_locations = [];
  vm.invoiceTargets = [
    {id: "lawson_invoice", selected: false, name: "清算書"},
    {id: "separate_invoice", selected: false, name: "個別"}
  ];
  vm.search_locations = [];
  vm.params.page = requestParams.page;
  var MULTI_SELECT_FIELDS = ["staff_id", "corporation_id", "location_id"];
  var DATETIME_FIELDS = ["effected_date_from", "effected_date_to", "joining_date_from", "joining_date_to"];
  var DOM_IDS_TO_CREATE = ["create_by_staff_id", "create_by_location_id", "create_by_corporation_id"];
  var DATE_DOM_IDS_TO_CREATE = ["create_by_effected_date", "create_by_joining_date"];
  var REQUIRE_ATTRS_TO_CREATE = ["staff_id", "location_id", "effected_date", "fee"];
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.referralFeeOptions = [50000];

  _.forEach(searchParamKeys, function(searchParamKey) {
    vm.params[searchParamKey] = requestParams[searchParamKey];
  });

  vm.search = function(resetPage) {
    if (resetPage) {vm.params.page = 1;};
    vm.params.per_page = vm.params.per_page || vm.perPageSettings[0];
    var searchParams = formatParamsBeforeSearch(angular.copy(vm.params));
    staffReferralLocationService.getList(searchParams).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.staff_referral_locations.length);
      vm.noRecord = !vm.hasRecord;
    }, function(error) {
    });
  };

  function formatParamsBeforeSearch(params) {
    params["invoice_target"] = vm.invoiceTargets.filter(function(field) {
      return !!field.selected;
    }).map(function(field){return field.id;}).join(",");
    _.forEach(MULTI_SELECT_FIELDS, function(attr) {
      if (!_.isUndefined(params[attr])) {
        if (Array.isArray(params[attr])) {
          params[attr] = params[attr].join(",");
        }
      }
    });
    return params;
  }

  vm.init = function() {
    MULTI_SELECT_FIELDS.forEach(function(field) {
      $("#" + field).val(vm.params[field]).trigger("change.select2");
    });
    DATETIME_FIELDS.forEach(function(attr) {
      $("[name='" + attr + "']").datepicker("setDate", new Date(vm.params[attr]));
    });
    vm.search(true);
  }

  vm.changePerPage = function() {
    vm.search(false);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".location-list-result").offset().top
    }, 1000);
  };

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  };

  vm.showCreatePopup = function(field) {
    vm.initCreateForm()
    $("#create-record-popup").modal("show");
  }

  vm.loadSearchLocations = function() {
    if (_.isNaN(vm.params.corporation_id)) {return;}
    vm.search_corporation_ids = vm.params.corporation_id.join(",");
  }

  vm.checkValidDateCreateParams = function(field) {
    vm.create_params[field] = checkValidDateFunction.checkValidDate(vm.create_params[field]) || vm.create_params[field];
  };

  vm.initCreateForm = function() {
    vm.create_params = {};
    vm.invalid_create = true;
    DOM_IDS_TO_CREATE.forEach(function(field) {
      $("#" + field).val(vm.create_params[field]).trigger("change.select2");
    });
    DATE_DOM_IDS_TO_CREATE.forEach(function(field) {
      $("#" + field).datepicker("setDate", new Date(vm.create_params[field]));
    });
    vm.create_params.fee = _.first(vm.referralFeeOptions);
  }

  vm.checkValidCreateParams = function() {
    var status = true;
    _.forEach(REQUIRE_ATTRS_TO_CREATE, function(key) {
      if (_.isUndefined(vm.create_params[key]) || vm.create_params[key] == "") {
        status = false;
      }
    });
    return status;
  }

  vm.onchangeCreateParams = function() {
    vm.invalid_create = !vm.checkValidCreateParams();
  }

  vm.createRecord = function() {
    if (vm.invalid_create) {return;}
    $("#loading-screen").modal("show");
    staffReferralLocationService.create(vm.create_params).then(function(res) {
      if (res.data.status) {
        $("#create-record-popup").modal("hide");
        $("#loading-screen").modal("hide");
        toaster.pop("success", "", res.data.message);
        vm.search();
      } else {
        $("#loading-screen").modal("hide");
        toaster.pop("error", "", res.data.message);
      }
    }, function(error) {
      $("#loading-screen").modal("hide");
      toaster.pop("error", "", error);
    });
  }

  vm.showDestroyPopup = function(id) {
    vm.destroy_id = id;
    $("#confirm-destroy").modal("show");
  }

  vm.destroyRecord = function() {
    if (!vm.destroy_id) {return;}
    staffReferralLocationService.destroy(vm.destroy_id).then(function(res) {
      vm.destroy_id = null;
      if (res.data.status) {
        toaster.pop("success", "", res.data.message);
        vm.search();
      } else {
        toaster.pop("error", "", res.data.message);
      }
    });
    $("#confirm-destroy").modal("hide");
  }

  vm.formatFee = function(fee) {
    return $filter("number")(fee, 0);
  }
}
