angular.module("adminApp").controller("ForeignEmploymentStatuses", ForeignEmploymentStatuses);

ForeignEmploymentStatuses.$inject = ["$location", "checkValidDateFunction", "foreignEmploymentStatusesService"];

function ForeignEmploymentStatuses($location, checkValidDateFunction, foreignEmploymentStatusesService) {
  var vm = this;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.params = $location.search();
  var SEARCH_CONDITION_TYPE = "foreign_employment_status_search_conditions";
  var CSV_TYPE = "csv";

  vm.init = function() {
    foreignEmploymentStatusesService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
        if (!vm.params.current_department_id) {vm.params.current_department_id = "";}
      } else {
        vm.params.per_page = vm.perPageSettings[0];
        vm.total_items = 0;
        vm.params.from_hire_date = vm.params.from_retirement_day = defaultStartMonth();
        vm.params.to_hire_date = vm.params.to_retirement_day = defaultEndMonth();
      }
      vm.search();
      angular.element(document).ready(function() {
        angular.element(".select2").select2();
      });
    })
  }

  vm.search = function(isSave) {
    foreignEmploymentStatusesService.getStaffs({search: vm.params}).then(function(res) {
      angular.extend(vm, res.data);
    });
    vm.selectAll = false;
    vm.isSelected = false;
    if (isSave) {
      foreignEmploymentStatusesService.createSearchCondition({search: vm.params, search_condition: SEARCH_CONDITION_TYPE});
    }
    $location.search(vm.params).replace();
    scrollToResult();
  };

  vm.checkAll = function() {
    _.forEach(vm.staffs, function(staff) {
      staff.select = vm.selectAll;
    });
    vm.checkSelected(true);
  };

  vm.checkSelected = function(isChecked) {
   var selectedItems = _.find(vm.staffs, {select: true});
   vm.isSelected = selectedItems ? true : false;
   if (vm.selectAll && !isChecked) {
     vm.selectAll = false;
   };
  };

  function scrollToResult() {
    $("html, body").animate({
      scrollTop: $(".search-result").offset().top
    }, 1000);
  };

  vm.changePerPage = function() {
    vm.search(true);
    scrollToResult();
  };

  vm.downloadCSV = function(e) {
    var selectedStaffs = _.filter(vm.staffs, function(staff) {
      return staff.select;
    });
    var selectedIds = _.map(selectedStaffs, "id");
    $.asyncDownload(e, "foreign_employment_statuses", JSON.stringify({id: selectedIds}), CSV_TYPE);
  };

  function defaultStartMonth() {
    return moment().startOf("month").format(ORDER_DATEPICKER_FORMAT);
  }

  function defaultEndMonth() {
    return moment().endOf("month").format(ORDER_DATEPICKER_FORMAT);
  }

  vm.checkValidDate = function(field) {
     vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  }
}
