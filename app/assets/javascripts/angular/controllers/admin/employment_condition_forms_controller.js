angular.module("adminApp").controller("EmploymentConditionFormsController", EmploymentConditionFormsController);

EmploymentConditionFormsController.$inject = ["$location", "employmentConditionFormService"];

function EmploymentConditionFormsController($location, employmentConditionFormService) {
  var vm = this;

  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  vm.init = function() {
    vm.params = {};
    vm.params.per_page = vm.params.per_page || vm.perPageSettings[0];
    vm.total_items = 0;
  };

  vm.search = function() {
    employmentConditionFormService.getArranges({search: vm.params}).then(function(res) {
      angular.extend(vm, res.data);
    });
    $location.search(vm.params).replace();
  };
}
