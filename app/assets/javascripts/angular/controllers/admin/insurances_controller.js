angular.module("adminApp").controller("InsurancesController", InsurancesController);

InsurancesController.$inject = ["$location", "insuranceService"]

function InsurancesController($location, insuranceService) {
  var vm = this;
  vm.hasRecord = false;
  vm.dates = [];
  CSV_TYPE = "csv";
  DEFAULT_PAGE = 1;
  DEFAULT_PER_PAGE = 10;
  vm.params = {};
  vm.init = function() {
    setDateForSearchField();
    initPage();
    vm.search();
  }

  vm.search = function() {
    var params = {from_date: vm.from_date, to_date: vm.to_date, page: vm.page};
    $("#spinner").removeClass("ng-hide");
    insuranceService.getDates({search_params: params}).then(function(res){
      $("#spinner").addClass("ng-hide");
      angular.extend(vm, res.data);
      vm.hasRecord = true;
      if (vm.data.dates.length == 0) {
        vm.hasRecord = false;
      }
    });
  }

  vm.refresh = function() {
    vm.search();
  }

  function initPage() {
    vm.page = DEFAULT_PAGE;
    vm.per_page = DEFAULT_PER_PAGE;
  }

  function setDateForSearchField() {
    var urlParams = $location.search();
    if (_.isEmpty(urlParams)) {
      vm.from_date = currentDate();
      vm.to_date = currentDate();
    } else {
      angular.extend(vm, urlParams);
    }
  };

  vm.blurFromDate = function() {
    vm.from_date = currentDate();
  }

  vm.blurToDate = function() {
    vm.to_date = currentDate();
  }

  vm.notExistExcludeDates = function(date, excludeDates) {
    return !_.includes(excludeDates, date);
  }

  vm.dowloadInsuranceLogs = function(e, insuranceType, date) {
    var params = {date: date, insurance_type: insuranceType};
    $.asyncDownload(e, "insurances_by_dates", JSON.stringify(params), CSV_TYPE, {insurance_type: insuranceType})
  }

  function currentDate() {
    var currentDate = moment(new Date());
    var year = currentDate.format("YYYY");
    var month = currentDate.format("MM");
    var day = currentDate.format("DD");
    var date = year + "/" + month + "/" + day
    return date;
  }
}