"use strict";

angular.module("adminApp").controller("NewBatchArrangesController", NewBatchArrangesController);
NewBatchArrangesController.$inject = ["$scope", "$location", "newBatchArrangeService", "toaster", "$sce"];

function NewBatchArrangesController($scope, $location, newBatchArrangeService, toaster, $sce) {
  var vm = this;
  var JOB_TABS = ["applying", "arranged", "confirming", "approving"];
  var SELECT_TAGS_MAX_HEIGHT = 252;
  var SELECT_TAGS_BODY_MAX_HEIGHT = 300;
  var SELECT_TAGS_BODY_PADDING = 80;
  var SEARCH_CONDITION_TYPE = "admin_batch_arrange_search_conditions";
  var $errorConditionArrangeModal = angular.element("#error-arrange-staff-modal");
  var $conditionArrangeModal = angular.element("#warning-arrange-staff-modal");
  var $changablePopupModal = angular.element("#changable-popup-modal");
  var $confirmWarningArrangeModal = angular.element("#confirm-warning-arrange-staff-modal");
  var $confirmBatchArrange = angular.element("#confirm-batch-arrange-modal");
  var $confirmWarningBatchArrange = angular.element("#confirm-warning-arranged");
  var $sendingModal = angular.element("#batch-arrange-sending-modal");
  var $offerSendingModal = angular.element("#offer-sending-modal");
  var $closeBatchArrangeModal = angular.element("#close-batch-arrange-modal");
  var $confirmWarningTempArrange = angular.element("#confirm-warning-temp-arrange");
  var $cancelTemporaryArrangeModal = angular.element("#cancel-temporary-arrange-modal");
  var $errorMailPopup = angular.element("#error-mail-popup");
  var $notesPopup = angular.element("#notes-popup");
  var $staffsListPopup = angular.element("#staffs-list-popup");
  var $searchstaffConditionsPopup = angular.element("#search-staff-condition");
  var $editMemoPopup = angular.element("#create-offer-memo-popup");
  var $regularTimeModal = angular.element("#regular-time-modal");
  var $regularInputPriceModal = angular.element("#regular-input-price");
  var $confirmSelectStaff = angular.element("#confirm-select-staff");
  var $confirmSearchTempArrangedStaff = angular.element("#confirm-search-temp-arranged-staff");
  var $confirmStaffArrange = angular.element("#modal-confirm-staff-arranged");
  var $errorMessagePopup = angular.element("#error-message-popup");
  var $responseErrorMessages = angular.element("#error-response-popup");
  var $removeStaffApplyOrderCasesPopup = angular.element("#remove-apply-popup");
  var $updateBasicFieldConfirm = angular.element("#confirm-update-basic-field");
  var $inputNotesForArrangeMail = angular.element("#input-notes-for-arrange-mail");
  var $confirmSendBatchOfferMail = angular.element("#confirm-send-offer-mail-modal");
  var DISABLE_SELECT_STATUS = ["absence", "cancel_after_arrange_has_insurance", "cancel_after_arrange_no_insurance", "absence_has_alternative"];
  var BILLING_FIELDS = ["billing_basic_unit_price", "billing_night_unit_price", "billing_field_2", "billing_field_1"];
  var PAYMENT_FIELDS = ["payment_basic_unit_price", "payment_night_unit_price", "payment_field_1"];
  var SEGMENT_IDS_BY_SEGMENT = {haken: "1", training: "2,3,4,5", regular: "6", contract: "7"};
  var NOT_REGULAR_SEGMENTS = ["haken" , "training", "contract"];
  vm.$scope = $scope;
  vm.data_departments = $("#data_departments").data("infos");
  vm.lastSearchConditions = $("#last_conditions").data("infos");
  vm.workingTimeStatusIds = $("#working_time_status_ids").data("infos");
  vm.tabStatus = "applying";
  vm.advance_info = {};
  vm.total = 0;
  vm.searchValues = {};
  vm.openCondition = "";
  vm.initSuccess = false;
  vm.workingTimeStatusIdsForSelect = angular.copy(vm.workingTimeStatusIds);
  vm.phoneStatusForSelect = [
    {key: "none_contact", text: I18n.t("admin.new_batch_arrange.phone_status.none_contact")},
    {key: "considering", text: I18n.t("admin.new_batch_arrange.phone_status.considering")},
    {key: "agreed", text: I18n.t("admin.new_batch_arrange.phone_status.agreed")},
    {key: "deny", text: I18n.t("admin.new_batch_arrange.phone_status.deny")}
  ];
  vm.columnsByStatus = {
    applying: ["workingday", "total", "fullname", "lastmonth", "absence_working_relevant", "ban", "transportation", "note", "onday", "remarks"],
    arranged: ["workingday", "total", "fullname", "adjustedhours", "goingwork", "arrival", "contactmemo", "configurator"],
    confirming: ["workingday", "total", "fullname", "scheduledhours", "submission", "difference", "status"],
    approving: ["workingday", "total", "fullname", "scheduledhours", "submission", "difference", "determiner"],
    deleted: ["fullname"]
  };
  vm.ClassByColumns = {
    workingday: {
      text: I18n.t("admin.new_batch_arrange.column.order_case")
    },
    total: {
    },
    fullname: {
      text: I18n.t("admin.new_batch_arrange.column.full_name")
    },
    lastmonth: {
      text: I18n.t("admin.new_batch_arrange.column.last_month") + "/" + I18n.t("admin.new_batch_arrange.column.newest")
    },
    absence: {
      text: I18n.t("admin.new_batch_arrange.column.absence"),
      sortable: true
    },
    working: {
      text: I18n.t("admin.new_batch_arrange.column.work"),
      sortable: true
    },
    relevant: {
      text: "（"+ I18n.t("admin.new_batch_arrange.column.relevant") + "）"
    },
    ban: {
      text: I18n.t("admin.new_batch_arrange.column.ban"),
      sortable: true
    },
    transportation: {
      text: I18n.t("admin.new_batch_arrange.column.transportation"),
      sortable: true
    },
    note: {
      text: I18n.t("admin.new_batch_arrange.column.caution")
    },
    onday: {
      text: I18n.t("admin.new_batch_arrange.column.on_day")
    },
    remarks: {
      text: I18n.t("admin.new_batch_arrange.column.remark")
    },
    untilstarted: {
      text: I18n.t("admin.new_batch_arrange.column.until_start")
    },
    adjustedhours: {
      text: I18n.t("admin.new_batch_arrange.column.adjusted_hours")
    },
    goingwork: {
      text: I18n.t("admin.new_batch_arrange.column.going_work")
    },
    arrival: {
      text: I18n.t("admin.new_batch_arrange.column.arrival")
    },
    contactmemo: {
      text: I18n.t("admin.new_batch_arrange.column.contact_memo")
    },
    configurator: {
      text: I18n.t("admin.new_batch_arrange.column.configurator")
    },
    scheduledhours: {
      text: I18n.t("admin.new_batch_arrange.column.scheduled_hours")
    },
    submission: {
      text: I18n.t("admin.new_batch_arrange.column.submission")
    },
    difference: {
      text: I18n.t("admin.new_batch_arrange.column.difference"),
      sortable: true
    },
    status: {
      text: I18n.t("admin.new_batch_arrange.column.status"),
      sortable: true
    },
    determiner: {
      text: I18n.t("admin.new_batch_arrange.column.determiner"),
      sortable: true
    }
  };
  vm.selectData = {
    area: [],
    staff: [],
    location: [],
    corporation: [],
    segment: [],
    applying_status: ["has_apply"],
    arranged_status: [],
    confirming_status: [],
    approving_status: []
  };
  vm.conditionData = {
    segment: [
      {value: "haken", label: I18n.t("admin.new_batch_arrange.search.segment.haken"), selected: vm.selectData.segment.includes("haken")},
      {value: "contract", label: I18n.t("admin.new_batch_arrange.search.segment.contract"), selected: vm.selectData.segment.includes("contract")},
      {value: "training", label: I18n.t("admin.new_batch_arrange.search.segment.training"), selected: vm.selectData.segment.includes("training")},
      {value: "regular", label: I18n.t("admin.new_batch_arrange.search.segment.regular"), selected: vm.selectData.segment.includes("regular")}
    ],
    applying_status: [
      {value: "no_offer", label: I18n.t("admin.new_batch_arrange.search.applying_status.no_offer"), selected: vm.selectData.applying_status.includes("no_offer")},
      {value: "no_apply", label: I18n.t("admin.new_batch_arrange.search.applying_status.no_apply"), selected: vm.selectData.applying_status.includes("no_apply")},
      {value: "has_apply", label: I18n.t("admin.new_batch_arrange.search.applying_status.has_apply"), selected: vm.selectData.applying_status.includes("has_apply")},
      {value: "finished_recruiting", label: I18n.t("admin.new_batch_arrange.search.applying_status.finished_recruiting"), selected: vm.selectData.applying_status.includes("finished_recruiting")},
      {value: "temporary_arranged", label: I18n.t("admin.new_batch_arrange.search.applying_status.temporary_arranged"), selected: vm.selectData.applying_status.includes("temporary_arranged")},
      {value: "cancel", label: I18n.t("admin.new_batch_arrange.search.applying_status.cancel"), selected: vm.selectData.applying_status.includes("cancel")}
    ],
    arranged_status: [
      {value: "arranged", label: I18n.t("admin.new_batch_arrange.search.arranged_status.arranged"), selected: vm.selectData.arranged_status.includes("arranged")},
      {value: "cancel_has_insurance", label: I18n.t("admin.new_batch_arrange.search.arranged_status.cancel_has_insurance"), selected: vm.selectData.arranged_status.includes("cancel_has_insurance")},
      {value: "cancel_no_insurance", label: I18n.t("admin.new_batch_arrange.search.arranged_status.cancel_no_insurance"), selected: vm.selectData.arranged_status.includes("cancel_no_insurance")},
      {value: "absence", label: I18n.t("admin.new_batch_arrange.search.arranged_status.absence"), selected: vm.selectData.arranged_status.includes("absence")}
    ],
    confirming_status: [
      {value: "not_inputted", label: I18n.t("admin.new_batch_arrange.search.confirming_status.not_inputted"), selected: vm.selectData.confirming_status.includes("not_inputted")},
      {value: "staff_approved", label: I18n.t("admin.new_batch_arrange.search.confirming_status.staff_approved"), selected: vm.selectData.confirming_status.includes("staff_approved")},
      {value: "owner_approved", label: I18n.t("admin.new_batch_arrange.search.confirming_status.owner_approved"), selected: vm.selectData.confirming_status.includes("owner_approved")},
      {value: "op_center_approved", label: I18n.t("admin.new_batch_arrange.search.confirming_status.op_center_approved"), selected: vm.selectData.confirming_status.includes("op_center_approved")},
      {value: "auto_approved", label: I18n.t("admin.new_batch_arrange.search.confirming_status.auto_approved"), selected: vm.selectData.confirming_status.includes("auto_approved")},
      {value: "staff_confirming", label: I18n.t("admin.new_batch_arrange.search.confirming_status.staff_confirming"), selected: vm.selectData.confirming_status.includes("staff_confirming")},
      {value: "owner_confirming", label: I18n.t("admin.new_batch_arrange.search.confirming_status.owner_confirming"), selected: vm.selectData.confirming_status.includes("owner_confirming")},
      {value: "op_center_confirming", label: I18n.t("admin.new_batch_arrange.search.confirming_status.op_center_confirming"), selected: vm.selectData.confirming_status.includes("op_center_confirming")},
      {value: "absence", label: I18n.t("admin.new_batch_arrange.search.confirming_status.absence"), selected: vm.selectData.confirming_status.includes("absence")}
    ],
    approving_status: []
  };
  vm.dataPopup = {
    area: {
      label: I18n.t("admin.new_batch_arrange.popup.area.label"),
      popupTitle: I18n.t("admin.new_batch_arrange.popup.area.title"),
      opening: vm.openCondition == "area"
    },
    corporation: {
      label: I18n.t("admin.new_batch_arrange.popup.corporation.label"),
      popupTitle: I18n.t("admin.new_batch_arrange.popup.corporation.title"),
      opening: vm.openCondition == "corporation"
    },
    location: {
      label: I18n.t("admin.new_batch_arrange.popup.location.label"),
      popupTitle: I18n.t("admin.new_batch_arrange.popup.location.title"),
      opening: vm.openCondition == "location"
    },
    staff: {
      label: I18n.t("admin.new_batch_arrange.popup.staff.label"),
      popupTitle: I18n.t("admin.new_batch_arrange.popup.staff.title"),
      opening: vm.openCondition == "staff"
    },
    segment: {
      label: I18n.t("admin.new_batch_arrange.popup.segment.label"),
      popupTitle: I18n.t("admin.new_batch_arrange.popup.segment.title"),
      opening: vm.openCondition == "segment",
      columnClass: "col-md-3"
    }
  };
  vm.department_ids = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 100];
  vm.departments = {
    "関東": [
      {value: 3, label: "", selected: vm.selectData.area.includes(3)},
      {value: 7, label: "", selected: vm.selectData.area.includes(7)},
      {value: 6, label: "", selected: vm.selectData.area.includes(6)},
      {value: 5, label: "", selected: vm.selectData.area.includes(5)},
      {value: 19, label: "", selected: vm.selectData.area.includes(19)}
    ],
    "北日本": [
      {value: 16, label: "", selected: vm.selectData.area.includes(16)},
      {value: 10, label: "", selected: vm.selectData.area.includes(10)},
      {value: 0, label: "", selected: vm.selectData.area.includes(0)},
      {value: 1, label: "", selected: vm.selectData.area.includes(1)},
      {value: 2, label: "", selected: vm.selectData.area.includes(2)},
      {value: 20, label: "", selected: vm.selectData.area.includes(20)},
      {value: 100, label: "", selected: vm.selectData.area.includes(100)}
    ],
    "中部": [
      {value: 8, label: "", selected: vm.selectData.area.includes(8)},
      {value: 13, label: "", selected: vm.selectData.area.includes(13)}
    ],
    "九州": [
      {value: 12, label: "", selected: vm.selectData.area.includes(12)},
      {value: 11, label: "", selected: vm.selectData.area.includes(11)},
      {value: 18, label: "", selected: vm.selectData.area.includes(18)},
      {value: 15, label: "", selected: vm.selectData.area.includes(15)}
    ],
    "近畿": [
      {value: 4, label: "", selected: vm.selectData.area.includes(4)},
      {value: 17, label: "", selected: vm.selectData.area.includes(17)},
      {value: 9, label: "", selected: vm.selectData.area.includes(9)}
    ]
  };
  vm.selectedStaffs = {};
  vm.JobsByStatus = {};
  vm.totalSelectedStaff = 0;
  vm.selectedStaffApplyOcIds = [];
  vm.selectedOcIds = [];
  vm.selectedStaffType = {applying: "", arranged: "", confirming: "", approving: ""};
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  var STAFF_SEARCH_FIELDS = ["staff_number", "address_kana", "account_email", "tel", "current_department_id",
    "account_name_kana", "account_name_lower", "only_arrangeble_staff", "only_match_schedule",
    "worked_for_location", "joined_insurance", "priority_for_location"];
  vm.removeStaffApplyOrderCasesMessages = {};
  vm.arrangeParams = {};
  vm.searchParams = {};
  vm.searchParams.page = 1;
  vm.searchParams.per_page = 10;
  vm.popupState = {};
  vm.popupState.confirmingStaff = {};
  vm.regularState = {};
  vm.regularState.staff = {};
  vm.regularState.order = "";
  vm.regularState.job = {};
  vm.regularState.jobs = [];

  vm.resetPopupStage = function(resetSearch) {
    vm.popupState = {};
    vm.popupState.confirmingStaff = {};
    vm.popupState.confirmingStaff.name = "search_applying";
    vm.popupState.confirmingStaff.arrangeCondition = {};
    vm.popupState.confirmingStaff.regularOrderCondition = {};
    vm.popupState.confirmingStaff.isWarning = false;
    vm.popupState.confirmingStaff.isNotValid = true;
  };

  vm.resetApplyingStaff = function(resetSearch) {
    vm.staffApplyingStage = {};
    vm.staffApplyingStage.params = {};
  };

  vm.resetSearchStaff = function(resetSearch) {
    vm.staffSearchState = {};
    vm.staffSearchState.currentOrderCaseId = "";
    vm.staffSearchState.currentSelectedJob = {};
    vm.staffSearchState.availableJobPortion = 0;
    vm.staffSearchState.currentSelectedStaff = {};
    vm.staffSearchState.searchSelectedStaffIds = [];
    vm.staffSearchState.searchStaffSelectedCount = 0;
    vm.staffSearchState.searchStaffDone = false;
    vm.staffSearchState.currentSearchParams = {};
    vm.staffSearchState.staffs = {};
    vm.staffSearchState.hasStaffs = false;
    vm.staffSearchState.searchParams = {};
    vm.staffSearchState.searchParams.page = 1;
    vm.staffSearchState.searchParams.per_page = 10;
    vm.staffSearchState.isOffered = false;
    vm.staffSearchState.isCalled = false;
    vm.staffSearchState.totalOffered = 0;
    vm.staffSearchState.totalCalled = 0;
    vm.staffSearchState.totalStaff = 0;
  };

  vm.getDepartmentLabel = function(DepartmentId) {
    var department_name = "";
    vm.data_departments.forEach(function(department) {
      if (department.id == DepartmentId) {department_name = department.name;}
    });
    return department_name;
  };

  vm.selectDepartmentsByRegion = function(regionName) {
    if (vm.isRegionChecked(regionName)) {
      vm.departments[regionName].forEach(function(department) {
        department.selected = false;
        var index = vm.selectData.area.indexOf(department.value);
        vm.selectData.area.splice(index, 1);
      });
    } else {
      vm.departments[regionName].forEach(function(department) {
        department.selected = true;
        if (!vm.selectData.area.includes(department.value)) {
          vm.selectData.area.push(department.value);
        }
      });
    }
  };

  vm.isRegionChecked = function(regionName) {
    var unselected = vm.departments[regionName].filter(function(department) {
      return department.selected == false;
    });
    return unselected.length === 0;
  };

  vm.initParams = function() {
    var currentDateTime = moment();
    var momentDateFormat = DEFAULT_DATE_TIME_FORMAT.toUpperCase();
    var oldSearchParams = {};
    oldSearchParams.working_from_date = vm.lastSearchConditions.working_from_date || currentDateTime.format(momentDateFormat);
    oldSearchParams.working_to_date = vm.lastSearchConditions.working_to_date || currentDateTime.add(12, "week").format(momentDateFormat);
    if (!moment(oldSearchParams.working_from_date, momentDateFormat,true).isValid()) {
      oldSearchParams.working_from_date = currentDateTime.format(momentDateFormat);
    }
    if (!moment(oldSearchParams.working_to_date, momentDateFormat, true).isValid()) {
      oldSearchParams.working_to_date = currentDateTime.add(12, "week").format(momentDateFormat);
    }
    var selected_deparments = vm.lastSearchConditions.department_id || "";
    oldSearchParams.department_id = selected_deparments;
    oldSearchParams.corporation_id = vm.lastSearchConditions.corporation_id || "";
    oldSearchParams.location_id = vm.lastSearchConditions.location_id || "";
    oldSearchParams.staff_id = vm.lastSearchConditions.staff_id || "";
    oldSearchParams.segment = vm.lastSearchConditions.segment || "haken";
    vm.searchParams.search = oldSearchParams;
    vm.searchParams.search.tab_name = vm.tabStatus;
    vm.excludeConditons = {};
    vm.excludeConditons.prefecture_id = vm.lastSearchConditions.prefecture_id;
    vm.excludeConditons.order_case_id = vm.lastSearchConditions.order_case_id;
    vm.excludeConditons.order_by = vm.lastSearchConditions.order_by;
    vm.excludeConditons.sort_by = vm.lastSearchConditions.sort_by;
    vm.excludeConditons.per_page = vm.lastSearchConditions.per_page;
    vm.excludeConditons.only_waiting_arrange = vm.lastSearchConditions.only_waiting_arrange;

    var areas = selected_deparments.split(",").filter(Boolean);
    var departmentIds = [];
    areas.forEach(function(departmentId) {
      if (vm.department_ids.includes(parseInt(departmentId))) {departmentIds.push(departmentId);}
    });
    vm.selectData.area = departmentIds.map(Number) || [];
    vm.selectData.corporation = oldSearchParams.corporation_id.split(",");
    vm.selectData.location = oldSearchParams.location_id.split(",");
    vm.selectData.staff = oldSearchParams.staff_id.split(",");
    vm.selectData.segment = oldSearchParams.segment.split(",");
  };

  vm.initSearchConditions = function() {
    vm.initFrontEndSuccess = false;
    Object.keys(vm.departments).forEach(function(regionName) {
      vm.departments[regionName].forEach(function(department) {
        department.label = vm.getDepartmentLabel(department.value);
        if (vm.selectData.area.includes(department.value)) {
          department.selected = true;
        }
      });
    });
    vm.conditionData.segment.forEach(function(segment) {
      segment.selected = vm.selectData.segment.includes(segment.value);
    });
    vm.initFrontEndSuccess = true;
  };

  vm.init = function() {
    $("#spinner").removeClass("ng-hide");
    $("body").css({"overflow":"hidden"});
    vm.initParams();
    vm.resetSearchStaff();
    vm.initSearchConditions();
    // vm.getBaseInformations();
    vm.formatSearchParrams();
    vm.searchParams["search"]["order_case_ids"] = vm.lastSearchConditions.order_case_ids || ""
    vm.searchJobs(vm.searchParams);
    vm.searchParams["search"]["order_case_ids"] = ""
  };

  vm.changeTab = function(tabName) {
    vm.tabStatus = tabName;
    vm.advance_info = {};
    vm.selectedArrangementsIds = [];
    vm.JobsByStatus[vm.tabStatus] = {};
    vm.selectedStaffs[vm.tabStatus] = {};
    vm.formatSearchParrams(true);
    vm.searchJobs(vm.searchParams);
  };

  vm.updateSearchConditions = function() {
    if (!vm.searchSuccess) {return;}
    vm.formatSearchParrams(true);
    // vm.getBaseInformations();
    vm.searchJobs(vm.searchParams, true);
  };

  vm.refreshPage = function() {
    vm.formatSearchParrams(false);
    // vm.getBaseInformations();
    vm.searchJobs(vm.searchParams, false);
  };

  vm.nextPage = function() {
    vm.formatSearchParrams();
    vm.searchJobs(vm.searchParams);
  };

  vm.getBaseInformations = function() {
    newBatchArrangeService.getBaseInformations(vm.searchParams).then(function(res) {
      angular.extend(vm, res.data);
    });
  };

  vm.searchJobs = function(params, isSaveCondition) {
    vm.searchSuccess = false;
    $("#spinner").removeClass("ng-hide");
    $("body").css({"overflow":"visible"});
    newBatchArrangeService.getBatchArranges(params).then(function(res) {
      if (res.data.status) {
        angular.extend(vm, res.data);
        vm.JobsByStatus[vm.tab_name] = vm.order_cases;
        vm.selectedStaffs[vm.tab_name] = {};
        vm.JobsByStatus[vm.tab_name].forEach(function(job) {
          vm.selectedStaffs[vm.tab_name][job.id] = {};
          job.staffs.forEach(function(staff) {
            vm.selectedStaffs[vm.tab_name][job.id][staff.id] = false;
          });
        });
      } else {
        vm.errorMessages = res.data.errors;
        $responseErrorMessages.modal("show");
      }
    }).then(function () {
      vm.searchSuccess = true;
      vm.initSuccess = true;
      $("#spinner").addClass("ng-hide");
      $("body").css({"overflow":"visible"});
    });
    vm.totalSelectedStaff = 0;
    vm.selectedStaffType[vm.tab_name] = "";
    if (!!isSaveCondition) {
      vm.saveSearchCondition();
    }
  };

  vm.saveSearchCondition = function() {
    var saveParams = Object.assign(vm.searchParams.search, vm.excludeConditons);
    saveParams = _.omit(saveParams, ["page", "tab_name", "show_all_option"]);
    saveParams["order_case_ids"] = ""
    newBatchArrangeService.createSearchCondition({search: saveParams});
  };

  vm.formatSearchParrams = function(isResetPage) {
    vm.searchParams.search.department_id = vm.selectData.area.join(",");
    vm.searchParams.search.corporation_id = vm.selectData.corporation.join(",");
    vm.searchParams.search.location_id = vm.selectData.location.join(",");
    vm.searchParams.search.staff_id = vm.selectData.staff.join(",");
    vm.searchParams.search.segment = vm.selectData.segment.join(",");
    vm.searchParams.search.applying_status = vm.selectData.applying_status.join(",");
    vm.searchParams.search.arranged_status = vm.selectData.arranged_status.join(",");
    vm.searchParams.search.confirming_status = vm.selectData.confirming_status.join(",");
    vm.searchParams.search.approving_status = vm.selectData.approving_status.join(",");
    vm.searchParams.search.tab_name = vm.tabStatus;
    if (!!isResetPage) {vm.searchParams.page = 1;}
  };

  vm.countDownStartedAt = function(job) {
    var now = new Date().getTime();
    if (now > job.case_started_at) {return "";}
    var distance = job.case_started_at - now;
    var days = Math.floor(distance / (1000 * 60 * 60 * 24));
    var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    var countdown = "";
    if (days > 0) {  countdown = countdown + days + "日";}
    if (hours > 0) {  countdown = countdown + hours + "時間";}
    if (minutes > 0) {  countdown = countdown + minutes + "分";}
    return countdown;
  };

  vm.selectedConditions = function(conditionName) {
    switch (conditionName) {
    case "area":
      var areasArr = Object.values(vm.departments).flat();
      return areasArr.filter(function(conditionData) {
        return vm.selectData.area.includes(conditionData.value);
      });
    case "location":
    case "staff":
    case "corporation":
      vm.searchValues[conditionName] = $('#' + conditionName + '_id').select2('data') || [];
      return vm.searchValues[conditionName].filter(function(data) {
        return vm.selectData[conditionName].includes(data.id);
      });
    default:
      return vm.conditionData[conditionName].filter(function(conditionData) {
        return vm.selectData[conditionName].includes(conditionData.value);
      });
    }
  };

  vm.unselectedCondition = function(conditionName, condition) {
    if (conditionName == "segment") {return;}
    if (conditionName == "location" || conditionName == "staff" || conditionName == "corporation") {
      var index = vm.selectData[conditionName].indexOf(condition);
      vm.selectData[conditionName].splice(index, 1);
      $("#" + conditionName+ "_id").val(vm.selectData[conditionName]).trigger("change.select2");
      return;
    }
    var NewIndex = vm.selectData[conditionName].indexOf(condition.value);
    condition.selected = false;
    vm.selectData[conditionName].splice(NewIndex, 1);
  };

  vm.showConditionPopup = function(condition) {
    vm.openCondition = condition;
    $("#list-selected-conditon").removeClass("d-none");
    $("#list-selected-conditon").addClass("d-none");
    $("#search-by-"+ vm.openCondition + "-popup").modal("show");
  };

  vm.closeConditionPopup = function(conditionName) {
    if (conditionName == "location" || conditionName == "staff" || conditionName == "corporation") {
      vm.searchValues[conditionName] = $('#' + conditionName + '_id').select2('data') || [];
    }
    $("#search-by-"+ conditionName + "-popup").modal("hide");
    $("#list-selected-conditon").removeClass("d-none");
    vm.openCondition = "";
  };

  vm.showProfilePopup = function(job, staff) {
    vm.profile = {};
    newBatchArrangeService.getStaffProfile(staff.id).then(function(res){
      vm.profile = res.data.data;
      $("#profile-popup").modal("show");
    });
  };

  vm.closeProfilePopup = function() {
    vm.profile = {};
    $("#profile-popup").modal("hide");
  };

  vm.tabActive = function(status) {
    return vm.tabStatus === status;
  };

  vm.jobsStatusClass = function(staff) {
    var statusClass = staff.apply_status;
    if (!!staff.special_status) {
      statusClass = statusClass + " special-staff";
    }
    if (!!staff.is_registration) {
      statusClass = statusClass + " registration";
    }
    return statusClass;
  };

  vm.isStaffReapplyAbsentTraining = function(job, staff) {
    var isApplyingTab     = vm.tabStatus == "applying";
    var isTrainingSegment = job.segment == "training";
    var isAbsentStatus    = staff.apply_status == "absence";
    if (isApplyingTab && isTrainingSegment && isAbsentStatus) return true;
    return false;
  };

  vm.togglesChkbox = function(data, conditionName) {
    if (data.selected) {
      if (!vm.selectData[conditionName].includes(data.value)) {
        vm.selectData[conditionName].push(data.value);
      }
    } else {
      var index = vm.selectData[conditionName].indexOf(data.value);
      vm.selectData[conditionName].splice(index, 1);
    }
  };

  vm.formatArrangeParams = function(action, changeTime, isWarning){
    vm.arrangeParams[action] = {};
    vm.selectedStaffApplyOcIds = [];
    vm.selectedOcIds = [];
    if (vm.totalSelectedStaff == 0) {return;}
    vm.arrangeParams[action].arrange = [];
    Object.keys(vm.selectedStaffs[vm.tabStatus]).forEach(function(jobId) {
      Object.keys(vm.selectedStaffs[vm.tabStatus][jobId]).forEach(function(StaffId) {
        if (vm.selectedStaffs[vm.tabStatus][jobId][StaffId]) {
          vm.selectedOcIds.push(jobId);
          var jobs = vm.JobsByStatus[vm.tabStatus].filter(function(job){ return job.id == jobId;})[0];
          var staff = jobs.staffs.filter(function(staff){ return staff.id == StaffId;})[0];
          var jobData = {};
          if (!changeTime && staff.note.is_changable){return;}
          if (!!changeTime && !staff.note.is_changable){return;}
          jobData.id = staff.staff_apply_order_case_id;
          jobData.order_case_id = staff.order_case_id;
          jobData.staff_id = staff.staff_id;
          jobData.staff_name = staff.staff_name;
          jobData.staff_number = staff.staff_number;
          jobData.location_name = staff.location_name;
          jobData.corporation_name = staff.corporation_name;
          jobData.store_note = staff.caution_to_staff_mail;
          jobData.arrange_comment = staff.arrange_comment;
          jobData.work_time_with_date = staff.changed_data.work_time_with_date || jobs.work_time_with_date;
          jobData.segment = jobs.segment;
          if (staff.apply_status == "temporary_arrange") {
            jobData.status = staff.apply_status;
          } else {
            jobData.status = staff.arrangement_status;
          }
          if (action == "arranged" && !!isWarning) {
            jobData.warning = isWarning;
          }
          vm.selectedStaffApplyOcIds.push(staff.staff_apply_order_case_id);
          vm.arrangeParams[action].arrange.push(jobData);
        }
      });
      if (action == "temporary_arranged" && !!changeTime) {
        vm.arrangeParams[action].change_arrangement_time = !!changeTime;
      }
      if (action == "arranged" && !!isWarning) {
        vm.arrangeParams[action].warning = isWarning;
      }
    });
  };

  vm.regularParams = {};
  vm.isShowErrorsPayment = false;
  vm.isShowErrorsBilling = false;
  vm.billingErrors = {};
  vm.paymentErrors = {};
  vm.saveTemporaryArrange = function() {
    vm.formatArrangeParams("temporary_arranged");
    if (NOT_REGULAR_SEGMENTS.includes(vm.searchParams.search.segment)) {
      vm.saveTemporaryArrangeByParams(vm.arrangeParams.temporary_arranged);
      return;
    }
    var selectedIds = vm.selectedOcIds.join(",");
    vm.regularParams = {};
    newBatchArrangeService.getRegularOrderPrice({order_case_id: selectedIds}).then(function(res){
      var data = res.data;
      if (data.status) {
        var orderSegmentId = data.order.order_segment_id;
        if (orderSegmentId == "regular_order") {
          vm.isBySearchStaff = false;
          vm.isShowErrorsPayment = false;
          vm.isShowErrorsBilling = false;
          BILLING_FIELDS.forEach(function(field){
            vm.regularParams[field] = res.data.arrange_billings[field];
          });
          PAYMENT_FIELDS.forEach(function(field){
            vm.regularParams[field] = res.data.arrange_payments[field];
          });
          $regularInputPriceModal.modal("show");
        } else {
          vm.saveTemporaryArrangeByParams(vm.arrangeParams.temporary_arranged);
        }
      } else {
        vm.errorMessages = data.errors;
        $responseErrorMessages.modal("show");
      }
    });
  };

  vm.saveTemporaryArrangeByParams = function(params) {
    $sendingModal.modal("show");
    $("#spinner").removeClass("ng-hide");
    $changablePopupModal.modal("hide");
    newBatchArrangeService.saveTemporaryArrange(params).then(function(res) {
      $("#spinner").addClass("ng-hide");
      if (res.data.status) {
        var jobId = res.data.jid;
        var intervalName = "job_" + jobId;
        window[intervalName] = setInterval(function() {
          vm.getsaveTemporaryArrangeStatus(jobId, intervalName);
        }, 2000);
      } else {
        $sendingModal.modal("hide");
        vm.staffNames = res.data.staff_name;
        if (!_.isEmpty(vm.staffNames)) {
          $confirmWarningTempArrange.modal("show");
        } else {
          if (!!res.data.errors) {
            var error_message = res.data.errors.join(",");
            toaster.pop("error", "", error_message);
          } else{
            toaster.pop("error", "", I18n.t("admin.batch_arrange.incorrect_apply_time"));
          }
        }
      }
    });
  };

  vm.getsaveTemporaryArrangeStatus = function(jobId, intervalName) {
    newBatchArrangeService.sendBatchArrangeMailStatus({job_id: jobId}).then(function(res) {
      if (res.data.status === "complete") {
        $("#spinner").addClass("ng-hide");
        $sendingModal.modal("hide");
        deleteIntervalJob(intervalName);
        newBatchArrangeService.getTemporaryArrangeWorkerData({job_id: jobId}).then(function(res) {
          var data = res.data;
          if (data.status) {
            toaster.pop("success", "", I18n.t("admin.arrangements.index.action_status.update_success"));
            vm.refreshPage();
          } else {
            vm.isTempArrangeError = true;
            vm.tempArrangeErrorMessages = data.errors;
            if (data.success.length > 0) {
              vm.refreshPage();
            }
            $confirmWarningTempArrange.modal("show");
          }
        });
      }
    });
  };

  vm.saveTemporaryArrangeRegular = function(){
    if (!vm.regularState.job || !vm.regularState.staff) {return;}
    var staff = vm.regularState.staff;
    var submitRegularParams =  {};
    submitRegularParams.arrange = [];
    var jobData = {};
    jobData.id = staff.staff_apply_order_case_id;
    jobData.order_case_id = vm.regularState.job.id;
    jobData.store_note = staff.store_note;
    jobData.staff_id = staff.id;
    jobData.arrange_comment = staff.arrange_comment;
    jobData.status = staff.arrangement_status;
    if (staff.apply_status == "temporary_arrange") {jobData.status = staff.arrange_comment;}
    var orderPriceParams = {
      billing: {
        billing_basic_unit_price: vm.regularParams.billing_basic_unit_price,
        billing_night_unit_price: vm.regularParams.billing_night_unit_price,
        billing_field_2: vm.regularParams.billing_field_2,
        billing_field_1: vm.regularParams.billing_field_1
      },
      payment: {
        payment_basic_unit_price: vm.regularParams.payment_basic_unit_price,
        payment_night_unit_price: vm.regularParams.payment_night_unit_price,
        payment_field_1: vm.regularParams.payment_field_1
      }
    };
    jobData.order_price = orderPriceParams;
    submitRegularParams.arrange.push(jobData);
    submitRegularParams.change_arrangement_time = false;
    newBatchArrangeService.checkInputOrderPrice({order_price: orderPriceParams,
      order_case_id: vm.regularState.job.id}).then(function(res){
      if(res.data.status) {
        $regularInputPriceModal.modal("hide");
        vm.saveTemporaryArrangeByParams(submitRegularParams);
      } else {
        vm.isShowErrorsPayment = true;
        vm.isShowErrorsBilling = true;
        vm.billingErrors = JSON.parse(res.data.billing_errors);
        vm.paymentErrors = JSON.parse(res.data.payment_errors);
      }
    });
  };

  vm.inputNotesForMailArrange = function() {
    vm.formatArrangeParams("arranged");
    $inputNotesForArrangeMail.modal("show");
  }

  vm.saveArrangedAlert = function() {
    $inputNotesForArrangeMail.modal("hide");
    newBatchArrangeService.alertMessageArranged(vm.arrangeParams.arranged).then(function(res) {
      if (res.data.status) {
        $confirmBatchArrange.modal("show");
      } else {
        vm.staffNames = res.data.staff_name;
        $confirmWarningBatchArrange.modal("show");
      }
    });
  };

  vm.saveArranged = function() {
    vm.saveArrangedByParams(vm.arrangeParams.arranged);
  };

  vm.saveArrangedByParams = function(params) {
    vm.isBatchArrange = true;
    $confirmWarningBatchArrange.modal("hide");
    $confirmBatchArrange.modal("hide");
    $sendingModal.modal("show");
    newBatchArrangeService.saveArrange(params).then(function(res) {
      var jobId = res.data.jid;
      var intervalName = "job_" + jobId;
      window[intervalName] = setInterval(function() {
        vm.getsaveArrangedStatus(jobId, intervalName);
      }, 2000);
    });
  };

  vm.getsaveArrangedStatus = function(jobId, intervalName) {
    newBatchArrangeService.sendBatchArrangeMailStatus({job_id: jobId}).then(function(res) {
      if (res.data.status === "complete") {
        $sendingModal.modal("hide");
        deleteIntervalJob(intervalName);
        newBatchArrangeService.getArrangeWorkerData({job_id: jobId}).then(function(res) {
          var dataArrange = res.data;
          vm.dataConditionOrderCases = getErrorTypeMessages(dataArrange.errors, "condition");
          vm.dataWarningOrderCases = getErrorTypeMessages(dataArrange.errors, "warning");
          if (!_.isEmpty(vm.dataConditionOrderCases)) {
            vm.refreshPage();
            $errorConditionArrangeModal.modal("show");
          } else if (_.isEmpty(vm.dataConditionOrderCases) && !_.isEmpty(vm.dataWarningOrderCases)) {
            $confirmWarningArrangeModal.modal("show");
          } else {
            $closeBatchArrangeModal.modal("show");
            vm.refreshPage();
          }
        });
      }
    });
  };

  function getErrorTypeMessages(errors, errorType) {
    return _.filter(errors, function(staff) {
      return staff.type === errorType;
    });
  };

 vm.confirmWarningArrangeToArrange = function() {
    vm.isBatchArrange = true;
    $sendingModal.modal("show");
    $confirmWarningArrangeModal.modal("hide");
    var selectedStaffParams = vm.getStaffWarningSelected();
    vm.formatArrangeParams("arranged", false, true);
    newBatchArrangeService.saveArrange({warning: true, arrange: selectedStaffParams}).then(function(res) {
      var data = res.data;
      var jobId = data.jid;
      var intervalName = "job_" + jobId;
      window[intervalName] = setInterval(function() {
        vm.getsaveTemporaryArrangeWarningStatus(jobId, intervalName);
      }, 2000);
    });
  };

  vm.getsaveTemporaryArrangeWarningStatus = function(jobId, intervalName) {
    newBatchArrangeService.sendBatchArrangeMailStatus({job_id: jobId}).then(function(res) {
      if (res.data.status === "complete") {
        $sendingModal.modal("hide");
        deleteIntervalJob(intervalName);
        newBatchArrangeService.getArrangeWorkerData({job_id: jobId}).then(function(res) {
          var dataArrange = res.data;
          $closeBatchArrangeModal.modal("show");
          vm.refreshPage();
        });
      }
    });
  };

  vm.closeAllModal = function() {
    vm.chkAllWarningState = false;
    $closeBatchArrangeModal.modal("hide");
     vm.refreshPage();
  };

  vm.toggleAllWarningChkboxes = function() {
    var chkBoxState = !!vm.chkAllWarningState;
    vm.dataWarningOrderCases.forEach(function(staffOrderCase) {
      staffOrderCase.selected = chkBoxState;
    });
  };

  vm.toggleWarningChkbox = function(staffOrderCase) {
    var selectedState = vm.getStaffWarningSelected();
    vm.chkAllWarningState = selectedState.length === vm.dataWarningOrderCases.length;
  };

  vm.getStaffWarningSelected = function() {
    if (_.isNull(vm.dataWarningOrderCases)) {
      return [];
    }
    if (vm.dataWarningOrderCases) {
      return vm.dataWarningOrderCases.filter(function(staffOrderCase) {
        return !!staffOrderCase.selected;
      });
    }
  };

  vm.warningArrangeToArrange = function() {
    $conditionArrangeModal.modal("hide");
    if (!_.isEmpty(vm.dataWarningOrderCases)) {
      $confirmWarningArrangeModal.modal("show");
    } else {
      $closeBatchArrangeModal.modal("show");
      vm.refreshPage();
    }
  };

  vm.cancelTemporaryArrange = function() {
     vm.confirmCancelTempArrange();
  };

  // vm.openConfirmCancelTempArrangeModal = function() {
  //   $cancelTemporaryArrangeModal.modal("show");
  // };

  vm.confirmCancelTempArrange = function() {
    vm.formatArrangeParams("temporary_arranged");
    var selectedIds = vm.selectedStaffApplyOcIds.join(",");
    vm.confirmCancelTempArrangeById(selectedIds);
  };

  vm.confirmCancelTempArrangeById = function(ids) {
    $sendingModal.modal("show");
    newBatchArrangeService.cancelTemporaryArrange({id: ids}).then(function(res) {
      $sendingModal.modal("hide");
      if (res.data.status) {
        toaster.pop("success", "", I18n.t("admin.batch_arrange.cancel_temp_arrange_success"));
      } else {
        toaster.pop("error", "", I18n.t("admin.batch_arrange.cancel_temp_arrange_failed"));
      }
      $cancelTemporaryArrangeModal.modal("hide");
      vm.refreshPage();
    });
  };

  vm.showIsChangedPopup = function(staff, job) {
    vm.changable = {};
    vm.changable.isValid = true;
    vm.changable.arrange_condition_message = [];
    vm.changable.staff = staff;
    vm.changable.old = {};
    vm.changable.old.break_time = job.break_time;
    vm.changable.old.work_time = job.work_time;
    vm.changable.old.work_day = job.work_day;
    vm.changable.old.work_started_day = job.work_started_day;
    vm.changable.old.required_time = job.required_time;
    vm.changable.new = staff.changed_data;
    vm.changable.value = staff.changed_data.value;
    vm.changable.requested_transportation_fee = staff.changed_data.requested_transportation_fee;
    vm.changable.comment = staff.changed_data.comment;
    vm.changable.type = staff.changed_data.type;
    vm.changable.params = {};
    vm.changable.params.arrange = [];
    var jobData = {};
    jobData.id = staff.staff_apply_order_case_id;
    jobData.order_case_id = staff.order_case_id;
    jobData.staff_id = staff.id;
    jobData.store_note = staff.store_note;
    jobData.arrange_comment = staff.arrange_comment;
    jobData.status = staff.arrangement_status;
    if (staff.apply_status == "temporary_arrange") {
      jobData.status = staff.arrange_comment;
    }
    vm.changable.params.arrange.push(jobData);
    vm.changable.params.change_arrangement_time = true;
    var selectParams = {id: staff.staff_apply_order_case_id, selected_ids: ""};
    newBatchArrangeService.selectStaffApplyOrderCase(selectParams).then(function(res){
      var data = res.data
      vm.changable.isValid = data.status;
      vm.changable.arrange_condition_message = data.arrange_condition.arrange_condition_message;
      $changablePopupModal.modal("show");
    });
  };

  vm.isShowFlagNote = function(notes) {
    if (!notes) {return false;}
    return notes.insurance || notes.from_8 || notes.from_28 || notes.from_40;
  };

  vm.removeStaffApplyOrderCases = function() {
    $("#confirmation-modal").modal("show");
  };

  vm.confirmedSubmitDetail = function() {
    vm.formatArrangeParams("temporary_arranged");
    var selectedIds = vm.selectedStaffApplyOcIds.join(",");
    vm.removeStaffApplyOrderCasesById(selectedIds);
    $("#confirmation-modal").modal("hide");
  }

  vm.removeStaffApplyOrderCasesById = function(ids) {
    vm.removeStaffApplyOrderCasesMessages = {};
    newBatchArrangeService.deleteStaffApplyOrderCase({id: ids}).then(function(res) {
      var data = res.data;
      vm.removeStaffApplyOrderCasesMessages = data.data_messages;
      $removeStaffApplyOrderCasesPopup.modal("show");
      vm.refreshPage();
    });
  };

  vm.showWarningupdateBasicField = function(staff, column, targetStatus) {
    vm.updateBasicFieldData = {};
    vm.updateBasicFieldData.staff = staff;
    vm.updateBasicFieldData.column = column;
    vm.updateBasicFieldData.targetStatus = targetStatus;
    $updateBasicFieldConfirm.modal("show");
  };

  vm.updateBasicField = function(staff, column, targetStatus) {
    var params = {};
    params.arrangement = {};
    params.arrangement[column] = targetStatus;
    var submitParams = {working_time_status_id: targetStatus, confirm_status: false};
    newBatchArrangeService.updateBasicField(staff.arrangement_id, params).then(function(res) {
      $updateBasicFieldConfirm.modal("hide");
      var data = res.data;
      if (data.status) {
        staff.arrival = !!data.arrangement.is_arrived;
        staff.going_work = !!data.arrangement.is_prepared;
      } else  {
        vm.errorMessages = data.errors;
        $responseErrorMessages.modal("show");
      }
    });
  };

  vm.batchUpdatePortionStatus = function(targetStatus, option) {
    vm.formatArrangementsParams();
    var selectedIDs = vm.selectedArrangementsIds.join(",");
    var params = {id: selectedIDs, target_status: targetStatus};
    if (targetStatus == "absence" && !!option){
      params.is_replace_portion = true;
    }
    $sendingModal.modal("show");
    newBatchArrangeService.batchUpdatePortionStatus(params).then(function(res) {
      var data = res.data;
      $sendingModal.modal("hide");
      if (data.status) {
        vm.updatedIds = data.success_ids;
        vm.hasErrors = data.has_error;
        vm.fullErrorMessages = data.errors;
        if (vm.hasErrors) { $errorMessagePopup.modal("show"); }
        vm.refreshPage();
      } else {
        vm.errorMessages = data.errors;
        $responseErrorMessages.modal("show");
      }
    });
  };

  vm.formatArrangementsParams = function(){
    vm.selectedArrangementsIds = [];
    if (vm.totalSelectedStaff == 0) {return;}
    Object.keys(vm.selectedStaffs[vm.tabStatus]).forEach(function(jobId) {
      Object.keys(vm.selectedStaffs[vm.tabStatus][jobId]).forEach(function(StaffId) {
        if (vm.selectedStaffs[vm.tabStatus][jobId][StaffId]) {
          var jobs = vm.JobsByStatus[vm.tabStatus].filter(function(job){return job.id == jobId;})[0];
          var staff = jobs.staffs.filter(function(staff){return staff.id == StaffId;})[0];
          vm.selectedArrangementsIds.push(staff.arrangement_id);
        }
      });
    });
  };

  vm.batchAdminConfirmWorkAchievements = function(targetStatus) {
    vm.formatArrangementsParams();
    var selectedIDs = vm.selectedArrangementsIds.join(",");
    var params = {id: selectedIDs, action_type: targetStatus};
    $sendingModal.modal("show");
    newBatchArrangeService.batchAdminConfirmWorkAchievements(params).then(function(res) {
      var data = res.data;
      if (data.status) {
      $sendingModal.modal("hide");
        vm.updatedIds = data.success_ids;
        vm.hasErrors = data.has_error;
        vm.fullErrorMessages = data.errors;
        $errorMessagePopup.modal("show");
        vm.refreshPage();
      }
    });
  };

  vm.updateWorkAchievementWorkingTimeStatusId = function(staff) {
    var targetStatus = staff.work_achievement_status;
    var submitParams = {
      working_time_status_id: targetStatus,
      confirm_status: true
    };
    newBatchArrangeService.updateWorkAchievementWorkingTimeStatusId(submitParams, staff.arrangement_id).then(function(res) {
      if (res.data.status) {
        vm.updatedIds = staff.arrangement_id;
        vm.hasErrors = false;
        return;
      } else {
        vm.hasErrors = true;
        var errorMessage = I18n.t("admin.arrangements.update_working_status.error_message")[targetStatus];
        if (res.data.errors){
          var isLocked = JSON.parse(res.data.errors)["record_locked"];
          if (isLocked) {errorMessage = JSON.parse(res.data.errors)["record_locked"][0];}
        }
        var error = {};
        error[staff.arrangement_id] = errorMessage;
        vm.fullErrorMessages = error;
        $errorMessagePopup.modal("show");
      }
    });
  };

  vm.showNotes = function(orderCase, noteType, staff) {
    var params = {
      id: orderCase.id,
      note_type: noteType
    };
    if (!!staff) {
      if (staff.remarks.sumary == "0/3") {return;}
      params.arrangement_id = staff.arrangement_id;
    }
    vm.specialOfferNote = "";
    vm.orderNote = "";
    vm.locationNote = "";
    vm.arrangementNote = "";
    newBatchArrangeService.showNotes(params).then(function(res) {
      var data = res.data;
      if (data.status) {
        vm.specialOfferNote = data.special_offer_note;
        vm.orderNote = data.order_note;
        vm.locationNote = data.location_note;
        vm.arrangementNote = data.arrangement_note;
        $notesPopup.modal("show");
      }
    });
  };

  vm.showStaffCondition = function(job) {
    vm.resetSearchStaff();
    vm.staffSearchState.currentSelectedJob = job;
    vm.staffSearchState.currentOrderCaseId = job.id;
    vm.staffSearchState.isCalled = false;
    vm.staffSearchState.isOffered = false;
    vm.staffSearchState.availableJobPortion = job.available_portion;
    vm.staffSearchState.current_department_id = "";
    $("#search_staff_department_id").val(vm.staffSearchState.current_department_id).trigger("change.select2");
    $searchstaffConditionsPopup.modal("show");
  };

  vm.formatSearchStaffParams = function(resetPage) {
    var params = vm.staffSearchState.searchParams;
    if (!params) {params = {};}
    params.is_offered = !!vm.staffSearchState.isOffered;
    params.is_called = !!vm.staffSearchState.isCalled;
    params.per_page = vm.perPageSettings[0];
    if (!params.page || resetPage) {params.page = 1;}
    params.desc = true;
    _.forEach(STAFF_SEARCH_FIELDS, function(field) {
      if (!params[field]) {params[field] = "";}
    });
    vm.staffSearchState.searchParams = params;
  };

  vm.searchStaff = function(resetPage, isChangeCondition) {
    $searchstaffConditionsPopup.modal("hide");
    if (!!isChangeCondition) {
      vm.staffSearchState.searchStaffSelectedCount = 0;
      vm.staffSearchState.searchSelectedStaffIds = [];
    }
    vm.staffSearchState.staffs = {};
    vm.staffSearchState.searchStaffDone = false;
    vm.staffSearchState.currentSearchParams = {search: vm.staffSearchState.searchParams, id: vm.staffSearchState.currentOrderCaseId};
    newBatchArrangeService.searchStaffs(vm.staffSearchState.currentSearchParams).then(function(res) {
      vm.staffSearchState.searchParams.page = res.data.page;
      vm.staffSearchState.searchParams.per_page = res.data.per_page;
      vm.staffSearchState.totalStaff = res.data.total;
      vm.staffSearchState.totalOffered = res.data.total_offered;
      vm.staffSearchState.totalCalled = res.data.total_called;
      vm.staffSearchState.staffs = res.data.staffs;
      vm.staffSearchState.staffs.forEach(function(staff) {
        staff.selected = false;
        if (vm.staffSearchState.searchSelectedStaffIds.includes(staff.id)) {staff.selected = true;}
      });
      vm.staffSearchState.hasStaffs = Boolean(vm.staffSearchState.staffs.length);
      vm.staffSearchState.searchStaffDone = true;
      $staffsListPopup.modal("show");
    });
  };

  vm.changePageStaffs = function() {
    vm.formatSearchStaffParams()
    vm.searchStaff(false, false);
  };

  vm.showStaffsList = function() {
    vm.formatSearchStaffParams(true);
    vm.searchStaff();
  };

  vm.showOfferStaffsList = function(isOffered, isCalled, job) {
    vm.staffSearchState.isOffered = !vm.staffSearchState.isOffered && !!isOffered;
    vm.staffSearchState.isCalled = !vm.staffSearchState.isCalled && !!isCalled;
    vm.staffSearchState.searchSelectedStaffIds = [];
    vm.staffSearchState.searchStaffSelectedCount = 0;
    if (!vm.staffSearchState.currentSelectedJob || !vm.staffSearchState.currentOrderCaseId || !vm.staffSearchState.availableJobPortion) {
      vm.staffSearchState.currentSelectedJob = job;
      vm.staffSearchState.currentOrderCaseId = job.id;
      vm.staffSearchState.availableJobPortion= job.available_portion;
    }
    vm.formatSearchStaffParams(true);
    vm.searchStaff();
  };

  vm.closeStaffsList = function() {
    vm.resetSearchStaff();
    vm.staffSearchState.staffs = {};
    $staffsListPopup.modal("hide");
  };

  vm.tempArrangeBySearch = function(isShowWarning) {
    $confirmSearchTempArrangedStaff.modal("hide");
    vm.formatArrangeParams("temporary_arranged");
    var staffIds = vm.staffSearchState.searchSelectedStaffIds.join(",");
    vm.tempArrangeBySearchSubmitParams = {staff_id: staffIds, id: vm.staffSearchState.currentOrderCaseId, ignore_warning: !isShowWarning};
    if (NOT_REGULAR_SEGMENTS.includes(vm.searchParams.search.segment)) {
      vm.tempArrangeByParams(vm.tempArrangeBySearchSubmitParams, isShowWarning);
      return;
    }
    vm.regularParams = {}
    newBatchArrangeService.getRegularOrderPrice({order_case_id: vm.staffSearchState.currentOrderCaseId}).then(function(res){
      var data = res.data;
      if (data.status) {
        var orderSegmentId = data.order.order_segment_id;
        if (orderSegmentId == "regular_order") {
          vm.isShowErrorsPayment = false;
          vm.isShowErrorsBilling = false;
          vm.isBySearchStaff = true;
          BILLING_FIELDS.forEach(function(field){
            vm.regularParams[field] = res.data.arrange_billings[field];
          })
          PAYMENT_FIELDS.forEach(function(field){
            vm.regularParams[field] = res.data.arrange_payments[field];
          })
          vm.tempArrangeBySearchSubmitParams.ignore_warning = true;
          $regularInputPriceModal.modal("show");
        } else {
          vm.tempArrangeByParams(vm.tempArrangeBySearchSubmitParams, false);
        }
      } else {
        vm.errorMessages = data.errors;
        $responseErrorMessages.modal("show");
      }
    });
  };

  vm.tempArrangeByParams = function(submitParams, isShowWarning) {
    if (!!isShowWarning) {
      var submitParams = {staff_id: vm.staffSearchState.currentSelectedStaff.id,
        id: vm.staffSearchState.currentSelectedJob.id};
      newBatchArrangeService.saveTemporaryArrangeBySearch(submitParams).then(function(res) {
        var data = res.data;
        if (!data.status && !!data.errors){
          vm.errorMessages = data.errors;
          $responseErrorMessages.modal("show");
          return;
        }
        vm.popupState.confirmingStaff.isNotValid = !data.status;
        vm.popupState.confirmingStaff.isWarning = !!data.is_warning;
        vm.popupState.confirmingStaff.arrangeCondition = data.arrange_condition;
        vm.popupState.confirmingStaff.regularOrderCondition = data.regular_order_condition;
        if (vm.popupState.confirmingStaff.isNotValid || vm.popupState.confirmingStaff.isWarning) {
          $confirmSearchTempArrangedStaff.modal("show");
        } else {
          vm.tempArrangeByParams(submitParams, false);
        }
      });
    } else {
      $regularInputPriceModal.modal("hide");
      $confirmStaffArrange.modal("hide");
      $confirmSearchTempArrangedStaff.modal("hide");
      newBatchArrangeService.saveTemporaryArrangeBySearch(submitParams).then(function(res) {
        var data = res.data;
        vm.popupState.confirmingArrangedStaff = {}
        var isValid = data.status;
        vm.popupState.confirmingArrangedStaff.isNotValid = !isValid
        vm.popupState.confirmingArrangedStaff.isWarning = !!data.is_warning;
        if (isValid && !vm.popupState.confirmingArrangedStaff.isWarning) {
          var newsubmitParams = submitParams;
          newsubmitParams.ignore_warning = true
          newBatchArrangeService.saveTemporaryArrangeBySearch(newsubmitParams).then(function(newRes) {
            vm.closeStaffsList();
            vm.refreshPage();
          });
        } else {
          if (!!data.errors) {
            vm.errorMessages = data.errors;
            $responseErrorMessages.modal("show");
          } else {
            vm.popupState.confirmingArrangedStaff.arrangeCondition = data.arrange_condition;
            vm.popupState.confirmingArrangedStaff.regularOrderCondition = data.regular_order_condition;
            $confirmStaffArrange.modal("show");
          }
        }
      });
    }
  };

  vm.sendOfferMailToStaffs = function() {
    var staffIds = vm.staffSearchState.searchSelectedStaffIds.join(",");
    var submitParams = {staff_id: staffIds, id: vm.staffSearchState.currentOrderCaseId, note: ""};
    $offerSendingModal.modal("show");
    newBatchArrangeService.sendOfferMailToStaffs(submitParams).then(function(res) {
      if (res.data.status) {
        var jobId = res.data.job_id;
        var intervalName = "job_" + jobId;
        window[intervalName] = setInterval(function() {
          getOfferMailStatus(jobId, intervalName);
        }, 2000);
      } else {
        $offerSendingModal.modal("hide");
      }
    });
  };

  vm.confirmSendBatchOfferMail = function() {
    $confirmSendBatchOfferMail.modal("show");
  }

  vm.sendBatchOfferMailToStaffs = function() {
    $confirmSendBatchOfferMail.modal("hide");
    $offerSendingModal.modal("show");
    var searchParams = angular.copy(vm.staffSearchState.currentSearchParams);
    searchParams.note = "";
    newBatchArrangeService.sendBatchOfferMailToStaffs(searchParams).then(function(res) {
      if (res.data.status) {
        var jobId = res.data.job_id;
        var intervalName = "job_" + jobId;
        window[intervalName] = setInterval(function() {
          getOfferMailStatus(jobId, intervalName);
        }, 2000);
      } else {
        $offerSendingModal.modal("hide");
      }
    });
  };


  function getOfferMailStatus(jobId, intervalName) {
    newBatchArrangeService.sendOfferMailStatus({job_id: jobId}).then(function(res) {
      if (res.data.status === "complete") {
        vm.sentFailed = false;
        $offerSendingModal.modal("hide");
        deleteIntervalJob(intervalName);
        $errorMailPopup.modal("show");
        vm.refreshPage();
        if (!!vm.staffSearchState.currentOrderCaseId) {vm.changePageStaffs();}
      } else if ((res.data.status === "retrying" || res.data.status === "queued" || res.data.status === "failed") && res.data.percentage == null) {
        deleteIntervalJob(intervalName);
        $offerSendingModal.modal("hide");
        vm.sentFailed = true;
        $errorMailPopup.modal("show");
        if (!!vm.staffSearchState.currentOrderCaseId) {vm.changePageStaffs();}
      }
    }, function(err) {
      deleteIntervalJob(intervalName);
      $offerSendingModal.modal("hide");
      vm.sentFailed = true;
      $errorMailPopup.modal("show");
      if (!!vm.staffSearchState.currentOrderCaseId) {vm.changePageStaffs();}
    });
  };

  vm.updateOrderCaseOffer = function(submitParams) {
    $offerSendingModal.modal("show");
    newBatchArrangeService.updateOrderCaseOffer(submitParams).then(function(res) {
      var response = res.data;
      if (res.data.status) {
        vm.changePageStaffs();
        vm.refreshPage();
      }
      $offerSendingModal.modal("hide");
    });
  };

  vm.editMemo = function(staff) {
    var orderCaseId = vm.staffSearchState.currentOrderCaseId;
    if (!!orderCaseId) {orderCaseId = staff.order_case_id;}
    vm.updateOffer = {
      order_case_id: orderCaseId,
      staff_id: staff.staff_id,
      phone_status: staff.phone_status,
      is_offered: staff.is_offered,
      is_called: staff.is_called,
      phone_memo: staff.phone_memo
    };
    $editMemoPopup.modal("show");
  };

  vm.saveMemo = function() {
    vm.updatePhoneStatus(vm.updateOffer);
    $editMemoPopup.modal("hide");
  };

  vm.updatePhoneStatus = function(staff) {
    var isCalled = staff.is_called;
    if (staff.phone_status != "none_contact" || !!staff.phone_memo){isCalled = true;}
    var orderCaseId = vm.staffSearchState.currentOrderCaseId;
    if (!!orderCaseId) {orderCaseId = staff.order_case_id;}
    var params = {
      order_case_id: orderCaseId,
      staff_id: staff.staff_id,
      phone_status: staff.phone_status,
      is_offered: staff.is_offered,
      is_called: isCalled,
      phone_memo: staff.phone_memo
    };
    vm.updateOrderCaseOffer(params);
  };

  vm.selectStaffForJob = function(job, staff) {
    $confirmSelectStaff.modal("hide");
    var jobId = job.id;
    if (vm.tabStatus == "applying" && staff.note.is_changable ) {
      return toaster.pop("error", "", I18n.t("admin.batch_arrange.cannot_select_to_temporary_arrange"));
    }
    if (!vm.selectedStaffs[vm.tabStatus][jobId][staff.id]) {
      var selectedStaffType = vm.selectedStaffType[vm.tabStatus]
      if (vm.tabStatus == "applying") {
        var apply_status = staff.apply_status;
        if (staff.is_registration && job.segment != "contract") {
          apply_status = "registration";
        }
        if (selectedStaffType != apply_status && selectedStaffType != "") {
          return toaster.pop("error", "", I18n.t("admin.new_batch_arrange.errors.select_same_status"));
        }
        vm.selectedStaffType[vm.tabStatus] = apply_status;
      }
      vm.selectedStaffs[vm.tabStatus][jobId][staff.id] = true;
      vm.updateTotalSelected();
    } else {
      vm.selectedStaffs[vm.tabStatus][jobId][staff.id] = false;
      vm.updateTotalSelected();
      if (vm.tabStatus == "applying" && vm.totalSelectedStaff == 0) {
        vm.selectedStaffType[vm.tabStatus] = "";
      }
    }
  };

  vm.selectStaffApplyOrderCase = function(job, staff) {
    vm.regularState.staff = {};
    vm.regularState.job = {};
    if (job.segment == "regular_order" && vm.tabStatus == "applying" && vm.totalSelectedStaff == 0) {
      vm.regularState.staff = staff;
      vm.regularState.job = job;
      vm.selectStaffForJob(job, staff);
      return;
    }
    if ((vm.selectedStaffs[vm.tabStatus][job.id][staff.id] || vm.tabStatus != "applying") ||
      (staff.is_registration && vm.tabStatus == "applying")) {
      vm.selectStaffForJob(job, staff);
      return;
    }
    if (!staff.is_used_original_condition) {
      return toaster.pop("error", "", I18n.t("admin.batch_arrange.cannot_select_to_temporary_arrange"));
    }
    vm.staffSearchState.currentSelectedJob = job;
    vm.staffSearchState.currentSelectedStaff = staff;
    vm.formatArrangeParams("temporary_arranged");
    var selectedIds = vm.selectedStaffApplyOcIds.join(",");
    var selectParams = {id: staff.staff_apply_order_case_id, selected_ids: selectedIds};
    vm.selectStaffApplyOrderCaseByParams(selectParams);
  };

  vm.selectStaffApplyOrderCaseByParams = function(selectParams) {
    vm.resetPopupStage();
    vm.popupState.confirmingStaff.name = "applying";
    newBatchArrangeService.selectStaffApplyOrderCase(selectParams).then(function(res){
      var data = res.data
      var isValid = data.status;
      vm.popupState.confirmingStaff.isWarning = !!data.is_warning;
      vm.popupState.confirmingStaff.isNotValid = !isValid;
      vm.popupState.confirmingStaff.arrangeCondition = data.arrange_condition;
      vm.popupState.confirmingStaff.regularOrderCondition = data.regular_order_condition;
      if (!isValid || vm.popupState.confirmingStaff.isWarning) {
        $confirmSelectStaff.modal("show");
      } else {
        vm.selectStaffForJob(vm.staffSearchState.currentSelectedJob, vm.staffSearchState.currentSelectedStaff);
      }
    })
  };

  vm.formatHtml = function(content) {
    return $sce.trustAsHtml(content);
  };

  vm.selectStaffSearchApplyOrderCase = function(staff) {
    vm.staffSearchState.currentSelectedStaff = staff;
    var currentJob = vm.staffSearchState.currentSelectedJob;
    var currentStaff = staff;
    vm.selectStaffSearch(currentStaff, currentJob);
  };

  vm.selectStaffSearch = function(staff, job) {
    $confirmSelectStaff.modal("hide");
    staff.selected = !!staff.selected ? false : true;
    vm.staffSearchState.staffs.forEach(function(staff) {
      if (staff.selected && !vm.staffSearchState.searchSelectedStaffIds.includes(staff.id)) {
        vm.staffSearchState.searchSelectedStaffIds.push(staff.id)
      }
      if (!staff.selected && vm.staffSearchState.searchSelectedStaffIds.includes(staff.id)) {
        var idIndex = vm.staffSearchState.searchSelectedStaffIds.indexOf(staff.id);
        if (idIndex > -1) {vm.staffSearchState.searchSelectedStaffIds.splice(idIndex, 1);}
      }
    });
    vm.staffSearchState.searchStaffSelectedCount = vm.staffSearchState.searchSelectedStaffIds.length;
  };

  vm.unselectSearchStaff = function(){
    vm.staffSearchState.searchSelectedStaffIds = [];
    vm.staffSearchState.searchStaffSelectedCount = 0;
    vm.staffSearchState.staffs.forEach(function(staff) {
      staff.selected = false;
    });
  };

  vm.unselectedStaffs = function() {
    Object.keys(vm.selectedStaffs[vm.tabStatus]).forEach(function(jobId) {
      Object.keys(vm.selectedStaffs[vm.tabStatus][jobId]).forEach(function(StaffId) {
        vm.selectedStaffs[vm.tabStatus][jobId][StaffId] = false;
      });
    });
    vm.updateTotalSelected();
    if (vm.totalSelectedStaff == 0) {
      vm.selectedStaffType[vm.tabStatus] = "";
    }
  };

  vm.updateTotalSelected = function() {
    var totalSelected = 0;
    Object.keys(vm.selectedStaffs[vm.tabStatus]).forEach(function(jobId) {
      Object.keys(vm.selectedStaffs[vm.tabStatus][jobId]).forEach(function(StaffId) {
        if (vm.selectedStaffs[vm.tabStatus][jobId][StaffId]) {totalSelected = totalSelected + 1;}
      });
    });
    vm.totalSelectedStaff = totalSelected;
  };

  vm.isShowSearchStaff = function(job) {
    return job.order_case_status == "recruiting" || job.order_case_status == "adjusting";
  };

  vm.searchStaffSelectedClass = function(staff) {
    return staff.selected ? "checked" : "unchecked";
  };

  vm.actionForSeachStaffClass = function(actionType) {
    if (vm.staffSearchState.searchStaffSelectedCount == 0) {return "inactive";}
    if (vm.staffSearchState.searchStaffSelectedCount > vm.staffSearchState.availableJobPortion && actionType == "temporary_arrange") {return "inactive";}
    return "active";
  };

  vm.staffSelectedClass = function(job, staff) {
    var jobId = job.id;
    if (vm.tabStatus == "applying" && staff.note.is_changable && staff.apply_status != "temporary_arrange") {return 'invisible';}
    if (DISABLE_SELECT_STATUS.includes(staff.apply_status)) {return 'invisible';}
    return vm.selectedStaffs[vm.tabStatus][jobId][staff.id] ? "checked" : "unchecked";
  };

  vm.btnActionActiveClass = function(actionType) {
    if (actionType == "sent_batch_offer") {
      return vm.total > 0 ? 'active' : 'inactive'
    }
    var status = "inactive";
    if (vm.totalSelectedStaff == 0) {return "inactive";}
    var selectStatus = vm.selectedStaffType.applying;
    if (selectStatus == "registration") {
      return actionType == "remove_staff_apply" ? "active" : "inactive";
    }
    switch(actionType) {
      case "cancel_temporary_arrange":
        if (selectStatus == "temporary_arrange") {status = "active";}
        break;
      case "temporary_arrange":
        if (selectStatus != "temporary_arrange") {status = "active";}
        break;
      case "remove_staff_apply":
        if (selectStatus == "not_processed") {status = "active";}
        break;
      case "arranged":
      case "absence":
      case "batch_confirm_work_achievements":
      case "batch_unconfirm_work_achievements":
      case "cancel_after_arrange_has_insurance":
      case "cancel_after_arrange_no_insurance":
        status = "active";
        break;
      default:
        status = "inactive";
    }
    return status;
  };

  function deleteIntervalJob(intervalName) {
    clearInterval(window[intervalName]);
    delete window[intervalName];
  }

  vm.showRegularOrderWorkTime = function(job) {
    newBatchArrangeService.getRegularOrderTime({order_id: job.order_id}).then(function(res) {
      if (res.data) {
        vm.regularState.order = angular.copy(res.data.order);
        vm.regularState.jobs = angular.copy(res.data.order_cases);
        $regularTimeModal.modal("show");
      }
    });
  };

  vm.arrangementNotes = {};
  vm.createNotes = function(job) {
    vm.arrangementNotes = {};
    newBatchArrangeService.getOrderCaseNotes({order_case_id: job.id}).then(function(res) {
      vm.arrangementNotes = res.data.arrangements;
      $("#create-note-popup").modal("show");
    });
  };

  vm.saveArrangementNotes = function() {
    var params = {arrangements: vm.arrangementNotes};
    newBatchArrangeService.saveOrderCaseNotes(params).then(function(res) {
      if (res.data.status) {
        $("#create-note-popup").modal("hide");
        vm.refreshPage();
      }
    });
  };

  vm.showStaffNotesPopup = function(staff) {
    vm.staffNotes = {}
    newBatchArrangeService.getStaffNotes(staff.id).then(function(res) {
      if (res.data.status && res.data.has_notes) {
        vm.staffNotes = res.data.notes;
        $("#staff-notes-popup").modal("show");
      }
    });
  };

  function onlyUnique(value, index, self) {
    return self.indexOf(value) === index;
  }

  vm.getNoteLabel = function(label) {
    return I18n.t("admin.new_batch_arrange.column." + label)
  };

  vm.sortByColumn = function(columnName) {
    vm.JobsByStatus[vm.tabStatus].forEach(function(job) {
      var sortedStaffs = job.staffs.sort(function(a, b){
        switch (columnName){
        case "absence":
        case "working":
          var firstRecord = a.absence_working_relevant[columnName];
          var secondRecord = b.absence_working_relevant[columnName];
          break;
        case "difference":
          var firstRecord = a.difference.value;
          var secondRecord = b.difference.value;
          break;
        default:
          var firstRecord = a[columnName];
          var secondRecord = b[columnName];
        }
        return (firstRecord > secondRecord) ? 1 : (firstRecord < secondRecord) ? -1 : 0;
      });
      job.staffs = sortedStaffs;
    });
  };

  vm.goToLocationPage = function(job) {
    window.open("/locations/" + job.location_id + "/edit", "_blank");
  };

  vm.goToCorporationPage = function(job) {
    window.open("/corporations/" + job.corporation_id + "/edit", "_blank");
  };

  vm.goToStaffPage = function(staff) {
    var urlType = "/registrations/";
    if (staff.status_id == "op_confirm") {urlType = "/staffs/";}
    window.open(urlType + staff.id + "/edit", "_blank");
  };

  vm.goToSearchYahoo = function(job, staff) {
    var startTime = staff.requested_started_at;
    if (_.isNull(startTime)) {
      startTime = job.case_started_at_original;
    };
    startTime = moment.parseZone(startTime);
    var minutes = startTime.format("mm").split("");
    var params = {
      from: staff.staff_home_station_station_name,
      to: job.location_stations_1_station_name,
      y: startTime.format("YYYY"),
      m: startTime.format("MM"),
      d: startTime.format("DD"),
      hh: startTime.format("HH"),
      m2: minutes[1],
      m1: minutes[0],
      type: 1,
      ticket: "normal",
      expkind: 1,
      ws: 3,
      s: 0,
      al: 1,
      shin: 1,
      ex: 1,
      hb: 1,
      lb: 1,
      sr: 1
    };
    var paramsList = [];
    _.forEach(params, function(value, key) {
      paramsList.push(key + "=" + value);
    });
    var newWindow =  window.open("https://transit.yahoo.co.jp/search/result?" + paramsList.join("&"), "_blank");
    newWindow.opener = null
  };

  vm.openOfferMailModal = function() {
    var searchParams = formatParamsBeforeSearch(angular.copy(vm.searchParams.search));
    newBatchArrangeService.checkMultipleDepartments({search: searchParams}).then(function(res) {
      if (res.data.status) {
        $("#offer-job-mail-modal").modal("show");
      } else {
        $("#modal-dont-send-offer-email").modal("show");
      }
    });
    vm.$scope.offerJobMailModal = {notifyChanges: true, search: searchParams};
  };

  function formatParamsBeforeSearch(params) {
    var newsubmitParams = {};
    newsubmitParams.working_started_at = params.working_from_date;
    newsubmitParams.working_ended_at = params.working_to_date;
    newsubmitParams.working_ended_at = params.working_to_date;
    newsubmitParams.corporation_id = params.corporation_id;
    newsubmitParams.location_id = params.location_id;
    newsubmitParams.department_id = params.department_id;
    newsubmitParams.staff_id = params.staff_id;
    newsubmitParams.order_case_segment_id = SEGMENT_IDS_BY_SEGMENT[params.segment];
    return newsubmitParams;
  };

  vm.updatePopupHeight = function(doomID) {
    var $popup = angular.element("#" + doomID)[0];
    var $popupBody = angular.element("#" + doomID + " .modal-body")
    var $popupselection = $popup.getElementsByClassName("select2-selection__rendered");
    var additionHeight = $popupselection[0].offsetHeight - SELECT_TAGS_MAX_HEIGHT;
    if(additionHeight <= 0) {return;}
    var bodyHeight = $popupBody[0].offsetHeight - SELECT_TAGS_BODY_PADDING;
    $popupBody.height(SELECT_TAGS_BODY_MAX_HEIGHT + additionHeight);
  };
}
