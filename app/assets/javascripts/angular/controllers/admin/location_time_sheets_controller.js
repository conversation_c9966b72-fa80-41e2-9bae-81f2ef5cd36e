angular.module("adminApp").controller("LocationTimeSheetsController", LocationTimeSheetsController);

LocationTimeSheetsController.$inject = ["$location", "locationTimeSheetService",
  "checkValidDateFunction", "adminService"];

function LocationTimeSheetsController($location, locationTimeSheetService, checkValidDateFunction, adminService) {
  var vm = this;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  DATETIME_FIELDS = ["working_started_at", "working_ended_at"];
  SELECT_FIELDS = ["location_id", "staff_id", "corporation_id"];
  ZIP_TYPE = "zip";
  vm.params = $location.search();
  vm.params.per_page = parseInt(vm.params.per_page) || 10;
  vm.status_exported = false;
  vm.status_not_exported = false;
  if (vm.params.already_exported == "true") {
    vm.status_exported = true;
  }
  if (vm.params.already_exported == "false") {
    vm.status_not_exported = true;
  }
  vm.total_items = 0;
  vm.init = function() {
    vm.search(true);
  };

  vm.search = function(isResetPage) {
    if (isResetPage) {
      vm.params.page = 1;
    }
    var search_params = formatParamsBeforeSearch(angular.copy(vm.params));
    locationTimeSheetService.getArranges({search: search_params}).then(function(res) {
      angular.extend(vm, res.data);
    });
    vm.selectAll = false;
    vm.isSelected = false;
    $location.search(search_params).replace();
    triggerSelectFields();
  };

  vm.checkAll = function() {
    _.forEach(vm.arrangements, function(arrangement) {
      arrangement.select = vm.selectAll;
    });
    vm.checkSelected(true);
  };

  vm.checkSelected = function(isChecked) {
   var selectedItems = _.find(vm.arrangements, {select: true});
   vm.isSelected = selectedItems ? true : false;
   if (vm.selectAll && !isChecked) {
     vm.selectAll = false;
   };
  };

  vm.updateExportedParams = function() {
    if (vm.status_exported) {
      vm.params.already_exported = true;
    } else {
      vm.params.already_exported = false;
    }
    if (vm.status_not_exported) {
      vm.params.already_exported = false;
    }
    if ((vm.status_not_exported && vm.status_exported) || (!vm.status_not_exported && !vm.status_exported)) {
      vm.params.already_exported = "";
    }
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".arrangement-search-result").offset().top
    }, 1000);
  };

  vm.changePerPage = function() {
    vm.search();
    vm.scrollToResult();
  };

  vm.downloadPdf = function(e) {
    var selectedArrangements = _.filter(vm.arrangements, function(arrangement) {
      return arrangement.select;
    });
    var selectedIds = _.map(selectedArrangements, "id");
    $.asyncDownload(e, "location_time_sheets", JSON.stringify({id: selectedIds}), ZIP_TYPE);
  };

  function triggerSelectFields() {
    DATETIME_FIELDS.forEach(function(attr) {
      $("[name='" + attr + "']").datepicker("setDate", new Date(vm.params[attr]));
    });
    SELECT_FIELDS.forEach(function(field) {
      $("#" + field).val(vm.params[field]).trigger("change.select2");
    });
  }

  function formatParamsBeforeSearch(params) {
    params.staff_id = Array.isArray(params.staff_id) ? params.staff_id.join(",") : params.staff_id;
    params.location_id = Array.isArray(params.location_id) ? params.location_id.join(",") : params.location_id;
    params.corporation_id = Array.isArray(params.corporation_id) ? params.corporation_id.join(",") : params.corporation_id;
    return params;
  }

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  };
}
