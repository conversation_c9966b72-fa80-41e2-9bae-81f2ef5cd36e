'use strict';

angular.module('adminApp').controller('RoomsController', RoomsController);
RoomsController.$inject = ['roomService'];

function RoomsController(roomService) {
  var vm = this;
  var searchParamKeys = ['keyword'];
  vm.hasRecord = vm.noRecord = false;
  vm.params = {};
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.idSelected = "";
  vm.enableCall = true;
  vm.init = function() {
    vm.search(true);
  };

  vm.search = function(resetPage) {
    if (vm.enableCall) {
      vm.enableCall = false;
      if (resetPage) {
        vm.params.page = 1;
      };
      vm.params.per_page = vm.params.per_page || vm.perPageSettings[0];
      roomService.searchRoom(vm.params).then(function(res) {
        angular.extend(vm, res.data);
        vm.hasRecord = Boolean(vm.rooms.length);
        vm.noRecord = !vm.hasRecord;
        vm.enableCall = true;
      });
    }
  };

  vm.changePerPage = function() {
    vm.search(false);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".room-list-result").offset().top
    }, 1000);
  };

  vm.confirmDelete = function(id) {
    vm.idSelected = id;
    $("#modal-delete-room").modal("show");
  };

  vm.destroyRoom = function() {
    if (vm.enableCall) {
      vm.enableCall = false;
      roomService.destroy(vm.idSelected).then(function(res) {
        if (res.status == 200 && res.data.status) {
          vm.enableCall = true;
          vm.search(false);
        }
      });
    }
  };
}
