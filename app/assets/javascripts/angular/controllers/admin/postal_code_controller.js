'use strict';

angular.module('adminApp')
  .controller('PostalCodeController', PostalCodeController);
PostalCodeController.$inject = ['$location', 'postalCodeService'];

function PostalCodeController($location, postalCodeService) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  vm.currentPage = 1;

  vm.params = {
    page: 1,
    postal_code: ""
  };

  vm.refresh = function() {
    vm.params.page = vm.currentPage;
    vm.params.postal_code = vm.postalCode;
    postalCodeService.getPostalCode(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.postal_codes.length);
      vm.noRecord = !vm.hasRecord;
      vm.params.limit = res.data.pagination_limit;
    }, function(error) {
    });
  };
}
