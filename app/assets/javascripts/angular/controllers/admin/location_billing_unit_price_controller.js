"use strict";

angular.module('adminApp').directive("directiveWhenScrolled", function() {
  return function(scope, elm, attr) {
    var raw = elm[0];
    elm.bind('scroll', function() {
      if (raw.scrollTop + raw.offsetHeight >= raw.scrollHeight) {
        scope.$apply(attr.directiveWhenScrolled);
      }
    });
  };
});

angular.module("adminApp").controller("LocationBillingUnitPriceController", LocationBillingUnitPriceController);
LocationBillingUnitPriceController.$inject = ["$scope", "locationBillingUnitPriceService"];

function LocationBillingUnitPriceController($scope, locationBillingUnitPriceService) {
  var vm = this;
  vm.$scope = $scope;
  vm.searchParams = {};
  vm.errors = [];
  vm.importFail = false;
  vm.importSuccess = false;

  vm.init = function() {
    vm.page = 1;
    vm.per_page = 10;
  }

  vm.search = function() {
    var corporationId = $("#corporation-select").val();
    var submitedParams = {
      corporation_id: corporationId,
      location_name: vm.searchParams.name,
      page: vm.page,
      per_page: vm.per_page
    }
    locationBillingUnitPriceService.searchBillingUnitPriceByLocations(submitedParams).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.locations.length);
    });
  };

  vm.importFile = function() {
    var file = $("#location_billing_unit_price_file").prop("files")[0];
    if (_.isUndefined(file)) {
      return
    }
    var formData = new FormData();
    formData.append("file", file);
    $("#spinner").removeClass("ng-hide");
    $.ajax({
      url: "/location_billing_unit_prices/import_file",
      processData: false,
      contentType: false,
      method: "POST",
      dataType: "json",
      data: formData,
      success: function(response) {
        $("#location_billing_unit_price_file").val(null);
        vm.$scope.$apply(function() {
          if (response.status) {
            vm.importSuccess = true;
            vm.importFail = false;
          } else {
            vm.importFail = true;
            vm.importSuccess = false;
          }
          vm.messages = response.messages;
          window.scrollTo(0,0);
        });
        $("#spinner").addClass("ng-hide");
      }
    })
  }
}
