'use strict';

angular.module('adminApp')
  .controller('PeriodRatesController', PeriodRatesController);
  PeriodRatesController.$inject = ['periodRatesService'];

function PeriodRatesController(periodRatesService) {
  var vm = this;
  vm.search_params = {};
  vm.sort_by = {};

  vm.init = function () {
    vm.page = 1;
    vm.per_page = 10;
    vm.search();
  };

  vm.search = function() {
    var params = {
      page: vm.page,
      per_page: vm.per_page,
      search: vm.search_params,
      sort_by: vm.sort_by
    };

    $(".search-btn").prop("disabled", true);
    periodRatesService.getPeriodRates(vm.peakPeriodId, params).then(function (res) {
      vm.periodRates = res.data.data;
      vm.total_count = res.data.total_count;
      $(".search-btn").prop("disabled", false);
    });
  };

  vm.clearSearchCondition = function () {
    vm.search_params = {};
    vm.sort_by = {};

    setTimeout(function() {
      angular.element("select.select2-multiple, select.select2-prefecture").trigger("change");
    }, 10);
  };

  vm.sortBy = function (column) {
    if (vm.sort_by[column]) {
      vm.sort_by[column] = vm.sort_by[column] === 'desc' ? 'asc' : null;
    } else {
      vm.sort_by[column] = 'desc';
    }

    vm.search();
  };

  vm.classForSortColumn = function (column) {
    if (vm.sort_by[column]) {
      var type = vm.sort_by[column] === 'desc' ? 'down' : 'up'
      return 'fa-sort-' + type;
    } else {
      return 'fa-sort';
    }
  };

  $('#target-date-datepicker').datepicker({
    format: "yyyy/mm/dd",
    autoclose: true
  });

  $('.select2-prefecture').select2({
    closeOnSelect: false,
    placeholder: I18n.t("common.please_select"),
    width: "100%",
    ajax: {
      url: "/prefectures",
      dataType: 'json',
      delay: 500,
      data: function(params) {
        return {
          search: params.term,
          page: params.page
        };
      },
      processResults: function(data, params) {
        return {
          results: data.data,
          pagination: {
            more: (params.page * 10) < data.total_count
          }
        };
      }
    }
  })

  $('[data-toggle="popover"]').popover();
  
  $(document).on('click', function (e) {
    if (!$(e.target).closest('[data-toggle="popover"]').length) {
      $('[data-toggle="popover"]').popover('hide');
    }
  });
}