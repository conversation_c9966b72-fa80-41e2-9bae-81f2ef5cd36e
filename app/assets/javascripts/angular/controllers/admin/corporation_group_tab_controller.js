'use strict';

angular.module('adminApp')
  .controller('CorporationGroupTabController', CorporationGroupTabController);
CorporationGroupTabController.$inject = ['$location', 'corporationGroupService'];

function CorporationGroupTabController($location, corporationGroupService) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  vm.currentPage = 1;

  vm.params = {
    corporation_id: angular.element('#corporation-group').data('corporation-id'),
    page: 1
  };

  vm.refresh = function() {
    vm.params.page = vm.currentPage;
    corporationGroupService.getCorporationGroup(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.corporation_groups.length);
      vm.noRecord = !vm.hasRecord;
      vm.params.limit = res.data.pagination_limit;
    }, function(error) {
    });
  };
}
