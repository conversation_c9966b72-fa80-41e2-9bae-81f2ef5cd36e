'use strict';

angular.module('adminApp')
  .controller('PeriodRateSetDefaultsController', PeriodRateSetDefaultsController);
PeriodRateSetDefaultsController.$inject = ['periodRateDefaultService'];

function PeriodRateSetDefaultsController(periodRateDefaultService) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  vm.errorMessages = {};
  vm.params = {};

  vm.init = function () {
    vm.search();
  };

  var editModal = $('#modal-edit-period-rate');
  var newModal = $('#modal-new-period-rate');
  var deleteModal = $('#modal-delete-period-rate');
  var submitButton = $('#btn-submit');

  editModal.on("hidden.bs.modal", function () {
    vm.selectedPeriodRate = {};
    vm.errorMessages = {};
    vm.showError = false;
  });

  newModal.on("hidden.bs.modal", function () {
    vm.newPeriodRate = {};
    vm.errorMessages = {};
    vm.showError = false;
  });

  deleteModal.on("hidden.bs.modal", function () {
    vm.selectedPeriodRate = {};
    vm.prefectureRate = {};
  });

  vm.search = function () {
    var params = {
      search: vm.params
    };
  
    periodRateDefaultService.getDefaultPeriodRates(params).then(function (res) {
      vm.periodRates = res.data;
    });
  };

  vm.editPeriodRate = function (periodRateId) {
    periodRateDefaultService.editDefaultPeriodRate(periodRateId).then(function (res) {
      vm.corporations = [
        {
          id: res.data.corporation_id,
          full_name: res.data.corporation_name
        }
      ];
      vm.selectedPeriodRate = res.data;
      editModal.modal("show");
    }).then(function (error) {
      toastr.error(error.statusText);
    });
  };

  vm.updatePeriodRate = function (periodRateId) {
    var is_all = vm.selectedPeriodRate.corporation_id === -1 || vm.selectedPeriodRate.corporation_id === null;
    var params = {
      corporation_id: is_all ? null : vm.selectedPeriodRate.corporation_id,
      unit_rate: vm.selectedPeriodRate.unit_rate,
      order_rate: vm.selectedPeriodRate.order_rate,
      prefecture_id: vm.selectedPeriodRate.prefecture_id,
      is_all: is_all
    }

    checkValidInput(params);
    if (vm.showError) return;

    showSpinner();
    disableSubmitButton("btn-update");
    periodRateDefaultService.updateDefaultRate(periodRateId, params).then(function (res) {
      if (res.data.status) {
        toastr.success(res.data.message);
        location.reload();
      } else {
        vm.errorMessages = res.data.message;
        vm.showError = true;
        enableSubmitButton("btn-update");
      };
    }, function (error) {
      toastr.error(error.statusText);
      enableSubmitButton("btn-update");
    }).then(function () {
      hideSpinner();
    });
  };

  vm.addNewPeriodRate = function(prefecture) {
    vm.newPeriodRate = {
      prefecture_id: prefecture.prefecture_id,
      prefecture_name: prefecture.prefecture_name,
      is_default: true
    }

    newModal.modal("show");
  }

  vm.createPeriodRate = function() {
    var is_all = vm.newPeriodRate.corporation_id === -1;
    var params = {
      unit_rate: vm.newPeriodRate.unit_rate,
      order_rate: vm.newPeriodRate.order_rate,
      prefecture_id: vm.newPeriodRate.prefecture_id,
      is_default: vm.newPeriodRate.is_default,
      corporation_id: is_all ? null : vm.newPeriodRate.corporation_id,
      is_all: is_all
    }

    checkValidInput(params);
    if (vm.showError) return;

    disableSubmitButton("btn-submit");
    showSpinner();
    periodRateDefaultService.createPeriodRate(params).then(function (res) {
      if (res.data.status) {
        toastr.success(res.data.message);
        location.reload();
      } else {
        vm.errorMessages = res.data.message;
        vm.showError = true;
        enableSubmitButton("btn-submit");
      };
    }, function (error) {
      toastr.error(error.statusText);
      enableSubmitButton("btn-submit");
    }).then(function(res) {
      hideSpinner();
    });
  };

  vm.confirmDelete = function(selectedRateId) {
    periodRateDefaultService.getDestroyedWarning(selectedRateId).then(function(res) {
      var response = res.data
      if (response.status) {
        vm.selectedPeriodRate = response.current_period_rate;
        vm.newRate = response.new_period_rate;
        deleteModal.modal("show");
      }
    }, function(error) {
      toastr.error(error.statusText);
    });
  };

  vm.deletePeriodRate = function(periodRateId) {
    disableSubmitButton("confirm-delete");
    periodRateDefaultService.deletePeriodRate(periodRateId).then(function(res) {
      if (res.data.status) {
        toastr.success(res.data.message);
        location.reload();
      } else {
        toastr.error(res.data.message);
        enableSubmitButton("confirm-delete");
      }
    }, function(error) {
      toastr.error(error.statusText);
      enableSubmitButton("confirm-delete");
    });
  };

  function checkValidInput(params) {
    vm.showError = true;

    if (params.unit_rate == null) {
      vm.errorMessages.unit_rate = [errorMessage('period_rate', 'unit_rate', 'required')];
    }

    if (params.order_rate == null) {
      vm.errorMessages.order_rate = [errorMessage('period_rate', 'order_rate', 'required')];
    }

    if (Object.keys(vm.errorMessages).length !== 0) return;

    vm.showError = false;
  }

  function errorMessage(model, attributes_name, type) {
    var attributeName = I18n.t(["activerecord.attributes", model, attributes_name].join("."));
    return I18n.t("activerecord.errors.messages." +  type, {attribute: attributeName});
  };

  vm.clearErrorMessages = function (type) {
    delete vm.errorMessages[type];
  }

  function showSpinner() {
    $("#spinner").removeClass("ng-hide");
  };

  function hideSpinner() {
    $("#spinner").addClass("ng-hide");
  };

  function disableSubmitButton(buttonId) {
    $("#" + buttonId).prop("disabled", true);
  }

  function enableSubmitButton(buttonId) {
    $("#" + buttonId).prop("disabled", false);
  }

  $('.select2-prefecture').select2({
    closeOnSelect: false,
    placeholder: I18n.t("common.please_select"),
    ajax: {
      url: "/prefectures",
      dataType: 'json',
      delay: 500,
      data: function(params) {
        return {
          search: params.term,
          page: params.page
        };
      },
      processResults: function(data, params) {
        return {
          results: data.data,
          pagination: {
            more: (params.page * 10) < data.total_count
          }
        };
      }
    }
  });
}