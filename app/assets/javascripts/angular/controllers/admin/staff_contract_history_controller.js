"use strict";

angular.module("adminApp")
  .controller("StaffContractHistoryController", StaffContractHistoryController);
StaffContractHistoryController.$inject = ["staffContractHistoryService", "$scope", "$timeout"];

function StaffContractHistoryController(staffContractHistoryService, $scope, $timeout) {
  var vm = this;
  vm.$scope = $scope;
  vm.enumIndefiniteEmploymentFlags = {employmentFlagBlank: "employment_flag_blank", term: "term", indefinite: "indefinite"};
  vm.contractTypeBlank = "blank_column";
  vm.isEditContract = false;
  var staffContractHistoryTemplate = {
    id: null,
    indefinite_employment_flag: vm.enumIndefiniteEmploymentFlags.employmentFlagBlank,
    contract_start_date: null,
    contract_end_date: null,
    contract_type: vm.contractTypeBlank,
    note: null,
    _destroy: false
  };
  var DATA_HOLDER_IDS = ["indefinite_employment_flags", "contract_types", "staff_id", "staff_last_contract"];
  var DATE_ATTRS = ["contract_start_date", "contract_end_date"];
  var $modalContract = angular.element("#modal-contract-histories");
  vm.staff_contract_histories = [];
  var limitOffset = 10;
  var offset = 0;
  var noMoreRecords = false;

  vm.addContract = function() {
    vm.isEditContract = true;
    var template = angular.copy(staffContractHistoryTemplate);
    vm.staff_contract_histories.push(template);
    vm.contractErrors.push({});
    setDatePicker();
  };

  vm.deleteContract = function(index) {
    vm.isEditContract = true;
    if (vm.staff_contract_histories[index].id)
      vm.staff_contract_histories[index]._destroy = true;
    else {
      vm.staff_contract_histories.splice(index, 1);
      vm.contractErrors.splice(index, 1);
    }
  };

  vm.initData = function() {
    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });
    vm.contractErrors = [];
    // vm.setCurrentContract();
    vm.currentContract = formatContractDate(vm.staff_last_contract)
    setDatePicker();
    vm.isEditContract = false;
  }

  function formatData() {
    vm.staff_contract_histories.forEach(function(contract) {
      DATE_ATTRS.forEach(function(attr) {
        if (contract[attr]) contract[attr] = moment.parseZone(contract[attr]).format(FULL_DATE_FORMAT);
      })
    });
  }

  function formatContractDate(contract) {
    DATE_ATTRS.forEach(function(attr) {
      if (contract[attr]) contract[attr] = moment.parseZone(contract[attr]).format(FULL_DATE_FORMAT);
    })
    
    return contract;
  }

  function setDatePicker() {
    setTimeout(function() {
      $(".staff-date-picker").datepicker({
        autoclose: true,
        format: DEFAULT_DATE_TIME_FORMAT,
        todayBtn: true,
        todayHighlight: true
      }).click();
    }, 0);
  }

  vm.validateContracts = function() {
    if (!vm.isEditContract) {
      $modalContract.modal("hide");
      return;
    }
    var contracts = {};
    _.forEach(vm.staff_contract_histories, function(contract, index) {
      contracts[index] = contract;
    });
    var params = {staff_id: vm.staff_id, staff_contract_histories_attributes: contracts};
    staffContractHistoryService.validateContracts(params).then(function mySuccess(response) {
      var status = response.data.status;
      if (!status) {
        vm.contractErrors = response.data.errors;
        return;
      }
      if (!!vm.staff_id && vm.isEditContract) {
        vm.saveContracts(params);
        return;
      } else {
        vm.setCurrentContract();
        vm.contractErrors = [];
        vm.isSuccessValidate = true;
        $modalContract.modal("hide");
      }
    });
  }

  vm.saveContracts = function(params) {
    staffContractHistoryService.saveContracts(params).then(function mySuccess(response) {
      if (response.data.status) {
        vm.setCurrentContract();
        vm.isSuccessValidate = true;
        vm.contractErrors = [];
        vm.staff_contract_histories = angular.copy(response.data.staff_contract_histories);
        vm.copyContractHistories = angular.copy(vm.staff_contract_histories);
        vm.isEditContract = false;
        $modalContract.modal("hide");
      } else {
        vm.contractErrors = response.data.errors;
      }
    });
  }

  vm.classForFormErrors = function(index, inputName) {
    return {
      "has-error": vm.contractErrors[index] && vm.contractErrors[index][inputName]
        && !!vm.contractErrors[index][inputName][0]
    }
  }

  $modalContract.on('shown.bs.modal', function () {
    vm.isSuccessValidate = false;
    vm.$scope.$apply(function() {
      vm.isEditContract = false;
      vm.copyContractHistories = angular.copy(vm.staff_contract_histories);
      vm.copyContractErrors = angular.copy(vm.contractErrors);
    })
  });

  $modalContract.on('hidden.bs.modal', function () {
    if (vm.isSuccessValidate) return;
    vm.$scope.$apply(function() {
      vm.staff_contract_histories = angular.copy(vm.copyContractHistories);
      vm.contractErrors = angular.copy(vm.copyContractErrors);
    });
    offset = 0;
    noMoreRecords = false;
    vm.staff_contract_histories = [];
  });

  vm.setCurrentContract = function() {
    var currentContract = angular.copy(staffContractHistoryTemplate);
    var contracts = _.filter(vm.staff_contract_histories, function(contract) {
      return !contract._destroy && contract.contract_start_date;
    });
    contracts = contracts.reverse();
    if (contracts.length > 0) {
      currentContract = contracts[0];
      _.forEach(contracts, function(contract, index){
        if (!contract._destroy) {
          if (moment(currentContract.contract_start_date).isBefore(moment(contract.contract_start_date))) {
            currentContract = contract;
          }
        }
      });
    }
    vm.currentContract = angular.copy(currentContract);
  }

  vm.resetContractField = function(contract) {
    vm.isEditContract = true;
    switch(contract.indefinite_employment_flag) {
      case vm.enumIndefiniteEmploymentFlags.employmentFlagBlank:
        contract.contract_start_date = null;
        contract.contract_end_date = null;
        contract.contract_type = vm.contractTypeBlank;
        break;
      case vm.enumIndefiniteEmploymentFlags.indefinite:
        contract.contract_end_date = null;
        contract.contract_type = vm.contractTypeBlank;
        break;
    }
  }

  vm.getStaffContractHistories = function() {
    offset += limitOffset;
    if (noMoreRecords) return;

    var params = {
      staff_id: vm.staff_id,
      infinite_scroll: true,
      per_page: offset
    };

    staffContractHistoryService.getStaffContractHistories(params).then(function (response) {
      noMoreRecords = response.data.length == vm.staff_contract_histories.length;
      if (noMoreRecords) return;

      $timeout(function () {
        vm.staff_contract_histories = response.data.map(function (contract) {
          return formatContractDate(contract);
        });

        $timeout(function (){
          $("#staff_contract_histories").scrollTop(300);
        }, 0);
      }, 0);
    })
  };

  $("#staff_contract_histories").on("scroll", function() {
    if ($(this).scrollTop() === 0 && vm.staff_contract_histories.length > 0) {
      vm.getStaffContractHistories();
    }
  });
}
