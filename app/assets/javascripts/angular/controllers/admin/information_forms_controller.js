angular.module("adminApp").controller("InformationFormsController", InformationFormsController);

InformationFormsController.$inject = ["$location", "informationFormService",
  "checkValidDateFunction", "adminService"];

function InformationFormsController($location, informationFormService, checkValidDateFunction, adminService) {
  var vm = this;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  DATETIME_FIELDS = ["working_started_at", "working_ended_at"];
  SELECT_FIELDS = ["location_id", "staff_id"];
  ZIP_TYPE = "zip";
  vm.params = $location.search();
  SEARCH_CONDITION_TYPE = "admin_information_form_search_conditions";
  vm.total_items = 0;

  vm.init = function(screenType) {
    vm.screenType = screenType;
    getLastSearchCondition();
  };

  vm.search = function(isSaveCondition, isResetPage) {
    if (isResetPage) {
      vm.params.page = 1;
    }
    var search_params = formatParamsBeforeSearch(angular.copy(vm.params));
    informationFormService.getArranges({search: search_params}).then(function(res) {
      angular.extend(vm, res.data);
      if (isSaveCondition) {
        adminService.createSearchCondition({search: _.omit(search_params, "page"),
          search_condition: SEARCH_CONDITION_TYPE, search_type: vm.screenType});
      };
    });
    vm.selectAll = false;
    vm.isSelected = false;
    $location.search(search_params).replace();
    triggerSelectFields();
  };

  vm.checkAll = function() {
    _.forEach(vm.arrangements, function(arrangement) {
      arrangement.select = vm.selectAll;
    });
    vm.checkSelected(true);
  };

  vm.checkSelected = function(isChecked) {
   var selectedItems = _.find(vm.arrangements, {select: true});
   vm.isSelected = selectedItems ? true : false;
   if (vm.selectAll && !isChecked) {
     vm.selectAll = false;
   };
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".arrangement-search-result").offset().top
    }, 1000);
  };

  vm.changePerPage = function() {
    vm.search(true);
    vm.scrollToResult();
  };

  vm.downloadPdf = function(e, screenType) {
    var selectedArrangements = _.filter(vm.arrangements, function(arrangement) {
      return arrangement.select;
    });
    var selectedIds = _.map(selectedArrangements, "id");
    $.asyncDownload(e, "information_forms", JSON.stringify({id: selectedIds, screen_type: screenType}), ZIP_TYPE);
  };

  function getLastSearchCondition() {
    adminService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE,
      search_type: vm.screenType}).then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
        if (!vm.params["department_id"]) vm.params["department_id"] = "";
        vm.params["department_id"] = vm.params["department_id"].split(",");
      } else {
        vm.params.per_page = vm.perPageSettings[0];
        vm.params.working_started_at = moment().startOf("month").format(ORDER_DATEPICKER_FORMAT);
        vm.params.working_ended_at = moment().endOf("month").format(ORDER_DATEPICKER_FORMAT);
      }

      angular.element(document).ready(function() {
        $("#department_id").trigger("change");
      });
      vm.search(false);
    });
  };

  function triggerSelectFields() {
    DATETIME_FIELDS.forEach(function(attr) {
      $("[name='" + attr + "']").datepicker("setDate", new Date(vm.params[attr]));
    });
    SELECT_FIELDS.forEach(function(field) {
      $("#" + field).val(vm.params[field]).trigger("change.select2");
    });
  };

  function formatParamsBeforeSearch(params) {
    params.department_id = (params.department_id || []).join(",");
    return params;
  };

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  }
};
