"use strict";

angular.module("adminApp")
  .controller("UsageAchievementsController", UsageAchievementsController);

UsageAchievementsController.$inject = ["usageAchievementsService", "checkValidDateFunction", "$location", "toaster"];

function UsageAchievementsController(usageAchievementsService, checkValidDateFunction, $location, toaster) {
  var vm = this;
  var SEARCH_CONDITION_TYPE = "admin_arrange_billing_search_conditions";
  vm.params = $location.search();
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  vm.search = function(isSave, isReset) {
    if (isReset) {
      vm.params.page = 1;
    };
    vm.params.per_page = _.toInteger(vm.params.per_page) || vm.perPageSettings[0];
    usageAchievementsService.getArrangeBillings({search: vm.params}).then(function(res) {
      if (res.data) {
        angular.extend(vm, res.data);
        _.forEach(vm.arrange_billings, function(arrangeBilling) {
          arrangeBilling.select = false;
        });
        vm.isSelected = false;
        vm.checkSelected();
      } else {
        vm.arrange_billings = [];
        vm.total_items = 0;
      };
    });
    $location.search(vm.params).replace();
    if (isSave) {
      usageAchievementsService.createSearchCondition({search: vm.params, search_condition: SEARCH_CONDITION_TYPE});
      vm.scrollToResult();
    };
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".search-result").offset().top
    }, 1000);
  };

  vm.checkAll = function() {
    _.forEach(vm.arrange_billings, function(arrangeBilling) {
      arrangeBilling.select = vm.selectAll;
    });
    vm.checkSelected(true);
  };

  vm.checkSelected = function(isChecked) {
    var selectedItems = _.find(vm.arrange_billings, {select: true});
    vm.isSelected = vm.is_check_all_records || selectedItems ? true : false;
    if (vm.selectAll && !isChecked) {
      vm.selectAll = false;
    };
  };

  vm.goToBatchDetail = function() {
    var params;
    if (vm.is_check_all_records) {
      var searchAllParams = _.omit(vm.params, "page", "per_page");
      searchAllParams.is_check_all_records = vm.is_check_all_records;
      params = $.param(searchAllParams);
    } else {
      var selectedItems = _.filter(vm.arrange_billings, function(arrangeBilling) {
        return arrangeBilling.select;
      });
      params = "ids=" + _.map(selectedItems, "id");
    }

    window.open("/usage_achievements/view_detail/?" + params, "_blank");
  };

  vm.changeCorporation = function() {
    vm.corporation_groups = [];
    vm.group_tags = [];
    vm.locations = [];
    usageAchievementsService.relateCorporationData({corporation_id: vm.params.corporation_id}).then(function(res){
      angular.extend(vm, res.data);
    });
  }

  vm.init = function() {
    vm.is_check_all_records = false;
    usageAchievementsService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
        vm.changeCorporation();
      } else {
        vm.params.per_page = vm.perPageSettings[0];
        vm.total_items = 0;
      };
      vm.search(false, true);
      angular.element(document).ready(function() {
        angular.element(".select2").select2();
      });
    });
  };

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  }
};
