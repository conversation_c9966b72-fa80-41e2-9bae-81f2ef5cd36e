"use strict";

angular.module("adminApp").controller("ArrangementsController", ArrangementsController);
ArrangementsController.$inject = ["$timeout", "$scope", "$sce", "$location",
  "checkValidDateFunction", "arrangementService", "toaster"];

function ArrangementsController($timeout, $scope, $sce, $location, checkValidDateFunction,
  arrangementService, toaster) {
  var vm = this;
  vm.$scope = $scope;
  vm.toasterTimeout = 6200;
  var FORMAT_PRICE = /\B(?=(\d{3})+(?!\d))/g;
  var SECOND_IN_HOUR = 3600;
  var SECOND_PER_MINUTE = 60;
  var arrangementsI18n = I18n.t("admin.arrangements.index");
  var NUMBER_PORTION = 15;
  var CHECKED_PORTION_STATUS = ["cancel", "cancel_after_arrange_has_insurance", "cancel_after_arrange_no_insurance"];
  var DISABLE_PORTION_STATUS = ["temporary_arrange", "arranged", "cancel",
    "cancel_after_arrange_has_insurance", "cancel_after_arrange_no_insurance"];
  var SEARCH_DATE_TIME_FIELDS = ["working_started_at", "working_ended_at", "order_created_from", "order_created_to"];
  var SELECT_FIELDS = ["chain_id", "department_id", "staff_id", "location_id", "corporation_id", "staff_department_id"];
  var MULTI_SELECT_FIELDS = ["department_id", "location_id", "corporation_id", "staff_department_id"];
  var OP_CENTER_CONFIRMING_STATUS = "op_center_confirming";
  var COMMA = I18n.t("common.comma");
  var $copyPortionModal = angular.element("#copy-portion-modal");
  var $warningPopup = angular.element("#warning-popup-modal");
  vm.warning = [];
  vm.totalWarning = 0;
  vm.warningIndex = 0;
  vm.hasRecord = false;
  vm.dataSum = {};
  vm.showSumCalculation = false;
  vm.showSumBtnCalculation = false;
  vm.breakTimeUpdating = false;
  vm.loadingSumCalculation = false;
  vm.currentLocationTypes = [
    {
      id: 1,
      key: "all_location",
      text: I18n.t("admin.arrangements.index.form_search.all_location")
    },
    {
      id: 2,
      key: "new_location",
      text: I18n.t("admin.arrangements.index.form_search.new_location")
    },
    {
      id: 3,
      key: "old_location",
      text: I18n.t("admin.arrangements.index.form_search.old_location")
    }
  ];

  var REST_TIMES = [1, 2, 3];
  var PAYMENT_BONUS_UNIT_PRICE_OPTIONS = [0, 50, 100, 150, 200, 250, 300];

  var SEARCH_ARR_PARAMS = ["order_case_segment_id", "arrange_status_id", "working_time_status_id",
    "billing_adjusment_type_id" ,"payment_adjusment_type_id", "invoice_target"];

  var CURRENT_LOCATION_TYPE = "current_location_type";

  var SEARCH_DETAILS_FIELD = ["is_expect_time", "arrange_status_id", "working_time_status_id",
    "not_prepared", "not_arrived", "billing_adjusment_type_id", "payment_adjusment_type_id",
    "special_offer", "urgent", "diff_working_time", "custom_billing_payment"];

  var SEARCH_BASIC_FIELDS = ["working_started_at", "working_ended_at", "department_id", "chain_id", "staff_id",
    "location_id", "order_segment_id", "corporation_id", "order_created_from", "order_created_to", "staff_department_id",
    "invoice_target"];

  var TIME_TO_MINUTES_ATTRS = ["payment_ot1_time", "payment_late_time", "payment_leave_early_time",
    "billing_ot_time", "billing_late_time", "billing_leave_early_time", "billing_basic_time",
    "payment_basic_time", "payment_actual_working_time", "payment_ot_night_time", "payment_ot_day_time",
    "billing_actual_working_time", "billing_night_time"];

  var TIME_VALUE_COLUMNS = ["working_time", "billing_actual_working_time", "billing_basic_time",
  "billing_ot_time", "billing_night_time", "billing_late_time", "billing_leave_early_time",
  "payment_actual_working_time", "payment_ot_time", "payment_ot_day_time", "payment_ot_night_time",
  "payment_night_time", "payment_late_time", "payment_leave_early_time"];

  var ITEM_LIMIT_NUMBER = 16;
  var LIMIT_PER_PAGE = 50;

  var ARRANGED_BILLING_STATUSES = ["arranged", "cancel_after_arrange_has_insurance",
    "cancel_after_arrange_no_insurance", "absence"];

  var HIDDEN_COLUMNS_BY_BILLING = ["billing_total_amount", "billing_field_7",
    "billing_basic_amount", "billing_ot_amount", "billing_night_amount", "billing_field_1",
    "billing_field_2", "billing_field_3", "billing_field_4", "billing_field_5", "billing_field_6"];

  var CANCEL_AFTER_ARRANGE_STTS = ["cancel_after_arrange_has_insurance",
    "cancel_after_arrange_no_insurance", "absence", "absence_has_alternative"];

  var REGULAR_ORDER = "regular_order"

  var $modalAlert = angular.element("#modal-alert-arrangement");
  var $modalAlertSum = angular.element("#modal-alert-arrangement-sum");
  var $modalAlertExport = angular.element("#modal-alert-arrangement-export");

  var search_params = "";

  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.displaySettingItems = arrangementsI18n.labels.display_setting.items;
  vm.displayPattern = Object.keys(vm.displaySettingItems)[0];
  vm.statisticHeaderColumns = arrangementsI18n.statistic_columns;
  vm.keyStatisticData = Object.keys(I18n.t("admin.arrangements.index.statistic_columns"));
  vm.genders = {};
  vm.genders.male = I18n.t("admin.staff.search_detail.male");
  vm.genders.female = I18n.t("admin.staff.search_detail.female");
  vm.isShowStatisticTable = false;
  vm.confirmOrderCaseData = [];
  vm.inputPaymentData = [];
  vm.inputBillingData = [];
  vm.optionBeforeWork = [
    {key: true, i18n_label: I18n.t("arrangements.confirm_working_options.done")},
    {key: false, i18n_label: I18n.t("arrangements.confirm_working_options.not_yet")}
    ];
  vm.optionPaymentBonusUnitPrices = initOptionPaymentBonusUnitPrices();
  vm.current_staff_evaluation = {};
  vm.current_staff_location_evaluation = {};
  vm.billingAdjusmentTypes = angular.element(".billing-adjusment").data("billing-adjusment-options");
  vm.segmentIds = angular.element("#order-segment-ids").data("order-segment-ids");
  vm.orderCaseSegmentIds = angular.element(".order-case-segment-ids").data("order-case-segment-ids");
  vm.invoiceTargets = [
    {id: "lawson_invoice", selected: false, name: "清算書"},
    {id: "separate_invoice", selected: false, name: "個別"}
  ];
  vm.locations = [];
  vm.corporations = [];
  vm.arrangeStatusIds = angular.element(".arrange-status-ids").data("arrange-status-ids");
  vm.orderSegmentIds = angular.element(".order-segment-ids").data("order-segment-ids");
  vm.workingTimeStatusIds = angular.element(".working-time-status-ids").data("working-time-status-ids");
  vm.workingTimeStatusIdsForSelect = angular.copy(vm.workingTimeStatusIds);
  vm.paymentAdjusmentTypeIds = angular.element(".payment-adjustment-type-ids").data("payment-adjustment-type-ids");
  vm.billingAdjusmentTypeIds = angular.element(".billing-adjustment-type-ids").data("billing-adjustment-type-ids");
  vm.limitRecordAlertLarge = angular.element("#limit-alert-large").data("limit-alert-large");
  // vm.limitRecordAlertSumLarge = angular.element("#limit-sum-large").data("limit-sum-large");
  vm.paymentWorkingTimes = [
    {type: "night", from: "00:00", to: "05:00", index: 1},
    {type: "basic", from: "05:01", to: "21:59", index: 2},
    {type: "night", from: "22:00", to: "23:59", index: 3}
  ];
  vm.staffHasMultipleReferral = {};

  vm.workingHours = [
    {name: "sunrise", from: "06:00", to: "08:59", class: "image-sunrise"},
    {name: "sun", from: "09:00", to: "16:59", class: "sun-color fa fa-sun-o"},
    {name: "sunset", from: "17:00", to: "21:59", class: "image-sunset"},
    {name: "night", from: "22:00", to: "23:59", class: "image-night"},
    {name: "night", from: "00:00", to: "05:59", class: "image-night"}
  ];
  vm.order_portion_status = {};
  vm.currentErrorArrangementId = 0;
  vm.basicFrozenLimitNumber = ITEM_LIMIT_NUMBER;
  vm.previousValues = [];
  vm.selectedTemplate = {};
  vm.alertLimitExportMessage = "";

  vm.init = function() {
    setToggleStatisticLabel();
    vm.params = {working_started_at: moment(new Date).format(ORDER_DATEPICKER_FORMAT),
      per_page: vm.perPageSettings[0]};

    vm.resetToLastSearchCondition(true, true);

    vm.currentParams = angular.copy(vm.params);
    angular.element(document).ready(function() {
      $("#search-basic").on("shown.bs.collapse", function(e) {
        $("#department_id").trigger("change");
        $("#staff_department_id").trigger("change");
        $("#location_id").trigger("change");
        $("#corporation_id").trigger("change");
      });
    });
  };

  function initOptionPaymentBonusUnitPrices() {
    var options = []
    _.forEach(PAYMENT_BONUS_UNIT_PRICE_OPTIONS, function(val) {
      var option = {key: val, i18n_label: val};
      options.push(option);
    });
    return options;
  }

  function formatTableHeaderColumns(labels) {
    vm.fullTableHeaderColumns = [
      {
        "key": "working_day",
        "text": labels.working_day,
        "class": "tbl-col-fixed-w-200 freeze-column",
        "number": 1
      },
      {
        "key": "working_time",
        "text": labels.working_time,
        "class": "tbl-col-fixed-w-200 freeze-column",
        "sort": true,
        "number": 2
      },
      {
        "key": "working_fixed_time",
        "text": labels.working_fixed_time,
        "class": "tbl-col-fixed-w-200",
        "number": 3
      },
      {
        "key": "arrange_basic_working_time",
        "text": labels.arrange_basic_working_time,
        "class": "tbl-col-fixed-w-125",
        "number": 4
      },
      {
        "key": "arrange_note",
        "text": labels.arrange_note,
        "class": "tbl-col-fixed-w-200",
        "number": 5
      },
      {
        "key": "corporation_id",
        "text": labels.corporation_id,
        "class": "tbl-col-fixed-w-200",
        "number": 6
      },
      {
        "key": "corporation_name",
        "text": labels.corporation_name,
        "class": "tbl-col-fixed-w-200",
        "number": 7
      },
      {
        "key": "billing_address",
        "text": labels.billing_address,
        "class": "tbl-col-fixed-w-200",
        "number": 8
      },
      {
        "key": "location_no",
        "text": labels.location_no,
        "class": "tbl-col-fixed-w-200",
        "number": 9
      },
      {
        "key": "location_name",
        "text": labels.location_name,
        "class": "tbl-col-fixed-w-200",
        "sort": true,
        "number": 10
      },
      {
        "key": "location_code",
        "text": labels.location_code,
        "class": "tbl-col-fixed-w-125",
        "number": 11
      },
      {
        "key": "nearest_station",
        "text": labels.nearest_station,
        "class": "tbl-col-fixed-w-200",
        "number": 12
      },
      {
        "key": "order_type",
        "text": labels.order_type,
        "class": "tbl-col-fixed-w-150",
        "number": 13
      },
      {
        "key": "staff_account_name",
        "text": labels.staff_account_name,
        "class": "tbl-col-fixed-w-200",
        "sort": true,
        "number": 14
      },
      {
        "key": "staff_kana",
        "text": labels.staff_kana,
        "class": "tbl-col-fixed-w-200",
        "number": 15
      },
      {
        "key": "order_case_status",
        "text": labels.order_case_status,
        "class": "tbl-col-fixed-w-200",
        "number": 16
      },
      {
        "key": "is_prepared",
        "text": labels.working_prepare,
        "class": "tbl-col-fixed-w-100",
        "sort": true,
        "number": 17
      },
      {
        "key": "working_need_talking",
        "text": labels.working_need_talking,
        "class": "tbl-col-fixed-w-200",
        "number": 18
      },
      {
        "key": "is_arrived",
        "text": labels.arrived,
        "class": "tbl-col-fixed-w-100",
        "sort": true,
        "number": 19
      },
      {
        "key": "working_has_problem",
        "text": labels.working_has_problem,
        "class": "tbl-col-fixed-w-200",
        "number": 20
      },
      {
        "key": "working_time_status_id",
        "text": labels.confirm_working,
        "class": "tbl-col-fixed-w-175",
        "sort": true,
        "number": 21
      },
      {
        "key": "staff_evaluation",
        "text": labels.staff_evaluation,
        "class": "tbl-col-fixed-w-125",
        "number": 22
      },
      {
        "key": "location_evaluation",
        "text": labels.location_evaluation,
        "class": "tbl-col-fixed-w-100",
        "number": 23
      },
      {
        "key": "history",
        "text": labels.history,
        "class": "tbl-col-fixed-w-75",
        "number": 24
      },
      {
        "key": "check_all",
        "text": labels.check_all,
        "class": "tbl-col-fixed-w-200",
        "number": 25
      },
      {
        "key": "check_payment",
        "text": labels.check_payment,
        "class": "tbl-col-fixed-w-200",
        "number": 26
      },
      {
        "key": "check_billing",
        "text": labels.check_billing,
        "class": "tbl-col-fixed-w-200",
        "number": 27
      },
      {
        "key": "adjustment_type",
        "text": labels.adjustment_type,
        "class": "tbl-col-fixed-w-200",
        "number": 28
      },
      {
        "key": "payment_start_time",
        "text": labels.payment_start_time,
        "class": "tbl-col-fixed-w-125",
        "number": 29
      },
      {
        "key": "payment_end_time",
        "text": labels.payment_end_time,
        "class": "tbl-col-fixed-w-125",
        "number": 30
      },
      {
        "key": "payment_fixed_time",
        "text": labels.payment_fixed_time,
        "class": "tbl-col-fixed-w-200",
        "number": 31
      },
      {
        "key": "payment_rest_1",
        "text": labels.payment_rest_1,
        "class": "tbl-col-fixed-w-200",
        "number": 32
      },
      {
        "key": "payment_rest_2",
        "text": labels.payment_rest_2,
        "class": "tbl-col-fixed-w-200",
        "number": 33
      },
      {
        "key": "payment_rest_3",
        "text": labels.payment_rest_3,
        "class": "tbl-col-fixed-w-200",
        "number": 34
      },
      {
        "key": "payment_actual_working_time",
        "text": labels.payment_actual_working_time,
        "class": "tbl-col-fixed-w-125",
        "number": 35
      },
      {
        "key": "payment_basic_time",
        "text": labels.payment_basic_time,
        "class": "tbl-col-fixed-w-125",
        "number": 36
      },
      {
        "key": "payment_ot_time",
        "text": labels.payment_ot_time,
        "class": "tbl-col-fixed-w-125",
        "number": 37
      },
      {
        "key": "payment_night_time",
        "text": labels.payment_night_time,
        "class": "tbl-col-fixed-w-125",
        "number": 38
      },
      {
        "key": "payment_late_time",
        "text": labels.payment_late_time,
        "class": "tbl-col-fixed-w-125",
        "number": 39
      },
      {
        "key": "payment_leave_early_time",
        "text": labels.payment_leave_early_time,
        "class": "tbl-col-fixed-w-125",
        "number": 40
      },
      {
        "key": "payment_basic_unit_price",
        "text": labels.payment_basic_unit_price,
        "class": "tbl-col-fixed-w-125",
        "number": 41
      },
      {
        "key": "payment_unit_price_addition",
        "text": labels.payment_unit_price_addition,
        "class": "tbl-col-fixed-w-150",
        "number": 42
      },
      {
        "key": "payment_total_amount",
        "text": labels.payment_total_amount,
        "class": "tbl-col-fixed-w-100",
        "number": 43
      },
      {
        "key": "payment_basic_amount",
        "text": labels.payment_basic_amount,
        "class": "tbl-col-fixed-w-125",
        "number": 44
      },
      {
        "key": "payment_ot_amount",
        "text": labels.payment_ot_amount,
        "class": "tbl-col-fixed-w-125",
        "number": 45
      },
      {
        "key": "payment_night_amount",
        "text": labels.payment_night_amount,
        "class": "tbl-col-fixed-w-125",
        "number": 46
      },
      {
        "key": "payment_field_1",
        "text": labels.payment_field_1,
        "class": "tbl-col-fixed-w-125",
        "sort": true,
        "number": 47
      },
      {
        "key": "confirm",
        "text": labels.confirm,
        "class": "tbl-col-fixed-w-75",
        "number": 48
      },
      {
        "key": "payment_field_2",
        "text": labels.payment_field_2,
        "class": "tbl-col-fixed-w-200",
        "number": 49
      },
      {
        "key": "payment_field_3",
        "text": labels.payment_field_3,
        "class": "tbl-col-fixed-w-200",
        "number": 50
      },
      {
        "key": "payment_field_4",
        "text": labels.payment_field_4,
        "class": "tbl-col-fixed-w-125",
        "number": 51
      },
      {
        "key": "payment_field_5",
        "text": labels.payment_field_5,
        "class": "tbl-col-fixed-w-100",
        "number": 52
      },
      {
        "key": "payment_field_6",
        "text": labels.payment_field_6,
        "class": "tbl-col-fixed-w-200",
        "number": 53
      },
      {
        "key": "payment_field_7",
        "text": labels.payment_field_7,
        "class": "tbl-col-fixed-w-200",
        "number": 54
      },
      {
        "key": "payment_field_8",
        "text": labels.payment_field_8,
        "class": "tbl-col-fixed-w-200",
        "number": 55
      },
      {
        "key": "payment_field_9",
        "text": labels.payment_field_9,
        "class": "tbl-col-fixed-w-200",
        "number": 56
      },
      {
        "key": "payment_field_10",
        "text": labels.payment_field_10,
        "class": "tbl-col-fixed-w-200",
        "number": 57
      },
      {
        "key": "payment_field_11",
        "text": labels.payment_field_11,
        "class": "tbl-col-fixed-w-200",
        "number": 58
      },
      {
        "key": "payment_field_12",
        "text": labels.payment_field_12,
        "class": "tbl-col-fixed-w-200",
        "number": 59
      },
      {
        "key": "payment_field_13",
        "text": labels.payment_field_13,
        "class": "tbl-col-fixed-w-200",
        "number": 60
      },
      {
        "key": "payment_field_14",
        "text": labels.payment_field_14,
        "class": "tbl-col-fixed-w-200",
        "number": 61
      },
      {
        "key": "payment_field_15",
        "text": labels.payment_field_15,
        "class": "tbl-col-fixed-w-200",
        "number": 62
      },
      {
        "key": "payment_field_16",
        "text": labels.payment_field_16,
        "class": "tbl-col-fixed-w-200",
        "number": 63
      },
      {
        "key": "payment_field_17",
        "text": labels.payment_field_17,
        "class": "tbl-col-fixed-w-200",
        "number": 64
      },
      {
        "key": "payment_field_18",
        "text": labels.payment_field_18,
        "class": "tbl-col-fixed-w-200",
        "number": 65
      },
      {
        "key": "payment_field_19",
        "text": labels.payment_field_19,
        "class": "tbl-col-fixed-w-200",
        "number": 66
      },
      {
        "key": "payment_field_20",
        "text": labels.payment_field_20,
        "class": "tbl-col-fixed-w-200",
        "number": 67
      },
      {
        "key": "deduction_field_1",
        "text": labels.deduction_field_1,
        "class": "tbl-col-fixed-w-200",
        "number": 68
      },
      {
        "key": "deduction_field_2",
        "text": labels.deduction_field_2,
        "class": "tbl-col-fixed-w-200",
        "number": 69
      },
      {
        "key": "deduction_field_3",
        "text": labels.deduction_field_3,
        "class": "tbl-col-fixed-w-200",
        "number": 70
      },
      {
        "key": "deduction_field_4",
        "text": labels.deduction_field_4,
        "class": "tbl-col-fixed-w-200",
        "number": 71
      },
      {
        "key": "deduction_field_5",
        "text": labels.deduction_field_5,
        "class": "tbl-col-fixed-w-200",
        "number": 72
      },
      {
        "key": "deduction_field_6",
        "text": labels.deduction_field_6,
        "class": "tbl-col-fixed-w-200",
        "number": 73
      },
      {
        "key": "deduction_field_7",
        "text": labels.deduction_field_7,
        "class": "tbl-col-fixed-w-200",
        "number": 74
      },
      {
        "key": "deduction_field_8",
        "text": labels.deduction_field_8,
        "class": "tbl-col-fixed-w-200",
        "number": 75
      },
      {
        "key": "deduction_field_9",
        "text": labels.deduction_field_9,
        "class": "tbl-col-fixed-w-200",
        "number": 76
      },
      {
        "key": "deduction_field_10",
        "text": labels.deduction_field_10,
        "class": "tbl-col-fixed-w-200",
        "number": 77
      },
      {
        "key": "deduction_field_11",
        "text": labels.deduction_field_11,
        "class": "tbl-col-fixed-w-200",
        "number": 78
      },
      {
        "key": "deduction_field_12",
        "text": labels.deduction_field_12,
        "class": "tbl-col-fixed-w-200",
        "number": 79
      },
      {
        "key": "deduction_field_13",
        "text": labels.deduction_field_13,
        "class": "tbl-col-fixed-w-200",
        "number": 80
      },
      {
        "key": "deduction_field_14",
        "text": labels.deduction_field_14,
        "class": "tbl-col-fixed-w-200",
        "number": 81
      },
      {
        "key": "deduction_field_15",
        "text": labels.deduction_field_15,
        "class": "tbl-col-fixed-w-200",
        "number": 82
      },
      {
        "key": "deduction_field_16",
        "text": labels.deduction_field_16,
        "class": "tbl-col-fixed-w-200",
        "number": 83
      },
      {
        "key": "deduction_field_17",
        "text": labels.deduction_field_17,
        "class": "tbl-col-fixed-w-200",
        "number": 84
      },
      {
        "key": "deduction_field_18",
        "text": labels.deduction_field_18,
        "class": "tbl-col-fixed-w-200",
        "number": 85
      },
      {
        "key": "deduction_field_19",
        "text": labels.deduction_field_19,
        "class": "tbl-col-fixed-w-200",
        "number": 86
      },
      {
        "key": "deduction_field_20",
        "text": labels.deduction_field_20,
        "class": "tbl-col-fixed-w-200",
        "number": 87
      },
      {
        "key": "payment_subtraction_amount",
        "text": labels.payment_subtraction_amount,
        "class": "tbl-col-fixed-w-125",
        "number": 88
      },
      {
        "key": "billing_adjustment_type",
        "text": labels.billing_adjustment_type,
        "class": "tbl-col-fixed-w-125",
        "number": 89
      },
      {
        "key": "billing_start_time",
        "text": labels.billing_start_time,
        "class": "tbl-col-fixed-w-125",
        "number": 90
      },
      {
        "key": "billing_end_time",
        "text": labels.billing_end_time,
        "class": "tbl-col-fixed-w-125",
        "number": 91
      },
      {
        "key": "billing_fixed_time",
        "text": labels.billing_fixed_time,
        "class": "tbl-col-fixed-w-200",
        "number": 92
      },
      {
        "key": "billing_rest_1",
        "text": labels.billing_rest_1,
        "class": "tbl-col-fixed-w-150",
        "number": 93
      },
      {
        "key": "billing_rest_2",
        "text": labels.billing_rest_2,
        "class": "tbl-col-fixed-w-150",
        "number": 94
      },
      {
        "key": "billing_rest_3",
        "text": labels.billing_rest_3,
        "class": "tbl-col-fixed-w-150",
        "number": 95
      },
      {
        "key": "billing_actual_working_time",
        "text": labels.billing_actual_working_time,
        "class": "tbl-col-fixed-w-125",
        "number": 96
      },
      {
        "key": "billing_basic_time",
        "text": labels.billing_basic_time,
        "class": "tbl-col-fixed-w-125",
        "number": 97
      },
      {
        "key": "billing_ot_time",
        "text": labels.billing_ot_time,
        "class": "tbl-col-fixed-w-125",
        "number": 98
      },
      {
        "key": "billing_night_time",
        "text": labels.billing_night_time,
        "class": "tbl-col-fixed-w-125",
        "number": 99
      },
      {
        "key": "billing_late_time",
        "text": labels.billing_late_time,
        "class": "tbl-col-fixed-w-125",
        "number": 100
      },
      {
        "key": "billing_leave_early_time",
        "text": labels.billing_leave_early_time,
        "class": "tbl-col-fixed-w-125",
        "number": 101
      },
      {
        "key": "billing_basic_unit_price",
        "text": labels.billing_basic_unit_price,
        "class": "tbl-col-fixed-w-125",
        "number": 102
      },
      {
        "key": "billing_unit_price_addition",
        "text": labels.billing_unit_price_addition,
        "class": "tbl-col-fixed-w-150",
        "number": 103
      },
      {
        "key": "billing_ot_unit_price",
        "text": labels.billing_ot_unit_price,
        "class": "tbl-col-fixed-w-125",
        "number": 104
      },
      {
        "key": "billing_night_unit_price",
        "text": labels.billing_night_unit_price,
        "class": "tbl-col-fixed-w-125",
        "number": 105
      },
      {
        "key": "billing_total_amount",
        "text": labels.billing_total_amount,
        "class": "tbl-col-fixed-w-125",
        "number": 106
      },
      {
        "key": "billing_basic_amount",
        "text": labels.billing_basic_amount,
        "class": "tbl-col-fixed-w-125",
        "number": 107
      },
      {
        "key": "billing_ot_amount",
        "text": labels.billing_ot_amount,
        "class": "tbl-col-fixed-w-125",
        "number": 108
      },
      {
        "key": "billing_night_amount",
        "text": labels.billing_night_amount,
        "class": "tbl-col-fixed-w-125",
        "number": 109
      },
      {
        "key": "billing_note",
        "text": labels.billing_note,
        "class": "tbl-col-fixed-w-200",
        "number": 110
      },
      {
        "key": "billing_field_1",
        "text": labels.billing_field_1,
        "class": "tbl-col-fixed-w-100",
        "number": 111
      },
      {
        "key": "billing_field_2",
        "text": labels.billing_field_2,
        "class": "tbl-col-fixed-w-100",
        "number": 112
      },
      {
        "key": "billing_field_3",
        "text": labels.billing_field_3,
        "class": "tbl-col-fixed-w-100",
        "number": 113
      },
      {
        "key": "billing_field_4",
        "text": labels.billing_field_4,
        "class": "tbl-col-fixed-w-100",
        "number": 114
      },
      {
        "key": "billing_field_5",
        "text": labels.billing_field_5,
        "class": "tbl-col-fixed-w-125",
        "number": 115
      },
      {
        "key": "billing_field_6",
        "text": labels.billing_field_6,
        "class": "tbl-col-fixed-w-100",
        "number": 116
      },
      {
        "key": "billing_field_7",
        "text": labels.billing_field_7,
        "class": "tbl-col-fixed-w-100",
        "number": 117
      },
      {
        "key": "billing_field_8",
        "text": labels.billing_field_8,
        "class": "tbl-col-fixed-w-200",
        "number": 118
      },
      {
        "key": "billing_field_9",
        "text": labels.billing_field_9,
        "class": "tbl-col-fixed-w-200",
        "number": 119
      },
      {
        "key": "billing_field_10",
        "text": labels.billing_field_10,
        "class": "tbl-col-fixed-w-200",
        "number": 120
      },
      {
        "key": "billing_tax_exemption",
        "text": labels.billing_tax_exemption,
        "class": "tbl-col-fixed-w-200",
        "number": 121
      },
      {
        "key": "billing_field_12",
        "text": labels.billing_field_12,
        "class": "tbl-col-fixed-w-200",
        "number": 122
      },
      {
        "key": "billing_field_13",
        "text": labels.billing_field_13,
        "class": "tbl-col-fixed-w-200",
        "number": 123
      },
      {
        "key": "billing_field_14",
        "text": labels.billing_field_14,
        "class": "tbl-col-fixed-w-200",
        "number": 124
      },
      {
        "key": "billing_field_15",
        "text": labels.billing_field_15,
        "class": "tbl-col-fixed-w-200",
        "number": 125
      },
      {
        "key": "billing_field_16",
        "text": labels.billing_field_16,
        "class": "tbl-col-fixed-w-200",
        "number": 126
      },
      {
        "key": "billing_field_17",
        "text": labels.billing_field_17,
        "class": "tbl-col-fixed-w-200",
        "number": 127
      },
      {
        "key": "tel",
        "text": labels.tel,
        "class": "tbl-col-fixed-w-125",
        "number": 128
      },
      {
        "key": "portable_tel",
        "text": labels.portable_tel,
        "class": "tbl-col-fixed-w-125",
        "number": 129
      },
      {
        "key": "restriction_condition",
        "text": labels.restriction_condition,
        "class": "tbl-col-fixed-w-200",
        "number": 130
      },
      {
        "key": "mail_address_1",
        "text": labels.mail_address_1,
        "class": "tbl-col-fixed-w-300",
        "number": 131
      },
      {
        "key": "mail_address_2",
        "text": labels.mail_address_2,
        "class": "tbl-col-fixed-w-300",
        "number": 132
      },
      {
        "key": "age",
        "text": labels.age,
        "class": "tbl-col-fixed-w-75",
        "number": 133
      },
      {
        "key": "gender",
        "text": labels.gender,
        "class": "tbl-col-fixed-w-75",
        "number": 134
      },
      {
        "key": "staff_total_work_experience",
        "text": labels.work_experience_times,
        "class": "tbl-col-fixed-w-125",
        "sort": true,
        "number": 135
      },
      {
        "key": "uniform_size",
        "text": labels.uniform_size,
        "class": "tbl-col-fixed-w-100",
        "number": 136
      },
      {
        "key": "staff_note_1",
        "text": labels.staff_note_1,
        "class": "tbl-col-fixed-w-200",
        "number": 137
      },
      {
        "key": "staff_note_2",
        "text": labels.staff_note_2,
        "class": "tbl-col-fixed-w-200",
        "number": 138
      },
      {
        "key": "staff_note_3",
        "text": labels.staff_note_3,
        "class": "tbl-col-fixed-w-200",
        "number": 139
      },
      {
        "key": "staff_note_4",
        "text": labels.staff_note_4,
        "class": "tbl-col-fixed-w-200",
        "number": 140
      },
      {
        "key": "staff_note_5",
        "text": labels.staff_note_5,
        "class": "tbl-col-fixed-w-200",
        "number": 141
      },
      {
        "key": "staff_note_6",
        "text": labels.staff_note_6,
        "class": "tbl-col-fixed-w-200",
        "number": 142
      },
      {
        "key": "staff_note_7",
        "text": labels.staff_note_7,
        "class": "tbl-col-fixed-w-200",
        "number": 143
      },
      {
        "key": "staff_note_8",
        "text": labels.staff_note_8,
        "class": "tbl-col-fixed-w-200",
        "number": 144
      },
      {
        "key": "location_note",
        "text": labels.location_note,
        "class": "tbl-col-fixed-w-200",
        "number": 145
      },
      {
        "key": "comment",
        "text": labels.comment,
        "class": "tbl-col-fixed-w-200",
        "number": 146
      },
      {
        "key": "remove",
        "text": labels.remove,
        "class": "tbl-col-fixed-w-75",
        "number": 147
      },
      {
        "key": "new_old_pos",
        "text": labels.new_old_pos,
        "class": "tbl-col-fixed-w-100",
        "number": 148
      },
      {
        "key": "use_car_parking_area",
        "text": labels.use_car_parking_area,
        "class": "tbl-col-fixed-w-100",
        "number": 149
      },
      {
        "key": "business_name",
        "text": labels.business_name,
        "class": "tbl-col-fixed-w-200",
        "number": 150
      },
      {
        "key": "arrange_department",
        "text": labels.arrange_department,
        "class": "tbl-col-fixed-w-200",
        "number": 151
      },
      {
        "key": "belonging_to_department",
        "text": labels.belonging_to_department,
        "class": "tbl-col-fixed-w-200",
        "number": 152
      },
      {
        "key": "time_foreigner_can_working",
        "text": labels.time_foreigner_can_working,
        "class": "tbl-col-fixed-w-175",
        "number": 153
      },
      {
        "key": "can_go_to_by_car",
        "text": labels.can_go_to_by_car,
        "class": "tbl-col-fixed-w-100",
        "number": 154
      },
      {
        "key": "rank_up_training",
        "text": labels.rank_up_training,
        "class": "tbl-col-fixed-w-150",
        "number": 155
      },
      {
        "key": "new_pos_training",
        "text": labels.new_pos_training,
        "class": "tbl-col-fixed-w-100",
        "number": 156
      },
      {
        "key": "order_note",
        "text": labels.order_note,
        "class": "tbl-col-fixed-w-200",
        "number": 157
      },
      {
        "key": "special_offer_note",
        "text": labels.special_offer_note,
        "class": "tbl-col-fixed-w-200",
        "number": 158
      },
      {
        "key": "payment_basic_break_time",
        "text": labels.payment_basic_break_time,
        "class": "tbl-col-fixed-w-125",
        "number": 159
      },
      {
        "key": "payment_night_break_time",
        "text": labels.payment_night_break_time,
        "class": "tbl-col-fixed-w-125",
        "number": 160
      },
      {
        "key": "billing_basic_break_time",
        "text": labels.billing_basic_break_time,
        "class": "tbl-col-fixed-w-125",
        "number": 161
      },
      {
        "key": "billing_night_break_time",
        "text": labels.billing_night_break_time,
        "class": "tbl-col-fixed-w-125",
        "number": 162
      },
      {
        "key": "payment_night_unit_price",
        "text": labels.payment_night_unit_price,
        "class": "tbl-col-fixed-w-125",
        "number": 163
      },
      {
        "key": "order_id",
        "text": labels.order_id,
        "class": "tbl-col-fixed-w-80 freeze-column",
        "sort": true,
        "number": 164
      },
      {
        "key": "order_case_id",
        "text": labels.order_case_id,
        "class": "tbl-col-fixed-w-120 freeze-column",
        "sort": true,
        "number": 165
      },
      {
        "key": "payment_urgent_unit_price_addition",
        "text": labels.payment_urgent_unit_price_addition,
        "class": "tbl-col-fixed-w-150",
        "number": 166
      },
      {
        "key": "resend_mail",
        "text": labels.resend_mail,
        "class": "tbl-col-fixed-w-125",
        "number": 167
      },
      {
        "key": "payment_ot_day_time",
        "text": labels.payment_ot_day_time,
        "class": "tbl-col-fixed-w-150",
        "number": 168
      },
      {
        "key": "payment_ot_night_time",
        "text": labels.payment_ot_night_time,
        "class": "tbl-col-fixed-w-150",
        "number": 169
      },
      {
        "key": "payment_bonus_unit_price",
        "text": labels.payment_bonus_unit_price,
        "class": "tbl-col-fixed-w-125",
        "number": 170
      },
      {
        "key": "billing_other_addition_fee",
        "text": labels.billing_other_addition_fee,
        "class": "tbl-col-fixed-w-125",
        "number": 171
      },
      {
        "key": "work_achievement_history",
        "text": labels.work_achievement_history,
        "class": "tbl-col-fixed-w-125",
        "number": 172
      },
      {
        "key": "total_amount_not_taxable",
        "text": labels.total_amount_not_taxable,
        "class": "tbl-col-fixed-w-150",
        "number": 173
      },
      {
        "key": "invoice_target",
        "text": labels.invoice_target,
        "class": "tbl-col-fixed-w-125",
        "number": 174
      },
    ];
  };

  function triggerChangeSelect2() {
    SELECT_FIELDS.forEach(function(field) {
      $("#" + field).val(vm.params[field]).trigger("change.select2");
    });
  }

  function fetchParams() {
    _.forEach(SEARCH_ARR_PARAMS, function(attr) {
      if (!_.isUndefined(vm.params[attr])) {
        var arr = vm.params[attr].split(",");
        vm["" + _.camelCase(attr) + "s"].forEach(function(field) {
          field.selected = _.includes(arr, field.id.toString());
        });
      }
    });

    triggerChangeSelect2();
  }

  vm.resetToLastSearchCondition = function(isResetPage, isLoadHeader) {
    // arrangementService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
    //   _.forEach(vm.params, function(value, key) {if(!_.isEqual(key, "page")) vm.params[key] = "";});
    //   if (res.data.status){
    //     var needJoinToArray = ["department_id"];

    //     _.forEach(JSON.parse(res.data.last_conditions), function(value, key) {
    //       vm.params[key] = (needJoinToArray.indexOf(key) >= 0 ? value.split(",") : value);
    //     });
    //   }
    //   else
    //     vm.params.per_page = vm.perPageSettings[0];

      fetchParams();
      // checkLocationsExists();

      vm.refresh(isResetPage, false, isLoadHeader, false);

      for (var idx in SEARCH_DETAILS_FIELD) {
        var fieldName = SEARCH_DETAILS_FIELD[idx];
        if (vm.params[fieldName]) {
          $("#search-detail").collapse("show");
          break;
        };
      }

      for (var idx in SEARCH_BASIC_FIELDS) {
        var fieldName = SEARCH_BASIC_FIELDS[idx];
        if (vm.params[fieldName]) {
          $("#search-basic").collapse("show");
          break;
        };
      }

      _.forEach(SEARCH_DATE_TIME_FIELDS, function(field) {
        $("[name=" + field + "]").datepicker('setDate', vm.params[field]);
      });
    // });
  };

  vm.classOfElementSum = function(elementType) {
    var compareElement = elementType == "button" ? vm.showSumBtnCalculation : vm.showSumCalculation;
    return compareElement ? "show-sum" : "d-none";
  };

  vm.showSumColumn = function() {
    if (vm.showSumBtnCalculation) {
      vm.fetchSumCaculate();
    }
  };

  vm.fetchSumCaculate = function() {
    if (vm.loadingSumCalculation === false && vm.total_items > 0) {
      $("#modal-loading-calculate-sum").modal("show");
      vm.loadingSumCalculation = true;
      var params = formatParamsBeforeSearch(vm.currentParams);
      arrangementService.getArrangementSumFooters({search: params}).then(function(res) {
        if (res.status === 200) {
          vm.dataSum = res.data.sum_by_columns;
          _.forEach(HIDDEN_COLUMNS_BY_BILLING, function(column) {
            vm.dataSum[column + "_temp"] = vm.dataSum[column];
          });
          vm.formatDisplayCaculate();
          vm.formatDisplaySum();
          vm.showSumCalculation = true;
        }
      }, function(error) {
        if (error.data.error) {
          $modalAlertSum.modal("show");
        } else {
          toastr.error(error.statusText);
        }
      }).then(function (){
        vm.loadingSumCalculation = false;
        $("#modal-loading-calculate-sum").modal("hide");
      });
    }
  };

  vm.refresh = function(isResetPage, isSave, isLoadHeader, isChangeNumPage) {
    $(".search-btn").attr("disabled", "disabled");
    vm.currentParams = angular.copy(vm.params);
    if (isLoadHeader) {
      arrangementService.loadArrangementHeaders().then(function(res) {
        formatTableHeaderColumns(res.data.labels);
        vm.tableHeaderColumns = getHeaderColumnsByPattern();
        vm.basicFrozenLimitNumber = ITEM_LIMIT_NUMBER;
      });
    }

    if (isChangeNumPage) {
      vm.basicFrozenLimitNumber = ITEM_LIMIT_NUMBER;
      vm.scrollWhenChangePage();
    }

    if (isResetPage) {
      vm.params.page = 1;
      vm.dataSum = {};
      vm.showSumCalculation = false;
      vm.loadingSumCalculation = false;
      vm.total_items = "";
    }
    // if (isSave)
    //   arrangementService.createSearchCondition({search: _.omit(vm.params, "page"), search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {});
    search_params = formatParamsBeforeSearch(angular.copy(vm.params));
    arrangementService.searchArrangement({search: search_params}).then(function(res) {
      angular.extend(vm, res.data);
      $(".search-btn").removeAttr("disabled");
      if (vm.total_items > vm.limitRecordAlertLarge) {
        vm.arrangements = [];
        vm.total_items = "";
        vm.statistic_data = {};
        vm.dataSum = {};
        vm.showSumCalculation = false;
        vm.showSumBtnCalculation = false;
        $modalAlert.modal("show");
      } else {
        if (isResetPage) vm.getStatisticData();
        vm.showSumBtnCalculation = true;
      }
      vm.hasRecord = Boolean(vm.arrangements.length);
      $location.search(search_params).replace();
      saveSearchParams(search_params);
      vm.changeDisplayPattern();
    });
  };

  function saveSearchParams(search_params) {
    delete search_params.per_page;
    delete search_params.page ;
    arrangementService.searchParams = search_params;
  };

  function formatParamsBeforeSearch(params) {
    _.forEach(MULTI_SELECT_FIELDS, function(attr) {
      if (!_.isUndefined(params[attr])) {
        if (Array.isArray(params[attr])) {
          params[attr] = params[attr].join(",");
        }
      }
    });
    _.forEach(SEARCH_ARR_PARAMS, function(attr) {
      params[attr] = vm["" + _.camelCase(attr) + "s"].filter(function(field) {
        return !!field.selected;
      }).map(function(field) {
        return field.id;
      }).join(",");
    });
    params[CURRENT_LOCATION_TYPE] = vm.currentLocationTypes.filter(function(field) {
      return !!field.selected;
    }).map(function(field) {
      return field.key;
    }).join(",");
    if (params[CURRENT_LOCATION_TYPE].includes("all_location")) {
      params[CURRENT_LOCATION_TYPE] = "";
    }
    return params;
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".staff-search-result").offset().top
    }, 1000);
  };

  vm.scrollWhenChangePage = function() {
    $(".unfrozen-view__rows").animate({
      scrollTop: $(".unfrozen-view__rows")
    }, 1000);
  }

  vm.statusConfirmWorking = function(option) {
    if (_.isNull(option)) return;
    return option ? vm.optionBeforeWork[0].i18n_label : vm.optionBeforeWork[1].i18n_label;
  }

  vm.changePerPage = function() {
    vm.refresh();
    vm.scrollToResult();
    vm.scrollWhenChangePage();
  };

  vm.changeDisplayPattern = function() {
    vm.tableHeaderColumns = getHeaderColumnsByPattern();
    vm.basicFrozenLimitNumber = ITEM_LIMIT_NUMBER;
    vm.formatDisplaySum();
    vm.scrollWhenChangePage();
  };

  vm.formatDisplaySum = function() {
    _.forEach(HIDDEN_COLUMNS_BY_BILLING, function(column) {
      if (vm.displayPattern === "input_billing") {
        vm.dataSum[column] = vm.dataSum[column + "_temp"];
      } else {
        vm.dataSum[column] = vm.dataSum[column + "_original"];
      }
    });
  };

  vm.formatDisplayCaculate = function() {
    _.forEach(Object.keys(vm.dataSum), function(column) {
      if (TIME_VALUE_COLUMNS.includes(column)) {
        if (column === "working_time") {
          vm.dataSum[column] = hoursWithMinutes(vm.dataSum[column + "_original"], "second");
        } else {
          vm.dataSum[column] = hoursWithMinutes(vm.dataSum[column + "_original"], "minute");
        }
      } else {
        vm.dataSum[column] = numberWithCommas(vm.dataSum[column]);
      }
    });
  };

  vm.showStaffHasMultipleReferralWarning = function(staffInfo, amount) {
    return I18n.t("admin.arrangements.modal.staff_referral_location_warning.warning", {staff_info: staffInfo, amount: amount});;
  }

  function hoursWithMinutes(data, type) {
    if (!data) {
      return 0 + I18n.t("common.time.hour");
    }
    var hour = 0;
    var minutes = 0;
    if (type === "second") {
      hour = _.toInteger(data / SECOND_IN_HOUR);
      minutes = _.toInteger(data / SECOND_PER_MINUTE) % SECOND_PER_MINUTE;
    } else {
      hour = _.toInteger(data / SECOND_PER_MINUTE);
      minutes = data % SECOND_PER_MINUTE;
    }
    var time = hour + I18n.t("common.time.hour");
    if (minutes != 0) {
      time += " " + minutes + I18n.t("common.time.minute");
    }
    return time;
  };

  function numberWithCommas(price) {
    return price.toString().replace(FORMAT_PRICE, ",");
  }

  vm.classForSortColumn = function(column) {
    return {
      "fas fa-sort": !_.isEqual(vm.params.order_key, column),
      "fas fa-sort-up": _.isEqual(vm.params.order_key, column) && !vm.params.desc,
      "fas fa-sort-down": _.isEqual(vm.params.order_key, column) && !!vm.params.desc
    }
  };

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
    }
    vm.refresh(false, true, false, false);
    vm.scrollToResult();
  };

  vm.toggleStatisticTable = function() {
    if (_.isUndefined(vm.limitGetStatisticData)) {
      vm.limitGetStatisticData = angular.element("#limit-get-statistic").data("limit-get-statistic");
      vm.getStatisticData();
    }
    vm.isShowStatisticTable = !vm.isShowStatisticTable;
    setToggleStatisticLabel();
  };

  vm.checkWorkingHourImage = function(startTime, id) {
    if (startTime) {
      var workingStartTime = moment(startTime.split("~")[0], TIME_PICKER_FORMAT);
      var workingHourElement = _.find(vm.workingHours, function(element) {
        var fromStartTime = moment(element.from, TIME_PICKER_FORMAT);
        var toStartTime = moment(element.to, TIME_PICKER_FORMAT);
        return (workingStartTime.isSameOrAfter(fromStartTime) && workingStartTime.isSameOrBefore(toStartTime));
      });
      var classAdded = $(".working-hour-image-" + id)[0].className.split("working-hour working-hour-image-" + id)[1];
      $(".working-hour-image-" + id).removeClass(classAdded).addClass(workingHourElement.class);
    }
  }

  function setToggleStatisticLabel() {
    vm.toggleStatisticLabel = (vm.isShowStatisticTable ? arrangementsI18n.labels.hide_statistic : arrangementsI18n.labels.show_statistic);
    vm.toggleStatisticIconClass = (vm.isShowStatisticTable ? "fa-arrow-circle-up" : "fa-arrow-circle-o-right");
  };

  function getHeaderColumnsByPattern() {
    var patternOrder = "164 165 1 2 4 14 15 134 135 7 10 12 16 5 17 18 19 20 150 22 23 21 172 41 163 42 166 47 48\
      151 11 13 152 51 52 102 103 111 112 113 114 115 116 117 170\
      128 129 131 133 136 145 146 148 149 153 154 155 156 157 158 167 174 24 147";
    var headerColumns = [];

    switch(vm.displayPattern) {
      case "input_payment":
        patternOrder = "164 165 1 2 4 7 151 10 11 12 150 13 152 14 15 16 17 18 19 20 22 23 21 172 28\
          29 30 32 33 34 159 160 35 36 37 168 169 38 39 40 41 163 42 166 170 43 44 45 46 47 48 51 52 56 88\
          128 129 131 133 134 135 136 145 146 148 149 153 154 155 156 5 157 158 174 24 147";
        setPatternData([], vm.arrangements, []);
        break;
      case "input_billing":
        patternOrder = "164 165 1 2 4 7 151 10 11 12 150 13 152 14 15 16 22 23 21 172 89 90\
          91 93 94 95 161 162 96 97 98 99 100 101 102 103 104 105 106 173 107 108 109 110 111 112 113 114 115\
          116 117 121 171 128 129 131 133 134 135 136 145 146 148 149 153 154 155 156 5 157 158 174 24 147";
        setPatternData([], [], vm.arrangements);
        break;
      default:
        setPatternData(vm.arrangements, [], []);
    }

    patternOrder = patternOrder.replace(/\s+/g, " ").split(" ");
    patternOrder.forEach(function(idx) {
      var headerData = vm.fullTableHeaderColumns[idx - 1];

      if (headerData) {
        headerColumns.push(headerData);
      }
    });
    return headerColumns;
  };

  function setPatternData(confirmOrderCase, inputPayment, inputBilling) {
    vm.confirmOrderCaseData = confirmOrderCase;
    vm.inputPaymentData = inputPayment;
    vm.inputBillingData = inputBilling;
  };

  vm.getStylePercent = function(avg) {
    return {"width": avg * 20 + "%"}
  }

  vm.openModalEvaluation = function(arrangementId) {
    $("#modal-staff-evaluation").modal("show");
    arrangementService.getStaffEvaluation(arrangementId).then(function(res) {
      var response = res.data;
      if (response.status) {
        vm.current_staff_evaluation = response.data;
      }
    })
  }

  $("#modal-staff-evaluation").on("hidden.bs.modal", function() {
    vm.current_staff_evaluation = {};
  })

  vm.openModalLocationEvaluation = function(arrangementId) {
    $("#modal-staff-location-evaluation").modal("show");
    arrangementService.getLocationEvaluation(arrangementId).then(function(res) {
      var response = res.data;
      if (response.status) {
        vm.current_staff_location_evaluation = response.data;
      }
    })
  }

  $("#modal-staff-location-evaluation").on("hidden.bs.modal", function() {
    vm.current_staff_location_evaluation = {};
  })

  vm.initModalOrderCaseStatus = function(arrangement_id, resetStatus) {
    vm.order_portion_status.current_arrangement_id = arrangement_id;
    vm.orderPortionStatusErrorMessage = "";

    arrangementService.getOrderPortionStatus({arrangement_id: arrangement_id}).then(function(res){
      angular.extend(vm.order_portion_status, res.data);
      if (resetStatus) {
        $("#select-order-portion-status").prop("selectedIndex", 0);
        vm.order_portion_status.target_status = "";
      }
      $("#portion-statuses").modal("show");
    });
  }

  vm.initRestTimes = function(arrangement) {
    var workingTime = {};
    var restTimes = [];
    _.forEach(REST_TIMES, function(rest_time) {
      var startTime = arrangement["rest" + rest_time + "_started_at"];
      var endTime = arrangement["rest" + rest_time + "_ended_at"];
      if (_.isEmpty(startTime)) return;
      workingTime["rest" + rest_time + "_started_at"] = formatTime(startTime);
      workingTime["rest" + rest_time + "_ended_at"] = formatTime(endTime);
      restTimes.push(rest_time);
    });
    workingTime["rest_times"] = restTimes.length === 0 ? [1] : restTimes;
    workingTime["id"] = arrangement.id;
    return workingTime;
  }

  vm.openInputBreakTimeModal = function(arrangement) {
    vm.arrangement = vm.initRestTimes(arrangement);
    vm.arrangement.working_started_at = formatTime(arrangement.working_started_at);
    vm.arrangement.working_ended_at = formatTime(arrangement.working_ended_at);
    vm.arrangement.is_time_changable = arrangement.is_time_changable;
    if (arrangement.required_start_time && arrangement.required_end_time) {
      vm.arrangement.required_start_time = formatTime(arrangement.required_start_time);
      vm.arrangement.required_end_time = formatTime(arrangement.required_end_time);
    }
    vm.arrangement.working_started_date = moment.parseZone(arrangement.working_started_at).format(FULL_DATE_FORMAT);
    $(".has-error").removeClass("has-error");
    $(".help-block").remove();
    $("#input-break-time").modal("show");
  }

  vm.isShowInputBreakTimeBtn = function(arrangement) {
    if (arrangement["is_regular_order"]) return false;
    return !arrangement["location_expired?"];
  };

  vm.isShowCopyPortionBtn = function(arrangement) {
    if (arrangement["is_regular_order"]) return false;
    return !arrangement['finish_apply?'] && !arrangement['location_expired?'] && !arrangement['deleted_user_order_pic?'];
  };

  vm.addRestTime = function(arrangement) {
    var restTimeCount = arrangement.rest_times.length;
    if (restTimeCount === REST_TIMES[2]) return;
    arrangement.rest_times.push(restTimeCount + 1);
  }

  vm.removeRestTime = function(restTime, arrangement) {
    if (arrangement.rest_times.length === REST_TIMES[0]) return;
    if (restTime === 2) {
      arrangement.rest2_started_at = arrangement.rest3_started_at;
      arrangement.rest2_ended_at = arrangement.rest3_ended_at;
    }
    arrangement.rest3_started_at = arrangement.rest3_ended_at = "";
    arrangement.rest_times.pop();
  }

  vm.updateBreakTime = function(skip_warning) {
    vm.breakTimeUpdating = true;
    var updateBreakTimeParams = $("form.form-input-break-time").serializeJSON();
    formatWorkingTime(updateBreakTimeParams);

    _.forEach(REST_TIMES, function(rest_time) {
      if (!_.includes(vm.arrangement.rest_times, rest_time)) {
        updateBreakTimeParams.arrangement["rest" + rest_time + "_started_at"] = "";
        updateBreakTimeParams.arrangement["rest" + rest_time + "_ended_at"] = "";
      }
    });
    vm.showSumCalculation = false;
    vm.dataSum = {};
    updateBreakTimeParams.skip_warning = !!skip_warning;
    $warningPopup.modal("hide");
    vm.warning = [];
    vm.totalWarning = 0;
    vm.warningIndex = 0;
    arrangementService.updateBreakTime(updateBreakTimeParams, vm.arrangement.id).then(function(res) {
      if (res.data.status) {
        vm.breakTimeUpdating = false;
        var arrangementIndex = _.findIndex(vm.arrangements, function(arrangement) {
          return arrangement.id === vm.arrangement.id;
        });
        _.forEach(Object.keys(res.data.arrangement), function(key) {
          vm.arrangements[arrangementIndex][key] = res.data.arrangement[key];
        });
        $("#input-break-time").modal("hide");
        displaySuccessMessage();
      } else {
        vm.breakTimeUpdating = false;
        $("#input-break-time").modal("hide");
        if (res.data.errors.length > 0) {
          displayErrorMessage(res.data.errors[0]);
          $.lawsonAjax(res.data);
        } else if (res.data.warning.length > 0) {
          vm.totalWarning = res.data.warning.length;
          vm.warning = res.data.warning;
          vm.currentWarning = vm.warning[0];
          $warningPopup.modal("show");
        } else {
          vm.breakTimeUpdating = false;
          $.lawsonAjax(res.data);
        }
      }
    });
  }

  vm.showWarning = function() {
    $warningPopup.modal("hide");
    if (vm.warningIndex == vm.totalWarning - 1) {
      vm.updateBreakTime(true)
    } else {
      vm.warningIndex = vm.warningIndex + 1;
      vm.currentWarning = vm.warning[vm.warningIndex];
      $timeout(function() {
        $warningPopup.modal("show");
      }, 600);
    }
  }

  function formatWorkingTime(updateBreakTimeParams) {
    _.forEach(["working_started_at", "working_ended_at"], function(key) {
      if (!_.isEmpty(updateBreakTimeParams.arrangement[key])) {
        updateBreakTimeParams.arrangement[key] = vm.arrangement.working_started_date + " " +
          updateBreakTimeParams.arrangement[key]
      }
    })
  }

  function formatTime(time) {
    if (_.isEmpty(time)) return;
    return moment.parseZone(time).format(TIME_PICKER_FORMAT);
  };

  vm.getStatusEvaluation = function(arrangement) {
    if (_.isEqual(arrangement.status_staff_evaluation, "has_staff")) {
      return arrangement.staff.account_name;
    } else {
      return I18n.t("admin.arrangements.status_evaluation_value." + arrangement.status_staff_evaluation);
    }
  }

  vm.isDisableRemoveArrangement = function(arrangement) {
    if (_.isUndefined(arrangement)) return;
    return _.includes(vm.disable_destroy, arrangement.status_staff_evaluation);
  }

  vm.actionStatusEvaluation = function(arrangement) {
    switch (arrangement.status_staff_evaluation) {
      case "arrange":
        vm.idArrangementSelected = arrangement.id;
        initStaffSeach();
        $("#modal-search-staff").modal("show");
        break;
      case "has_staff":
        window.open("/staffs/" + arrangement.staff.id + "/edit", "_blank");
        break;
      case "waiting_arrange":
        window.open("/new_batch_arranges?order_case_ids=" + arrangement.order_case_id);
        break;
    }
  }

  vm.removeArrangement = function(arrangement) {
    vm.arrangementRemove = arrangement;
    $("#modal-remove-arrangement").modal({backdrop: "static", keyboard: false});
  }

  vm.confirmRemoveArrangement = function() {
    if (_.isUndefined(vm.arrangementRemove)) return;
    arrangementService.deleteArrangement(vm.arrangementRemove.id).then(function mySuccess(res) {
      if (res.data.status) {
        $("#modal-remove-arrangement").modal("hide");
        _.remove(vm.arrangements, function(arrangement) {
          return _.isEqual(arrangement.id, vm.arrangementRemove.id);
        });
        vm.refresh(false);
        toaster.pop("success", "", I18n.t("admin.arrangements.index.action_status.delete_success"));
      }
    });
  }

  $('#modal-remove-arrangement').on('hidden.bs.modal', function (e) {
    vm.arrangementRemove = undefined;
  });

  // modal search staff

  var STAFF_SEARCH_CONDITION_TYPE = "admin_staff_search_conditions";
  var STAFF = "staff";
  var STAFF_SEARCH_FIELDS = ["staff_number", "address_kana", "account_email", "tel", "current_department_id",
    "account_name_kana", "account_name_lower", "only_arrangeble_staff", "only_match_schedule"];
  vm.staff_search_params = {};
  vm.simpleSearchStaff = {};

  function initStaffSeach() {
    vm.staff_search_params.desc = vm.staff_search_params.desc == "false" ? false : true;
    vm.staff_search_params.page = 1;
    vm.staff_search_params.per_page = vm.perPageSettings[0];
    vm.simpleSearchStaff.isCloseAllModal = false;
    _.forEach(STAFF_SEARCH_FIELDS, function(field) {
      vm.staff_search_params[field] = "";
    });
    $("#current_department_id").val(vm.staff_search_params.current_department_id).trigger("change.select2");
    vm.currentStaffParams = angular.copy(vm.staff_search_params);
    vm.refresh_staff();
  };

  vm.refresh_staff = function(resetPage, isUseSearchCondition) {
    var paramSearch = isUseSearchCondition ? vm.staff_search_params : formatPerPageParam(vm.currentStaffParams);
    if (resetPage) {
      paramSearch.page = 1;
    }
    if (!vm.staff_search_params.per_page) paramSearch.per_page = 10;

    arrangementService.seachStaffForTemporaryArrange({search: paramSearch, id: vm.idArrangementSelected}).then(function(res) {
      vm.idStaffSelected = "";
      vm.staffs = res.data.staffs;
      vm.total_staff_items = res.data.total_items;
      vm.simpleSearchStaff.hasRecord = Boolean(vm.staffs.length);
      vm.currentStaffParams = angular.copy(paramSearch);
    }, function(error) {
      vm.staffs = [];
    });
  };

  $('#modal-search-staff').on('hidden.bs.modal', function (e) {
    vm.idStaffSelected = "";
    vm.idArrangementSelected = "";
  });

  vm.classForSortColumnStaffSearch = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.staff_search_params.order_key, column),
      "fa-sort-up": _.isEqual(vm.staff_search_params.order_key, column) && !vm.staff_search_params.desc,
      "fa-sort-down": _.isEqual(vm.staff_search_params.order_key, column) && !!vm.staff_search_params.desc
    }
  };

  vm.sortStaffSearch = function(column) {
    if (_.isEqual(vm.staff_search_params.order_key, column)) {
      vm.staff_search_params.desc = !vm.staff_search_params.desc;
      vm.currentStaffParams.desc = !vm.currentStaffParams.desc;
    } else {
      vm.staff_search_params.desc = true;
      vm.staff_search_params.order_key = column;
      vm.currentStaffParams.desc = true;
      vm.currentStaffParams.order_key = column;
    }
    vm.refresh_staff(false, false);
  };

  function formatPerPageParam(currentStaffParams) {
    currentStaffParams.per_page = vm.staff_search_params.per_page;
    currentStaffParams.page = vm.staff_search_params.page;
    return currentStaffParams;
  };

  vm.changePerPageStaffSearch = function() {
    vm.refresh_staff(true, true);
  };

  $(".auto-search").keydown(function(event) {
    if(event.keyCode == ENTER_KEY_CODE) {
      event.preventDefault();
      vm.refresh_staff(true, true);
    }
  });

  vm.selectStaff = function(idStaff) {
    vm.idStaffSelected = idStaff;
  }

  vm.closeModalConfirmArrangeStaff = function() {
    if(vm.simpleSearchStaff.isCloseAllModal) {
      $("#modal-confirm-staff-arranged").modal("hide");
      $("#modal-search-staff").modal("hide");
    } else {
      $("#modal-confirm-staff-arranged").modal("hide");
      $("#modal-confirm-staff-arranged").on("hidden.bs.modal", function() {
        $('body').addClass('modal-open');
      });
    }
  }

  vm.confirmArrangeStaff = function(ignoreWarning) {
    var submitParams = {staff_id: vm.idStaffSelected, id: vm.idArrangementSelected, ignore_warning: ignoreWarning};
    arrangementService.arrangeStaff(submitParams).then(function mySuccess(res) {
      var response = res.data;
      vm.arrangeCondition = {};
      vm.simpleSearchStaff.hasTemporaryArrange = Boolean(response.has_temporary_arrange);
      if (vm.simpleSearchStaff.hasTemporaryArrange) {
        $("#modal-confirm-staff-arranged").modal("show");
        var index = _.findIndex(vm.arrangements, {id: response.arrangement.id});
        vm.arrangements.splice(index, 1, res.data.arrangement);
        vm.simpleSearchStaff.isCloseAllModal = true;
      } else {
        if (!_.isUndefined(response.arrange_condition)) {
          vm.arrangeCondition = response.arrange_condition;
        }
        if (_.isEmpty(vm.arrangeCondition) || (vm.arrangeCondition.valid_arrange && (!vm.arrangeCondition.warning_arrange && !vm.arrangeCondition.notify_arrange))) {
          $("#modal-search-staff").modal("hide");
          $("#modal-confirm-staff-arranged").modal("hide");
          var index = _.findIndex(vm.arrangements, {id: res.data.arrangement.id});
          vm.arrangements.splice(index, 1, res.data.arrangement);
        } else {
          $("#modal-confirm-staff-arranged").modal("show");
        }
      }
    });
  }

  vm.submitOrderPortionStatus = function() {
    vm.arrangeCondition = vm.order_portion_status.arrange_condition;
    if (vm.order_portion_status.current_status !== "arranged" &&
      vm.order_portion_status.target_status === "arranged") {
      if (vm.arrangeCondition.valid_arrange && !vm.arrangeCondition.warning_arrange && !vm.arrangeCondition.notify_arrange) {
        vm.confirmSubmitOrderPortionStatus();
      } else if (!vm.arrangeCondition.valid_arrange || vm.arrangeCondition.warning_arrange || vm.arrangeCondition.notify_arrange) {
        $("#modal-confirm-portion-status-arranged").modal("show");
      }
    } else if (vm.order_portion_status.current_status === "type_public" &&
      vm.order_portion_status.target_status === "cancel") {
      if (vm.order_portion_status.order_case_segment_id === REGULAR_ORDER) {
        $("#portion-statuses").modal("hide");
        $("#modal-confirm-cancel-trigger").modal("show");
      } else {
        vm.confirmSubmitOrderPortionStatus();
      }
    } else {
      vm.confirmSubmitOrderPortionStatus();
    }
  }

  vm.confirmArrangeWarning = function() {
    vm.confirmSubmitOrderPortionStatus();
  }

  vm.onConfirmCancelTriggerModal = function() {
    $("#modal-confirm-cancel-trigger").modal("hide");
    vm.order_portion_status.should_trigger = true;
    vm.confirmSubmitOrderPortionStatus();
  }

  vm.onCancelCancelTriggerModal = function() {
    $("#modal-confirm-cancel-trigger").modal("hide");
    vm.order_portion_status.should_trigger = false;
    vm.confirmSubmitOrderPortionStatus();
  }

  vm.confirmSubmitOrderPortionStatus = function() {
    var targetStatus = vm.order_portion_status.target_status;
    var currentStatus = vm.order_portion_status.current_status;
    var submitParams = {
      target_status: targetStatus,
      is_urgent: vm.order_portion_status.is_urgent,
      arrangement_id: vm.order_portion_status.current_arrangement_id,
      status: vm.order_portion_status.current_status,
      store_note: vm.order_portion_status.store_note,
      arrange_comment: vm.order_portion_status.arrange_comment,
      should_trigger: vm.order_portion_status.should_trigger
    };
    submitParams = extendStatusPortionParams(submitParams);

    if (targetStatus == "type_limited") {
      submitParams.staff_limited = {
        staff_ids: _.map(vm.staff_limited.choose_list, "id"),
        is_public: vm.staff_limited.is_public
      }
    }

    $("#modal-loading-when-change-status").modal("show");
    arrangementService.updateOrderPortionStatus(submitParams, vm.order_portion_status.order_case_id).then(function(res){
      var response = res.data;
      if (response.status) {
        var cancelStatuses = ["absence", "cancel_after_arrange_has_insurance", "cancel_after_arrange_no_insurance"];
        if ((currentStatus === "arranged" && _.includes(cancelStatuses, targetStatus)) ||
          (_.includes(cancelStatuses, currentStatus) && targetStatus === "arranged") ||
          targetStatus === "absence") {
          $timeout(function() {
            vm.refresh();
          }, 100);
        } else {
          _.forEach(response.arrangements, function(currentArrangement) {
            var arrangementIndex = _.findIndex(vm.arrangements, function(arrangement) {
              return arrangement.id === currentArrangement.id;
            });
            if (arrangementIndex != -1) {
              vm.arrangements[arrangementIndex] = currentArrangement;
            }
          });
        }
        $("#portion-statuses").modal("hide");
        $("#modal-confirm-portion-status-arranged").modal("hide");
        displaySuccessMessage();
        if (currentStatus === "absence" && targetStatus === "arranged") {
          $("#modal-warning-arranged-from-absence").modal("show");
        }
        vm.refresh(false, false);
      } else if (res.data.lock_msg) {
        displayErrorMessage(res.data.lock_msg);
        $("#portion-statuses").modal("hide");
      } else {
        vm.initModalOrderCaseStatus(vm.order_portion_status.current_arrangement_id, false);
        vm.orderPortionStatusErrorMessage = I18n.t("arrangement_status.errors")[response.error_key];
      }
      $("#modal-loading-when-change-status").modal("hide");
    });
  }

  vm.openEditOrderSegmentModal = function(arrangement) {
    vm.editOrderSegmentModal = {arrangement: arrangement,
      selectedSegmentId: angular.copy(arrangement.order_case_segment_id)};
    $("#edit-order-segment-modal").modal("show");
  };

  vm.isRegularOrder = function(arrangement) {
    return arrangement.order_case_segment_id == REGULAR_ORDER;
  }

  vm.updateOrderCaseSegementId = function() {
    vm.editOrderSegmentModal.isSubmiting = true;
    arrangementService.updateOrderCaseSegementId({segment_id: vm.editOrderSegmentModal.selectedSegmentId},
      vm.editOrderSegmentModal.arrangement.id).then(function(res) {
      var response = res.data;
      vm.editOrderSegmentModal.isSubmiting = false;
      if (response.status) {
        vm.arrangements.forEach(function(arrangement) {
          if (arrangement.order_case_id === response.arrangement.order_case_id) {
            arrangement.order_case_segment_id = response.arrangement.order_case_segment_id;
          }
        });
        displaySuccessMessage();
      } else if (res.data.lock_msg) {
        displayErrorMessage(res.data.lock_msg);
      }
      $("#edit-order-segment-modal").modal("hide");
    });
  };

  vm.initForTargetStatus = function() {
    switch(vm.order_portion_status.target_status) {
      case "type_limited":
        vm.initForStatusLimitStaff();
        break;
      default:
        break;
    }
  }

  vm.initForStatusLimitStaff = function() {
    vm.staff_limited = {};
    arrangementService.getCurrentStaffLimited({order_case_id: vm.order_portion_status.order_case_id}).then(function(res){
      if (res.data.staffs.length > 0) {
        vm.staff_limited.choose_list = res.data.staffs;
        vm.staff_limited.is_public = res.data.type_limited;
      } else {
        vm.staff_limited.choose_list = [];
        vm.staff_limited.is_public = true;
      }
    });
    vm.staff_limited.source_list = [];
    vm.staff_limited.choose_list = [];
    vm.staff_limited.is_public = true;
    vm.staff_limited.arr_staff_no = []; vm.addMoreStaffNo();
    vm.staff_limited.search_params = {
      total_page: 1,
      page: 1,
      nationality: '',
      free_word: '',
      current_department_id: '',
      prefecture_id: '',
      staff_number: ''
    };
    vm.searchStaffForStatusLimit(true);
  }

  vm.searchStaffForStatusLimit = function(resetList) {
    if (resetList) {
      vm.staff_limited.source_list = [];
      vm.staff_limited.search_params.page = 1;
      vm.staff_limited.search_params.total_page = 1;
    }
    vm.staff_limited.search_params.staff_number = _.compact(vm.staff_limited.arr_staff_no).join(",");
    var params = {search: vm.staff_limited.search_params, arrangement_id: vm.order_portion_status.current_arrangement_id};
    arrangementService.seachStaffForStatusLimit(params).then(function(res) {
      var staffs = _.map(res.data.staffs, function(staff) {
        return _.extend({}, staff, {selected: false});
      });
      vm.staff_limited.search_params.page = res.data.page;
      vm.staff_limited.search_params.total_page = Math.ceil(res.data.total_items / res.data.per_page);
      vm.staff_limited.source_list = _.concat(vm.staff_limited.source_list, staffs);
      vm.staff_limited.source_list = _.uniqBy(vm.staff_limited.source_list, "id");
    });
  }

  vm.delMoreStaffNo = function(index) {
    vm.staff_limited.arr_staff_no.splice(index, 1);
  }

  vm.addMoreStaffNo = function() {
    vm.staff_limited.arr_staff_no.push("");
  }

  vm.loadMoreStaffForStatusLimit = function() {
    if (vm.staff_limited.search_params.page + 1 <= vm.staff_limited.search_params.total_page) {
      vm.staff_limited.search_params.page += 1;
      vm.searchStaffForStatusLimit();
    }
  }

  vm.chooseStaffForPublic = function(staff) {
    staff.selected = !staff.selected;
  }

  vm.moveSelectedStaff = function() {
    var selectedStaff = _.remove(vm.staff_limited.source_list, function(s){return s.selected;});
    _.forEach(selectedStaff, function(s){s.selected = false});
    vm.staff_limited.choose_list = _.concat(vm.staff_limited.choose_list, selectedStaff);
    vm.staff_limited.choose_list = _.uniqBy(vm.staff_limited.choose_list, "id");
  }

  vm.moveAllStaff = function() {
    vm.staff_limited.choose_list = _.concat(vm.staff_limited.choose_list, vm.staff_limited.source_list);
    _.forEach(vm.staff_limited.choose_list, function(s){s.selected = false});
    vm.staff_limited.choose_list = _.uniqBy(vm.staff_limited.choose_list, "id");
    vm.staff_limited.source_list = [];
    vm.loadMoreStaffForStatusLimit();
  }

  vm.removeStaffFromChoose = function(staff) {
    _.remove(vm.staff_limited.choose_list, function(s){
      return s.id == staff.id;
    });
    vm.staff_limited.source_list.push(staff);
    vm.staff_limited.choose_list = _.uniqBy(vm.staff_limited.choose_list, "id");
  }

  vm.publicStaffBtnClass = function(buttonName) {
    if (_.isEmpty(vm.order_portion_status.target_status)) return "disabled";
    if (vm.order_portion_status.target_status != "type_limited") {
      return "";
    }
    if ((buttonName == "save" && vm.staff_limited.choose_list.length == 0) ||
      (buttonName == "move_choose" && _.findIndex(vm.staff_limited.source_list, {selected: true}) == -1)) {
      return "disabled";
    }
  }

  function extendStatusPortionParams(params) {
    switch(vm.order_portion_status.target_status) {
      case "absence":
        params.is_replace_portion = vm.order_portion_status.is_replace_portion;
        params.penalty_evaluation = vm.order_portion_status.penalty_evaluation;
        break;
      case "cancel":
      case "cancel_after_arrange_no_insurance":
      case "cancel_after_arrange_has_insurance":
        params.order_portion_ids = cancelOrderPortionParamId();
        break;
    }
    return params;
  }

  vm.initCancelPortionData = function() {
    arrangementService.getListOrderPortionCancel(vm.order_portion_status.order_case_id).then(function(res) {
      vm.cancelStatusOrderPortions = res.data.order_portions;
      _.forEach(vm.cancelStatusOrderPortions, function(orderPortion) {
        if (selectedPortionCondition(orderPortion)) {
          orderPortion.selected = true;
        }
      });
    });
  }

  function selectedPortionCondition(orderPortion) {
    var selectedCondition = (orderPortion.status_id == "arranged") && (orderPortion.id == vm.order_portion_status.order_portion_id);
    if (vm.order_portion_status.target_status == "cancel") {
      selectedCondition = _.includes(CHECKED_PORTION_STATUS, orderPortion.status_id) || (orderPortion.id == vm.order_portion_status.order_portion_id);
    }
    return selectedCondition;
  }

  vm.styleScrollElement = function() {
    var portionWrapStyle = {};
    if (vm.cancelStatusOrderPortions && (vm.cancelStatusOrderPortions.length > NUMBER_PORTION)) {
      portionWrapStyle = {
        "height": "400px",
        "overflow": "auto"
      }
    }
    return portionWrapStyle;
  }

  vm.disablePortionCheckbox = function(orderPortion) {
    var isDisabled = orderPortion.status_id != "arranged";
    if (vm.order_portion_status.target_status == "cancel"
      || vm.order_portion_status.target_status == "cancel_after_arrange_no_insurance"
      || vm.order_portion_status.target_status == "cancel_after_arrange_has_insurance") {
      isDisabled = _.includes(DISABLE_PORTION_STATUS, orderPortion.status_id) && (orderPortion.id != vm.order_portion_status.order_portion_id);
    }
    return isDisabled;
  }

  function cancelOrderPortionParamId() {
    var portionIds = [];
    _.forEach(vm.cancelStatusOrderPortions, function(orderPortion) {
      var validPortion = !vm.disablePortionCheckbox(orderPortion);
      if (!_.includes(CHECKED_PORTION_STATUS, orderPortion.status_id) && orderPortion.selected && validPortion) {
        portionIds.push(orderPortion.id)
      }
    });
    return portionIds;
  }

  vm.showSaveOrderPortionStatusButton = function() {
    var targetStatus = vm.order_portion_status.target_status;
    var currentStatus = vm.order_portion_status.current_status;
    return (targetStatus != "temporary_arrange" && targetStatus != "arranged") ||
      (targetStatus == "arranged" && currentStatus != "arranged" && 
      (vm.order_portion_status.not_finish_recruiting_status || !vm.order_portion_status.has_alternative_op_applied))
  }

  vm.openConfirmArrangeJobMailModal = function() {
    vm.$scope.confirmArrangeJobMailModal = {notifyChanges: true};
    $("#confirm-arrange-job-mail-modal").modal("show");
  }

  vm.openOfferMailModal = function() {
    var searchParams = formatParamsBeforeSearch(angular.copy(vm.params));
    arrangementService.checkMultipleDepartments({search: searchParams}).then(function(res) {
      if (res.data.status) {
        $("#offer-job-mail-modal").modal("show");
      } else {
        $("#modal-dont-send-offer-email").modal("show");
      }
    });
    vm.$scope.offerJobMailModal = {notifyChanges: true, search: searchParams};
  }

  vm.exportArrangements = function(e) {
    search_params = formatParamsBeforeSearch(angular.copy(vm.params));
    search_params.total_items = vm.total_items;
    var additionParams = {format_template: "arrangement"};
    $.asyncDownload(e, "arrangements", JSON.stringify({condition: search_params}), "xlsx", additionParams);
  };

  vm.exportBillingsReform = function(e) {
    search_params = formatParamsBeforeSearch(angular.copy(vm.params));
    search_params.total_items = vm.total_items;
    $("#staff-referral-location-warning-modal").modal("hide");
    exportBillings(e, search_params);
  };

  vm.checkValidToExportBillingsReform = function(e) {
    search_params = formatParamsBeforeSearch(angular.copy(vm.params));
    search_params.total_items = vm.total_items;

    arrangementService.checkValidToExport({condition: search_params}).then(function(res) {
      if(res.data.status) {
        exportBillings(e,search_params);
      } else {
        vm.staffHasMultipleReferral = res.data.staff_has_multiple_referral;
        $("#staff-referral-location-warning-modal").modal("show");
      }
    });
  }

  vm.checkLimitExport = function(e, format_template) {
    var params = {format_template: format_template};
    params.search = formatParamsBeforeSearch(angular.copy(vm.params));

    arrangementService.checkLimitExport(params).then(function(res) {
      if (res.data.status) {
        continueExportEvent(e, format_template);
      } else {
        vm.alertLimitExportMessage = res.data.message;
        $modalAlertExport.modal("show");
      }
    })
  };

  function continueExportEvent(e, format_template) {
    if (format_template == "arrangement") {
      vm.exportArrangements(e);
    } else {
      vm.checkValidToExportBillingsReform(e);
    }
  }

  vm.openModalPaymentUnitPrice = function(arrangement) {
    $(".has-error").removeClass("has-error");
    $(".help-block").remove();
    $("#input-payment-unit-price").modal("show");
    vm.arrangement = arrangement;
    var arrangePayment = arrangement.arrange_payment;
    var orderPortion = arrangement.order_portion;
    var startTime = formatTime(orderPortion.case_started_at);
    var endTime = formatTime(orderPortion.case_ended_at);
    vm.paymentPrices = getPaymentPrices(startTime, endTime);
    _.forEach(vm.paymentPrices, function(paymentPrice) {
      if (arrangePayment["payment_" + paymentPrice.type + "_unit_price"]) {
        paymentPrice.price = parseInt(arrangePayment["payment_" + paymentPrice.type + "_unit_price"]);
      }
    });
  };

  vm.savePaymentUnitPrice = function() {
    var arrangePayment = vm.arrangement.arrange_payment;
    var basicPaymentPrice = paymentPriceByType("basic");
    var nightPaymentPrice = paymentPriceByType("night");
    var arrangePaymentParams = {payment_basic_unit_price: basicPaymentPrice,
      payment_night_unit_price: nightPaymentPrice};
    arrangementService.updateArrangePayment({arrange_payment: arrangePaymentParams}, vm.arrangement.id).then(function(res) {
      if (res.data.status) {
        displaySuccessMessage();
        arrangePayment.payment_basic_unit_price = basicPaymentPrice;
        arrangePayment.payment_night_unit_price = nightPaymentPrice;
        $("#input-payment-unit-price").modal("hide");
      } else {
        $.lawsonAjax(res.data);
      };
    });
  };

  function exportBillings(e, search_params) {
    var additionParams = {
      format_template: "billing_reform",
      from: search_params.working_started_at,
      to: search_params.working_ended_at,
      invoice_target: search_params.invoice_target
    };
    $.asyncDownload(e, "arrangements", JSON.stringify({condition: search_params}), "xlsx", additionParams);
  };

  function paymentPriceByType(type) {
    var payment = _.find(vm.paymentPrices, function(paymentPrice) {
      return paymentPrice.type == type;
    });
    if (payment) {
      return payment.price;
    };
  };

  function getPaymentPrices(startTime, endTime) {
    var startTimeFormat = momentTime(startTime);
    var endTimeFormat = momentTime(endTime);
    var startElement = timeElement(startTime, "to");
    var endElement = timeElement(endTime, "from");
    var result = [];
    if (startElement.index == endElement.index) {
      if (startTimeFormat.isSameOrBefore(endTimeFormat)) {
        result = [{type: startElement.type, time: startTime + " ~ " + endTime}];
      } else {
        if (startTimeFormat.isBefore(momentTime(vm.paymentWorkingTimes[2].from))) {
          result = [{time: [startTime + " ~ " + vm.paymentWorkingTimes[1].to,
            vm.paymentWorkingTimes[1].from + " ~ " + endTime].join(COMMA), type: "basic"},
            {time: vm.paymentWorkingTimes[2].from + " ~ " + vm.paymentWorkingTimes[0].to, type: "night"}];
        } else {
          result = [{time: vm.paymentWorkingTimes[1].from + " ~ " + endTime, type: "basic"},
            {time: startTime + " ~ " + vm.paymentWorkingTimes[0].to, type: "night"}];
        };
      }
    } else if (momentTime(startElement.end).add(1, "minutes").format(TIME_PICKER_FORMAT) == endElement.start) {
      if (startElement.type == endElement.type) {
        result = [{type: startElement.type, time: startElement.start + " ~ " + endElement.end}];
      } else {
        result = [startElement, endElement];
      }
    } else if (momentTime(endElement.start).isSameOrBefore(momentTime(vm.paymentWorkingTimes[0].to))) {
      endElement.start = momentTime(startElement.end).add(1, "minutes").format(TIME_PICKER_FORMAT);
      endElement.time = endElement.start + " ~ " + endElement.end;
      result = [startElement, endElement];
    } else {
      startElement.end =  momentTime(endElement.start).add(-1, "minutes").format(TIME_PICKER_FORMAT);
      startElement.time = startElement.start + " ~ " + startElement.end;
      result = [startElement, endElement];
    };
    return  _.orderBy(result, "type", "asc");
  };

  function timeElement(workTime, type) {
    var workingTime = momentTime(workTime);
    var workingTimeElement = _.find(vm.paymentWorkingTimes, function(element) {
      var fromStartTime = momentTime(element.from);
      var toStartTime = momentTime(element.to);
      return (workingTime.isSameOrAfter(fromStartTime) && workingTime.isSameOrBefore(toStartTime));
    });
    var time = "", start = "", end = "";
    if (type == "to") {
      start = workTime;
      end = workingTimeElement[type];
    } else {
      start = workingTimeElement[type];
      end = workTime;
    };
    time = start + " ~ " + end;
    return _.merge(workingTimeElement, {time: time, start: start, end: end})
  };

  function momentTime(time) {
    return moment(time, TIME_PICKER_FORMAT);
  };

  vm.updateWorkingTimeStatusId = function(arrangement) {
    if (arrangement.payment_requested) {
      vm.selectedStatusArrangement = arrangement;
      $("#modal-confirm-working-time-status").modal("show");
    } else {
      vm.submitWorkingTimeStatusId(arrangement, false);
    }
  }

  vm.submitWorkingTimeStatusId = function(arrangement, confirmStatus) {
    var targetStatus = arrangement.work_achievement_working_time_status_id;
    var submitParams = {
      working_time_status_id: targetStatus,
      confirm_status: confirmStatus
    };
    arrangementService.updateWorkingTimeStatusId(submitParams, arrangement.id).then(function(res) {
      if (res.data.status) {
        $("#modal-confirm-working-time-status").modal("hide");
        displaySuccessMessage();
      } else {
        arrangement.work_achievement_working_time_status_id = res.data.arrangement.work_achievement_working_time_status_id;
        if (!_.isUndefined(res.data.errors) && JSON.parse(res.data.errors)["record_locked"]) {
          displayErrorMessage(JSON.parse(res.data.errors)["record_locked"][0]);
        } else {
          displayErrorMessage(I18n.t("admin.arrangements.update_working_status.error_message")[targetStatus]);
        }
      }
    });
  }

  vm.updateInvoceTarget = function(arrangement) {
    arrangementService.updateInvoicetarget({invoice_target: arrangement.invoice_target}, arrangement.id)
      .then(function(res) {
        if (res.data.status) {
          vm.resetToLastSearchCondition(true)
          displaySuccessMessage();
        } else {
          displayErrorMessage();
        }
    });
  }

  function displaySuccessMessage() {
    return toaster.pop("success", "", I18n.t("admin.arrangements.index.action_status.update_success"));
  }

  function displayErrorMessage(lockMsg) {
    var msg = lockMsg || I18n.t("admin.arrangements.index.action_status.update_fail");
    return toaster.pop("error", "", msg);
  }

  vm.onSave = function(value, names, arrangementId, actionService, modelName) {
    vm.showSumCalculation = false;
    vm.dataSum = {};
    var updateParams = {};
    updateParams[modelName] = {};
    var valueArr = _.toString(value).split("~");
    var nameArr = names.split(",");
    _.map(nameArr, function(name, index) {
      var valueAttr = valueArr[index];
      if (_.includes(TIME_TO_MINUTES_ATTRS, name)) {
        var formatedTime = valueAttr.split(":");
        valueAttr = _.toInteger(formatedTime[0]) * 60 + _.toInteger(formatedTime[1]);
      };
      return updateParams[modelName][name] = valueAttr;
    });
    arrangementService[actionService](updateParams, arrangementId).then(function(res){
      var arrangement = _.find(vm.arrangements, function(arrangement){
        return arrangement.id == res.data.arrangement.id;
      });
      if (res.data.status) {
        displaySuccessMessage();
        if (res.data.arrangement) {
          arrangement = _.merge(arrangement, res.data.arrangement);
          var oldArrangement = _.find(vm.previousValues, function(previousArrangement) {
            return previousArrangement.id == arrangement.id;
          });
          if (!_.isEmpty(oldArrangement)) {
            _.pull(vm.previousValues, oldArrangement);
          }
          oldArrangement = angular.copy(arrangement);
          vm.previousValues.push(oldArrangement);
        }
      } else {
        var arrangementUpdate = _.find(vm.previousValues, function(previousArrangement) {
          return previousArrangement.id == arrangement.id;
        });
        if (!_.isEmpty(arrangementUpdate)) {
          arrangement = _.merge(arrangement, arrangementUpdate);
        } else {
          vm.currentErrorArrangementId = arrangementId;
        }
        if (Array.isArray(res.data.errors)) {
          displayErrorMessage(res.data.errors[0]);
          return;
        }
        if (JSON.parse(res.data.errors)["record_locked"]) {
          displayErrorMessage(JSON.parse(res.data.errors)["record_locked"][0]);
        } else {
          displayErrorMessage();
        }
      }
    })
  }

  vm.updateIsStoreParkingAreaUsable = function(value, name, arrangementId) {
    var updateParams = {};
    updateParams[name] = value;
    arrangementService.updateIsStoreParkingAreaUsable(updateParams, arrangementId).then(function(res){
      var arrangements = _.filter(vm.arrangements, function(arrangement){
        return arrangement.order_id == res.data.arrangement.order_id;
      });
      displaySuccessMessage();
      _.forEach(arrangements, function(arrangement) {
        _.merge(arrangement, res.data.arrangement);
      });
      var oldArrangement = _.find(vm.previousValues, function(previousArrangement) {
        return previousArrangement.id == arrangement.id;
      });
      if (!_.isEmpty(oldArrangement)) {
        _.pull(vm.previousValues, oldArrangement);
      }
      oldArrangement = angular.copy(arrangement);
      vm.previousValues.push(oldArrangement);
    })
  }

  vm.openResendMailModal = function(arrangement) {
    vm.arrangementResendMail = arrangement;
    angular.element("#arrange-resend-mail-modal").modal("show");
  }

  vm.resendConfirmArrangeMail = function(toOwner) {
    if (_.isUndefined(vm.arrangementResendMail)) return;

    arrangementService.resendConfirmArrangeMail(toOwner, vm.arrangementResendMail.id).then(function (res) {
      $("#arrange-resend-mail-modal").modal("hide");
      if (res.data.status) {
        toaster.pop("success", "", I18n.t("admin.arrangements.index.action_status.resend_mail_success"));
      } else {
        toaster.pop("error", "", I18n.t("admin.arrangements.index.action_status.resend_mail_failed"));
      }
    });
  }

  vm.openHistoryModal = function(arrangement) {
    angular.element("#arrange-history-modal").modal("show");
    vm.arrangeLogs = {};
    arrangementService.getArrangeHistory(arrangement.id).then(function(res) {
      vm.arrangeLogs = res.data.arrange_logs;
    });
  }

  vm.openWorkAchievementHistoryModal = function(arrangement) {
    angular.element("#arrange-history-modal").modal("show");
    vm.arrangeLogs = {};
    arrangementService.getWorkAchievementHistory(arrangement.id).then(function(res) {
      vm.arrangeLogs = res.data.work_achievement_logs;
    });
  }

  vm.getStatisticData = function() {
    getStatisticDataFunc(1, 0, 0);
  };

  vm.check_different_time = function(arrangement, time) {
    var portionTime = moment.parseZone(arrangement.order_portion["case_" + time]).format(TIME_PICKER_FORMAT);
    var workingTime = moment.parseZone(arrangement["working_" + time]).format(TIME_PICKER_FORMAT);
    return portionTime != workingTime;
  };

  vm.splitWorkingTime = function(arrangementWorkingTime) {
    return arrangementWorkingTime.split("~");
  };

  function getStatisticDataFunc(page, totalRecords, gettedRecords) {
    var params = _.merge(angular.copy(vm.params), {per_page: vm.limitGetStatisticData, page: page});
    params = formatParamsBeforeSearch(params);
    arrangementService.getStatisticData({search: params}).then(function(res) {
      vm.total_items = res.data.total_items
      if (page === 1) {
        totalRecords = vm.total_items ? vm.total_items : res.data.total_items;
        vm.statistic_data = res.data.statistic_data;
      } else {
        _.forEach(res.data.statistic_data, function(value, key) {
          if (_.isUndefined(vm.statistic_data[key]) || _.isNaN(vm.statistic_data[key]))
            vm.statistic_data[key] = value;
          else
            vm.statistic_data[key] += value;
        })
      }
      gettedRecords += vm.limitGetStatisticData;
      if((totalRecords - gettedRecords) > 0) getStatisticDataFunc(page + 1, totalRecords, gettedRecords);
    });
  }

  vm.goToSearchYahoo = function(arrangement) {
    var startTime = arrangement.order_portion.case_started_at;
    startTime = moment.parseZone(startTime);
    var minutes = startTime.format("mm").split("");
    var params = {
      from: arrangement.staff_home_station_station_name,
      to: arrangement.order_location_stations_1_station_name,
      y: startTime.format("YYYY"),
      m: startTime.format("MM"),
      d: startTime.format("DD"),
      hh: startTime.format("HH"),
      m2: minutes[1],
      m1: minutes[0],
      type: 1,
      ticket: "normal",
      expkind: 1,
      ws: 3,
      s: 0,
      al: 1,
      shin: 1,
      ex: 1,
      hb: 1,
      lb: 1,
      sr: 1
    };
    var paramsList = [];
    _.forEach(params, function(value, key) {
      paramsList.push(key + "=" + value);
    });
    var newWindow = window.open("https://transit.yahoo.co.jp/search/result?" + paramsList.join("&"), "_blank");
    newWindow.opener = null;
  };

  vm.canEditPaymentTime = function(arrangement) {
    return arrangement.work_achievement_working_time_status_id != OP_CENTER_CONFIRMING_STATUS;
  }

  vm.checkDifferentTime = function(arrangement) {
    if (!vm.isShowWorkingTime(arrangement)) {
      return false;
    }
    var time = moment.parseZone(arrangement.working_started_at).format(TIME_PICKER_FORMAT);
    return arrangement.payment_start_time != time;
  }

  vm.differrentBreakTime = function(arrangement) {
    if (!vm.isShowWorkingTime(arrangement)) {
      return false;
    }
    var breakTime = 0;
    _.each(REST_TIMES, function(restTime) {
      var startTime = moment(arrangement["work_achievement_rest" + restTime + "_started_date"]);
      var endTime = moment(arrangement["work_achievement_rest" + restTime + "_ended_date"]);
      var diff = endTime.diff(startTime, "minutes");
      breakTime += diff;
    });
    return breakTime != arrangement.break_time;
  }

  vm.getColumnClass = function(columnKey) {
    var columnData = _.find(vm.tableHeaderColumns, {key: columnKey});

    return (typeof columnData !== "undefined" ? columnData.class : "tbl-col-fixed-w-200");
  }

  vm.formatHtml = function(content) {
    return $sce.trustAsHtml(content);
  }

  vm.initPortionData = function() {
    vm.portionParams = {};
    vm.billingFields = _.map([1, 2, 5, 6, 7], function(index) {
      var fieldName = "billing_field_" + index;
      return {
        field: fieldName,
        label: I18n.t("admin.arrangements.modal.add_portion_modal.billing." + fieldName),
        checked: false
      };
    });
  }

  vm.showCopyPortionModal = function(arrangement) {
    removeErrors();
    vm.portionParams.working_started_at = moment.parseZone(arrangement.working_started_at).format(FULL_DATE_FORMAT);
    vm.portionParams.working_time_started_at = moment.parseZone(arrangement.working_started_at).format(TIME_PICKER_FORMAT);
    vm.portionParams.working_time_ended_at = moment.parseZone(arrangement.working_ended_at).format(TIME_PICKER_FORMAT);
    var minDate = new Date(vm.portionParams.working_started_at);
    var maxDate = moment(minDate).add(1, "day")["_d"];
    $("#started_datepicker").datepicker("setDate", vm.portionParams.working_started_at);
    $("#started_datepicker").datepicker("setStartDate", minDate);
    $("#started_datepicker").datepicker("setEndDate", maxDate);
    vm.currentArrangement = vm.initRestTimes(arrangement);
    $copyPortionModal.modal("show");
  }

  vm.copyPortion = function(arrangement) {
    $(".btn-add-portion").prop("disabled", true);
    var addPortionParams = $("form.form-add-portion").serializeJSON();
    addPortionParams["id"] = arrangement.id;
    addPortionParams.arrangement["working_started_at"] = vm.portionParams.working_started_at;
    arrangementService.copyPortion(addPortionParams).then(function(res) {
      var data = res.data;
      if (data.status) {
        $timeout(function() {
          vm.refresh(true, true, false, false);
        }, 800);
        $copyPortionModal.modal("hide");
        toaster.pop("success", "", I18n.t("admin.arrangements.modal.add_portion_modal.update_success"));
      } else {
        if (Array.isArray(data.errors) && !!data.errors[0]) {
          $copyPortionModal.modal("hide");
          toaster.pop("error", "", data.errors[0]);
        } else if (_.isEmpty(JSON.parse(data.errors))) {
          $copyPortionModal.modal("hide");
          toaster.pop("error", "", I18n.t("admin.arrangements.modal.add_portion_modal.update_fail"));
        } else {
          $.lawsonAjax(data);
        }
      }
      $(".btn-add-portion").prop("disabled", false);
    });
  }

  function removeErrors() {
    $(".has-error").removeClass("has-error");
    $(".help-block").remove();
    $(".radio-billing-2").prop("checked", true);
  }

  vm.clearSearchCondition = function() {
    // Reset params
    vm.params = angular.copy({
      per_page: vm.perPageSettings[0]
    });

    // Reset all status checkboxes
    _.concat(vm.orderCaseSegmentIds, vm.arrangeStatusIds, vm.workingTimeStatusIds, vm.invoiceTargets,
      vm.paymentAdjusmentTypeIds, vm.billingAdjusmentTypeIds).forEach(function(checkboxStatus) {
        checkboxStatus.selected = false;
      });

    // Reset Select2 state
    setTimeout(function() {
      angular.element("select.select2, select.select2-multiple, select.select2-custom, select.select-location").trigger("change");
    }, 10);
  };

  vm.submitWorkAchievement = function(value, names, arrangementId) {
    var updateParams = {};
    updateParams["work_achievement"] = {};
    var nameArr = names.split(",");
    var valueTime = _.toString(value).split(" ");
    if (nameArr[0] == "working_ended_at" || nameArr[0] == "working_started_at") {
      var valueArr = valueTime;
    } else {
      var valueArr = _.toString(valueTime[1]).split("~");
    }
    if (_.isUndefined(valueArr[1])){
      valueArr[1] = " ";
    }
    _.map(nameArr, function(name, index) {
      var valueAttr = valueArr[index];
      if (_.includes(TIME_TO_MINUTES_ATTRS, name)) {
        var formatedTime = valueAttr.split(":");
        valueAttr = _.toInteger(formatedTime[0]) * 60 + _.toInteger(formatedTime[1]);
      };
      return updateParams["work_achievement"][name] = valueAttr;
    });
    vm.currentWorkAchievementParams = updateParams;
    vm.currentArrangementId = arrangementId;
    vm.updateWorkAchievement(true);
  }

  vm.updateWorkAchievement = function(checkBreakTime) {
    vm.currentWorkAchievementParams.check_break_time = checkBreakTime;
    arrangementService.updateWorkingAchievementBasicField(vm.currentWorkAchievementParams, vm.currentArrangementId).then(function(res){
      if (res.data.status) {
        if (res.data.break_time_warning) {
          $("#modal-confirm-break-time-warning").modal("show");
        } else {
          displaySuccessMessage();
        }
        if (res.data.arrangement) {
          var arrangement = _.find(vm.arrangements, function(arrangement){return arrangement.id == res.data.arrangement.id})
          arrangement = _.merge(arrangement, res.data.arrangement);
        }
      } else {
        vm.currentErrorArrangementId = vm.currentArrangementId;
        displayErrorMessage(res.data.locked_msg);
      }
    })
  }

  vm.showBreakTimeWarningErrors = function() {
    vm.currentErrorArrangementId = vm.currentArrangementId;
    $("#modal-confirm-break-time-warning").modal("hide");
    displayErrorMessage(I18n.t("admin.arrangements.break_time_warning_modal.not_save"));
  }

  vm.loadMore = function(tabName) {
    if (vm.params.per_page == LIMIT_PER_PAGE && vm[tabName] < LIMIT_PER_PAGE) {
      $timeout(function() {
        vm[tabName] += ITEM_LIMIT_NUMBER;
        if (vm[tabName] > LIMIT_PER_PAGE) {
          vm[tabName] = LIMIT_PER_PAGE;
        }
      }, 100);
      vm.scrollToResult();
    }
    if (vm.params.per_page < LIMIT_PER_PAGE || vm[tabName] == LIMIT_PER_PAGE) {
      vm[tabName] = vm.params.per_page;
    }
  }

  vm.limitNumber = function() {
    if (vm.params.per_page < LIMIT_PER_PAGE) {
      return vm.params.per_page;
    }
    if (vm.params.per_page == LIMIT_PER_PAGE) {
      return vm.basicFrozenLimitNumber;
    }
  }

  vm.isShowBillingField = function(arrangement) {
    return _.includes(ARRANGED_BILLING_STATUSES, arrangement.order_case_status);
  }

  vm.isShowWorkingTime = function(arrangement) {
    return !_.includes(CANCEL_AFTER_ARRANGE_STTS, arrangement.order_case_status)
  }

  vm.isNotArrangeInsurance = function(arrangement) {
    return arrangement.order_case_status != CANCEL_AFTER_ARRANGE_STTS[1];
  }

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  }

  vm.showEditTemplate = function(arrangement) {
    vm.currentArrangement = arrangement;
    vm.selectedTemplateId = arrangement.billing_payment_template_id;
    vm.templates = null;
    vm.loadLocationTemplates();
  }

  vm.closeEditTemplate = function(){
    vm.currentArrangement = {};
    vm.selectedTemplateId = null;
    vm.templates = null;
    $("#update-template-modal").modal("hide");
  }

  vm.changeTemplate = function() {
    if (_.isUndefined(vm.templates) || !vm.templates) { return; }
    var selectedTemplateId = vm.selectedTemplateId || vm.currentArrangement.billing_payment_template_id;
    if (_.isUndefined(selectedTemplateId) || !selectedTemplateId) {
      selectedTemplate = vm.defaultTemplate();
    } else {
      var selectedTemplate = _.find(vm.templates, function(template) {
        return template.id == selectedTemplateId;
      });
    }
    vm.selectedTemplate = selectedTemplate;
  }

  vm.defaultTemplate = function() {
    var arrangement = vm.currentArrangement;
    return {
      billing_basic_unit_price: arrangement.billing_basic_unit_price || 0,
      billing_night_unit_price: arrangement.billing_night_unit_price || 0,
      area_allowance: arrangement.billing_field_1 || 0,
      short_allowance: arrangement.billing_field_2 == 0 ? 0 : null,
      absence_discount: arrangement.billing_field_4 || 0,
      tax_exemption: arrangement.billing_tax_exemption || 0,
      payment_basic_unit_price: arrangement.payment_basic_unit_price || 0,
      payment_night_unit_price: arrangement.payment_night_unit_price || 0,
      transportation_fee: arrangement.payment_field_1 || 0,
    };
  }

  vm.loadLocationTemplates = function() {
    arrangementService.getBillingPaymentTemplates(vm.currentArrangement.order_location_id).then(function(res){
      vm.templates = res.data.templates;
      vm.changeTemplate();
      $("#update-template-modal").modal("show");
    });
  }

  vm.showBillingPaymentValue = function(field) {
    var normalTxt = I18n.t("admin.order.order_form.new_step3.normal_calculation");
    return !!vm.selectedTemplate[field] || vm.selectedTemplate[field] == 0 ? vm.selectedTemplate[field] : normalTxt;
  }

  vm.updateBillingPaymentTemplateId = function() {
    if (!vm.currentArrangement.id || !vm.selectedTemplate.id) {return;}
    var params = {"billing_payment_template_id": vm.selectedTemplate.id};
    arrangementService.updateBillingPaymentTemplateId(vm.currentArrangement.id, params).then(function(res){
      if (res.data.status) {
        vm.closeEditTemplate()
        vm.refresh(false);
        displaySuccessMessage();
      } else {
        vm.closeEditTemplate()
        displayErrorMessage();
      }
    });
  }
}