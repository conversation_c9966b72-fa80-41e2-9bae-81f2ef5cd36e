"use strict";

angular.module("adminApp")
  .controller("DeviceLoginLogListController", DeviceLoginLogListController);
DeviceLoginLogListController.$inject = ["$location", "deviceLoginLogService", "deviceVerificationService", "toaster"];

function DeviceLoginLogListController($location, deviceLoginLogService, deviceVerificationService, toaster) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  vm.params = $location.search();
  vm.toasterTimeout = 6200;
  var SEARCH_CONDITION_TYPE = "admin_device_login_log_search_conditions";
  vm.statusParam = {id: "", status: ""};
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  vm.init = function() {
    var lastCondition = angular.element(".device-login-log-wrap").data("last-condition");
    vm.deviceID = angular.element(".device-login-log-wrap").data("device-verification");
    var existLastCondition = false;

    if (!_.isEmpty(lastCondition)) {
      vm.params = lastCondition;
      existLastCondition = true;
    }
    vm.params.per_page = _.toInteger(vm.params.per_page) || vm.perPageSettings[0];
    vm.currentParams = vm.params;
    vm.refresh();
  };

  vm.refresh = function(isSaveCondition) {
    vm.params = isSaveCondition ? vm.currentParams : vm.params;
    deviceLoginLogService.getDeviceLoginLogList(vm.deviceID, vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.device_login_logs.length);
      vm.noRecord = !vm.hasRecord;
      if (isSaveCondition) {
        var searchConditionParams = _.omit(vm.params, "page", "limit");
        deviceLoginLogService.createSearchCondition({search: searchConditionParams, search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
        });
      }
      $location.search(vm.params).replace();
    }, function (error) {
    });
  };

  vm.changePerPage = function() {
    vm.refresh(true);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".device-log-list-result").offset().top
    }, 1000);
  };

  vm.changeStatusDevice = function() {
    deviceVerificationService.updateStatus(vm.device_verification.id, {status_id: vm.statusParam.status}).then(function(res) {
      vm.device_verification = res.data.device_verification;
      if (res.data.status) {
        toaster.pop("success", "", res.data.message);
      } else {
        toaster.pop("error", "", res.data.message);
      }
    });
  }
}
