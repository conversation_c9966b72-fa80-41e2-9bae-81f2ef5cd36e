"use strict";

angular.module("adminApp").controller("RolesController", RolesController);

RolesController.$inject = ["toaster", "rolesService", "$scope"];

function RolesController(toaster, rolesService, $scope) {
  var vm = this;
  var SINGLE_ROLE = {id: null, name: "", permissions: {}};
  vm.$scope = $scope;
  vm.toasterTimeout = 6200;
  vm.rolesChanged = false;
  vm.isSaving = false;

  vm.$scope.$watch("vm.roles", function(newRoles, oldRoles) {
    if (_.isNil(oldRoles) || vm.skipWatch) {
      vm.skipWatch = false;
      return;
    }
    vm.rolesChanged = vm.roles.length > 0;
  }, true);

  vm.refresh = function() {
    rolesService.loadRolesList().then(function(res) {
      angular.extend(vm, res.data);
      vm.deletedRoles = [];
      vm.allPermissons = vm.permissions.map(function(a){ return a.permission; });
      vm.roles.forEach(function(role) {
        initializePermission(role);
        togglePermission(role);
      });
    })
  }

  vm.addRole = function() {
    vm.roles.push(angular.copy(SINGLE_ROLE));
    initializePermission(vm.roles[vm.roles.length - 1]);
  }

  vm.deleteRole = function(index) {
    vm.deletedRoles.push(vm.roles[index].id);
    vm.roles.splice(index, 1);
  }

  vm.changeRole = function(role, modelName, permission) {
    var currentPermission = role.permissions[modelName];
    if(_.isUndefined(currentPermission)) role.permissions[modelName] = [permission];
    else {
      if(_.includes(vm.allPermissons, currentPermission[0]))
        currentPermission[0] = permission;
      else
        currentPermission.unshift(permission);
    }
  }

  vm.save = function() {
    vm.isSaving = true;
    var params = {roles: updateRolesParams(), deleted_roles: vm.deletedRoles};
    rolesService.updateRoles(params).then(function(res) {
      var response = res.data;
      vm.isSaving = false;
      vm.rolesChanged = false;
      vm.skipWatch = true;
      if (res.data.status) vm.refresh();
      showClientError(response);
      showToasterMsg(response, response.status);
    });
  }

  function initializePermission(role) {
    vm.left_menu_subjects.forEach(function(subject) {
      role[subject.model_name] = {permission: "manage"};
      if (_.isNull(role.id)) {
        role["permissions"][subject.model_name] = ["manage"];
      };
    });
  }

  function togglePermission(role) {
    if(!_.isUndefined(role.permissions["all"])) toggleForAllPermission(role);
    else {
      _.forEach(role.permissions, function(permissions, modelName) {
        if(_.includes(vm.allPermissons, permissions[0]) && !_.isUndefined(role[modelName]))
          role[modelName].permission = permissions[0];
      })
    }
  }

  function toggleForAllPermission(role) {
    vm.left_menu_subjects.forEach(function(subject) {
      role[subject.model_name].permission = "manage";
      role.disabled = true;
    })
  }

  function updateRolesParams() {
    return vm.roles.map(function(role) {
      { return _.pick(role, ["id", "name", "permissions", "except_permissions"]) }
    })
  }

  function showClientError(response) {
    if(_.isEmpty(response.errors)) return;
    response.errors.forEach(function(error, index) {
      var error = JSON.parse(error);
      if(!_.isEmpty(error)) vm.roles[index].error = error.name[0];
      else vm.roles[index].error = "";
    });
  }

  function showToasterMsg(response, status) {
    if(_.isEmpty(response.toaster_msg)) return;
    var toaster_type = status ? "success" : "error";
    toaster.pop(toaster_type, "", response.toaster_msg);
  }
}
