"use strict";

angular.module("adminApp").controller("PayrollListController", PayrollListController);
PayrollListController.$inject = ["payrollService", "$location", "toaster", "$scope"];

function PayrollListController(payrollService, $location, toaster, $scope) {
  var vm = this;
  var MIN_MONTH_DAY = 15;
  var DECEMBER = 12, JANUARY = 1;
  var $sendingModal = angular.element("#payroll-calculate-sending-modal");
  var $confirmModal = angular.element("#payroll-confirm-modal");
  var $msgModal = angular.element("#payroll-message-modal");
  var $progressElm = angular.element("#export-csv-status div[role=progressbar]");
  var $statusElm = angular.element("#export-csv-status");
  var $calculatingMsg = angular.element("#calculating-message");
  var MAX_FAILED_NUMBER = 1;
  var SEARCH_CONDITION_TYPE = "admin_payroll_search_conditions";
  var INIT_VALUE_FIELDS = ["moved_day_off_count", "deleted_day_off_count", "granted_day_count",
    "allowed_day_off_count", "used_day_off_count", "remaining_holiday_count", "basic_salary_price",
    "ot45_amount", "ot60_amount", "working_day_count", "attendance_day_count", "attendance_time_count",
    "absence_day_count", "late_early_day_count", "late_day_count", "leave_early_day_count"];
  var PAYMENT_TOTAL_AMOUNT_FIELDS = ["basic_salary_price", "overtime_amount", "working_night_amount", "paid_amount",
    "payment_field_1", "payment_field_2", "payment_field_3", "payment_field_4", "payment_field_5", "payment_field_6",
    "payment_field_7", "payment_field_8", "payment_field_9", "payment_field_10", "payment_field_11", "payment_field_12",
    "payment_field_13"];
  var TAX_EXEMPTION_AMOUNT_FIELDS = ["payment_field_1", "payment_field_10", "payment_field_11", "payment_field_12"];
  var DEDUCTION_TOTAL_AMOUNT_FIELDS = ["deduction_field_1", "deduction_field_2", "deduction_field_3", "deduction_field_4", "deduction_field_5",
    "deduction_field_6", "deduction_field_7", "deduction_field_8", "deduction_field_9", "deduction_field_10",
    "deduction_field_11", "deduction_field_12", "deduction_field_13", "deduction_field_21"];
  var ALLOWENCE_FIELDS = TAX_EXEMPTION_AMOUNT_FIELDS.concat(["payment_field_2", "payment_field_3",
    "payment_field_4", "payment_field_5", "payment_field_6", "payment_field_7", "payment_field_8",
    "payment_field_9", "payment_field_13"]);
  var ATTENDANCE_TIME_ATTRS = ["actual_working_time", "basic_time", "ot1_time", "ot2_time", "night_time",
    "late_time", "leave_early_time", "ot45_time", "ot60_time"]
  var PAYROLL_MAIN_INFO = ["payroll_date", "staff_id", "wage_payment_date",
    "wage_closing_date", "duration"];
  vm.isCalculating = angular.element("#is-calculating").data("infos");
  vm.$scope = $scope;
  vm.params = $location.search();
  vm.toasterTimeout = 6200;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.dateRankAndLevel = "";
  vm.dayRangeFormat = "";

  vm.serverTime = moment(angular.element("#server-time-now").data("time")).format("YYYY/MM/DD");
  vm.defaultTimeValue = "0:00";

  var modalTypeFuncMapping = {
    1: performCalculate,
    2: cancelCalculate,
    3: exportStaffInfo,
    4: exportPayroll,
    5: exportFBPayroll,
    6: sendNotification,
    7: cancelNotification
  };

  var PERFORM_CALCULATE_MODAL = 1, CANCEL_CALCULATE = 2, EXPORT_STAFF_INFO = 3, EXPORT_PAYROLL = 4, EXPORT_FB_PAYROLL = 5;
  var SEND_NOTIFICATION = 6, CANCEL_NOTIFICATION = 7, PERFORM_CALCULATE_RANK_AND_LEVEL = 8;

  moment.locale(I18n.locale);

  var fetchData = function(params) {
    payrollService.getStafsfWithPayroll(params).then(function(res) {
      angular.extend(vm, res.data);
      $location.search(params).replace();
      vm.staffs = formatData(vm.staffs);
      vm.isCheckAll = false;
    });
  };

  var setCurrentDate = function() {
    var currentDate = moment(new Date());
    var year = currentDate.format("YYYY");
    var month = currentDate.format("M");
    var day = currentDate.format("D");
    vm.salary_payment_month = year + "/" + month;
    if (day <= MIN_MONTH_DAY){
      year = (month == JANUARY) ? year - 1 : year;
      month = (month == JANUARY) ? DECEMBER : month - 1;
    };
    vm.payroll_date = year + "/" + month;
    vm.current_payroll_date = vm.payroll_date;

    $("#payroll-date").datepicker("setDate", vm.salary_payment_month).trigger("change");
  };

  var setCurrentMonth = function() {
    var currentDate = moment(new Date());
    var year = currentDate.format("YYYY");
    var month = currentDate.format("M");
    var day = currentDate.format("D");
    if (day > MIN_MONTH_DAY) {
      year = (month == DECEMBER) ? parseInt(year) + 1 : parseInt(year);
      month = (month == DECEMBER) ? JANUARY : parseInt(month) + 1;
    };
    vm.monthForCalculate = String(year) + "/" + String(month);
    vm.changeRangeCalculate();
    if (day <= MIN_MONTH_DAY) {
      var calculate_year = (month == JANUARY) ? year - 1 : year;
      var calculate_month = (month == JANUARY) ? DECEMBER : month - 1;
    }
  }

  var setLastConditionForCurrentDate = function(payroll_date){
    var payroll_year = moment(payroll_date).format("YYYY");
    var payroll_month = moment(payroll_date).format("M");
    var salary_year = (payroll_month == DECEMBER) ? parseInt(payroll_year) + 1 : payroll_year;
    var salary_month = (payroll_month == DECEMBER) ? JANUARY : parseInt(payroll_month) + 1;
    var salary_date = salary_year + "/" + salary_month;
    vm.salary_payment_month = moment(new Date(salary_date)).format("YYYY/M");
    vm.current_payroll_date = vm.payroll_date;
    $("#payroll-date").datepicker("setDate", vm.salary_payment_month).trigger("change");
  };

  vm.downloadSalaryDetail = function(id) {
    var actionURL = "/payrolls/"+ id +"/generate_salary_detail.pdf";
    window.open(actionURL, '_blank');
  }

  vm.changeDuration = function() {
    if (vm.salary_payment_month == "") {
      vm.durationFormat = "";
      return;
    }
    var salary_year = moment(vm.salary_payment_month).format("YYYY");
    var salary_month = moment(vm.salary_payment_month).format("MM");
    var payroll_year = (salary_month == JANUARY) ?  salary_year - 1 : salary_year;
    var payroll_month = (salary_month == JANUARY) ? DECEMBER : salary_month - 1;
    vm.payroll_date = payroll_year + "/" + payroll_month;
    var month = moment(vm.payroll_date).month() + 1;
    var year = moment(vm.payroll_date).year();
    var prevMonth = month == JANUARY ? DECEMBER : month - 1;
    vm.durationFormat = moment(prevMonth + "-" + (MIN_MONTH_DAY + 1)).format(DAY_MONTH_FORMAT) + "～" +
      moment(month + "-" + MIN_MONTH_DAY).format(DAY_MONTH_FORMAT);
    vm.payrollDateFormat = moment(vm.salary_payment_month).format(FULL_YEAR_W_MONTH_FORMAT);
  };

  vm.blurPayrollDate = function() {
    if (!moment(vm.salary_payment_month).isValid()) {
      setCurrentDate();
      vm.changeDuration();
    }
  }

  vm.changeRangeCalculate = function() {
    if (vm.monthForCalculate == "") {
      vm.dayRangeFormat = "";
      return;
    }
    var salary_year = moment(vm.monthForCalculate).format("YYYY");
    var salary_month = moment(vm.monthForCalculate).format("MM");
    var payroll_year = (salary_month == JANUARY) ?  salary_year - 1 : salary_year;
    var payroll_month = (salary_month == JANUARY) ? DECEMBER : salary_month - 1;
    vm.dateRankAndLevel = payroll_year + "/" + payroll_month;
    var month = moment(vm.dateRankAndLevel).month() + 1;
    var year = moment(vm.dateRankAndLevel).year();
    var prevMonth = month == JANUARY ? DECEMBER : month - 1;
    vm.dayRangeFormat = moment(prevMonth + "-" + (MIN_MONTH_DAY + 1)).format(DAY_MONTH_FORMAT) + "～" +
      moment(month + "-" + MIN_MONTH_DAY).format(DAY_MONTH_FORMAT);
    vm.range_date = payroll_year + "/" + payroll_month + "/01";
  }

  vm.search = function(isSave) {
    var payrollDate = moment(vm.payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    vm.params.payroll_date = payrollDate;
    var searchParams = formatSearchParams(angular.copy(vm.params));
    if (isSave){
      payrollService.createSearchCondition({search: _.omit(searchParams, ["page", "per_page"]), search_condition: SEARCH_CONDITION_TYPE});
    }
    vm.current_payroll_date = vm.payroll_date;
    vm.currentParams = angular.copy(searchParams);
    fetchData(searchParams);
  };

  function formatSearchParams(params) {
    params.current_department_id = params.current_department_id.join(",");
    return params;
  }

  vm.init = function() {
    payrollService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res){
      vm.params = res.data.status ? JSON.parse(res.data.last_conditions) : {};
      _.isEmpty(vm.params.payroll_date) ? setCurrentDate() : setLastConditionForCurrentDate(vm.params.payroll_date);
      setCurrentMonth();
      vm.params.current_department_id = vm.params.current_department_id ? vm.params.current_department_id : "";
      _.merge(vm.params, {page: 1, per_page: 10, payroll_date: vm.payroll_date, current_department_id: vm.params.current_department_id.split(",")});
      angular.element(document).ready(function() {
        $("#current_department_id").trigger("change");
      });
      vm.changeDuration();
      vm.search();
    });
  };

  vm.changePerPage = function() {
    vm.search();
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".payroll-search-result").offset().top
    }, 1000);
  };

  vm.checkOrUnCheckAll = function() {
    vm.staffs.forEach(function(staff) {
      staff.is_check = !!vm.isCheckAll;
    });
  };

  vm.uncheckAllIfNeed = function(isCheck) {
    if (!isCheck) vm.isCheckAll = false;
  };

  vm.openPerformCalculateModal = function() {
    $(".disable-submit-btn").prop("disabled", true);
    var selectedStaffs = vm.staffs.filter(function(staff) {
      return staff.is_check;
    });

    var staffIds = selectedStaffs.map(function(staff) {
      return staff.id;
    });

    var payroll_date = moment(vm.current_payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = {staff_ids: staffIds, is_check_all_records: vm.checkAllRecords, payroll_date: payroll_date};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());

    payrollService.checkIsCalculating(params).then(function(res) {
      if (res.data.status) {
        vm.msgContent = res.data.message
        $msgModal.modal("show");
      } else {
        var selectedStaffWPayrolls = selectedStaffs.filter(function(staff) {
          return !!staff.payroll_id;
        });

        if (selectedStaffWPayrolls.length > 0) {
          vm.msgContent = I18n.t("payroll.list.modal.message.calculated");
          $msgModal.modal("show");
        } else {
          vm.modalType = PERFORM_CALCULATE_MODAL;
          vm.wage_payment_date = "";
          $("#payroll-wage-payment-date-picker").datepicker("setDate", null);
          $confirmModal.modal("show");
        }
      }
      $(".disable-submit-btn").prop("disabled", false);
      if (vm.modalType == 1) { $("#btn-submit-perform-calculate").prop("disabled", true); }
    });
  };

  vm.openPerformCalculateRankAndLevel = function() {
    payrollService.checkIsCalculatingRankLevel().then(function(res) {
      if (res.data.status) {
        vm.msgContent = res.data.message
        $msgModal.modal("show");
      } else {
        $("#select-month-calculate").modal("show");
      }
    });
  };

  vm.openCancelCalculateModal = function() {
    var selectedStaffs = vm.staffs.filter(function(staff) {
      return staff.is_check;
    });

    var selectedStaffWPayrolls = selectedStaffs.filter(function(staff) {
      return !!staff.payroll_id;
    });

    var idxExportedPayroll = _.findIndex(selectedStaffWPayrolls, function(staff) {
      return staff.notification_status == 1;
    });

    var staffIds = _.map(selectedStaffs, function(staff){
      return staff.id;
    });

    var payroll_date = moment(vm.current_payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    if (vm.checkAllRecords) {
      var params = _.merge(currentParamsSearch(), {is_check_all_records: true});
      payrollService.checkIsAbleToCancelPayroll(params).then(function(res) {
        if (res.data.status) {
          vm.modalType = CANCEL_CALCULATE;
          $confirmModal.modal("show");
        } else {
          vm.msgContent = res.data.message;
          $msgModal.modal("show");
        }
      });
    } else {
      if (selectedStaffWPayrolls.length < selectedStaffs.length) {
        vm.msgContent = I18n.t("payroll.list.modal.message.not_exist_payroll");
        $msgModal.modal("show");
      } else if (idxExportedPayroll != -1) {
        vm.msgContent = I18n.t("payroll.list.modal.message.notified");
        $msgModal.modal("show");
      } else {
        payrollService.checkIsAbleToCancelPayroll({staff_ids: staffIds, payroll_date: payroll_date}).then(function(res) {
          if (res.data.status) {
            vm.modalType = CANCEL_CALCULATE;
            $confirmModal.modal("show");
          } else {
            vm.msgContent = res.data.message;
            $msgModal.modal("show");
          }
        });
      }
    }
  };

  vm.openSendNotificationModal = function() {
    $(".disable-submit-btn").prop("disabled", true);
    var selectedStaffIds = vm.staffs.filter(function(staff) {
      return staff.is_check;
    }).map(function(staff) {
      return staff.id;
    });

    var payroll_date = moment(vm.current_payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = {staff_ids: selectedStaffIds, payroll_date: payroll_date, is_check_all_records: vm.checkAllRecords};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());

    payrollService.checkBeforeSendNotification(params).then(function(res) {
      if (res.data.status) {
        vm.modalType = SEND_NOTIFICATION;
        $confirmModal.modal("show");
      } else {
        vm.msgContent = res.data.message;
        $msgModal.modal("show");
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  };

  vm.openCancelNotificationModal = function() {
    $(".disable-submit-btn").prop("disabled", true);
    var selectedStaffIds = vm.staffs.filter(function(staff) {
      return staff.is_check;
    }).map(function(staff) {
      return staff.id;
    });

    var payroll_date = moment(vm.current_payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = {staff_ids: selectedStaffIds, payroll_date: payroll_date,
      is_check_all_records: vm.checkAllRecords};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());

    payrollService.checkBeforeCancelNotification(params).then(function(res) {
      if (res.data.status) {
        vm.modalType = CANCEL_NOTIFICATION;
        $confirmModal.modal("show");
      } else {
        vm.msgContent = res.data.message;
        $msgModal.modal("show");
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  };

  vm.openExportStaffInfoModal = function() {
    vm.modalType = EXPORT_STAFF_INFO;
    $confirmModal.modal("show");
  };

  vm.openExportPayrollModal = function() {
    var selectedStaffs = vm.staffs.filter(function(staff) {
      return staff.is_check;
    });

    var selectedStaffWPayrolls = selectedStaffs.filter(function(staff) {
      return !!staff.payroll_id;
    });

    if (vm.checkAllRecords) {
      var params = _.merge(currentParamsSearch(), {is_check_all_records: true});
      payrollService.checkIsAbleToPerformForAllPayrolls(params).then(function(res) {
        if (res.data.status) {
          vm.modalType = EXPORT_PAYROLL;
          $confirmModal.modal("show");
        } else {
          vm.msgContent = I18n.t("payroll.list.modal.message.not_exist_payroll");
          $msgModal.modal("show");
        }
      });
    } else {
      if (selectedStaffWPayrolls.length < selectedStaffs.length) {
        vm.msgContent = I18n.t("payroll.list.modal.message.not_calculated");
        $msgModal.modal("show");
      } else {
        vm.modalType = EXPORT_PAYROLL;
        $confirmModal.modal("show");
      }
    }
  }

  vm.openActionExportPayroll = function() {
    $(".disable-submit-btn").prop("disabled", true);
    var staffIds = vm.staffs.filter(function(staff) {
      return staff.is_check;
    }).map(function(staff) {
      return staff.id;
    });

    var params = {staff_ids: staffIds, payroll_date: vm.current_payroll_date, is_check_all_records: vm.checkAllRecords};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());

    payrollService.conditionExportFBPayroll(params).then(function(res) {
      vm.wagePaymentDate = res.data.wagePaymentDate;

      if (!res.data.status) {
        vm.msgContent = res.data.message;
        $msgModal.modal("show");
      } else {
        vm.modalType = EXPORT_FB_PAYROLL;
        $confirmModal.modal("show");
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  }

  function exportFBPayroll() {
    $(".disable-submit-btn").prop("disabled", true);
    var staffIds = vm.staffs.filter(function(staff) {
      return staff.is_check;
    }).map(function(staff) {
      return staff.id;
    });

    vm.isSubmiting = true;
    var params = {staff_ids: staffIds, transfer_date: vm.wagePaymentDate,
      payroll_date: vm.current_payroll_date, is_check_all_records: vm.checkAllRecords};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());

    payrollService.exportFBPayroll(params).then(function(res) {
      vm.isSubmiting = false;

      if (res.data.force_reload) {
        location.reload();
      }

      if (!res.data.status) {
        $confirmModal.modal("hide");
        vm.msgContent = res.data.message;
        $msgModal.modal("show");
        $(".disable-submit-btn").prop("disabled", false);
      } else {
        var jobId = res.data.jid;
        var intervalName = "job_" + jobId;
        $statusElm.toggleClass("hidden");
        $confirmModal.modal("hide");
        $(".disable-submit-btn").prop("disabled", false);
        window[intervalName] = setInterval(function() {
          getExportJobStatus(jobId, intervalName, false, true);
        }, 5000);
      }
    }, function(err) {
      showExportCSVErr();
    });
  }

  vm.submit = function() {
    modalTypeFuncMapping[vm.modalType]();
  };

  vm.disableBtn = function() {
    if (vm.checkAllRecords) return false;
    var idx = _.findIndex(vm.staffs, function(staff) {
      return staff.is_check;
    });
    return idx == -1;
  };

  vm.reset = function() {
    vm.isSubmiting = false;
  };

  vm.haveSalaryDetail = function(staffId) {
    return _.includes(vm.have_salary_detail_ids, staffId);
  }

  function formatData(staffs) {
    staffs.forEach(function(staff) {
      staff.ot_time = (staff.ot1_time || 0) + (staff.ot2_time || 0);
      staff.exist_payroll = _.isNil(staff.updated_at) ? I18n.t("common.exist_lbl.not_yet") : I18n.t("common.exist_lbl.already");
    });
    return staffs;
  }

  function performCalculate() {
    $(".disable-submit-btn").prop("disabled", true);
    var selectedStaffIds = vm.staffs.filter(function(staff) {
      return staff.is_check;
    }).map(function(staff) {
      return staff.id;
    });
    var payroll_date = moment(vm.current_payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = {staff_ids: selectedStaffIds, wage_payment_date: vm.wage_payment_date,
      payroll_date: payroll_date, is_check_all_records: vm.checkAllRecords};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());

    payrollService.performCalculate(params).then(function(res) {
      vm.isSubmiting = false;

      if (res.data.force_reload) {
        location.reload();
      }

      var jobId = res.data.jid;
      var intervalName = "job_" + jobId;
      $msgModal.modal("hide");
      $confirmModal.modal("hide");
      $calculatingMsg.show();

      vm.search();
      $(".disable-submit-btn").prop("disabled", false);
    });
  }

  vm.performCalculateRankAndLevel = function() {
    vm.isCalculatingRankAndLevel = true;
    var range_date = moment(vm.range_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = {range_date: range_date}
    payrollService.performCalculateRankAndLevel(params).then(function(res) {
      var jobId = res.data.jid;
      var intervalName = "job_" + jobId;
      $msgModal.modal("hide");
      $("#select-month-calculate").modal("hide");
      $calculatingMsg.show();

      vm.search();
    });
  }

  function cancelCalculate() {
    $(".disable-submit-btn").prop("disabled", true);
    vm.isSubmiting = true;
    var payrollIds = vm.staffs.filter(function(staff) {
      return staff.is_check && !!staff.payroll_id && staff.notification_status == 0;
    }).map(function(staff) {
      return staff.original_payroll_id || staff.payroll_id;
    });
    var payrollDate = moment(vm.current_payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = {payroll_ids: payrollIds, is_check_all_records: vm.checkAllRecords,
      payroll_date: payrollDate};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());

    payrollService.batchCancelCalculate(params).then(function(res) {
      vm.isSubmiting = false;

      if (res.data.force_reload) {
        location.reload();
      } else if (res.data.message) {
        vm.msgContent = res.data.message;
        $msgModal.modal("show");
      } else {
        $msgModal.modal("hide");
        $confirmModal.modal("hide");
        vm.search();
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  }

  function exportStaffInfo() {
    $(".disable-submit-btn").prop("disabled", true);
    var staffIds = vm.staffs.filter(function(staff) {
      return staff.is_check;
    }).map(function(staff) {
      return staff.id;
    });
    vm.isSubmiting = true;
    var payrollDate = moment(vm.current_payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = {staff_ids: staffIds, is_check_all_records: vm.checkAllRecords,
      payroll_date: payrollDate};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());
    payrollService.exportStaffInfo(params).then(function(res) {
      vm.isSubmiting = false;

      if (res.data.force_reload) {
        location.reload();
      }

      var jobId = res.data.jid;
      var intervalName = "job_" + jobId;
      $statusElm.toggleClass("hidden");
      $confirmModal.modal("hide");
      $(".disable-submit-btn").prop("disabled", false);
      window[intervalName] = setInterval(function() {
        getExportJobStatus(jobId, intervalName, false);
      }, 5000);
    }, function(err) {
      showExportCSVErr();
    });
  }

  function exportPayroll() {
    $(".disable-submit-btn").prop("disabled", true);
    var payrollIds = vm.staffs.filter(function(staff) {
      return staff.is_check && !!staff.payroll_id;
    }).map(function(staff) {
      return staff.payroll_id;
    });

    vm.isSubmiting = true;
    var payrollDate = moment(vm.current_payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = {payroll_ids: payrollIds, is_check_all_records: vm.checkAllRecords,
      payroll_date: payrollDate};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());

    payrollService.exportPayroll(params).then(function(res) {
      vm.isSubmiting = false;

      if (res.data.force_reload) {
        location.reload();
      }

      var jobId = res.data.jid;
      var intervalName = "job_" + jobId;
      $statusElm.toggleClass("hidden");
      $confirmModal.modal("hide");
      $(".disable-submit-btn").prop("disabled", false);
      window[intervalName] = setInterval(function() {
        getExportJobStatus(jobId, intervalName, true);
      }, 5000);
    }, function(err) {
      showExportCSVErr();
    });
  }

  function sendNotification() {
    $(".disable-submit-btn").prop("disabled", true);
    var selectedStaffIds = vm.staffs.filter(function(staff) {
      return staff.is_check;
    }).map(function(staff) {
      return staff.id;
    });

    var payroll_date = moment(vm.current_payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = {staff_ids: selectedStaffIds, payroll_date: payroll_date,
      is_check_all_records: vm.checkAllRecords};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());

    payrollService.sendNotification(params).then(function(res) {
      if (res.data.status) {
        vm.isSubmiting = true;

        if (res.data.force_reload) {
          location.reload();
        }

        var jobId = res.data.jid;
        var intervalName = "job_" + jobId;
        $msgModal.modal("hide");
        $confirmModal.modal("hide");
        $sendingModal.modal("show");
        $(".disable-submit-btn").prop("disabled", false);
        window[intervalName] = setInterval(function() {
          getSendNotificationJobStatus(jobId, intervalName);
        }, 5000);
      } else {
        vm.msgContent = res.data.message
        $msgModal.modal("show");
        $confirmModal.modal("hide");
      }
    });
  }

  function cancelNotification() {
    $(".disable-submit-btn").prop("disabled", true);
    var selectedStaffIds = vm.staffs.filter(function(staff) {
      return staff.is_check;
    }).map(function(staff) {
      return staff.id;
    });

    var payroll_date = moment(vm.current_payroll_date).format(FULL_DATE_FORMAT_HYPHEN);
    var params = {staff_ids: selectedStaffIds, payroll_date: payroll_date,
      is_check_all_records: vm.checkAllRecords};
    if (vm.checkAllRecords) _.merge(params, currentParamsSearch());

    payrollService.cancelNotification(params).then(function(res) {
      if (res.data.status) {
        vm.isSubmiting = true;

        if (res.data.force_reload) {
          location.reload();
        }

        var jobId = res.data.jid;
        var intervalName = "job_" + jobId;
        $msgModal.modal("hide");
        $confirmModal.modal("hide");
        $sendingModal.modal("show");
        $(".disable-submit-btn").prop("disabled", false);
        window[intervalName] = setInterval(function() {
          getCancelNotificationJobStatus(jobId, intervalName);
        }, 5000);
      } else {
        vm.msgContent = res.data.message
        $msgModal.modal("show");
        $confirmModal.modal("hide");
      }
    });
  }

  function getExportJobStatus(jobId, intervalName, isExportPayroll, isExportFBPayroll) {
    payrollService.getJobStatus({job_id: jobId}).then(function(res) {
      if (res.data.status === "working") return;
      var percentage = res.data.percentage;
      setProgressPercentage($progressElm, percentage);

      if (res.data.status === "complete") {
        setProgressPercentage($progressElm, "100");
        setTimeout(function() {
          deleteIntervalJob(intervalName);
          $statusElm.toggleClass("hidden");
          setProgressPercentage($progressElm, "0");

          if (isExportPayroll) {
            $(location).attr("href", "/payrolls/export_download.csv?model_name=payroll&id=" + jobId);
          } else if (isExportFBPayroll) {
            $(location).attr("href", "/payrolls/export_download.csv?model_name=payroll_request&id=" + jobId);
          } else {
            $(location).attr("href", "/payrolls/export_download.csv?model_name=staff_info&id=" + jobId);
          }
        }, 500);
      } else if ((res.data.status === "retrying" || res.data.status === "queued" || res.data.status === "failed") && res.data.percentage == null) {
        vm.failedRequestNumber += 1;
         if (vm.failedRequestNumber >= MAX_FAILED_NUMBER) {
          deleteIntervalJob(intervalName);
          showExportCSVErr();
        }
      }
    }, function(err) {
      deleteIntervalJob(intervalName);
      $statusElm.toggleClass("hidden");
      showExportCSVErr();
    });
  }

  function getSendNotificationJobStatus(jobId, intervalName) {
    payrollService.getJobStatus({job_id: jobId}).then(function(res) {
      if (res.data.status === "working") return;
      if (res.data.status === "complete") {
        setTimeout(function() {
          deleteIntervalJob(intervalName);
          $sendingModal.modal("hide");
          vm.msgContent = I18n.t("payroll.list.modal.message.notification_success");
          $msgModal.modal("show");
          vm.search();
        }, 500);
      } else if ((res.data.status === "retrying" || res.data.status === "queued" || res.data.status === "failed") && res.data.percentage == null) {
        vm.failedRequestNumber += 1;
         if (vm.failedRequestNumber >= MAX_FAILED_NUMBER) {
          deleteIntervalJob(intervalName);
          $sendingModal.modal("hide");
          vm.msgContent = I18n.t("payroll.list.modal.message.notification_fail");
          $msgModal.modal("show");
        }
      }
      vm.isSubmiting = false;
    }, function(err) {
      deleteIntervalJob(intervalName);
      $sendingModal.modal("hide");
      vm.msgContent = I18n.t("payroll.list.modal.message.notification_fail");
      $msgModal.modal("show");
      vm.isSubmiting = false;
    });
  }

  function getCancelNotificationJobStatus(jobId, intervalName) {
    payrollService.getJobStatus({job_id: jobId}).then(function(res) {
      if (res.data.status === "working") return;
      if (res.data.status === "complete") {
        setTimeout(function() {
          deleteIntervalJob(intervalName);
          $sendingModal.modal("hide");
          vm.msgContent = I18n.t("payroll.list.modal.message.cancel_notification_success");
          $msgModal.modal("show");
          vm.search();
        }, 500);
      } else if ((res.data.status === "retrying" || res.data.status === "queued" || res.data.status === "failed") && res.data.percentage == null) {
        vm.failedRequestNumber += 1;
         if (vm.failedRequestNumber >= MAX_FAILED_NUMBER) {
          deleteIntervalJob(intervalName);
          $sendingModal.modal("hide");
          vm.msgContent = I18n.t("payroll.list.modal.message.cancel_notification_fail");
          $msgModal.modal("show");
        }
      }
      vm.isSubmiting = false;
    }, function(err) {
      deleteIntervalJob(intervalName);
      $sendingModal.modal("hide");
      vm.msgContent = I18n.t("payroll.list.modal.message.cancel_notification_fail");
      $msgModal.modal("show");
      vm.isSubmiting = false;
    });
  }

  function getCalculateJobStatus(jobId, intervalName) {
    payrollService.getJobStatus({job_id: jobId}).then(function(res) {
      if (res.data.status === "working") return;
      if (res.data.status === "complete") {
        setTimeout(function() {
          deleteIntervalJob(intervalName);
          $sendingModal.modal("hide");
          vm.msgContent = I18n.t("payroll.list.modal.message.calculate_success");
          $msgModal.modal("show");
          vm.search();
        }, 500);
      } else if ((res.data.status === "retrying" || res.data.status === "queued" || res.data.status === "failed") && res.data.percentage == null) {
        vm.failedRequestNumber += 1;
         if (vm.failedRequestNumber >= MAX_FAILED_NUMBER) {
          deleteIntervalJob(intervalName);
          $sendingModal.modal("hide");
          vm.msgContent = I18n.t("payroll.list.modal.message.calculate_fail");
          $msgModal.modal("show");
        }
      }
    }, function(err) {
      deleteIntervalJob(intervalName);
      $sendingModal.modal("hide");
      vm.msgContent = I18n.t("payroll.list.modal.message.calculate_fail");
      $msgModal.modal("show");
    });
  }

  function deleteIntervalJob(intervalName) {
    clearInterval(window[intervalName]);
    delete window[intervalName];
    vm.failedRequestNumber = 0;
  }

  function setProgressPercentage($progressElm, percentage) {
    $progressElm.attr("aria-valuenow", percentage)
      .css("width", percentage + "%")
      .text(percentage + "%");
  }

  function showExportCSVErr() {
    $statusElm.toggleClass("hidden");
    toaster.pop("error", "", I18n.t("payroll.list.modal.message.export_failed"));
  }

  vm.$scope.$watch("vm.current_payroll_date", function() {
    if (vm.current_payroll_date) {
      vm.isDisablePaymentCalc = moment(vm.serverTime).diff(moment(vm.current_payroll_date + "/" + "16"), "days") < 0;
    }
  });

  function currentParamsSearch() {
    return _.pick(vm.currentParams, ["current_department_id", "staff_number", "payroll_date", "account_name_lower"]);
  }

  vm.openCreatePayrollModal = function() {
    vm.initPayroll();
    $("#create-payroll-modal").modal("show");
  }

  vm.initPayroll = function(payroll) {
    if (payroll) {
      vm.payrollInitData = angular.copy(payroll);
    }
    vm.payroll =  angular.copy(vm.payrollInitData);
    _.forEach(INIT_VALUE_FIELDS, function(field) {
      vm.payroll[field] = 0;
    });
    _.forEach(ATTENDANCE_TIME_ATTRS, function(field) {
      vm.payroll[field] = vm.defaultTimeValue;
    });
    _.forEach(PAYROLL_MAIN_INFO, function(field) {
      vm.payroll[field] = "";
    });
    vm.payroll.salary_payment_month = moment().format(DATE_MONTH_YEAR_FORMAT);
    setCurrentPayrollDate();
    vm.changeSalaryPaymentMonth();
    setTimeout(function() {
      $("#staff_id").trigger("change");
    }, 100);
    $("li.active").removeClass("active");
    $("ul.nav-tabs li:first").addClass("active");
    $(".help-block").remove();
    $(".has-error").removeClass("has-error");
    $(".error-tab").removeClass("error-tab");
  }

  vm.calculateAllowedDayOffCount = function() {
    vm.payroll.allowed_day_off_count = Number(vm.payroll.moved_day_off_count) - Number(vm.payroll.deleted_day_off_count) +
      Number(vm.payroll.granted_day_count);
    vm.calculateRemainingHolidayCount();
  };

  vm.calculateRemainingHolidayCount = function() {
    vm.payroll.remaining_holiday_count = Number(vm.payroll.allowed_day_off_count) - Number(vm.payroll.used_day_off_count);
  };

  vm.calculatePaymentTotalAmount = function() {
    vm.payroll.payment_total_amount = calculateTotal(PAYMENT_TOTAL_AMOUNT_FIELDS);
    vm.calculateSubtractionAmount();
  };

  vm.calculateTaxExemptionAmount = function() {
    vm.payroll.tax_exemption_amount = calculateTotal(TAX_EXEMPTION_AMOUNT_FIELDS);
    vm.calculatePaymentTotalAmount();
  };

  vm.calculateDeductionTotalAmount = function() {
    vm.payroll.deduction_total_amount = calculateTotal(DEDUCTION_TOTAL_AMOUNT_FIELDS);
    vm.calculateSubtractionAmount();
  };

  vm.calculateSubtractionAmount = function() {
    vm.payroll.payment_subtraction_amount = vm.payroll.payment_total_amount - vm.payroll.deduction_total_amount;
  }

  function calculateTotal(fields) {
    var total = 0;
    fields.forEach(function(field) {
      total += _.isNaN(Number(vm.payroll[field])) ? 0 : Number(vm.payroll[field]);
    });
    return total;
  }

  vm.changeSalaryPaymentMonth = function() {
    if (vm.payroll.salary_payment_month == "") {
      vm.payroll.duration = "";
      return;
    }
    var salary_year = moment(vm.payroll.salary_payment_month).format("YYYY");
    var salary_month = moment(vm.payroll.salary_payment_month).format("MM");
    var payroll_year = (salary_month == JANUARY) ?  salary_year - 1 : salary_year;
    var payroll_month = (salary_month == JANUARY) ? DECEMBER : salary_month - 1;
    vm.payroll.wage_closing_date = payroll_year + "/" + payroll_month;
    vm.payroll.payroll_date = moment(vm.payroll.wage_closing_date + "/01").format(FULL_DATE_FORMAT);
    var month = moment(vm.payroll.payroll_date).month() + 1;
    var year = moment(vm.payroll.payroll_date).year();
    var prevMonth = month == JANUARY ? DECEMBER : month - 1;
    var startDate = payroll_year + "/" + prevMonth + "/" + (MIN_MONTH_DAY + 1);
    var endDate = payroll_year + "/" + month + "/" + MIN_MONTH_DAY;
    vm.payroll.duration = startDate + "～" + endDate;
  };

  vm.setPayrollDate = function() {
    if (!moment(vm.payroll.salary_payment_month).isValid()) {
      setCurrentPayrollDate();
      vm.changeSalaryPaymentMonth();
    }
  }

  var setCurrentPayrollDate = function() {
    var currentDate = moment(new Date());
    var year = currentDate.format("YYYY");
    var month = currentDate.format("MM");
    var day = currentDate.format("DD");
    vm.payroll.salary_payment_month = year + "/" + month;
    if (day <= MIN_MONTH_DAY){
      year = (month == JANUARY) ? year - 1 : year;
      month = (month == JANUARY) ? DECEMBER : month - 1;
    };
    vm.payroll.wage_closing_date = year + "/" + month;
    vm.payroll.payroll_date = moment(vm.payroll.wage_closing_date + "/01").format(FULL_DATE_FORMAT);
    vm.current_payroll_date = vm.payroll.payroll_date;

    $("#create-payroll-date").datepicker("setDate", vm.payroll.salary_payment_month).trigger("change");
  };

  vm.submitCreatePayroll = function() {
    vm.isSaving = true;
    var params = vm.payroll;
    payrollService.createPayroll(params).then(function(res) {
      if (res.data.status) {
        $("#create-payroll-modal").modal("hide");
        vm.isSaving = false;
        vm.search();
      } else {
        $.lawsonAjax(res.data);
        vm.isSaving = false;
      }
    });
  }
}
