"use strict";

angular.module("adminApp").controller("BillingPaymentTemplateController", BillingPaymentTemplateController);
BillingPaymentTemplateController.$inject = ["$location", "billingPaymentTemplateService", "$window"];

function BillingPaymentTemplateController($location, billingPaymentTemplateService, $window) {
  var vm = this;
  var DEFAULT_PAGE = 1;
  
  vm.hasRecord = vm.noRecord = false;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.params = $location.search();
  vm.params.page = DEFAULT_PAGE;
  vm.params.per_page = vm.perPageSettings[0];;
  vm.templates = [];

  vm.init = function() {
    vm.search(true);
  }

  vm.search = function(resetPage) {
    if (resetPage) {
      vm.params.page = DEFAULT_PAGE;
    };
    billingPaymentTemplateService.getTemplates(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.templates.length);
      vm.noRecord = !vm.hasRecord;
      $location.search(vm.params).replace();
    }, function(_) {
    });
  };

  vm.deleteTemplate = function(templateId) {
    $("#spinner").removeClass("ng-hide");
    billingPaymentTemplateService.deleteTemplate(templateId).then(function(res) {
      if (!!res.data && !!res.data.redirect_path) {
        $("#spinner").addClass("ng-hide");
        $window.location.href = res.data.redirect_path;
      }
    });
    $("#spinner").addClass("ng-hide");
  };

  vm.changePerPage = function() {
    vm.search(false);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".template-list-result").offset().top
    }, 1000);
  };
}