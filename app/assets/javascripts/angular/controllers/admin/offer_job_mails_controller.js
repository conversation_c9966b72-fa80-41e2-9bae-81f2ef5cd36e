"use strict";

angular.module("adminApp")
  .controller("OfferJobMailController", OfferJobMailController);
OfferJobMailController.$inject = ["arrangementService", "$scope"];

function OfferJobMailController(arrangementService, $scope) {
  var vm = this;
  vm.orderCases = [];
  vm.$scope = $scope;
  vm.params = {page: 1};
  vm.confirmParams = {page: 1};
  vm.isCheckAllOrderCase = false;
  vm.confirmOrderCase = [];
  var $selectionModalOffer = angular.element("#offer-job-mail-modal");
  var $confirmModalOffer = angular.element("#confirm-offer-job-mail-modal");
  var $sendingModal = angular.element("#offer-mail-sending-modal");
  var $confirmCloseModal = angular.element("#offer-mail-confirm-close-modal");
  var MAX_FAILED_NUMBER = 10;
  vm.totalSelectedOrderCase = 0;
  vm.selectedOrderCaseIds = [];
  vm.isCheckedAll = false;
  vm.isUrgent = false;
  vm.$scope.$watch("vm.$scope.$parent.offerJobMailModal", function(newVal, oldVal) {
    var searchParams = _.has(newVal, "search") ? newVal["search"] : "{}";
    vm.params = {page: 1, search: searchParams};
    vm.orderCases = [];
    if (vm.$scope.$parent.offerJobMailModal) {
      vm.init = false;
      loadOrderCases();
    }
  });

  vm.loadMoreOrderCase = function() {
    if (!vm.init) return;
    if (vm.orderCases.length < vm.totalItems && vm.params.page == vm.params.currentPage) {
      vm.params.page++;
      loadOrderCases();
    }
  };

  function loadOrderCases() {
    arrangementService.getJobAvailable(vm.params).then(function(res) {
      vm.orderCases = vm.orderCases.concat(res.data.order_cases);
      vm.totalItems = res.data.total_items;
      vm.orderCaseIds = res.data.order_case_ids;
      vm.params.currentPage = res.data.page;
      vm.init = true;
      if (vm.isCheckAllOrderCase) {
        vm.checkAllOrderCase();
      }
      if (vm.isCheckedAll) {
        _.forEach(res.data.order_cases, function(orderCase) {
          orderCase.isCheck = true;
        });
      }
    });
  };

  vm.sendConfirm = function() {
    vm.staffSchedule = angular.element(".staff-schedule option:selected").text();
    vm.confirmParams = {page: 1, currentPage: 1};
    vm.confirmOrderCase = [];
    vm.initConfirm = false;
    loadConfirmOrderCases();
    hideModal($selectionModalOffer);
    showModal($confirmModalOffer);
  };

  vm.loadMoreConfirmOrderCase = function() {
    if (!vm.initConfirm) return;
    if (vm.confirmOrderCase.length < vm.totalConfirmItems && vm.confirmParams.page == vm.confirmParams.currentPage) {
      vm.confirmParams.page++;
      loadConfirmOrderCases();
    }
  };

  function loadConfirmOrderCases() {
    vm.confirmParams.order_case_ids = vm.selectedOrderCaseIds.join(",");
    arrangementService.getSelectedOfferJob(vm.confirmParams).then(function(res) {
      vm.confirmOrderCase = vm.confirmOrderCase.concat(res.data.order_cases);
      vm.totalConfirmItems = res.data.total_items;
      vm.confirmParams.currentPage = res.data.page;
      vm.initConfirm = true;
      vm.notedOrderCase = vm.getCheckedOrderCase();
      _.forEach(res.data.order_cases, function(orderCase) {
        var notedCase = _.find(vm.notedOrderCase, function(notedCase) {return notedCase.id == orderCase.id});
        if (notedCase) {
          orderCase.note = notedCase.note;
        }
      });
    });
  };

  vm.checkOrderCaseItem = function(orderCase) {
    vm.isCheckAllOrderCase = vm.orderCases.length === vm.getCheckedOrderCase().length;
    if (orderCase.isCheck) {
      vm.totalSelectedOrderCase += 1;
      vm.selectedOrderCaseIds.push(orderCase.id);
    } else {
      vm.totalSelectedOrderCase -= 1;
      _.pull(vm.selectedOrderCaseIds, orderCase.id);
    }
  };

  vm.checkAllOrderCase = function() {
    if (!!vm.isCheckAllOrderCase) {
      vm.totalSelectedOrderCase = vm.totalItems;
      vm.selectedOrderCaseIds = vm.orderCaseIds;
      vm.isCheckedAll = true;
    } else {
      vm.totalSelectedOrderCase = 0;
      vm.selectedOrderCaseIds = [];
      vm.isCheckedAll = false;
    }
    _.forEach(vm.orderCases, function(orderCase) {
      orderCase.isCheck = !!vm.isCheckAllOrderCase;
    });
  };

  vm.getCheckedOrderCase = function() {
    return _.filter(vm.orderCases, function(orderCase) {return orderCase.isCheck});
  };

  vm.backToSelectionOfferModal = function() {
    showModal($selectionModalOffer);
    hideModal($confirmModalOffer);
  };

  vm.sendMailOffer = function() {
    var params = _.map(vm.confirmOrderCase, function(orderCase) {
      return {id: orderCase.id, note: orderCase.note}
    });
    var isMatchingExpect = vm.isMatchingExpect === "1";
    showModal($sendingModal);
    $("#offer-mail-sending-modal").modal("show")
    arrangementService.sendOfferMail({order_cases: params, is_matching_expect: isMatchingExpect, is_urgent: vm.isUrgent}).then(function(res) {
      var jobId = res.data.job_id;
      var intervalName = "job_" + jobId;
      window[intervalName] = setInterval(function() {
        getExportJobStatus(jobId, intervalName);
      }, 5000);
    }, function(err) {
      hideModal($sendingModal);
    });
  };

  vm.selectedJobNote = function() {
    return I18n.t("admin.arrangements.modal.offer_job_mail_modal.job_selected",
      {selected: vm.totalSelectedOrderCase, total: vm.totalItems});
  };

  function getExportJobStatus(jobId, intervalName) {
    arrangementService.sendOfferMailStatus({job_id: jobId}).then(function(res) {
      var percentage = res.data.percentage;
       if (res.data.status === "complete") {
        setTimeout(function() {
          deleteIntervalJob(intervalName);
        }, 500);
        hideModal($sendingModal);
        vm.sentFailed = false;
        showModal($confirmCloseModal);
      } else if ((res.data.status === "retrying" || res.data.status === "queued" || res.data.status === "failed") && res.data.percentage == null) {
        vm.failedRequestNumber += 1;
         if (vm.failedRequestNumber >= MAX_FAILED_NUMBER) {
          deleteIntervalJob(intervalName);
          hideModal($sendingModal);
          vm.sentFailed = true;
          showModal($confirmCloseModal);
        }
      }
    }, function(err) {
      deleteIntervalJob(intervalName);
      hideModal($sendingModal);
      vm.sentFailed = true;
      showModal($confirmCloseModal);
    });
  };

  function deleteIntervalJob(intervalName) {
    clearInterval(window[intervalName]);
    delete window[intervalName];
    vm.failedRequestNumber = 0;
  };

  function showModal($modal) {
    $modal.modal("show");
  };

  function hideModal($modal) {
    $modal.modal("hide");
  };

  vm.closeAllModal = function() {
    vm.isCheckAllOrderCase = false;
    vm.isCheckedAll = false;
    vm.selectedOrderCaseIds = [];
    vm.totalSelectedOrderCase = 0;
    vm.isUrgent = false;
    hideModal($confirmCloseModal);
    hideModal($confirmModalOffer);
  };
}
