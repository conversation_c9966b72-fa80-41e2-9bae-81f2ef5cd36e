"use strict";

angular.module("adminApp")
  .controller("StaffsSalaryTabController", StaffsSalaryTabController);

StaffsSalaryTabController.$inject = ["$scope"]

function StaffsSalaryTabController($scope) {
  var vm = this;
  vm.$scope = $scope;
  var INCOMETAXTYPE = "i_t_target";
  var INSURANCESUBSECTIONTYPE = "subscription";
  var EMPLOYMENTINSURANCETYPE = "e_i_target";
  vm.incomeTax = {
    "incomeTaxType": angular.element("#staff_staff_salary_attributes_income_tax_type").val(),
    "taxType": angular.element("#staff_staff_salary_attributes_tax_type").val(),
    "isSpouse": angular.element("#staff_staff_salary_attributes_is_spouse").val()
  };
  vm.insuranceSubsection = {
    "insuranceSubsectionType": angular.element("#staff_staff_salary_attributes_insurance_subsection_type").val(),
    "insuranceNumber": angular.element("#staff_staff_salary_attributes_insurance_number").val(),
    "basicPensionNumber": angular.element("#staff_staff_salary_attributes_basic_pension_number").val(),
  };
  vm.employmentInsurance = {
    "employmentInsuranceType": angular.element("#staff_staff_salary_attributes_employment_insurance_type").val(),
    "employmentInsuranceNumber": angular.element("#staff_staff_salary_attributes_employment_insurance_number").val()
  };
  vm.prevIncomeTax = {};
  vm.prevInsuranceSubsection = {};
  vm.prevEmploymentInsurance = {
    "employmentInsuranceNumber": angular.element("#staff_staff_salary_attributes_copied_employment_insurance_number").val()
  }

  vm.init = function(){
    vm.incomeTax;
    vm.insuranceSubsection;
    vm.employmentInsurance;
    vm.prevEmploymentInsurance;
    vm.setRelevantIncomeTax();
    vm.setRelevantInsuranceSubsection();
    vm.setRelevantEmploymentInsurance();
  };

  vm.checkValid = function(el){
    switch (el){
      case "IncomeTaxType":
        return (vm.incomeTax.incomeTaxType != INCOMETAXTYPE);
      case "InsuranceSubsectionType":
        return (vm.insuranceSubsection.insuranceSubsectionType != INSURANCESUBSECTIONTYPE);
      case "EmploymentInsuranceNumber":
        return (vm.employmentInsurance.employmentInsuranceType != EMPLOYMENTINSURANCETYPE);
    }
  };

  vm.setRelevantIncomeTax = function(){
    if (vm.incomeTax.incomeTaxType != INCOMETAXTYPE){
      $.each(vm.incomeTax, function(key, value){
        if (!_.isEmpty(value)){
          vm.prevIncomeTax[key] = value;
        }
      });
      vm.incomeTax.taxType = "";
      vm.incomeTax.isSpouse = "";
    }else{
      if (!_.isEmpty(vm.prevIncomeTax)){
        vm.incomeTax.taxType = vm.prevIncomeTax.taxType;
        vm.incomeTax.isSpouse = vm.prevIncomeTax.isSpouse;
      }else {
        vm.prevIncomeTax.taxType = vm.incomeTax.taxType;
        vm.prevIncomeTax.isSpouse = vm.incomeTax.isSpouse;
      }
    }
  };

  vm.setRelevantInsuranceSubsection = function(){
    if (vm.insuranceSubsection.insuranceSubsectionType != INSURANCESUBSECTIONTYPE){
      $.each(vm.insuranceSubsection, function(key, value){
        if (!_.isEmpty(value)){
          vm.prevInsuranceSubsection[key] = value;
        }
      });

      vm.insuranceSubsection.insuranceNumber = "";
      vm.insuranceSubsection.basicPensionNumber = "";
    }else{
      if (!_.isEmpty(vm.prevInsuranceSubsection)){
        vm.insuranceSubsection.insuranceNumber = vm.prevInsuranceSubsection.insuranceNumber;
        vm.insuranceSubsection.basicPensionNumber = vm.prevInsuranceSubsection.basicPensionNumber;
      };

      vm.prevInsuranceSubsection.insuranceNumber = vm.insuranceSubsection.insuranceNumber;
      vm.prevInsuranceSubsection.basicPensionNumber = vm.insuranceSubsection.basicPensionNumber;
    }
  };

  vm.setRelevantEmploymentInsurance = function(){
    if (vm.employmentInsurance.employmentInsuranceType != EMPLOYMENTINSURANCETYPE){
      if (!_.isEmpty(vm.employmentInsurance.employmentInsuranceNumber)){
        vm.prevEmploymentInsurance.employmentInsuranceNumber = vm.employmentInsurance.employmentInsuranceNumber;
      }
      vm.employmentInsurance.employmentInsuranceNumber = "";
    }else{
      if (!_.isEmpty(vm.prevEmploymentInsurance.employmentInsuranceNumber)){
        vm.employmentInsurance.employmentInsuranceNumber = vm.prevEmploymentInsurance.employmentInsuranceNumber;
      };
    };
  };
}
