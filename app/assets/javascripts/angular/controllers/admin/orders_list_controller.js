"use strict";

angular.module("adminApp")
  .controller("OrderListController", OrderListController);
OrderListController.$inject = ["$location", "$timeout", "checkValidDateFunction", "orderService", "toaster"];

function OrderListController($location, $timeout, checkValidDateFunction, orderService, toaster) {
  var vm = this;
  var HAVE_PLACEHOLDER_INP_FIELD = ["definition_id", "overall_start_date", "overall_end_date",
    "from_violation_date", "from_order_date"];
  var SEARCH_DETAILS_FIELD = ["from_violation_date", "to_violation_date",
    "definition_id", "corporation_id", "business_id", "occupation_id", "organization_id"];
  vm.locations = [];
  vm.corporations = [];
  vm.organizations = [];
  var DATE_FIELD = ["overall_start_date", "overall_end_date", "from_violation_date",
    "to_violation_date", "from_order_date", "to_order_date"];
  var DRAFT_STATUS = "draft";
  var selectIDs = ["business_id", "occupation_id"];
  vm.toasterTimeout = 6200;
  moment.locale("ja");
  var EXPORTED_STATUS = ["exported", "not_exported"];
  var COMMA = I18n.t("common.comma");

  vm.loadOrganizations = function() {
    vm.organizations = [];
    var corporationIds = Array.isArray(vm.params.corporation_id) ? vm.params.corporation_id.join(",") : vm.params.corporation_id;
    orderService.loadOrganizations({corporation_ids: corporationIds}).then(function(res) {
      vm.params.organization_id = "";
      vm.organizations = res.data.organizations;
    });
  };

  vm.refresh = function(resetPage, isSearchBtn, isUseAdminCondition, isSave) {
    DATE_FIELD.forEach(function(fieldName) {
      if (!!vm.params[fieldName]) {vm.checkIsDate(fieldName);}
    });
    var params = isUseAdminCondition ? formatPerPageParam(vm.currentParams) : angular.copy(vm.params);
    var searchParams = formatParamsBeforeSearch(params, resetPage);
    orderService.searchOrder({search: searchParams}).then(function(res) {
      angular.extend(vm, res.data);
      vm.orders = formatDataOrders(vm.orders);
      $location.search(searchParams).replace();

      if (isSave) {
        orderService.createSearchCondition({search: searchParams}).then(function(res) {
        });
      }
      vm.currentParams = angular.copy(searchParams);
    }, function(error) {
      vm.orders = [];
    });
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".admin-order-result").offset().top
    }, 1000);
  };

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !vm.params.desc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !!vm.params.desc
    };
  };

  vm.init = function () {
    vm.statuses = angular.element(".search-info__status").data("statuses");
    vm.perPageSettings = angular.element(".per-page-settings").data("settings");
    vm.params = $location.search();
    vm.params.per_page = vm.perPageSettings[0];
    vm.params.desc = vm.params.desc == "false" ? false : true;
    vm.params.page = 1;
    vm.params.export_status = "";
    vm.order_segment_ids = angular.element(".order-segment-ids").data("order-segment-ids");
    var locationElement = angular.element(".js-list-location");
    var lastCondition = locationElement.data("last-condition");
    var locations = locationElement.data("locations");
    var corporations = locationElement.data("corporations");
    var organizations = locationElement.data("organizations");
    var existLastCondition = false;

    if (!_.isEmpty(lastCondition)) {
      vm.params = lastCondition;
      vm.locations = locations;
      vm.corporations = corporations;
      vm.organizations = organizations;
      existLastCondition = true;
    } else {
      if (!vm.params.overall_start_date) {
          vm.params.overall_start_date = moment().format(ORDER_DATEPICKER_FORMAT);
      }
      if (!vm.params.overall_end_date) {
        vm.params.overall_end_date = moment().add(1, "months").format(ORDER_DATEPICKER_FORMAT);
      }
    }

    DATE_FIELD.forEach(function(fieldName) {
      if (!Date.parse(vm.params[fieldName])) {
        vm.params[fieldName] = "";
      }

      $("[name='" + fieldName + "']").datepicker("setDate", vm.params[fieldName]);
    });

    HAVE_PLACEHOLDER_INP_FIELD.forEach(function(fieldName) {
      var inpVal = vm.params[fieldName];
      if (inpVal) {
        $("." + fieldName).val(inpVal).trigger("input");
      }
    });

    for (var idx in SEARCH_DETAILS_FIELD) {
      var fieldName = SEARCH_DETAILS_FIELD[idx];
      if (vm.params[fieldName]) {
        $(".search-details-title").click();
        break;
      }
    }

    formatParamsLocationAndOrganization(existLastCondition);
    initStatusInpStates(existLastCondition);
    initExportedStatus(existLastCondition);
    initOrderSegmentInpStates(existLastCondition);
    vm.currentParams = vm.params;
    vm.refresh(true);
  };

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
      vm.currentParams.desc = !vm.currentParams.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
      vm.currentParams.desc = true;
      vm.currentParams.order_key = column;
    }
    vm.refresh(false, false, true, true);
    vm.scrollToResult();
  };

  vm.isAbleToDelete = function(order) {
    return order.status_id == DRAFT_STATUS;
  };

  vm.openDestroyStaffModal = function(id) {
    vm.beDeletedOrderId = id;
    angular.element(".delete-order-modal").modal("show");
  };

  vm.deleteOrder = function() {
    orderService.deleteOrder(vm.beDeletedOrderId).then(function(res) {
      $timeout(function() {
        vm.refresh(false, false, false, false);
      }, 800);
      angular.element(".delete-order-modal").modal("hide");
      vm.currentParams = null;
      toaster.pop(res.data.status, "", res.data.message);
      vm.beDeletedOrderId = null;
    });
  };

  function formatParamsBeforeSearch(params, resetPage) {
    if (resetPage) {
      params.page = 1;
    }
    params.status_before_cast = vm.statuses.filter(function(status) {
      return !!status.selected;
    }).map(function(status) {
      return status.id;
    }).join(",");

    params.order_segment_before_cast = vm.order_segment_ids.filter(function(order_segment_id) {
      return !!order_segment_id.selected;
    }).map(function(order_segment_id) {
      return order_segment_id.id;
    }).join(",");

    var exported = vm.exported ? 1 : "";
    var not_exported = vm.not_exported ? 0 : "";
    params.export_status = [exported, not_exported].join(" ").trim();
    params.location_id = Array.isArray(params.location_id) ? params.location_id.join(",") : params.location_id;
    params.corporation_id = Array.isArray(params.corporation_id) ? params.corporation_id.join(",") : params.corporation_id;
    return params;
  }

  function formatDate(dateStr) {
    return moment(dateStr).format(DATE_TIME_FORMAT_WITH_DAY_NAME);
  }

  function formatParamsLocationAndOrganization(paramStatus) {
    if (paramStatus) {
      var locationParams = [];
      var corporationParams = [];
      $("#corporation_id").empty();
      $("#location_id").empty();
      if (!_.isNil(vm.params.corporation_id)) {
        corporationParams = Array.isArray(vm.params.corporation_id) ? vm.params.corporation_id :  vm.params.corporation_id.split(",");
      }
      if (!_.isNil(vm.params.location_id)) {
        locationParams = Array.isArray(vm.params.location_id) ? vm.params.location_id :  vm.params.location_id.split(",");
      }
      vm.params.corporation_id = corporationParams;
      vm.params.location_id = locationParams;
      vm.corporations.forEach(function(corporation) {
        corporation.selected = _.includes(corporationParams, corporation.id.toString());
        var corporationOption = new Option(corporation.name, corporation.id);
        $('#corporation_id').append($(corporationOption)).trigger('change');
      });
      vm.locations.forEach(function(location) {
        location.selected = _.includes(locationParams, location.id.toString());
        var locationOption = new Option(location.name, location.id);
        $('#location_id').append($(locationOption)).trigger('change');
      });
      $('#corporation_id').val(vm.params.corporation_id).trigger('change');
      $('#location_id').val(vm.params.location_id).trigger('change');
      $('#organization_id').val(vm.params.organization_id).trigger('change');
    }
  }

  function formatDataOrders(orders) {
    return orders.map(function(order) {
      var status = vm.statuses.find(function(status) {
        return _.isEqual(status.key, order.status_id);
      });
      order.status = status.name;
      return order;
    });
  }

  vm.toggleAllStatusChkboxes = function() {
    var chkBoxState = !!vm.chkAllStatusState;
    vm.statuses.forEach(function(status) {
      status.selected = chkBoxState;
    });
  };

  vm.changeStatusCheckbox = function() {
    var selectedStatus = vm.statuses.filter(function(status) {
      return !!status.selected;
    });
    if (selectedStatus.length < vm.statuses.length) {
      vm.chkAllStatusState = false;
    }
  };

  function initExportedStatus(paramStatus) {
    if (paramStatus) {
      var export_status = vm.params.export_status.split(" ");
      if (_.includes(export_status, "1")) {vm.exported = true};
      if (_.includes(export_status, "0")) {vm.not_exported = true};
    } else {
      EXPORTED_STATUS.forEach(function(status) {
        vm[status] = false;
      });
    }
  }

  function initStatusInpStates(paramStatus) {
    if (paramStatus) {
      var statusParams = !_.isNil(vm.params.status_before_cast) ? vm.params.status_before_cast.split(",") : [];
      var numSelectedStatus = 0;
      vm.statuses.forEach(function(status) {
        status.selected = _.includes(statusParams, status.id.toString());
        if (status.selected) {numSelectedStatus += 1;}
      });
      if (_.isEqual(numSelectedStatus, vm.statuses.length)) {vm.chkAllStatusState = true;}
    } else {
      vm.statuses.forEach(function(status) {
        status.selected = true;
      });
      vm.chkAllStatusState = true;
    }
  }

  angular.element(function () {
    selectIDs.forEach(function(name_id) {
      $("#" + name_id).trigger("change");
    });
  });

  vm.changePerPage = function() {
    vm.refresh(false, false, true, true);
    vm.scrollToResult();
  }

  vm.changePage = function() {
    vm.currentParams.page = vm.params.page;
    vm.refresh(false, false, true);
    vm.scrollToResult();
  }

  vm.setDefaultIdSearch = function(searchId, model){
    if(_.isUndefined(searchId) || searchId < 0){
      vm.params[model] = 0;
    };
  }

  function formatPerPageParam(currentParams) {
    currentParams.per_page = vm.params.per_page;
    return currentParams;
  }

  vm.toggleAllOrderSegmentChkboxes = function() {
    var chkBoxState = !!vm.chkAllOrderSegmentState;
    vm.order_segment_ids.forEach(function(order_segment_id) {
      order_segment_id.selected = chkBoxState;
    });
  };

  vm.changeOrderSegmentCheckbox = function() {
    var selectedOrderSegmentIds = vm.order_segment_ids.filter(function(order_segment_id) {
      return !!order_segment_id.selected;
    });
    if (selectedOrderSegmentIds.length < vm.order_segment_ids.length) {
      vm.chkAllOrderSegmentState = false;
    }
  };

  function initOrderSegmentInpStates(paramStatus) {
    if (paramStatus) {
      var orderSegment = !_.isNil(vm.params.order_segment_before_cast) ? vm.params.order_segment_before_cast.split(",") : [];
      var numSelectedOrderSegment = 0;
      vm.order_segment_ids.forEach(function(order_segment_id) {
        order_segment_id.selected = _.includes(orderSegment, order_segment_id.id.toString());
        if (order_segment_id.selected) {numSelectedOrderSegment += 1;}
      });
      if (_.isEqual(numSelectedOrderSegment, vm.order_segment_ids.length)) {vm.chkAllOrderSegmentState = true;}
    } else {
      vm.order_segment_ids.forEach(function(order_segment_id) {
        order_segment_id.selected = true;
      });
      vm.chkAllOrderSegmentState = true;
    }
  };

  vm.isValidStaffExpressOrderId = function() {
    return _.isNumber(vm.staff_express_order_id) && vm.staff_express_order_id >= 0;
  };

  vm.clearSearchCondition = function() {
    // Reset params
    vm.params = angular.copy({
      per_page: vm.perPageSettings[0]
    });
    vm.chkAllStatusState = false;
    vm.chkAllOrderSegmentState = false;
    vm.not_exported = false;
    vm.exported = false;
    // Reset all events
    vm.toggleAllStatusChkboxes();
    vm.toggleAllOrderSegmentChkboxes();

    // Update Select2 state
    setTimeout(function() {
      angular.element("select.select2").trigger("change");
      $("#department_id").val(null).trigger("change");
    }, 10);
  };

  vm.checkIsDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  }

  vm.exportOrders = function(e) {
    var searchParams = formatParamsBeforeSearch(angular.copy(vm.params));
    $.asyncDownload(e, "orders", JSON.stringify(searchParams), "csv")
  }

  $("#department_id").select2({
    placeholder: I18n.t("common.please_select"),
    width: "100%",
    allowClear: true,
    ajax: {
      url: "/departments",
      dataType: 'json',
      delay: 500,
      data: function(params) {
        return {
          search: params.term,
        };
      },
      processResults: function(data) {
        return {
          results: data
        };
      },
      cache: true
    }
  });

  $("#department_id").on("change", function() {
    vm.params.department_id = $(this).val();
  });
}
