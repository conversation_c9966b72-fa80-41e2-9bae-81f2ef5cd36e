"use strict";

angular.module("adminApp").controller("CreateBillingPaymentTemplateController", CreateBillingPaymentTemplateController);
CreateBillingPaymentTemplateController.$inject = ["$location", "orderService"];

function CreateBillingPaymentTemplateController($location, orderService) {
  var vm = this;
  vm.locations = {};

  vm.init = function(corporationId, locationId) {
    vm.corporation_id = corporationId;
    vm.location_id = locationId;
  }

  vm.loadLocations = function() {
    if (_.isNaN(vm.corporation_id)) {
      vm.locations = {};
      return;
    }
    orderService.loadLocations({
      corporation_id: vm.corporation_id
    }).then(function(res) {
      angular.extend(vm, res.data);
    }, function(_) {
    });
  };
}