'use strict';

angular.module('adminApp').controller('LocationSurveysController', LocationSurveysController);
LocationSurveysController.$inject = ['$location', 'locationSurveysService', 'checkValidDateFunction'];

function LocationSurveysController($location, locationSurveysService, checkValidDateFunction) {
  var vm = this;
  var requestParams = $location.search();
  var searchParamKeys = ['pic_department_id', 'order_case_from', 'order_case_to'];
  var SELECT_FIELDS = ["pic_department_id"];
  var DATETIME_FIELDS = ["order_case_from", "order_case_to"];
  var ZIP_TYPE = "zip";
  vm.hasRecord = vm.noRecord = false;
  vm.isSearch = false;
  vm.params = {};
  vm.params.page = requestParams.page;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  _.forEach(searchParamKeys, function(searchParamKey) {
    vm.params[searchParamKey] = requestParams[searchParamKey];
  });

  vm.search = function(resetPage) {
    if (resetPage) {
      vm.isSearch = true;
      vm.params.page = 1;
    };
    vm.isSelected = false;
    vm.selectAll = false;
    vm.params.per_page = vm.params.per_page || vm.perPageSettings[0];
    locationSurveysService.searchLocation(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.locations.length);
      vm.noRecord = !vm.hasRecord;
      $location.search(vm.params).replace();
    }, function(error) {
    });
  };

  vm.init = function() {
    vm.search(true);
  }

  vm.changePerPage = function() {
    vm.search(false);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".location-list-result").offset().top
    }, 1000);
  };

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  };

  vm.checkAll = function() {
    _.forEach(vm.locations, function(location) {
      location.select = vm.selectAll;
    });
    vm.checkSelected(true);
  };

  vm.checkSelected = function(isChecked) {
    var selectedItems = _.find(vm.locations, {select: true});
    vm.isSelected = selectedItems ? true : false;
    if (vm.selectAll && !isChecked) {
      vm.selectAll = false;
    };
  };

  vm.downloadZip = function(e, isDownloadAll) {
    var selectedlocations = _.filter(vm.locations, function(location) {
      return location.select;
    });
    var selectedIds = _.map(selectedlocations, "id").join(",");
    var json_object = {file_type: ZIP_TYPE};
    if (!!isDownloadAll) {
      json_object.is_download_all = true;
      json_object.search_condition = vm.params;
    } else {
      json_object.location_ids = selectedIds;
    }
    var json_str = JSON.stringify(json_object)
    $.asyncDownload(e, "location_surveys", json_str, ZIP_TYPE, json_object);
  };
}
