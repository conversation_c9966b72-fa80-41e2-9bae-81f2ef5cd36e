"use strict";

angular.module("adminApp").controller("ImportPayrollController", ImportPayrollController);
ImportPayrollController.$inject = ["$scope", "$window"];

function ImportPayrollController($scope, $window) {
  var vm = this;
  vm.$scope = $scope;
  var MIN_MONTH_DAY = 15;
  var DECEMBER = 12, JANUARY = 1;

  moment.locale(I18n.locale);

  var setCurrentDate = function() {
    var currentDate = moment(new Date());
    var year = currentDate.format("YYYY");
    var month = currentDate.format("M");
    var day = currentDate.format("D");
    if (parseInt(day) <= MIN_MONTH_DAY){
      year = (parseInt(month) == JANUARY) ? parseInt(year) - 1 : year;
      month = (parseInt(month) == JANUARY) ? DECEMBER : parseInt(month) - 1;
    }
    vm.import_date = year + "/" + month;
    $(".import-payroll-date-picker").datepicker("setDate", vm.import_date).trigger("change");
  };

  vm.openModal = function() {
    vm.confirmMsg = I18n.t("payroll.import.modal.content", {date: moment(vm.import_date).format(YEAR_W_MONTH_FORMAT)});
    $("#confirm-import-modal").modal("show");
  };

  vm.changeDuration = function() {
    if (vm.import_date == "") {
      vm.durationFormat = "";
      return;
    }
    var importYear = moment(vm.import_date).format("YYYY");
    var importMonth = moment(vm.import_date).format("M");
    var payrollYear = (importMonth == JANUARY) ? importYear - 1 : importYear;
    var payrollMonth = (importMonth == JANUARY) ? DECEMBER : importMonth - 1;
    vm.payroll_date = payrollYear + "/" + payrollMonth;
    var month = moment(vm.payroll_date).month() + 1;
    var year = moment(vm.payroll_date).year(); 
    var prevMonth = month == JANUARY ? DECEMBER : month - 1;
    vm.durationFormat = moment(prevMonth + "-" + (MIN_MONTH_DAY + 1)).format(DAY_MONTH_FORMAT) + "～" +
      moment(month + "-" + MIN_MONTH_DAY).format(DAY_MONTH_FORMAT);
  };

  vm.blurPayrollDate = function() {
    if (!moment(vm.import_date).isValid()) {
      setCurrentDate();
      vm.changeDuration();
    }
  }

  vm.fileChanged = function(event) {
    vm.$scope.$apply(function() {
      vm.isChoseFile = !_.isNil(event.target.files[0]);
    });
  };

  vm.import = function() {
    vm.isImporting = true;
    $("#spinner").removeClass("ng-hide");
    $.ajax({
      url: "/payrolls/import",
      method: "POST",
      dataType: "json",
      data: new FormData($("#import-payroll")[0]),
      processData: false,
      contentType: false,
      success: function(response) {
        $window.location.href = response.redirect_path;
      }
    })
  };
}
