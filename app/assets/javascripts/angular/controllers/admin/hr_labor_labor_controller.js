angular.module("adminApp").controller("HrLaborFileController", HrLaborFileController);

HrLaborFileController.$inject = ["hrLaborFileService", "$window", "$scope"]

function HrLaborFileController(hrLaborFileService, $window, $scope) {
  var vm = this;
  vm.$scope = $scope;
  vm.files_by_type = [];
  vm.targetFileType = "";
  vm.fileError = "";

  vm.init = function() {
    vm.getDocumentFiles();
  };

  vm.getDocumentFiles = function() {
    $("#spinner").removeClass("ng-hide");
    hrLaborFileService.getFiles().then(function(res) {
      var data = res.data;
      if (data.files_by_type) {
        vm.files_by_type = data.files_by_type;
    }
      $("#spinner").addClass("ng-hide");
    });
  };

  vm.openFile = function(id) {
    $window.open("/hr_labor_files/view_file?id=" + id, "_blank");
  };

  vm.openUploadFileModal = function(type) {
    vm.fileError = "";
    vm.targetFileType = type;
    vm.targetFileTypeText = I18n.t("admin.hr_labor_files." + type);
    $("#hr-labor-file-upload").val(null);
    $("#hr-labor-file-upload-modal").modal("show");
  };

  vm.uploadFile = function() {
    vm.fileError = "";
    var file = $("#hr-labor-file-upload").prop("files")["0"];
    var formData = new FormData();
    formData.append("document_file", file);
    formData.append("document_type", vm.targetFileType);
    
    $.ajax({
      url: "/hr_labor_files",
      processData: false,
      contentType: false,
      method: "POST",
      dataType: "json",
      data: formData,
      success: function(res) {
        vm.$scope.$apply(function() {
          if (res.status) {
            vm.getDocumentFiles();
            $("#hr-labor-file-upload").val(null);
            $("#hr-labor-file-upload-modal").modal("hide");
          } else {
            vm.fileError = res.file_error;
          }
        });
      }
    });
  }

  vm.openConfirmDeleteModal = function(type) {
    // var file = vm.files_by_type[type];
  };
};