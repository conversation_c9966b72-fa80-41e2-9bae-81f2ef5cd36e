"use strict";

angular.module("adminApp")
  .controller("MessageTabController", MessageTabController);
MessageTabController.$inject = ["staffMessageTabService"];

function MessageTabController(staffMessageTabService) {
  var vm = this;
  var msgTemplate = {
    id: null,
    start_date: null,
    end_date: null,
    corporation_id: null,
    corporation_full_name: null,
    location_id: null,
    location_name: null,
    check_type: null,
    message_title: null,
    message_content: null,
    note: null,
    _destroy: false
  };
  var DATA_HOLDER_IDS = ["check_types", "corp_messages", "location_messages"];

  vm.corp_messages = [];
  vm.location_messages = [];
  vm.CORPORATION_TYPE = "from_corporation";
  vm.LOCATION_TYPE = "from_location";

  vm.addMsg = function(type, messages) {
    var template = angular.copy(msgTemplate);
    template.message_type = type;
    messages.push(template);
    setDatePicker();
  };

  vm.deleteMsg = function(collection, index) {
    collection[index]._destroy = true;
  };

  vm.formatSelectedCheckType = function(id, msgItem) {
    if (_.isEqual(id, "") || _.isNil(id)) return "";
    return vm.check_types.filter(function(item) {
      return item.id == id;
    })[0].name;
  };

  vm.getIndexOfLocTable = function(index) {
    return index + vm.corp_messages.length;
  };

  vm.initData = function() {
    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });
    formatData(vm.corp_messages);
    formatData(vm.location_messages);
  }

  function formatData(messages) {
    messages.forEach(function(msg) {
      msg.corporation_id = msg.corporation_id ? msg.corporation_id + "" : "";
      msg.location_id = msg.location_id ? msg.location_id + "" : "";
      msg.start_date = moment(msg.start_date).format(FULL_DATE_FORMAT);
      msg.end_date = moment(msg.end_date).format(FULL_DATE_FORMAT);
    });
  }

  function setDatePicker() {
    setTimeout(function() {
      $(".staff-date-picker").datepicker({
        autoclose: true,
        format: DEFAULT_DATE_TIME_FORMAT,
        todayBtn: true,
        todayHighlight: true
      }).click();
    }, 0);
  }

  vm.setCorporationName = function(msg) {
    if (_.isNull(msg.location_id) || msg.location_id === "") {
      msg.corporation_id = "";
      msg.corporation_full_name = "";
      return;
    }

    staffMessageTabService.loadCorporation({location_id: msg.location_id})
      .then(function mySuccess(res) {

      msg.corporation_id = res.data.corporation.id;
      msg.corporation_full_name = res.data.corporation.full_name;
    })
  }
}
