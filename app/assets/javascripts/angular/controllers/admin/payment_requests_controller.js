"use strict";

angular.module("adminApp").controller("PaymentRequestsController", PaymentRequestsController);
PaymentRequestsController.$inject = ["$scope", "$location", "checkValidDateFunction", "paymentRequestService", "toaster"];

function PaymentRequestsController($scope, $location, checkValidDateFunction, paymentRequestService, toaster) {
  var vm = this;
  var DATETIME_FIELDS = ["created_date_start", "created_date_end", "transfer_date_start", "transfer_date_end"];
  vm.$scope = $scope;
  vm.toasterTimeout = 6200;
  vm.hasRecord = false;
  vm.selectAllPaymentRequests = false;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.params = $location.search();
  var CSV_TYPE = "csv";
  var $msgModal = angular.element("#export-in-progress-dialog");

  vm.init = function() {
    DATETIME_FIELDS.forEach(function(attr) {
      if (vm.params[attr]) {
        $("." + attr).datepicker("setDate", vm.params[attr]);
      };
    });
    if (_.isEmpty(vm.params.page)) vm.params.page = 1;
    if (_.isEmpty(vm.params.per_page)) vm.params.per_page = vm.perPageSettings[0];
    vm.process_statuses = angular.element("#process_statuses").data("infos");
    vm.process_statuses_labels = angular.element("#process_statuses_labels").data("labels");
    vm.processStatusSelected = Object.keys(vm.process_statuses)[0];
    initChkSearch();
    vm.params.desc = true;
    vm.params.order_key = 'created_at';
    vm.refresh(true, true);
    vm.currentParams = angular.copy(vm.params);
    getDefaultTransferDate();
  }

  vm.initDataPaymentRequest = function(payment_request, status) {
    var isDisabled = vm.isDisableRadioChangeStatus(payment_request, status);
    payment_request["disabled_" + status] = isDisabled;
    if (vm.isEnableModalChangeStatus) return;
    vm.isEnableModalChangeStatus = !isDisabled;
  }

  function getDefaultTransferDate() {
    paymentRequestService.getDefaultTransferDate().then(function mySuccess(res) {
      vm.defaultTransferDate = res.data.default_date;
      vm.transferDate = vm.defaultTransferDate;
    });
  }

  $scope.formatCurrency = function(price) {
    return price.toLocaleString();
  };

  function initChkSearch() {
    vm.chk_search = {};
    if (_.isEmpty(vm.params.process_statuses)) return;
    var arrProcessStatus = vm.params.process_statuses.split(",");
    _.forEach(vm.process_statuses, function(value, key) {
      if (_.includes(arrProcessStatus, value.toString())) vm.chk_search[key] = true;
    });
  }

  vm.refresh = function(resetPage, isUseSearchCondition) {
    var paramSearch = isUseSearchCondition ? vm.params : formatPerPageParam(vm.currentParams);
    setParamsCheckboxSearch();
    vm.isEnableModalChangeStatus = false;
    paymentRequestService.loadDataPaymentRequest(paramSearch).then(function mySuccess(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.payment_requests.length);
      $location.search(paramSearch).replace();
      vm.currentParams = angular.copy(paramSearch);
      vm.selectAllPaymentRequests = false;
      vm.payment_requests_original = angular.copy(vm.payment_requests);
    }, function myError() {
      vm.payment_requests = [];
    });
  }

  vm.formatDate = function(date) {
    if (!_.isEmpty(date)) return date.replace(/-/g, "/");
  }

  vm.changePerPage = function() {
    vm.refresh();
  }

  vm.openModalChangeStatus = function() {
    if (vm.isEnableModalChangeStatus)
      $("#modal-status-confirm").modal("show");
  }

  vm.updateProcessStatus = function() {
    $(".disable-submit-btn").prop("disabled", true);
    paymentRequestService.listValidUpdateStatus({ids: getIdsPaymentRequests(), status: vm.processStatusSelected})
      .then(function mySuccess(res) {
      _.forEach(vm.payment_requests, function(payment_request) {
        if (_.includes(res.data.ids, payment_request.id))
          payment_request.process_status = vm.processStatusSelected;
      });
      $("#modal-status-confirm").modal("hide");
      $(".disable-submit-btn").prop("disabled", false);
    });
  }

  vm.savePaymentRequests = function() {
    $(".disable-submit-btn").prop("disabled", true);
    paymentRequestService.updatePaymentRequests(getRequestPaymentParams()).then(function mySuccess(res) {
      $("#modal-confirm-save").modal("hide");
      if (res.data.status){
        toaster.pop("success", "", I18n.t("admin.payment_request.update.success"));
        vm.processStatusSelected = Object.keys(vm.process_statuses)[0];
        vm.refresh();
      } else {
        $("#modal-title-export-name").text("");
        $("#modal-body-export-name").text(res.data.message);
        $msgModal.modal("show");
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  }

  function getRequestPaymentParams() {
    var requestGroupByStatus = {};
    _.forEach(vm.process_statuses, function(value, key) {
      var payments = _.map(vm.payment_requests, function(payment_request) {
        if (_.isEqual(payment_request.process_status, key)) return payment_request.id;
      });

      requestGroupByStatus[key] = _.compact(payments);
    });
    return requestGroupByStatus;
  }

  function setParamsCheckboxSearch() {
    var ids = [];
    _.forEach(vm.process_statuses, function(value, key) {
      if (vm.chk_search[key])
        ids.push(value);
    });
    vm.params.process_statuses = ids.join();
  }

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !vm.params.desc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !!vm.params.desc
    }
  };

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
      vm.currentParams.desc = !vm.currentParams.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
      vm.currentParams.desc = true;
      vm.currentParams.order_key = column;
    }
    vm.refresh(false, false);
  };

  function formatPerPageParam(currentParams) {
    currentParams.per_page = vm.params.per_page;
    currentParams.page = vm.params.page;
    return currentParams;
  };

  vm.checkAllChkSearch = function(allField) {
    _.forEach(vm[allField], function(value, key) {
      vm.chk_search[key] = vm["search_all_" + allField];
    });
  }

  vm.setStatusAll = function(allField, field) {
    if (vm.chk_search[field]) return;
    vm["search_all_" + allField] = false;
  }

  $('.staff-selectbox').select2({
    width: '100%',
    ajax: {
      url: '/payment_requests/load_data_staff',
      dataType: 'json',
      data: function(params) {
        return {search_content: params.term, staff_selected: vm.params.staff_id}
      },
      processResults: function(data) {
        return {results: data.staffs};
      }
    },
    templateResult: formatResult,
    templateSelection: formatSelection,
  }).trigger('change');

  function formatResult(staff) {
    return staff.account_name;
  }

  function formatSelection(staff) {
    return staff.text || staff.account_name;
  }

  vm.actionExport = function() {
    $(".disable-submit-btn").prop("disabled", true);
    var requestPaymentIds = getIdsPaymentRequests();
    toggleInValidTransferDate("", "removeClass");

    paymentRequestService.checkValidToDownload(vm.params)
      .then(function mySuccess(res) {
      if (res.data.status){
        if (res.data.warning)
          $("#modal-warning-export").modal("show");
        else
          $("#modal-confirm-export").modal("show");
      } else {
        $("#modal-title-export-name").text(I18n.t("admin.staff.export.cant_export"));
        $("#modal-body-export-name").text(I18n.t("admin.payment_request.errors.trade_failed_to_trade_success"));
        $msgModal.modal("show");
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  }
  vm.continueExport = function() {
    $("#modal-warning-export").modal("hide");
    $("#modal-confirm-export").modal("show");
  }

  $(".btn-export-payment-requests").on("click", function(e){
    $(".disable-submit-btn").prop("disabled", true);
    paymentRequestService.checkTransferDate({transfer_date: vm.transferDate}).then(function mySuccess(res) {
      if (res.data.invalid_field) {
        toggleInValidTransferDate(res.data.message, "addClass");
      } else {
        $.asyncDownload(e, "payment_requests", JSON.stringify({search_params: vm.params,
          transfer_date: vm.transferDate, is_history: false}), CSV_TYPE,
          {action: "payment_requests_export"});
        $("#modal-confirm-export").modal("hide");
        vm.transferDate = vm.defaultTransferDate;
        vm.payment_requests_original = angular.copy(vm.payment_requests);
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  });

  function getIdsPaymentRequests() {
    return _.map(vm.payment_requests, function(payment_request) {
      return payment_request.id;
    });
  }

  vm.actionExportHistory = function(e) {
    $.asyncDownload(e, "payment_requests", JSON.stringify({search_params: vm.params,
      is_history: true, transfer_date: vm.transferDate}), CSV_TYPE, {}, "export_history");
  }

  vm.isDisableRadioChangeStatus = function(payment_request, status) {
    var payment_original = _.find(vm.payment_requests_original, function(payment) {
      return _.isEqual(payment_request.id, payment.id);
    });

    var isEnable = payment_original && payment_original["is_trade_success?"] &&
      _.includes(["trade_success", "trade_failed"], status);

    return !isEnable || payment_original["is_locked_or_trade_failed?"] || payment_original["is_migrate"];
  }

  function toggleInValidTransferDate(message, type){
    $(".message-invalid-transfer-date").html(message);
    $(".label-validate-transfer-date")[type]("valid-transfer-date-label");
    $(".input-validate-transfer-date")[type]("valid-transfer-date-input");
  }

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  }

  vm.reschedulePaymentRequests = function() {
    var selectedItems = _.filter(vm.payment_requests, {select: true});
    if (_.isEmpty(selectedItems)) {
      $("#modal-no-items-selected").modal("show");
      return;
    }
    var selectedItemIds = _.map(selectedItems, "id");
    $("#spinner").removeClass("ng-hide");
    paymentRequestService.reschedule({payment_request_ids: selectedItemIds})
      .then(function(res) {
      var data = res.data;
      vm.rescheduleResultMessage = data.message;
      if (data.status) {
        _.forEach(vm.payment_requests, function(paymentRequest) {
          paymentRequest.select = false;
        });
      }
      $("#spinner").addClass("ng-hide");
      $("#modal-reschedule-result").modal("show");
    });
  }
}
