"use strict";

angular.module("adminApp")
  .controller("CorporationListController", CorporationListController);
CorporationListController.$inject = ["$location", "corporationListService"];

function CorporationListController($location, corporationListService) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  var requestParams = $location.search();
  var SEARCH_CONDITION_TYPE = "admin_corporation_search_conditions";
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  init();

  vm.params = {
    page: requestParams.page,
    keyword: requestParams.keyword,
    type: requestParams.type
  };

  vm.refresh = function(resetPage, isSave) {
    if (resetPage) {
      vm.params.page = 1;
    }
    vm.params.per_page = vm.params.per_page || vm.perPageSettings[0];
    if (_.isEqual(vm.currentParams, vm.params)) {return;}
    corporationListService.getCorporationList(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.corporations.length);
      vm.noRecord = !vm.hasRecord;
      if (isSave) {
        var searchConditionParams = _.omit(vm.params, "page", "limit");
        corporationListService.createSearchCondition({search: searchConditionParams, search_condition: SEARCH_CONDITION_TYPE});
      }
      $location.search(vm.params).replace();
      vm.currentParams = angular.copy(vm.params);
    }, function(error) {
    });
  };

  vm.changePerPage = function() {
    vm.refresh(false, true);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".corporation-list-result").offset().top
    }, 1000);
  };

  function init() {
    corporationListService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
      } else {
        vm.params.type = "corporation";
      }
      vm.refresh(true);
    });
  }
}
