angular.module("adminApp").controller("IndividualNumbersController", IndividualNumbersController);

IndividualNumbersController.$inject = ["individualNumberService", "$window"]

function IndividualNumbersController(individualNumberService, $window) {
  var vm = this;
  vm.hasRecord = false;
  vm.dates = [];
  CSV_TYPE = "csv";
  DEFAULT_PAGE = 1;
  DEFAULT_PER_PAGE = 10;
  STATUSES = ["confirming_number", "approved_number", "returned_number"];
  I18n = I18n.t("admin.individual_number.individual_number_list.search_form.statuses");
  vm.params = {};
  vm.exported_params = {};
  vm.partners = [];
  vm.statuses = {};
  vm.returnReason = "";
  vm.init = function() {
    initPage();
    vm.setStatusDefault();
    vm.search();
  }

  vm.setStatusDefault = function() {
    vm.statuses.confirming_number = true;
  }

  vm.search = function(isSearch) {
    $("#spinner").removeClass("ng-hide");
    if (isSearch) {
      vm.params.page = DEFAULT_PAGE;
    }
    vm.params.my_number_status_id = formatStatusBeforeRequest();
    individualNumberService.getIndividualNumbers({search: vm.params}).then(function(res){
      $("#spinner").addClass("ng-hide");
      var data = res.data;
      if(data.status) {
        vm.partners = data.partners;
        if(vm.partners.length != 0) {
          vm.hasRecord = true;
          vm.total_items = data.total_items;
        } else {
          vm.hasRecord = false;
        }
      } else {
        vm.hasRecord = false;
      }
    })
  }

  vm.refresh = function() {
    vm.search(false);
  }

  vm.displayMyNumberStatus = function(status) {
    return I18n[status];
  }

  vm.approveIndividualNumber = function(staffID) {
    $('#spinner').removeClass('ng-hide');
    individualNumberService.approveIndividualNumber({staff_id: staffID}).then(function(res){
      var res = res.data;
      $('#spinner').removeClass('ng-hide');
      $window.location.href = res.redirect_path;
    });
  }

  vm.returnIndividualNumber = function(staffID) {
    $("#return-reason-modal").modal("show");
    $('#spinner').removeClass('ng-hide');
    individualNumberService.returnIndividualNumber({staff_id: staffID, return_reason: vm.returnReason})
      .then(function(res){
      var res = res.data;
      $('#spinner').removeClass('ng-hide');
      $window.location.href = res.redirect_path;
    });
  }

  vm.showReturnReasonModal = function() {
    $("#return-reason-modal").modal("show");
  }


  function formatStatusBeforeRequest() {
    if (vm.statuses == undefined) {
      return;
    }
    var statuses = [];
    if (vm.statuses.all) {
      statuses = STATUSES;
    }
    else {
      _.forEach(STATUSES, function(name){
        if(vm.statuses[name]) {
          statuses.push(name)
        }
      });
    }
    var statusesFormated = statuses.join(",")
    if (statusesFormated.length == 0) {
      return STATUSES.join(",")
    }
    return statusesFormated;
  }

  function initPage() {
    vm.params.page = DEFAULT_PAGE;
    vm.params.per_page = DEFAULT_PER_PAGE;
  }

  vm.exportIndividualNumbers = function(e) {
    vm.exported_params.staff_number = vm.params.staff_number;
    vm.exported_params.my_number_status_id = formatStatusBeforeRequest();
    $.asyncDownload(e, "individual_numbers", JSON.stringify(vm.exported_params), CSV_TYPE)
  }
}