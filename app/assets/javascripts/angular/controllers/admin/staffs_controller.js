"use strict";

angular.module("adminApp") .controller("StaffsController", StaffsController);

StaffsController.$inject = ["$location", "$scope", "staffRegistrationService", "stationService", "imageService",
  "staffDepartmentsService", "$timeout"];

function StaffsController($location, $scope, staffRegistrationService, stationService,
  imageService, staffDepartmentsService, $timeout) {
  var vm = this;
  vm.$scope = $scope;
  vm.staff = {};
  vm.oldStaff = {};
  vm.version_contents = {};
  vm.staff_levels = [];
  vm.staff_ranks = [];
  vm.enumRanks = I18n.t("enum_label.staff_rank.ranks");
  vm.enumLevels = I18n.t("enum_label.staff_level.levels");
  vm.staff_account_transfer = {};
  vm.modal_image_url = "";
  vm.changedImage = false;
  vm.modalConfirm = {
    isReject: true,
    rejectReason: "",
    rejectSubmited: false
  };
  vm.staff_departments = [];
  vm.departments = [];
  toastr.options = {
    "closeButton": true,
    "positionClass": "toast-top-center toast-format"
  }
  var staffId = angular.element("#staff_id").data("infos");
  var $residenceExpDate = angular.element("label[for=staff_residence_expiration_date]");
  vm.level_options = angular.element("#staff-levels").data("infos");

  var DATA_HOLDER_IDS = ["staff", "account", "residence_statues", "residence_permissions", "nationalities", "staff_versions",
    "japan", "other_nationality_id", "banks", "bank_branches", "staff_account_transfer",
    "current_rank", "current_level", "default_level", "staff_check_item", "staff_salary", "social_attributes_for_japanese",
    "social_attributes_for_foreigner", "verify_account_status", "reviewing_staff_registration_answer",
    "is_from_release_entry_v2", "recent_department"];
  var RESIDENCE_DATETIME_FIELDS = ["residence_validity", "residence_expiration_date",
    "residence_permission_validity"];
  var FOREIGNER_FIELDS = ["residence_status", "residence_card_name", "residence_validity", "residence_expiration_date",
    "residence_permission", "residence_permission_validity", "foreigner_note1", "foreigner_note2", "residence_number"];
  var STATION_ATTR_FIELDS = ["home_station", "school_station", "use_station1", "use_station2", "use_station3"];
  var ACCOUNT_ATTRS = ["name", "name_kana"];
  var ANSWER_ATTR = ['answer_1', 'answer_2', 'answer_3', 'answer_4', 'answer_5'];
  var PERSONAL_IMAGES_ATTRS = ["residence_img_front", "residence_img_back", "certificate_img"];
  var SPECIAL_CHARACTERS = "********";
  vm.insuranceSubsectionType = angular.element("#staff_staff_salary_attributes_insurance_subsection_type").val();
  vm.employmentInsuranceType = angular.element("#staff_staff_salary_attributes_employment_insurance_type").val();
  vm.params = {};
  vm.params.resgistration_answer = {};
  vm.params.reviewing_staff_registration_answer = {};
  vm.verify_password_error = false;
  vm.authenticated = false;
  var INSURANCE_SUBSECTION_TYPE = "subscription";
  var EMPLOYMENT_INSURANCE_TYPE = "e_i_target";
  var SOCIAL_ATTRIBUTE_JAPANESE_FIRST = "day_student";
  var SOCIAL_ATTRIBUTE_FOREIGNER_FIRST = "international_student";
  var OTHER_SOCIAL_ATTRIBUTE = "other";

  vm.$scope.$watch("vm.staff.nationality", function() {
    if(vm.staff.nationality == vm.japan.id || !vm.staff.nationality) {
      vm.disableForeignersInfos();
      vm.staff.other_nationality = "";
    } else {
      if (vm.staff.nationality != vm.other_nationality_id) vm.staff.other_nationality = "";
      else vm.staff.other_nationality = vm.oldStaff.other_nationality;
      vm.resetForeignersInfos();
    }
  });

  vm.$scope.$watch("vm.version_contents.nationality", function() {
    if(vm.version_contents.nationality == vm.japan.id || !vm.version_contents.nationality) {
      disableTab("nationality-tab-version");
    } else enableTab("nationality-tab-version");
  });

  vm.$scope.$watch("vm.verify_account_status", function() {
    if(vm.verify_account_status === "error") {
      $("#account_status").removeClass("alert-info");
      $("#account_status").addClass("alert-danger");
    }
  });

  vm.$scope.$watch("vm.staff_departments", function() {
    $timeout(function() {
      setDatePicker(".staff-date-picker");
      setSelect2StaffDepartment();
    }, true);
  });

  function activeTab(tab) {
    $('.nav-tabs a[href="#' + tab + '"]').tab('show');
  };

  function enableTab(tab) {
    if (isDisabledTab(tab)){
      $('.nav-tabs a[href="#' + tab + '"]').removeClass("admin-staff-tab-disabled");
    }
  }

  function disableTab(tab) {
    $('.nav-tabs a[href="#' + tab + '"]').addClass("admin-staff-tab-disabled");
  }

  function isDisabledTab(tab) {
    return $('.nav-tabs a[href="#' + tab + '"]').hasClass("admin-staff-tab-disabled");
  }

  function isActiveTab(tab) {
    return $('.nav-tabs a[href="#' + tab + '"]').parents("li").hasClass("active");
  }

  function formatDate(date, formatType) {
    if (_.isEmpty(date)) return "";
    return moment.parseZone(date).format(formatType);
  };

  vm.isStatusConfirming = function(status) {
    return status === "confirming";
  }

  vm.openInsuranceLossModal = function() {
    var today = new Date();
    var currentRetirementDate = new Date($("#staff_retirement_date").val());
    if (currentRetirementDate < new Date(today.getFullYear(), today.getMonth(), today.getDate()) &&
      isJoinedInsurance()) {
      $("#confirm-insurance-loss").modal("show");
    } else {
      vm.openSubmitModal();
    }
  }

  vm.beforeSubmit = function() {
    var $retirement_date = $("#staff_retirement_date");
    var current_retirement_date = "";
    if (!_.isEmpty(vm.staff.retirement_date)) current_retirement_date = vm.staff.retirement_date.replace(/-/g,"/");
    if ($retirement_date.val() === "" || $retirement_date.val() == current_retirement_date) {
      vm.openSubmitModal();
    } else {
      vm.openInsuranceLossModal();
    }
  }

  vm.openSubmitModal = function() {
    $("#confirm-modal").modal("show");
  }

  vm.initTabPanel = function() {
    if(!vm.staff.id) {
      disableTab("request-tab");
      activeTab("basic-tab");
    }
  }

  vm.classForNationalityTab = function() {
    if (vm.staff.nationality == vm.japan.id || !vm.staff.nationality) {
      return "admin-staff-tab-disabled";
    }
  }

  vm.classForAnswerTab = function() {
    if (!vm.is_from_release_entry_v2 || vm.is_from_release_entry_v2 == null) {
      return "admin-staff-tab-disabled";
    }
  }

  vm.disableForeignersInfos = function() {
    vm.resetResidenceFields(true);
    if(isActiveTab("nationality-tab")) {
      if(isDisabledTab("request-tab")) {
        activeTab("basic-tab");
      } else {
        activeTab("request-tab");
      }
    }
  }

  vm.resetForeignersInfos = function() {
    vm.resetResidenceFields(false);
  }

  vm.resetResidenceFields = function(isRemoveAll) {
    if(isRemoveAll){
      FOREIGNER_FIELDS.forEach(function(attr) {
        vm.staff[attr] = "";
      });
    } else {
      FOREIGNER_FIELDS.forEach(function(attr) {
        vm.staff[attr] = vm.oldStaff[attr];
      });
    }
    if (_.isEmpty(vm.staff.residence_permission)) {
      vm.staff.residence_permission = "not_selected";
    };
  }

  vm.$scope.$watch("vm.staff_version_selected", function() {
    if (!_.isUndefined(vm.staff_version_selected)) {
      staffRegistrationService.loadDataStaffVersion({staff_version_id: vm.staff_version_selected}).then(function mySuccess(response){
        vm.current_staff_version = response.data.staff_version;
        if (!_.isEmpty(vm.current_staff_version)){
          vm.version_contents = response.data.contents;
          formatLinesStations();
          vm.version_bank = _.filter(vm.banks, function(bank){
            return bank.id == vm.version_contents.staff_account_transfer.bank_id;
          })[0];
          vm.version_bank_branch = response.data.bank_branch;
          vm.setCurrentContract();
          if ($('#modal-show-version').hasClass('in')) return;
          $("#modal-show-version").modal("show");
        }
      });
    }
  });

  vm.setCurrentContract = function() {
    var contracts = vm.version_contents.staff_contract_histories || [];
    contracts = _.filter(contracts, function(contract) {
      return contract.contract_start_date;
    });
    contracts = contracts.reverse();
    if (contracts.length === 0) return;
    var currentContract = contracts[0];
    _.forEach(contracts, function(contract, index){
      if (moment(currentContract.contract_start_date).isBefore(moment(contract.contract_start_date))) {
        currentContract = contract;
      }
    });
    var contractDateAttrs = ["contract_start_date", "contract_end_date"];
    contractDateAttrs.forEach(function(attr) {
      if (currentContract[attr])
        vm.version_contents[attr] = moment.parseZone(currentContract[attr]).format(FULL_DATE_FORMAT);
    })
    vm.version_contents.indefinite_employment_flag = currentContract.indefinite_employment_flag;
    vm.version_contents.contract_type = currentContract.contract_type;
  }

  vm.checkBankAccName = function(field) {
    if (vm.isNew && (vm.staff_account_transfer["account_"+ field +"_touched"] == false)) {
      vm.staff_account_transfer["account_" + field] = vm.staff[field];
    }
  }

  vm.init = function(personalAttributes, isNewObject, residenceImgFront,
    residenceImgBack, certificateImg) {
    vm.isNew = $location.path().includes('new');
    if(checkDateLicense()) {
      toggleValidLicenseClass("addClass");
    }

    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });

    RESIDENCE_DATETIME_FIELDS.forEach(function(attr) {
      vm.staff[attr] = formatDate(vm.staff[attr], ORDER_DATEPICKER_FORMAT);
    });
    vm.oldStaff = angular.copy(vm.staff);
    vm.staff_level = vm.current_level ? angular.copy(vm.current_level) : angular.copy(vm.default_level);
    if (vm.isNew) {
      vm.staff_account_transfer.account_name_touched = false;
      vm.staff_account_transfer.account_name_kana_touched = false;
    } else {
      ACCOUNT_ATTRS.forEach(function(field) {
        vm.staff[field] = vm.account[field];
      })
    }
    if(!_.isNull(vm.staff.residence_status)){
      vm.checkResidenseRequired();
    };
    initSocialAttr();
    vm.residence_img_front = residenceImgFront;
    vm.residence_img_back = residenceImgBack;
    vm.certificate_img = certificateImg;
    vm.isNewObject = isNewObject;
    lockedPersonalInfo(personalAttributes, isNewObject);
  }

  function lockedPersonalInfo(personalAttributes, isNewObject) {
    if (!isNewObject) {
      _.forEach(personalAttributes, function(attributes, key){
        _.forEach(attributes, function(val){
          setPersonalInfo(key, val, SPECIAL_CHARACTERS, true);
          setPersonalInfoForMasterStaff(val, SPECIAL_CHARACTERS, true);
        });
      });
    }
  }

  vm.verifyAdmin = function() {
    $("#verify-admin-password-modal").modal("show");
  }

  vm.authenticate = function(){
    if (vm.isNewObject) {
      return true;
    }
    return vm.authenticated;
  }

  vm.viewStaffInfos = function(e) {
    var submitParams = {
      password: vm.verify_admin_password,
      staff_id: vm.staff.id
    }
    $("#spinner").removeClass("ng-hide");
    staffRegistrationService.viewStaffInfos(submitParams).then(function(res){
      var res = res.data;
      if (res.status) {
        $("#verify-admin-password-modal").modal("hide");
        vm.verify_admin_password = "";
        unLockedPersonalInfos(res.personal_infos);
        vm.staff.age = res.personal_infos.staff.age;
        unLockedRegistrationAnswer(res.personal_infos.registration_answer);
        vm.authenticated = true;
      } else {
        vm.verify_password_error = true;
        vm.verify_password_error_class = "has-error";
      }
      $("#spinner").addClass("ng-hide");
    });
  }

  vm.handleInputPassword = function(e) {
    var keyCode = e.keyCode || e.which;
    if (keyCode === 13) {
      vm.viewStaffInfos(e);
      return false;
    }
  }

  function unLockedPersonalInfos(personalInfos){
    _.forEach(personalInfos, function(data, key){
      _.forEach(data, function(info, keyData){
        if (keyData == "email") {
          info = data["account_email"];
        }
        setPersonalInfo(key, keyData, info, false);
        setPersonalInfoForMasterStaff(keyData, info, false);
      });
    });
  }

  function unLockedRegistrationAnswer(answer){
    if (answer == null || answer.upload_file_1.url == null) {
      return
    }
    $(".upload_file_1").attr("src", answer.upload_file_1.url);
  }

  function setPersonalInfo(key, keyData, value, disabled) {
    if (_.includes(PERSONAL_IMAGES_ATTRS, keyData)) {
      inputName = "."+keyData;
      if (typeof(value) == "object"){
        $(inputName).attr("src", value.url);
      } else {
        $(inputName).attr("src", vm[keyData]);
      }
    }
    else {
      if (key == "staff") {
        var inputName = "[name$='staff[" + keyData + "]']";
        setValueByElement($(inputName), disabled, keyData, value);
      }
    }
  }

  function setValueByElement(element, disabled, keyData, value) {
    var tagName = element.prop("tagName");
    switch (tagName) {
      case "SELECT":
        if (value == null && !disabled) {
          var selectedElement = element.children("option:first");
          selectedElement.text(I18n.t("common.please_select"));
        } else {
          var selectedElement = element.children("option:selected");
          selectedElement.text(value);
        }
        element.prop("disabled", disabled);
        break;
      case "INPUT":
        if ((keyData == "postal_code" || keyData == "emergency_postal_code") && disabled) {
          var postalCode = vm.staff[keyData].split("-");
          postalCode[1] = SPECIAL_CHARACTERS;
          element.attr("value", postalCode.join("-"));
        } else {
          element.attr("value", value);
        }
        element.prop("disabled", disabled);
        break;
      default:
        break;
    }
  }

  function setPersonalInfoForMasterStaff(keyData, info, disabled) {
    if (_.includes(PERSONAL_IMAGES_ATTRS, keyData)) {
      var reviewImageName = ".review_"+keyData;
      if (typeof(info) == "object"){
        $(reviewImageName).attr("src", info.url);
      } else {
        $(reviewImageName).attr("src", vm[keyData]);
      }
    } else {
      var element = $(".master_staff_"+keyData);
      setValueByElement(element, disabled, keyData, info);
      element.prop("disabled", true);
    }
  }

  function initSocialAttr() {
    vm.selectedNationality = vm.staff.nationality;
    vm.selectedSocialAttr = vm.staff.social_attribute;
    var isJapanese = vm.staff.nationality == vm.japan.id;
    setOptionsForSocialAttr(isJapanese);
    setSelectedForSocialAttr(vm.selectedNationality);
    setSocialAttributeOther(vm.staff.social_attribute);
  }

  vm.getBankBraches = function(bankId){
    staffRegistrationService.getBankBraches({bank_id: bankId}).then(function(res) {
      vm.bank_branches = res.data.bank_branches;
    });
  }

  vm.fileChanged = function(event) {
    var previewElm = event.target.getAttribute("target-preview");

    vm.changedImage = true;
    if (!event.target.files[0]) {
      var oldImage = $(previewElm).attr("data-old-image");
      vm.modal_image_url = oldImage;
      $(previewElm).attr("src", oldImage);
      vm.$scope.$apply(function() {
        vm.staff_account_transfer["passbook_pic"] = "";
      });
    } else {
      var reader = new FileReader();
      reader.onload = function(evt) {
        vm.modal_image_url = evt.target.result;
        $(previewElm).attr("src", evt.target.result);
        vm.$scope.$apply(function() {
          vm.staff_account_transfer["passbook_pic"] = evt.target.result;
        });
      }
      reader.readAsDataURL(event.target.files[0]);
    }
  }

  vm.isChekedCheckbox = function(array, key) {
    return _.includes(array, key)
  }

  $('#modal-show-version').on('hide.bs.modal', function () {
    vm.$scope.$apply(function() {
      vm.staff_version_selected = "";
    });
  });

  function formatLinesStations() {  
    stationService.getStation({"prefecture_id": vm.version_contents.prefecture_id}).then(function(res) {
      vm.stations = res.data.stations;
      vm.stations.forEach(function(station) {
        STATION_ATTR_FIELDS.forEach(function(attr) {
          if (vm.version_contents[attr + "_id"] == station.id || vm.version_contents[attr] == station.id) {
            vm.version_contents[attr + "_name"] = station.name;
          }
        });
      });
    });
  }

  $("#staff-checklist-license-depend").change(function(){
    if(checkDateLicense()) {
      toggleValidLicenseClass("addClass");
    } else {
      toggleValidLicenseClass("removeClass");
    }
  });

  function toggleValidLicenseClass(type){
    $("#check-valid-license-label")[type]('valid-license-label');
    $("#staff-checklist-license-depend")[type]('valid-license-date');
    $("#check-valid-license-date")[type]('valid-license-date');
  }

  function checkDateLicense() {
    var pickedDate = moment($("#staff-checklist-license-depend").val());
    var currentDate = moment(moment().format(FULL_DATE_FORMAT));
    if (currentDate.diff(pickedDate, 'day') > 0)
      return true;
    return false;
  }

  vm.openModalLevel = function() {
    if (_.isEqual(vm.staff_levels.length, 0)) {
      vm.initLevel = false;
      vm.level_params = {page: 1, per_page: ANGULAR_ITEMS_PER_PAGE[1]}
      refreshLevels();
    }
    $("#level-modal").modal("show");
  }

  vm.openModalRank = function() {
    if (_.isEqual(vm.staff_ranks.length, 0)) {
      vm.initRank = false;
      vm.rank_params = {page: 1, per_page: ANGULAR_ITEMS_PER_PAGE[1]}
      refreshRanks();
    }
    $("#rank-modal").modal("show");
  }

  vm.openModaHakenTypeHistory = function() {
    $("#haken-type-history-modal").modal("show");
  }

  function refreshRanks() {
    var params = {staff_id: vm.staff.id, page: vm.rank_params.page, per_page: vm.rank_params.per_page};
    staffRegistrationService.loadDataStaffRank(params).then(function mySuccess(res) {
      vm.staff_ranks = vm.staff_ranks.concat(res.data.staff_ranks);
      vm.rank_params.current_page = angular.copy(vm.rank_params.page);
      vm.rank_params.total_items = res.data.total_items;
      vm.initRank = true;
    });
  }

  function refreshLevels() {
    var params = {staff_id: vm.staff.id, page: vm.level_params.page, per_page: vm.level_params.per_page};
    staffRegistrationService.loadDataStaffLevel(params).then(function mySuccess(res) {
      vm.staff_levels = vm.staff_levels.concat(res.data.staff_levels);
      vm.level_params.current_page = angular.copy(vm.level_params.page);
      vm.level_params.total_items = res.data.total_items;
      vm.initLevel = true;
    });
  }

  vm.loadMoreRanks = function() {
    if (!vm.initRank) return;
    if (vm.staff_ranks.length < vm.rank_params.total_items && vm.rank_params.page == vm.rank_params.current_page) {
      vm.rank_params.page++;
      refreshRanks();
    }
  }

  vm.loadMoreLevels = function() {
    if (!vm.initLevel) return;
    if (vm.staff_levels.length < vm.level_params.total_items && vm.level_params.page == vm.level_params.current_page) {
      vm.level_params.page++;
      refreshLevels();
    }
  }

  vm.getImage = function(id, modelName, field) {
    if (!vm.changedImage){
      if (id == null || _.isUndefined(id)) return;
      var params = {model_name: modelName, field: field};
      imageService.getImage(id, params).then(function(res) {
        $scope.$applyAsync(function() {
          vm.modal_image_url = res.data.src;
        });
      })
    }
    $("#modal-image").modal("show");
  }

  vm.approve = function() {
    vm.modalConfirm = {isReject: false, rejectReason: ""};
    showConfirmDialog();
  };

  vm.changeNationality = function() {
    var isJapanese = vm.staff.nationality == vm.japan.id;
    setOptionsForSocialAttr(isJapanese);
    setSelectedForSocialAttr(vm.staff.nationality);
  }

  vm.changeSocialAttribute = function() {
    setSocialAttributeOther(vm.staff.social_attribute);
  }

  function setOptionsForSocialAttr(isJapanese){
    vm.optionsForSocialAttr = isJapanese ? vm.social_attributes_for_japanese : vm.social_attributes_for_foreigner;
  }

  function setSelectedForSocialAttr(selectedNationality) {
    if (selectedNationality == null) {
      vm.staff.social_attribute = SOCIAL_ATTRIBUTE_FOREIGNER_FIRST;
    } else {
      if (selectedNationality == vm.selectedNationality) {
        vm.staff.social_attribute = vm.selectedSocialAttr;
      } else {
        vm.staff.social_attribute = selectedNationality == vm.japan.id ? SOCIAL_ATTRIBUTE_JAPANESE_FIRST : SOCIAL_ATTRIBUTE_FOREIGNER_FIRST;
      }
    }
  }

  function setSocialAttributeOther(selectedSocialAttr) {
    if(selectedSocialAttr == OTHER_SOCIAL_ATTRIBUTE) {
      $("#staff_social_attribute_other").prop("disabled", false);
    } else {
      $("#staff_social_attribute_other").prop("disabled", true);
      vm.staff.social_attribute_other = "";
    }
  }

  vm.reject = function() {
    vm.modalConfirm = {isReject: true, rejectSubmited: false};
    showConfirmDialog();
  };

  function showConfirmDialog() {
    $(".request-modal-confirm").modal("show");
  }

  vm.isShowErrorMsgReject = function() {
    return vm.modalConfirm.isReject && vm.modalConfirm.rejectSubmited;
  };

  vm.submit = function(isReject) {
    vm.isSubmitting = true;
    if (isReject) {
      if (!vm.modalConfirm.rejectReason) {
        vm.modalConfirm.rejectSubmited = true;
        vm.isSubmitting = false;
        return;
      }
      staffRegistrationService.rejectRequestChangeProfile({id: staffId, rejected_reason: vm.modalConfirm.rejectReason})
        .then(function(res) {
        vm.modalConfirm.rejectSubmited = false;
        if (res.data.status) {
          location.reload();
        } else {
          vm.isSubmitting = false;
        }
      });
    } else {
      staffRegistrationService.approveRequestChangeProfile({id: staffId}).then(function(res) {
        if (res.data.status) {
          location.reload();
        } else {
          vm.isSubmitting = false;
        }
      });
    }
  };

  vm.checkResidenseRequired = function(){
    if (vm.staff.residence_status != NOT_REQUIRED_RESIDENSE_STATUS_ID){
      toggleResidenseRequired($residenceExpDate, true);
    }else{
      toggleResidenseRequired($residenceExpDate, false);
    }
  };

  vm.getBankCode = function(bank_id) {
    var bank = _.find(vm.banks, function(bank) {return bank.id == bank_id});
    if (_.isUndefined(bank)) return "";
    return bank.code;
  }

  vm.getBankBranchCode = function(branch_id) {
    var branch = _.find(vm.bank_branches, function(branch) {return branch.id == branch_id});
    if (_.isUndefined(branch)) return "";
    return branch.code;
  }

  function isJoinedInsurance() {
    return vm.insuranceSubsectionType == INSURANCE_SUBSECTION_TYPE ||
      vm.employmentInsuranceType == EMPLOYMENT_INSURANCE_TYPE;
  };

  function toggleResidenseRequired(el, isRequired){
    if (isRequired){
      el.text(I18n.t("activerecord.attributes.staff.residence_expiration_date").concat(I18n.t("common.label.required")));
      el.addClass('required');
    }else {
      el.text(I18n.t("activerecord.attributes.staff.residence_expiration_date"));
      el.removeClass('required');
    };
  };

  $(".body_staff_education_backgrounds").on("cocoon:after-insert", function(_, addedTask){
    var elements = addedTask.find(".form-authenticate");
    elements.remove();
  });

  vm.openStaffDepartmentModal = function() {
    $("#modal-staff-department").modal("show");
    getDepartments();

    staffDepartmentsService.getStaffDepartments(staffId).then(function(response) {
      if (response.data.status) {
        vm.staff_departments = response.data.staff_departments;
        vm.countNewDepartment = 0;
      }
    });
  };

  $("#modal-staff-department").on('hidden.bs.modal', function () {
    vm.staff_departments = [];

    if (!!vm.hasUpdatedDepartment) {
      staffDepartmentsService.fetchRecentDepartment(staffId).then(function(response) {
        vm.recent_department = response.data;
        vm.hasUpdatedDepartment = false;
      })
    }
  });

  vm.editDepartment = function(staffDepartmentId) {
    enableInputStaffDepartment(staffDepartmentId);

    var staff_department = _.find(vm.staff_departments, function(department) {return department.id == staffDepartmentId});
    staff_department.isEnabled = true;
  };

  vm.deleteDepartment = function(staffDepartmentId) {
    var staff_department = _.find(vm.staff_departments, function(department) {return department.id == staffDepartmentId});
    staff_department.isDelete = true;
    staff_department.isEnabled = true;
  };

  vm.updateDepartment = function(staffDepartmentId) {
    var staff_department = _.find(vm.staff_departments, function(department) {return department.id == staffDepartmentId});

    if (!!!staff_department) return

    var params = {
      department_id: staff_department.department_id,
      affiliation_date: staff_department.affiliation_date,
    }

    $("#spinner").removeClass("ng-hide");
    staffDepartmentsService.updateStaffDepartment(staffId, staffDepartmentId, params).then(function(response) {
      if (response.data.status) {
        staff_department = response.data.staff_department;
        disableInputStaffDepartment(staff_department.id);
        vm.hasUpdatedDepartment = true;
        toastr.success(response.data.message);
      } else {
        toastr.error(response.data.message)
      }
    }, function(error) {
      toastr.error(error.statusText)
    }).then(function(){
      $("#spinner").addClass("ng-hide");
    });
  };

  vm.confirmDeleteDepartment = function(staffDepartmentId) {
    var staff_department = _.find(vm.staff_departments, function(department) {return department.id == staffDepartmentId});

    if (!!!staff_department) return

    $("#spinner").removeClass("ng-hide");
    staffDepartmentsService.deleteStaffDepartment(staffId, staffDepartmentId).then(function(response) {
      if (response.data.status) {
        staff_department = response.data.staff_department;
        vm.hasUpdatedDepartment = true;
        removeItemFromStaffDepartments(staff_department.id)
        toastr.success(response.data.message);
      } else {
        toastr.error(response.data.message)
      }
    }, function(error) {
      toastr.error(error.statusText)
    }).then(function(){
      $("#spinner").addClass("ng-hide");
    });
  }
  
  vm.cancelEditDepartment = function(staffDepartmentId) {
    disableInputStaffDepartment(staffDepartmentId);
  };

  function getDepartments() {
    if (vm.departments.length > 0) return;

    staffDepartmentsService.getDepartments().then(function(response) {
      vm.departments = response.data;
    });
  };

  function setDatePicker(element) {
    $(element).datepicker({
      autoclose: true,
      todayBtn: true,
      todayHighlight: true,
      format: DEFAULT_DATE_TIME_FORMAT
    });
  };

  function setSelect2StaffDepartment() {
    var select2Options = {
      width: "100%",
    };
    
    $(".staff-departments .select-department").select2(select2Options);
  };

  function enableInputStaffDepartment(staffDepartmentId) {
    $("#department-name-" + staffDepartmentId).prop("disabled", false);
    $("#affiliation-date-" + staffDepartmentId).prop("disabled", false);
  };

  function disableInputStaffDepartment(staffDepartmentId) {
    $("#department-name-" + staffDepartmentId).prop("disabled", true);
    $("#affiliation-date-" + staffDepartmentId).prop("disabled", true);

    var staff_department = _.find(vm.staff_departments, function(department) {return department.id == staffDepartmentId});
    staff_department.isEnabled = false;
    staff_department.isDelete = false;
  };

  function replaceStaffDepartment(oldStaffDepartment, newStaffDepartment) {
    $("#department-name-" + oldStaffDepartment.id).attr("id",  "department-name-" + newStaffDepartment.id);
    $("#affiliation-date-" + oldStaffDepartment.id).attr("id", "affiliation-date-" + newStaffDepartment.id);

    var index = vm.staff_departments.indexOf(oldStaffDepartment);
    vm.staff_departments[index] = newStaffDepartment;
  };

  function removeItemFromStaffDepartments(staffDepartmentId) {
    var staff_departments = _.filter(vm.staff_departments, function(department) {
      return department.id !== staffDepartmentId;
    });

    vm.staff_departments = staff_departments;
  };

  function addItemToStaffDepartments(newStaffDepartment) {
    vm.staff_departments.push(newStaffDepartment);

    $timeout(function() {
      enableInputStaffDepartment(newStaffDepartment.id);
      setDatePicker(".staff-date-picker");
      setSelect2StaffDepartment();
    }, true);
  };

  vm.addDepartment = function() {
    vm.countNewDepartment++;

    var newStaffDepartment = {
      id: "new_item_" + vm.countNewDepartment,
      department_id: null,
      affiliation_date: "",
      isNewItem: true
    };

    addItemToStaffDepartments(newStaffDepartment);
  };

  vm.cancelCreateDepartment = function(staffDepartmentId) {
    removeItemFromStaffDepartments(staffDepartmentId)
  };

  vm.createDepartment = function(staffDepartmentId) {
    var newStaffDepartment = _.find(vm.staff_departments, function(department) {return department.id == staffDepartmentId});

    if (!!!newStaffDepartment) return;
    
    var params = {
      department_id: newStaffDepartment.department_id,
      affiliation_date: newStaffDepartment.affiliation_date,
    };

    $("#spinner").removeClass("ng-hide");
    staffDepartmentsService.createStaffDepartment(staffId, params).then(function(response) {
      if (response.data.status) {
        disableInputStaffDepartment(newStaffDepartment.id);
        replaceStaffDepartment(newStaffDepartment, response.data.staff_department);
        vm.hasUpdatedDepartment = true;
        toastr.success(response.data.message);
      } else {
        toastr.error(response.data.message)
      }
    }, function(error) {
      toastr.error(error.statusText)
    }).then(function() {
      $("#spinner").addClass("ng-hide");
    });
  };
}
