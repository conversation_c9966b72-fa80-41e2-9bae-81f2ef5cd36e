'use strict';

angular.module('adminApp')
  .controller('PeriodRateCustomByDateController', PeriodRateCustomByDateController);
  PeriodRateCustomByDateController.$inject = ['periodRateCreateNewCustomService', '$location'];

function PeriodRateCustomByDateController(periodRateCreateNewCustomService, $location) {
  var vm = this;
  var url = $location.absUrl();
  vm.peakPeriodId = url.split('/').pop();
  vm.errorMessages = {};

  vm.init = function () {
    vm.search();
  };

  var editModal = $('#modal-edit-period-rate');
  var newModal = $('#modal-new-period-rate');
  var deleteModal = $('#modal-delete-period-rate');

  editModal.on("hidden.bs.modal", function () {
    vm.selectedPeriodRate = {};
    vm.errorMessages = {};
    vm.showError = false;
  });

  newModal.on("hidden.bs.modal", function () {
    vm.newPeriodRate = {};
    vm.errorMessages = {};
    vm.showError = false;
  });

  deleteModal.on("hidden.bs.modal", function () {
    vm.selectedPeriodRate = {};
    vm.prefectureRate = {};
  });

  vm.search = function () {
    periodRateCreateNewCustomService.getCustomRatesByDate(vm.peakPeriodId).then(function (res) {
      vm.periodRates = res.data.period_rates;
      vm.peakPeriod = res.data.peak_period;
    });
  };

  vm.editPeriodRate = function (periodRateId) {
    periodRateCreateNewCustomService.editPeriodRate(periodRateId).then(function (res) {
      vm.corporations = [
        {
          id: res.data.corporation_id,
          full_name: res.data.corporation_name
        }
      ];
      vm.selectedPeriodRate = res.data;
      editModal.modal("show");
    }).then(function (error) {
      toastr.error(error.statusText);
    });
  };

  vm.updatePeriodRate = function (periodRateId) {
    var is_all = vm.selectedPeriodRate.corporation_id === -1 || vm.selectedPeriodRate.corporation_id === null;
    var params = {
      corporation_id: is_all ? null : vm.selectedPeriodRate.corporation_id,
      is_all: is_all,
      unit_rate: vm.selectedPeriodRate.unit_rate,
      order_rate: vm.selectedPeriodRate.order_rate,
      prefecture_id: vm.selectedPeriodRate.prefecture_id,
      peak_period_id: vm.peakPeriodId
    }

    checkValidInput(params);
    if (vm.showError) return;

    showSpinner();
    disableSubmitButton("btn-update");
    periodRateCreateNewCustomService.updatePeriodRate(periodRateId, params).then(function (res) {
      if (res.data.status) {
        toastr.success(res.data.message);
        location.reload();
      } else {
        vm.errorMessages = res.data.message;
        vm.showError = true;
        enableSubmitButton("btn-update");
      };
    }, function (error) {
      toastr.error(error.statusText);
      enableSubmitButton("btn-update");
    }).then(function () {
      hideSpinner();
    });
  };

  vm.addNewPeriodRate = function(prefecture) {
    vm.newPeriodRate = {
      prefecture_id: prefecture.prefecture_id,
      prefecture_name: prefecture.prefecture_name,
      is_default: false
    }

    newModal.modal("show");
  }

  vm.createPeriodRate = function() {
    var is_all = vm.newPeriodRate.corporation_id === -1;
    var params = {
      unit_rate: vm.newPeriodRate.unit_rate,
      order_rate: vm.newPeriodRate.order_rate,
      prefecture_id: vm.newPeriodRate.prefecture_id,
      is_default: vm.newPeriodRate.is_default,
      corporation_id: is_all ? null : vm.newPeriodRate.corporation_id,
      is_all: is_all,
      peak_period_id: vm.peakPeriodId
    }

    checkValidInput(params);
    if (vm.showError) return;

    disableSubmitButton("btn-submit");
    showSpinner();
    periodRateCreateNewCustomService.periodRateCreateNewCustomService(params).then(function (res) {
      if (res.data.status) {
        toastr.success(res.data.message);
        location.reload();
      } else {
        vm.errorMessages = res.data.message;
        vm.showError = true;
        enableSubmitButton("btn-submit");
      };
    }, function (error) {
      toastr.error(error.statusText);
      enableSubmitButton("btn-submit");
    }).then(function() {
      hideSpinner();
    });
  };

  vm.confirmDelete = function(selectedRateId) {
    periodRateCreateNewCustomService.getDestroyedWarning(selectedRateId).then(function(res) {
      var response = res.data
      if (response.status) {
        vm.selectedPeriodRate = response.current_period_rate;
        vm.newRate = response.new_period_rate;
        deleteModal.modal("show");
      }
    }, function(error) {
      toastr.error(error.statusText);
    });
  };

  vm.deletePeriodRate = function(periodRateId) {
    disableSubmitButton("confirm-delete");
    periodRateCreateNewCustomService.deletePeriodRate(periodRateId).then(function(res) {
      if (res.data.status) {
        toastr.success(res.data.message);
        location.reload();
      } else {
        toastr.error(res.data.message);
        enableSubmitButton("confirm-delete");
      }
    }, function(error) {
      toastr.error(error.statusText);
      enableSubmitButton("confirm-delete");
    });
  };

  function checkValidInput(params) {
    vm.showError = true;

    if (params.unit_rate == null) {
      vm.errorMessages.unit_rate = [errorMessage('period_rate', 'unit_rate', 'required')];
    }

    if (params.order_rate == null) {
      vm.errorMessages.order_rate = [errorMessage('period_rate', 'order_rate', 'required')];
    }

    if (Object.keys(vm.errorMessages).length !== 0) return;

    vm.showError = false;
  }

  function errorMessage(model, attributes_name, type) {
    var attributeName = I18n.t(["activerecord.attributes", model, attributes_name].join("."));
    return I18n.t("activerecord.errors.messages." +  type, {attribute: attributeName});
  };

  vm.clearErrorMessages = function (type) {
    delete vm.errorMessages[type];
  }

  function showSpinner() {
    $("#spinner").removeClass("ng-hide");
  };

  function hideSpinner() {
    $("#spinner").addClass("ng-hide");
  }

  function disableSubmitButton(buttonId) {
    $("#" + buttonId).prop("disabled", true);
  }

  function enableSubmitButton(buttonId) {
    $("#" + buttonId).prop("disabled", false);
  }
}