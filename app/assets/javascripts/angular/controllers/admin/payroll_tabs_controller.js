"use strict";

angular.module("adminApp")
  .controller("PayrollTabsController", PayrollTabsController);
PayrollTabsController.$inject = ["payrollService"];

function PayrollTabsController(payrollService) {
  var vm = this;
  var DATA_HOLDER_IDS = ["payroll", "staff_id", "payroll_id"];
  var PAYMENT_TOTAL_AMOUNT_FIELDS = ["basic_salary_price", "overtime_amount", "working_night_amount", "paid_amount",
    "payment_field_1", "payment_field_2", "payment_field_3", "payment_field_4", "payment_field_5", "payment_field_6",
    "payment_field_7", "payment_field_8", "payment_field_9", "payment_field_10", "payment_field_11", "payment_field_12",
    "payment_field_13"];
  var TAX_EXEMPTION_AMOUNT_FIELDS = ["payment_field_1", "payment_field_10", "payment_field_11", "payment_field_12"];
  var DEDUCTION_TOTAL_AMOUNT_FIELDS = ["deduction_field_1", "deduction_field_2", "deduction_field_3", "deduction_field_4", "deduction_field_5",
    "deduction_field_6", "deduction_field_7", "deduction_field_8", "deduction_field_9", "deduction_field_10",
    "deduction_field_11", "deduction_field_12", "deduction_field_13", "deduction_field_21"];
  var ALLOWENCE_FIELDS = TAX_EXEMPTION_AMOUNT_FIELDS.concat(["payment_field_2", "payment_field_3",
    "payment_field_4", "payment_field_5", "payment_field_6", "payment_field_7", "payment_field_8",
    "payment_field_9", "payment_field_13"]);
  var $msgModal = angular.element("#payroll-message-modal");
  var INIT_ZERO_VALUE = ["ot45_amount", "ot60_amount"];

  vm.init = function() {
    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });
    vm.otherPayroll = angular.element("#other-payroll").data("infos");
    vm.payroll.wage_payment_date = moment(vm.payroll.wage_payment_date).format(FULL_DATE_FORMAT);
    vm.currentPayroll = angular.copy(vm.payroll);
    vm.isNotificationSent = angular.element("#notification-sent").data("infos");
    INIT_ZERO_VALUE.forEach(function(attr) {
      if (!vm.payroll[attr]) vm.payroll[attr] = 0;
    });
  };

  vm.disableWagePaymentDate = function() {
    return moment(vm.currentPayroll.wage_payment_date) < moment() || vm.isNotificationSent;
  }

  vm.calculateAllowedDayOffCount = function() {
    vm.payroll.allowed_day_off_count = Number(vm.payroll.moved_day_off_count) - Number(vm.payroll.deleted_day_off_count) +
      Number(vm.payroll.granted_day_count);
    vm.calculateRemainingHolidayCount();
  };

  vm.calculateRemainingHolidayCount = function() {
    vm.payroll.remaining_holiday_count = Number(vm.payroll.allowed_day_off_count) - Number(vm.payroll.used_day_off_count);
  };

  vm.calculatePaymentTotalAmount = function() {
    vm.payroll.payment_total_amount = calculateTotal(PAYMENT_TOTAL_AMOUNT_FIELDS);
    vm.calculateSubtractionAmount();
  };

  vm.calculateTaxExemptionAmount = function() {
    vm.payroll.tax_exemption_amount = calculateTotal(TAX_EXEMPTION_AMOUNT_FIELDS);
    vm.calculatePaymentTotalAmount();
  };

  vm.calculateDeductionTotalAmount = function() {
    vm.payroll.deduction_total_amount = calculateTotal(DEDUCTION_TOTAL_AMOUNT_FIELDS);
    vm.calculateSubtractionAmount();
  };

  vm.calculateSubtractionAmount = function() {
    vm.payroll.payment_subtraction_amount = vm.payroll.payment_total_amount - vm.payroll.deduction_total_amount;
  }

  vm.classForChangedField = function(field) {
    return {"field-changed": isDifferentField(field)};
  };

  function isDifferentField(field) {
    return vm.payroll[field] != vm.currentPayroll[field] ||
      (vm.otherPayroll && vm.payroll[field] != vm.otherPayroll[field])
  }

  vm.save = function() {
    vm.isSaving = true;
    payrollService.updatePayroll({payroll: vm.payroll}, vm.staff_id, vm.payroll_id).then(function(res) {
      vm.isSaving = false;
      $.lawsonAjax(res.data);
    });
  };

  vm.cancelCalculate = function() {
    vm.isSaving = true;
    payrollService.cancelCalculate(vm.staff_id, vm.payroll_id).then(function(res) {
      vm.isSaving = false;
      if (res.data.status) {
        window.location = res.data.redirect_path;
      } else {
        vm.msgContent = res.data.message;
        $msgModal.modal("show");
      }
    });
  };

  vm.openModalConfirmCalcelCalculation = function() {
    payrollService.checkIsAbleToCancelPayroll({staff_ids: [vm.payroll.staff_id], payroll_date: vm.payroll.payroll_date}).then(function(res) {
      if (res.data.status) {
        $("#cancel-cal-modal").modal("show");
      } else {
        vm.msgContent = res.data.message;
        $msgModal.modal("show");
      }
    });
  }

  function calculateTotal(fields) {
    var total = 0;
    fields.forEach(function(field) {
      total += _.isNaN(Number(vm.payroll[field])) ? 0 : Number(vm.payroll[field]);
    });
    return total;
  }
}
