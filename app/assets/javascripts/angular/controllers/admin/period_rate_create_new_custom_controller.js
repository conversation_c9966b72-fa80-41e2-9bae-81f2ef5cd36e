"use strict";

angular
  .module("adminApp")
  .controller(
    "PeriodRateCreateNewCustomController",
    PeriodRateCreateNewCustomController
  );
PeriodRateCreateNewCustomController.$inject = [
  "$scope",
  "periodRateCreateNewCustomService",
  "peakPeriodsService",
  "$sce"
];

function PeriodRateCreateNewCustomController(
  $scope,
  periodRateCreateNewCustomService,
  peakPeriodsService,
  $sce
) {
  var vm = this;
  vm.$scope = $scope;
  vm.periodRates = [];
  vm.prefectures = {};
  vm.target_date = "";
  vm.targetDateError = "";
  vm.hasErrors = true;
  vm.selectedCorporations = [];
  vm.targetPrefecture = {};

  vm.$scope.$watch("vm.target_date", function (newValue, oldValue) {
    if (newValue !== oldValue) {
      var params = { target_date: newValue };
      peakPeriodsService.getByTargetDate(params).then(
        function (res) {
          if (res.data.status) {
            vm.peakPeriod = res.data.data;
            vm.targetDateError = "";
          } else {
            vm.peakPeriod = null;
            vm.targetDateError = res.data.message;
          }
        },
        function (error) {
          toastr.error(error.statusText);
        }
      );
    }
  });

  vm.$scope.$watch("vm.peakPeriod", function () {
    if (vm.prefectures.length > 0 || !vm.peakPeriod) {
      return;
    }

    periodRateCreateNewCustomService.getPrefectures().then(function (res) {
      vm.prefectures = res.data.data;
      formatPeriodRates();
    });
  });

  vm.formatHtml = function(content) {
    return $sce.trustAsHtml(content);
  }

  $("#target-date-datepicker").datepicker({
    format: "yyyy/mm/dd",
    autoclose: true,
  });

  function formatPeriodRates() {
    vm.prefectures.map(function (prefecture) {
      var object = {
        prefecture_id: prefecture.id,
        prefecture_name: prefecture.name,
        period_rates: [],
      };
      vm.periodRates.push(object);
    });
  }

  vm.addNewPeriodRate = function (prefectureId) {
    vm.targetPrefecture = findPeriod(prefectureId);

    vm.targetPrefecture.period_rates.push({
      prefecture_id: prefectureId,
      is_default: false,
      corporation_id: "",
    });
  };

  vm.removePeriodRate = function (prefectureId, rateIndex) {
    var object = findPeriod(prefectureId);
    object.period_rates.splice(rateIndex, 1);
    vm.checkDuplicateCorporationId(object.period_rates, prefectureId);

    vm.$scope.$applyAsync(function () {
      vm.selectedCorporations = angular.copy(vm.selectedCorporations);
    });
  };

  function findPeriod(prefectureId) {
    return _.find(vm.periodRates, function (item) {
      return item.prefecture_id === prefectureId;
    });
  }

  vm.checkDuplicateCorporationId = function (period_rates, prefectureId) {
    var idCounts = period_rates.reduce(function (acc, item) {
      acc[item.corporation_id] = (acc[item.corporation_id] || 0) + 1;
      return acc;
    }, {});

    var exists_items = Object.keys(idCounts)
      .filter(function (id) {
        return idCounts[id] > 1;
      })
      .map(Number);

    _.forEach(period_rates, function (item, index) {
      var corporationField =
        vm.customPeriodRatesForm[
          "corporation[" + index + "][" + prefectureId + "]"
        ];
      if (corporationField) {
        var isTaken = exists_items.indexOf(item.corporation_id) !== -1;
        corporationField.$setValidity("taken", !isTaken);
      }
    });

    vm.checkErrorsForm();
  };

  vm.checkErrorsForm = function () {
    vm.hasErrors = vm.customPeriodRatesForm.$invalid;
  };

  vm.addOption = function (prefectureId, rateIndex) {
    var selectedData = $("#select2_" + prefectureId + "_" + rateIndex).select2(
      "data"
    );

    if (selectedData.length < 0) {
      return;
    }

    selectedData = selectedData[0];
    vm.targetPrefecture = findPeriod(prefectureId);
    var corporationId = parseInt(selectedData.id);

    vm.targetPrefecture.period_rates[rateIndex]["corporation_id"] =
      corporationId;

    vm.selectedCorporations.push({
      id: corporationId,
      full_name: selectedData.text,
    });

    vm.checkDuplicateCorporationId(
      vm.targetPrefecture.period_rates,
      prefectureId
    );
  };

  vm.errorMessage = function (model_name, attributes_name, type) {
    var attributeName = I18n.t(
      ["activerecord.attributes", model_name, attributes_name].join(".")
    );
    return I18n.t("activerecord.errors.messages." + type, {
      attribute: attributeName,
    });
  };

  vm.createCustomRates = function () {
    var params = {
      period_rates: formatParams()
    }

    showSpinner();
    disableSubmitButton();
    periodRateCreateNewCustomService.createCustomRatesByDate(params).then(function (res) {
      if (res.data.status) {
        toastr.success(res.data.message);
        window.location.href = res.data.link_to;
        hideSpinner();
      } else {
        vm.hasErrors = true;
        enableSubmitButton();
        hideSpinner();
      }
    });
  };

  function formatParams () {
    var formattedParams = [];
    vm.periodRates.forEach(function (periodRate) {
      periodRate.period_rates.forEach(function (rate) {
        var is_all = rate.corporation_id == -1 ? true : false;
        var formattedPeriodRates =  {
          peak_period_id:vm.peakPeriod.id,
          prefecture_id: rate.prefecture_id,
          is_all: is_all,
          corporation_id: is_all ? null : rate.corporation_id,
          is_default: rate.is_default,
          order_rate: rate.order_rate,
          unit_rate: rate.unit_rate
        };

        formattedParams.push(formattedPeriodRates);
      });
    })

    return formattedParams;
  }

  function disableSubmitButton() {
    $('#btn-submit').prop("disabled", true);
  }

  function enableSubmitButton() {
    $('#btn-submit').prop("disabled", false);
  };

  function showSpinner() {
    $("#spinner").removeClass("ng-hide");
  };

  function hideSpinner() {
    $("#spinner").addClass("ng-hide");
  }
}
