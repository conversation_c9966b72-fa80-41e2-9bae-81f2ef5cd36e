"use strict"

angular.module("adminApp").controller("DeleteStaffLogController", DeleteStaffLogController);
DeleteStaffLogController.$inject = ["$location", "deleteStaffLogService"]

function DeleteStaffLogController($location, deleteStaffLogService) {
  var vm = this;
  var BY_ADMIN = "0";
  var BY_STAFF = "1";
  vm.hasRecord = false;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.byStaff = vm.byAdmin = false;
  vm.params = $location.search();
  vm.total = 0;
  vm.logs = [];
  // var SEARCH_CONDITION_TYPE = "admin_staff_search_conditions";
  // var SEARCH_TYPE = "deleted";
  // var DELETER_TYPES = {
  //   "by_admin": "by_admin",
  //   "by_staff": "by_staff"
  // };

  vm.init = function() {
    vm.params.desc = true;
    vm.params.order_key = 'id';
    if (_.isEmpty(vm.params.page)) vm.params.page = 1;
    if (_.isEmpty(vm.params.per_page)) vm.params.per_page = vm.perPageSettings[0];
    if (!_.isEmpty(vm.params.deleter_type_id)) {
      var deleterTypes = vm.params.deleter_type_id.split(',');
      vm.byAdmin = _.includes(deleterTypes, BY_ADMIN);
      vm.byStaff = _.includes(deleterTypes, BY_STAFF);
    }
    vm.refresh();
  };

  vm.refresh = function() {
    var searchParams = angular.copy(vm.params);
    deleteStaffLogService.getDeleteStaffLogs(vm.params).then(function(res) {
      if (!_.isEmpty(res.data)) {
        angular.extend(vm, res.data);
        vm.hasRecord = Boolean(vm.logs.length);
        $location.search(searchParams).replace();
      }
    }, function(_error) {
      vm.logs = [];
    });
  }

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
    }
    vm.refresh();
  };

  vm.changePerPage = function() {
    vm.refresh();
  }

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !vm.params.desc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !!vm.params.desc
    }
  };

  vm.changeDeleterTypeCondition = function() {
    var deleterTypes = [];
    if (!!vm.byAdmin) deleterTypes.push(BY_ADMIN);
    if (!!vm.byStaff) deleterTypes.push(BY_STAFF);
    vm.params.deleter_type_id = deleterTypes.toString();
  }
}
