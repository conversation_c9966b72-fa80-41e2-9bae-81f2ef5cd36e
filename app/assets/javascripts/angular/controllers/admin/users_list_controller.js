"use strict";

angular.module("adminApp")
  .controller("UsersListController", UsersListController);
UsersListController.$inject = ["$location", "usersListService", "searchConditionFunction"];

function UsersListController($location, usersListService, searchConditionFunction) {
  var vm = this;
  vm.hasRecord = vm.noRecord = false;
  var SEARCH_CONDITION_TYPE = "user_search_conditions";
  var OMIT_FIELDS = ["page", "limit"];
  var SELECT_FIELDS = ["corporation_id"];
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  vm.refresh = function(resetPage) {
    if (resetPage) {
      vm.params.page = 1;
    };
    vm.params.per_page = vm.params.per_page || vm.perPageSettings[0];
    usersListService.getUsersList(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.users.length);
      vm.noRecord = !vm.hasRecord;
      $location.search(vm.params).replace();
    }, function(error) {
    });
  };

  vm.init = function() {
    searchConditionFunction.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE})
      .then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
        $("#select2-corporation_id-container").val(vm.params.corporation_id).trigger("change.select2");
        $("#select2-location_id-container").val(vm.params.location_id).trigger("change.select2");
        vm.refresh(true);
      } else {
        var requestParams = $location.search();
        vm.params = {
          page: requestParams.page,
          corporation_id: requestParams.corporation_id,
          name: requestParams.name,
          name_kana: requestParams.name_kana,
          email: requestParams.email
        };
        vm.refresh(false);
      }

      SELECT_FIELDS.forEach(function(field) {
        $("#" + field).val(vm.params[field]).trigger("change.select2");
      });
    });
  };

  vm.search = function(resetPage) {
    searchConditionFunction.createSearchCondition({search: _.omit(vm.params, OMIT_FIELDS),
      search_condition: SEARCH_CONDITION_TYPE});
    vm.refresh(resetPage);
  };

  vm.changePerPage = function() {
    vm.search(false);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".table-search-admin-users").offset().top
    }, 1000);
  };
}
