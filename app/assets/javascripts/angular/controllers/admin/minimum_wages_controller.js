"use strict";

angular.module("adminApp").controller("MinimumWagesController", MinimumWagesController);
MinimumWagesController.$inject = ["$location", "minimumWageService", "toaster"];

function MinimumWagesController($location, minimumWageService, toaster) {
  var vm = this;
  vm.checkedAll = false;
  vm.setting_date_system = new Date();
  vm.disabledCopiedBtn = true;
  vm.toasterTimeout = 6200;

  vm.init = function(minimumWages) {
    vm.minimumWages = minimumWages;
    vm.originalMinimumWage = angular.copy(minimumWages);
  }

  vm.checkAllWage = function() {
    vm.disabledCopiedBtn = !vm.checkedAll;
    _.forEach(vm.minimumWages, function(wage) {
      wage.selected = vm.checkedAll;
    })
  }

  vm.toggleWageChkbox = function(wage) {
    var selectedWages = getSeletedWages();
    vm.checkedAll = selectedWages.length === vm.minimumWages.length;
    vm.disabledCopiedBtn = selectedWages.length <= 0;
  }

  vm.setDefaultWageDate = function() {
    if (_.isEmpty(vm.setting_date_system)) {
      vm.setting_date_system = moment().format("YYYY/MM/DD");
    }
  }

  vm.showMinimumDropdown = function() {
    $(".wage-dropdown .current-wage-dropdown").toggle();
  }

  vm.showDateWageDropdown = function() {
    $(".wage-dropdown .wage-date-dropdown").toggle();
  }

  vm.setEffectiveDate = function() {
    _.forEach(getSeletedWages(), function(wage) {
      wage.start_date = vm.setting_date_system;
    });
    $(document).trigger("click");
  }

  function getSeletedWages() {
    return vm.minimumWages.filter(function(wage) {
      return !!wage.selected;
    });
  }

  vm.saveMinimumWage = function() {
    $(".save-minimum-wage").prop("disabled", true);
    var wageParam = formatParamsBeforeSend();
    minimumWageService.updateMinimumWageList({minimum_wages: wageParam}).then(function(res) {
      $(".save-minimum-wage").prop("disabled", false);
      $.lawsonMinimumWageAjax(res.data);
    });
  }

  function formatParamsBeforeSend() {
    var updateParams = [];
    _.forEach(vm.originalMinimumWage, function(minimumWage) {
      var currentOriginal = _.find(vm.minimumWages, function(wage) {return wage.prefecture_id == minimumWage.prefecture_id});
      if ((currentOriginal.current_wage !== minimumWage.current_wage) ||
        (currentOriginal.start_date != "" && currentOriginal.start_date !== minimumWage.start_date_format)) {
        updateParams.push(currentOriginal);
      }
    });
    return updateParams;
  }
}
