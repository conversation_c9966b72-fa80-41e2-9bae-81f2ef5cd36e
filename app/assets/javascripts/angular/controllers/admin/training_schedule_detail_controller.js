"use strict";

angular.module("adminApp").controller("TrainingScheduleDetailController", TrainingScheduleDetailController);
TrainingScheduleDetailController.$inject = ["$scope", "trainingScheduleService"];

function TrainingScheduleDetailController($scope, trainingScheduleService) {
  var vm = this;
  vm.$scope = $scope;
  var MAX_PORTION = 99;
  var MIN_PORTION = 1;
  var DEFAULT_ABSENT_CODE = "absent_with_notice";
  var DEFAULT_SEND_ABSENT_STAFF_MAIL = true;
  vm.minPortion = 0;
  vm.markedForAbsentId = "";
  vm.markedForJoinedId = "";
  vm.disabledBtn = false;
  vm.params = {};
  vm.currentAdminId = "";
  vm.absentStatusCode = DEFAULT_ABSENT_CODE;
  vm.createApplicantParams = {};
  vm.prevParams = {};
  vm.sendAbsentStaffMail = DEFAULT_SEND_ABSENT_STAFF_MAIL;
  vm.isChangeCancelReason = false;
  vm.errorAnswerId = false;

  vm.$scope.$watch('vm.params', function(newVal, oldVal) {
    if (!_.isEqual(newVal, oldVal)) {disableSubmitBtn(false)};
    if (_.isEqual(newVal, vm.prevParams)) {disableSubmitBtn(true)};
  }, true);

  vm.init = function(schedule, totalApplicants, current_admin_id, surveyAnswers) {
    vm.minPortion = totalApplicants < MIN_PORTION ? MIN_PORTION : totalApplicants;
    vm.params.total_portion = isNaN(schedule.total_portion) ? MIN_PORTION : schedule.total_portion;
    vm.params.person_in_charge_id = _.toString(schedule.person_in_charge_id);
    vm.createApplicantParams.training_schedule_id = schedule.id;
    vm.currentAdminId = current_admin_id;
    vm.surveyAnswers = surveyAnswers;

    vm.currentSchedule = schedule;
    _.set(vm.currentSchedule, "filled_slots", totalApplicants);
    vm.prevParams = angular.copy(vm.params);
    disableSubmitBtn(true);
  };

  vm.openHistoryModal = function(trainingScheduleId) {
    angular.element("#training-schedule-history-modal").modal("show");

    trainingScheduleService.getTrainingScheduleHistory(trainingScheduleId).then(function(res) {
      vm.trainingScheduleLogs = res.data.training_schedule_logs;
    });
  }

  vm.assignToMe = function() {
    vm.params.person_in_charge_id = vm.currentAdminId;

    angular.element(document).ready(function() {
      $("#select2-person-in-charge-for-edit").val(vm.currentAdminId)
      $("#select2-person-in-charge-for-edit").trigger("change");
    });
  }

  vm.confirmBeforeMarkAbsent = function(applicantId) {
    vm.markedForAbsentId = applicantId;
    $("#confirm-absent-modal").modal("show");
  };

  vm.confirmBeforeMarkJoined = function(applicantId) {
    vm.markedForJoinedId = applicantId;
    $("#confirm-joined-modal").modal("show");
  };

  vm.editStaffReasonCancel = function() {
    vm.isChangeCancelReason = true;
    vm.errorAnswerId = false;
    vm.surveyResponse = vm.surveyAnswers.find(function(surveyAnswer) {
      return surveyAnswer.id == vm.surveyAnswerId
    });

    if (vm.surveyResponse && vm.surveyResponse.type == 'text') {
      vm.surveyAnswerResponse = vm.cancelReasonText;
    }

    $("#select-reason-cancel-modal").modal("show");
  }

  vm.checkErrorSurveyAnswer = function() {
    if (vm.surveyResponse) return;

    vm.errorAnswerId = true;
    
  }

  vm.errorSurveyAnswer = function(){
    return {
      "error-msg": vm.errorAnswerId
    } 
  }

  vm.markApplicantAbsent = function() {
    $("#confirm-absent-modal").modal("hide");

    if (vm.absentStatusCode == DEFAULT_ABSENT_CODE) {
      return vm.markApplicantAbsentWithNotice()
    }

    $("#spinner").removeClass("ng-hide");

    var params = {
      training_schedule_applicant: {
        schedule_status_code: vm.absentStatusCode
      },
      send_absent_staff_mail: vm.sendAbsentStaffMail
    };

    trainingScheduleService.updateStatus(vm.markedForAbsentId, params).then(function(_) {
      location.reload();
    });
  };

  vm.markApplicantAbsentWithNotice = function() {
    vm.isChangeCancelReason = false;
    vm.errorAnswerId = false;
    return $("#select-reason-cancel-modal").modal("show");
  }

  vm.submitAbsentWithNotice = function() {
    vm.checkErrorSurveyAnswer();
  
    var contentResponse = vm.surveyResponse.content;

    if (vm.surveyResponse.type == "text") {
      contentResponse = vm.surveyAnswerResponse;
    }

    var params = {
      training_schedule_applicant: {
        schedule_status_code: vm.absentStatusCode,
        cancel_reason: contentResponse
      },
      send_absent_staff_mail: vm.sendAbsentStaffMail,
      survey_answer_id: vm.surveyResponse.id,
    };
  
    $("#select-reason-cancel-modal").modal("hide");
    $("#spinner").removeClass("ng-hide");

    trainingScheduleService.updateStatus(vm.markedForAbsentId, params).then(function(_) {
      location.reload();
    });
  }

  vm.submitEditedCancelReason = function() {
    vm.checkErrorSurveyAnswer();

    var contentResponse = vm.surveyResponse.content;

    if (vm.surveyResponse.type == "text") {
      contentResponse = vm.surveyAnswerResponse;
    }

    var params = {
      survey_answer_id: vm.surveyResponse.id,
      survey_answer_response: contentResponse,
      survey_question_id: vm.surveyResponse.survey_question_id,
    };

    $("#spinner").removeClass("ng-hide");
    trainingScheduleService.updateReasonCancel(vm.markedForAbsentId, params).then(function(_) {
      vm.markedForAbsentId = "";
      $("#reason_cancel_modal").modal("hide");
      $("#select-reason-cancel-modal").modal("hide");
      $("#spinner").addClass("ng-hide");
    });
  }

  vm.markApplicantJoined = function() {
    $("#confirm-joined-modal").modal("hide");
    $("#spinner").removeClass("ng-hide");
    vm.disabledBtn = true;
    var params = {schedule_status_code: "joined"};
    trainingScheduleService.updateStatus(vm.markedForJoinedId, params).then(function(_) {
      vm.markedForJoinedId = "";
      vm.disabledBtn = false;
      $("#spinner").addClass("ng-hide");
      location.reload();
    });
  };

  vm.showCancelReason = function(applicantId) {
    trainingScheduleService.getStaffAnswerSurvey(applicantId).then(function(res) {
      vm.cancelReasonText = res.data.response;
      vm.markedForAbsentId = applicantId;
      vm.surveyAnswerId = res.data.survey_answer_id;
      $("#reason_cancel_modal").modal("show");
    });
  }

  vm.scheduleStaff = function() {
    $("#select-staff-modal").modal("hide");
    $("#spinner").removeClass("ng-hide");
    trainingScheduleService.createApplicant({training_schedule_applicant: vm.createApplicantParams}).then(function(_) {
      $("#spinner").addClass("ng-hide");
      location.reload();
    });
  };

  vm.addPortion = function() {
    if (vm.params.total_portion == MAX_PORTION) {return;}
    vm.params.total_portion++;
  };

  vm.subtractPortion = function() {
    if (vm.params.total_portion == vm.minPortion) {return;}
    vm.params.total_portion--;
  };

  vm.checkValidTotalPortion = function() {
    if (vm.params.total_portion > MAX_PORTION) {
      vm.params.total_portion = MAX_PORTION;
      return;
    }
    if (vm.params.total_portion < vm.minPortion) {
      vm.params.total_portion = vm.minPortion;
      return;
    }
  };

  vm.checkDisableButtonJoin = function(isUnavailable) {
    return isUnavailable || vm.disabledBtn;
  };

  function defaultAbsentStatusCode() {
    vm.absentStatusCode = DEFAULT_ABSENT_CODE;
  }

  vm.showStafffApplicantsModal = function(staff_id) {
    $("#staff-info-modal").modal("show");
    trainingScheduleService.getStaffAppliedHistory(staff_id).then(function(res){
      if (res.status == 200) {
        vm.staffApplicants = res.data;
      }
    });
  }

  vm.deleteSchedule = function($event) {
    $($event.currentTarget).prop("disabled", true);
    var schedule_ids = _.map(vm.training_schedules, function(schedule) {
      return schedule.schedule_id;
    })
    var trigger_schedule_id = vm.currentSchedule.id;
    addLoading();

    trainingScheduleService.bulkDestroy({
      schedule_ids: schedule_ids.join(","),
      trigger_schedule_id: trigger_schedule_id
    }).then(function(res) {
      if (res.status == 200 && res.data.status) {
        submitEditForm();
      }
      removeLoading();
    });
  }

  vm.closeConfirmModal = function() {
    $("#confirm-delete-schedule-modal").modal("hide");
    vm.training_schedules = [];
    vm.person_in_charge = '';
  }

  vm.beforeSubmit = function() {
    addLoading();
    // If change only total portion, allow submit form
    if (!_.isEqual(vm.params.total_portion, vm.prevParams.total_portion)) { return submitEditForm(); }

    // If filled slots = 0, allow submit form
    if (_.get(vm.currentSchedule, "filled_slots", 0) == 0) { return submitEditForm(); }

    trainingScheduleService.list({
      search: {
        training_date_from: new Date(vm.currentSchedule.start_time),
        training_date_to: new Date(vm.currentSchedule.start_time),
        person_in_charge_ids: [vm.params.person_in_charge_id],
      }
    }).then(function(res) {
      vm.training_schedules = _.filter(res.data.training_schedules, function(schedule) {
        var isPresent = !schedule.deleted_at.date;
        var isAvailable = schedule.slots.filled == 0;
        return isPresent && isAvailable;
      }, []);
      vm.person_in_charge = res.data.training_schedules[0]["person_in_charge"] || '';
    }, function(error) {
      vm.training_schedules = [];
      vm.person_in_charge = '';
    }).finally(function() {
      if (vm.training_schedules.length > 0) {
        $("#confirm-delete-schedule-modal").modal("show");
      } else {
        submitEditForm();
      }
      removeLoading();
    })
  }

  // -- Func
  function sortObject(obj) {
    return Object.keys(obj).sort().reduce(function (result, key) {
      result[key] = obj[key];
      return result;
    }, {});
  }

  function disableSubmitBtn(val) {
    $(".btn-submit-form-ajax").prop("disabled", val);
  }

  function submitEditForm() {
    $("#training-schedule-form").submit();
  }

  function addLoading() {
    $("#spinner").removeClass("ng-hide");
  }

  function removeLoading() {
    $("#spinner").addClass("ng-hide");
  }

  $("#staff-info-modal").on('hidden.bs.modal', function() {
    vm.staffApplicants = null;
  });
  //
}
