"use strict";

angular.module('adminApp').directive("directiveWhenScrolled", function() {
  return function(scope, elm, attr) {
    var raw = elm[0];
    elm.bind('scroll', function() {
      if (raw.scrollTop + raw.offsetHeight >= raw.scrollHeight) {
        scope.$apply(attr.directiveWhenScrolled);
      }
    });
  };
});

angular.module("adminApp").controller("BillingUnitPriceController", BillingUnitPriceController);
BillingUnitPriceController.$inject = ["$location", "billingUnitPriceService", "toaster", "$timeout"];

function BillingUnitPriceController($location, billingUnitPriceService, toaster, $timeout) {
  var vm = this;
  var requestParams = $location.search();
  var searchParamKeys = ["chain_id", "prefecture_id"];
  var CHAIN_DEFAULT = 1;
  vm.hasRecord = vm.noRecord = false;
  vm.params = {};
  vm.newPaymentRates = [];
  var MODEL_PRICES = ["payment_rates", "option_payment_rates", "period_rates"];
  var PAYMENT_RATE_ATTRS = ["chain_id", "corporation_id", "effective_date", "is_all", "new_area_allowance",
    "new_base_rate", "new_night_rate", "new_overtime_rate", "new_short_allowance", "now_area_allowance",
    "now_base_rate", "now_night_rate", "now_overtime_rate", "now_short_allowance", "prefecture_id"];
  var OPTION_PAYMENT_RATE_ATTRS = ["chain_id", "corporation_id", "now_immediate_rate", "now_absence_discount",
    "new_immediate_rate", "new_absence_discount", "effective_date"];
  var NEW_BASE_RATE_ATTRS = ["id", "prefecture_id", "corporation_id", "new_base_rate", "new_night_rate",
    "new_short_allowance", "new_area_allowance", "effective_date"];
  var NEW_OPTION_RATE_ATRS = ["id", "prefecture_id", "corporation_id", "new_immediate_rate",
    "new_absence_discount", "effective_date"];
  var NEW_PERIOD_RATE_ATRS = ["id", "prefecture_id", "corporation_id", "unit_rate", "order_rate"];
  var NEW_PAYMENT_RATE_ATTRS = ["new_base_rate", "new_night_rate", "new_short_allowance", "new_area_allowance"];
  var ITEM_LIMIT_NUMBER = 10;

  vm.toasterTimeout = 6200;
  vm.paymentIndex = 0;
  vm.optionPaymentIndex = 100000;
  vm.periodIndex = 0;
  vm.disable = {
    payment_rates: true,
    option_payment_rates: true,
    period_rates: true
  };
  vm.payment_rate_effective_date = new Date();
  vm.option_effective_date = new Date();
  var newObject = {
    payment_rates: [],
    option_payment_rates: [],
    period_rates: []
  };
  vm.originData = angular.copy(newObject);
  vm.destroyIds = _.merge(angular.copy(newObject), {peak_periods: []});
  vm.newData = angular.copy(newObject);
  var newPriceAttrs = {
    payment_rates: NEW_BASE_RATE_ATTRS,
    option_payment_rates: NEW_OPTION_RATE_ATRS,
    period_rates: NEW_PERIOD_RATE_ATRS
  };
  var PRICE_ATTRS = {
    payment_rates: ["base_rate", "night_rate", "short_allowance", "area_allowance"],
    option_payment_rates: ["immediate_rate", "absence_discount"]
  };

  var SUBMIT_PARAMS = {}

  vm.invalidPrefectures = angular.copy(newObject);

  _.forEach(searchParamKeys, function(searchParamKey) {
    vm.params[searchParamKey] = requestParams[searchParamKey];
  });

  init();
  vm.basicFeeTabLimitNumber = ITEM_LIMIT_NUMBER;
  vm.optionFeeTabLimitNumber = ITEM_LIMIT_NUMBER;
  vm.periodFeeTabLimitNumber = ITEM_LIMIT_NUMBER;
  vm.loadOptionFeeTab = false;
  vm.loadPeriodFeeTab = false;

  function init(){
    if (!vm.params["chain_id"]) {
      vm.params["chain_id"] = CHAIN_DEFAULT;
    }
  }

  vm.onSwitchTab = function(tabName) {
    if (!vm[tabName]) {
      angular.element("#angular-ng-repeat-loader").removeClass("ng-hide");
      $timeout(function() {
        vm[tabName] = true;
      }, 100);
    }
  };

  vm.onNgRepeatEnd = function() {
    angular.element("#angular-ng-repeat-loader").addClass("ng-hide");
  };

  vm.loadMore = function(tabName) {
    if (vm[tabName] < vm.prefectures.length) {
      angular.element("#angular-ng-repeat-loader").removeClass("ng-hide");

      $timeout(function() {
        vm[tabName] += ITEM_LIMIT_NUMBER;
      }, 100);
    }
  };

  vm.search = function() {
    billingUnitPriceService.searchBillingUnitPrice(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.prefectures.length);
      vm.noRecord = !vm.hasRecord;
      $location.search(vm.params).replace();
      _.forEach(MODEL_PRICES, function(modelName) {
        var originData = formatParams(vm.prefectures, modelName);
        angular.copy(originData, vm.originData[modelName]);
      });
      changeFormatDate();
      vm.disable = {
        payment_rates: true,
        option_payment_rates: true,
        period_rates: true
      };
      vm.invalidPrefectures = angular.copy(newObject);
      vm.selectedItems = angular.copy(vm.corporations);
    }, function (error) {
    });
  };

  vm.savePaymentRate = function(modelName) {
    var paramsData = formatParams(vm.prefectures, modelName);
    var submitParams = changeParams(angular.copy(paramsData));
    var params = [{objects: submitParams, destroy_ids: vm.destroyIds[modelName], model_name: modelName}];
    if (modelName == "period_rates") {
      var peakParams = {objects: vm.peak_periods, destroy_ids: vm.destroyIds.peak_periods,
        model_name: "peak_periods"};
      params.push(peakParams);
    };
    billingUnitPriceService.saveBillingUnitPrice({models: params}).then(function(res) {
      var data = res.data;
      var status = data.status;
      vm.invalidPrefectures[data.model_name] = data.invalid_prefecture_ids;
      toaster.pop(status, "", data.message);
      if (status == "success") {
        vm.search();
        vm.invalidPrefectures = angular.copy(newObject);
      };
    });
  };

  vm.save = function(modelName) {
    vm.invalidPrefectures[modelName] = [];
    var paramsData = formatParams(vm.prefectures, modelName);
    var groupedPrefectures = _.chain(paramsData).groupBy("prefecture_id").value();
    _.forEach(groupedPrefectures, function(dataPrefecture, prefectureId) {
      var isAll = _.find(dataPrefecture, function(rate) {
        return (rate.corporation_id == -1) || (rate.is_all && _.isNull(rate.corporation_id));
      });
      if (!isAll) {
        vm.invalidPrefectures[modelName].push(_.toInteger(prefectureId))
      };
    });
    var submitParams = {
      objects: SUBMIT_PARAMS[modelName],
      chain_id: vm.params.chain_id,
      model_name: modelName,
      invalid_prefecture_ids: vm.invalidPrefectures[modelName]
    };
    billingUnitPriceService.updatePaymentRate(submitParams).then(function(res) {
      var data = res.data;
      var status = data.status;
      vm.invalidPrefectures[data.model_name] = data.invalid_prefecture_ids;
      toaster.pop(status, "", data.message);
      if (status == "success") {
        vm.search();
      };
    });
  };

  vm.isInvalidPrefecture = function(prefectureId, modelName) {
    return _.includes(vm.invalidPrefectures[modelName], prefectureId);
  };

  vm.incrementIndex = function(type) {
    vm[type + "Index"] += 1;
    return vm[type + "Index"];
  };

  vm.getNewPaymentPrice = function(modelName) {
    var rates = formatPaymentRates(vm.originData[modelName], modelName);
    var newRates = formatPaymentRates(formatParams(vm.prefectures, modelName), modelName);

    var newPrefectureCorporations = _.chain(newRates).groupBy("prefecture_id").value();
    _.each(newPrefectureCorporations, function(value, key) {
      newPrefectureCorporations[key] = _.map(value, "id");
    });

    var adds = _.filter(newRates, function(rate) {
      return _.isUndefined(rate.id)
    });
    var removes = _.filter(rates, function(rate) {
      return !_.includes(newPrefectureCorporations[rate.prefecture_id], rate.id)
    });

    _.forEach(removes, function(remove) {
      var removeElement = _.find(vm.originData[modelName], function(originData) {
        return originData.id == remove.id;
      });
      if (removeElement && PRICE_ATTRS[modelName] && !removeElement.effective_date) {
        _.forEach(PRICE_ATTRS[modelName], function(priceAttr) {
          remove["new_" + priceAttr] = removeElement["now_" + priceAttr];
        });
      };
    });

    rates = _.difference(rates, removes);
    newRates = _.difference(newRates, adds);
    var changes = _.differenceWith(newRates, rates, _.isEqual);

    adds = _.enhance(adds, {type: "add"});
    removes = _.enhance(removes, {type: "delete"});
    changes = _.enhance(changes, {type: "update"});
    _.forEach(changes, function(rateChange) {
      var originRate = _.find(rates, function(r) {
        return r.id == rateChange.id;
      });
      if (originRate.corporation_id != rateChange.corporation_id) {
        _.merge(rateChange, {is_change_corporation: true, old_corporation_id: originRate.corporation_id})
      };
    });
    var diffRates = _.concat(adds, removes, changes);
    SUBMIT_PARAMS[modelName] = diffRates;
    vm.newData[modelName] = _.chain(diffRates).groupBy("prefecture_id").value();
  };

  vm.checkDisableSubmit = function(modelName) {
    vm.getNewPaymentPrice(modelName);
    vm.disable[modelName] = _.isEmpty(vm.newData[modelName]);
  };

  vm.addOptionForSelect = function() {
    _.forEach($(".select-prefecture :selected"), function(itemSelected) {
      var $itemSelected = $(itemSelected);
      var item = {id: parseInt($itemSelected.val()), full_name: $itemSelected.text()}
      vm.selectedItems.push(item);
    });
  };

  vm.getCorporationName = function(rate, modelName) {
    var corporationId = rate.corporation_id;
    if (_.isNull(corporationId)) {
      var prefecture = _.find(vm.prefectures, function(prefec) {
        return prefec.id == rate.prefecture_id;
      });
      var corporationName = I18n.t("admin.billing_unit_price.select.all");
      if (prefecture[modelName].length !== 1) {
        corporationName = I18n.t("admin.billing_unit_price.select.all_other");
      };
      return corporationName;
    };
    var corporation = _.find(vm.selectedItems, function(co) {
      return co.id == corporationId;
    });
    if (corporation) {
      return corporation.full_name;
    };
  };

  vm.getPrefectureName = function(prefectureId) {
    var prefecture = _.find(vm.prefectures, function(prefec) {
      return prefec.id == prefectureId;
    });
    if (prefecture) {
      return prefecture.name;
    };
  };

  vm.formatDate = function(date, formatType) {
    if (_.isEmpty(date)) return "";
    return moment.parseZone(date).format(formatType);
  };

  vm.checkRequireNewRate = function(objectPayment) {
    return !_.isEmpty(objectPayment.effective_date);
  };

  vm.addNewPayment = function(prefecture, modelName) {
    var newPayment = {
      prefecture_id: prefecture.id,
      chain_id: vm.params.chain_id
    };
    prefecture[modelName].push(newPayment);
    vm.checkDisableSubmit(modelName);
  };

  vm.removePayment = function(prefecture, destroyId, index, modalName) {
    if (destroyId) {
      vm.destroyIds[modalName].push(destroyId);
    };
    prefecture[modalName].splice(index, 1);
    if (prefecture[modalName].length == 1) {
      var savePrefecture = angular.copy(prefecture[modalName][0]);
      prefecture[modalName] = [];
      prefecture[modalName].push(savePrefecture);
    }
  };

  vm.optionAllLabel = function(paymentRates) {
    var label = I18n.t("admin.billing_unit_price.select.all_other");
    if (paymentRates.length === 1 && paymentRates[0].is_all) {
      label = I18n.t("admin.billing_unit_price.select.all")
    };
    return label;
  };

  vm.errorMessage = function(model_name, attributes_name, type) {
    if (type == "in_future") {
      return I18n.t("admin.billing_unit_price.messages.invalid_start_date");
    }
    var attributeName = I18n.t(["activerecord.attributes", model_name, attributes_name].join("."));
    return I18n.t("activerecord.errors.messages." +  type, {attribute: attributeName});
  };

  vm.checkValidNewRate = function(field, paymentRate, model) {
    if (_.isEmpty(paymentRate.effective_date)) {
      field.$setValidity("in_future", true);
      return;
    };
    var date = vm.formatDate(paymentRate.effective_date, ORDER_DATEPICKER_FORMAT);
    if (paymentRate.id) {
      var originRate = _.find(vm.originData[model], function(rate) {
        return rate.id === paymentRate.id;
      });
      var originEfectiveDate = originRate.effective_date;
      if (!_.isEmpty(originEfectiveDate) && date === vm.formatDate(originEfectiveDate, ORDER_DATEPICKER_FORMAT)) {
        field.$setValidity("in_future", true);
        return;
      };
    };
    var inFuture = !_.isEmpty(date) && moment().isSameOrAfter(date, "day");
    field.$setValidity("in_future", !inFuture);
  };

  vm.checkRequireDate = function(paymentRate) {
    var newRates = _.map(NEW_PAYMENT_RATE_ATTRS, function(attr) {
      return paymentRate[attr];
    });
    var date = paymentRate.effective_date;
    return _.compact(newRates).length > 0 && _.isEmpty(date);
  };

  vm.getDataToggle = function(form) {
    return form.$valid ? "modal" : "";
  };

  vm.errorClassDate = function(formField, paymentRate, model) {
    if (_.isUndefined(formField)) {
      return;
    };
    vm.checkValidNewRate(formField, paymentRate, model);
    return {"form-error": formField.$error.in_future};
  };

  vm.initCorporationId = function(paymentRate) {
    if (paymentRate && paymentRate.id && _.isNull(paymentRate.corporation_id)) {
      return -1;
    };
    return paymentRate.corporation_id;
  };

  vm.showDateWageDropdown = function() {
    $(".wage-dropdown .wage-date-dropdown").toggle();
  };

  vm.setEffectiveDate = function(batchDate, modelName) {
    if (vm.isInvalidDate(batchDate)) {
      return;
    };
    _.forEach(vm.prefectures, function(prefecture) {
      _.forEach(prefecture[modelName], function(payment) {
        payment.effective_date = batchDate;
      });
    });
    vm.checkDisableSubmit(modelName);
    $(document).trigger("click");
  };

  vm.setDefaultDate = function(fieldName) {
    if (_.isEmpty(vm[fieldName])) {
      vm[fieldName] = moment().format(ORDER_DATEPICKER_FORMAT);
    };
  };

  vm.isInvalidDate = function(date) {
   return !_.isEmpty(date) && moment().isSameOrAfter(date, "day");
  };

  vm.checkValidCorporation = function(prefecture, modelName, index, maxIndex, form) {
    var corporationIds = _.map(prefecture[modelName], function(payment) {
      return payment.corporation_id;
    });
    var groupped = _.groupBy(corporationIds, function (n) {return n});
    var duplicateCoporationIds = _.uniq(_.flatten(_.filter(groupped, function (n) {return n.length > 1})));
    duplicateCoporationIds = _.compact(duplicateCoporationIds);
    var arrayIndex = _.range(index + 1, maxIndex + 1);
    _.forEach(arrayIndex, function(index) {
      var prefectureField = form[modelName + '[' + index + '][prefecture_id]'];
      if (_.isUndefined(prefectureField) || prefectureField.$modelValue != prefecture.id) {
        return;
      };
      var corporationField = form[modelName + '[' + index + '][corporation_id]'];
      if (corporationField) {
        corporationField.$setValidity("taken", !_.includes(duplicateCoporationIds, corporationField.$modelValue));
      };
    });
    vm.checkDisableSubmit(modelName);
  };

  vm.removePeakPeriod = function(index, destroyId) {
    if (destroyId) {
      vm.destroyIds.peak_periods.push(destroyId);
    };
    vm.peak_periods.splice(index, 1);
  };

  vm.addNewPeakPeriod = function(newPeak) {
    newPeak.chain_id = vm.params.chain_id;
    vm.peak_periods.push(angular.copy(newPeak));
  };

  function formatParams(prefectures, modalName) {
    var paymentRates = _.map(prefectures, function(prefecture) {
      return prefecture[modalName];
    });
    return _.flatten(paymentRates);
  };

  function formatPaymentRates(paymentRates, modelName) {
    var formatRates = _.map(paymentRates, function(payment) {
      var newRate = {};
      _.forEach(newPriceAttrs[modelName], function(attr) {
        newRate[attr] = payment[attr];
        if (attr == "corporation_id" && payment[attr] == "-1") {
          newRate[attr] = null;
        };
      });
      newRate.effective_date = vm.formatDate(payment.effective_date, ORDER_DATEPICKER_FORMAT);
      return newRate;
    });
    return formatRates;
  };

  function changeParams(paymentRates) {
    var submitParams = _.map(paymentRates, function(payment) {
      if ((payment.corporation_id == -1) || (payment.is_all && _.isNull(payment.corporation_id))) {
        payment.corporation_id = null;
        payment.is_all = true;
      } else {
        payment.is_all = false;
      };
      return payment;
    });
    return submitParams;
  };

  function changeFormatDate() {
    _.forEach(vm.peak_periods, function(peak) {
      peak.target_date = vm.formatDate(peak.target_date, ORDER_DATEPICKER_FORMAT);
    });
  };

  _.enhance = function(list, source) {
    return _.map(list, function(element) { return _.extend({}, element, source); });
  }
}
