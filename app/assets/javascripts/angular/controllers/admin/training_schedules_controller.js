"use strict";

angular.module("adminApp")
  .controller("TrainingSchedulesController", TrainingSchedulesController);
TrainingSchedulesController.$inject = ["$scope", "trainingScheduleService", "checkValidDateFunction", "toaster"];
function TrainingSchedulesController($scope, trainingScheduleService, checkValidDateFunction, toaster) {
  var vm = this;
  vm.$scope = $scope;
  var LOCATION_COLORS = [
    "peacock",
    "tomato",
    "grape",
    "flamingo",
    "sage",
    "lavender",
    "graphite",
    "banana",
    "basil",
    "tangerine",
    "blueberry"
  ]
  var SEARCH_CONDITION_TYPE = "admin_training_schedule_search_conditions";
  var MAXIMUM_TRAINING_HOUR = 22

  vm.height = 40;
  vm.system_days_of_week = []
  vm.days_of_week = [];
  vm.str_days_of_week = I18n.t("date.day_names");
  vm.times = [];
  vm.year = "";
  vm.month = "";
  vm.week = "";
  vm.hasRecord = false;
  vm.currentDate = "";
  vm.current = "";
  vm.enableCall = true;
  vm.timeOptions = [];
  vm.fromSelected = "";
  vm.toSelected = "";
  vm.check = false;
  vm.weekOptions = [1,2,3,4,5,6,7,8,9,10,11,12];
  vm.weekSelected = 1;
  vm.excludingHoliday = false;
  vm.candidateOptions = [1,2,3,4,5,6,7,8,9,10];
  vm.candidateSelected = 1;
  vm.daySelected = ["","","","","","",""];
  vm.training_schedule = {};
  vm.training_schedules = {};
  vm.currentId = null;
  vm.saveOption = 1;
  vm.tableHeight = 400;
  vm.change = 0;
  vm.training_schedules_applied = [];
  vm.locationOptions = [];
  vm.personInCharge = "";
  vm.searchParams = {
    location_ids: [],
    trainingSessionCode: "",
    personInChargeId: ""
  }
  vm.currentAdminId = "";
  vm.selectedYear = "";

  // --- Training Group Variables ---
  var DEFAULT_TRAINING_GROUP_PARAMS = {
    selectedDates: [],
    params: {
      training_schedules: [],
      dates: [],
      selected_year: "",
    }
  }
  vm.trainingGroup = DEFAULT_TRAINING_GROUP_PARAMS;
  vm.toasterTimeout = 6200;
  vm.scheduleErrorMsg = "";
  // ------

  vm.downloadParams = {
    started_from: "",
    started_to: ""
  }

  vm.init = function(locationOptions) {
    var colorNumber = LOCATION_COLORS.length;
    vm.locationOptions = _.map(locationOptions, function (option, index) {
      option.push(LOCATION_COLORS[index%colorNumber]);
      return option;
    })

    // Lấy location đầu tiên
    vm.locationSelected = vm.locationOptions[0][1].toString();

    trainingScheduleService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
      if (res.data.status) {
        vm.searchParams = JSON.parse(res.data.last_conditions);
      }

      angular.element(document).ready(function() {
        $("#location_ids").trigger("change");
      });
      vm.callData(vm.current, vm.month, vm.week);
    });
  };

  vm.setTableStyle = function() {
    return "height: " + vm.tableHeight + "px;";
  };

  vm.reset = function() {
    vm.fromSelected = vm.times[0];
    vm.toSelected = vm.times[0];
    vm.check = false;
    vm.daySelected = ["","","","","","",""];
    vm.weekSelected = 1;
    vm.excludingHoliday = false;
    vm.candidateSelected = 1;
    vm.locationSelected = vm.locationOptions[0][1].toString();
    vm.personInCharge = "";
    vm.scheduleErrorMsg = ""
    $(".day").removeClass("selected");
    $(".day").next().val("");
  };

  vm.refresh = function() {
    trainingScheduleService.createSearchCondition({search: vm.searchParams, search_condition: SEARCH_CONDITION_TYPE});
    vm.callData(vm.current, vm.month, vm.week);
  };

  vm.edit = function(training_schedule, date) {
    vm.currentId = training_schedule.id;
    vm.current = vm.year + "/" + date;
    vm.setOptions(training_schedule.start_time, training_schedule.end_time);
    vm.show();
    $("#details-modal").modal("show");
  };

  vm.applicantStaffText = function(applicant) {
    return applicant.staff_number + " " + applicant.staff_name;
  }

  vm.setOptions = function(from, to) {
    vm.fromSelected = from;
    vm.toSelected = to;
  };

  vm.show = function() {
    var modalContent = angular.element("#details-modal").find(".modal-content");
    modalContent.addClass("loading-skeleton");
    if (vm.enableCall) {
      vm.enableCall = false;
      trainingScheduleService.show(vm.currentId).then(function(res) {
        if (res.status == 200) {
          vm.enableCall = true;
          vm.training_schedule = res.data;
          modalContent.removeClass("loading-skeleton");
        }
      });
    }
  };

  vm.callData = function(current, month, week) {
    $("#spinner").removeClass("ng-hide");
    if (vm.enableCall) {
      vm.enableCall = false;
      trainingScheduleService.getTrainingSchedules({
        current: current,
        month: month,
        week: week,
        change: vm.change,
        location_ids: _.join(vm.searchParams.location_ids, ','),
        training_session_code: vm.searchParams.trainingSessionCode,
        person_in_charge: vm.searchParams.personInChargeId
      }).then(function(res) {
        if (res.status == 200) {
          vm.change = 0;
          vm.system_days_of_week = res.data.days_of_week;
          vm.days_of_week = formatDaysOfWeek(vm.system_days_of_week)
          vm.times = res.data.times;
          vm.year = res.data.year;
          vm.month = res.data.month;
          vm.current = res.data.current;
          vm.week = res.data.week;
          vm.training_schedules = res.data.training_schedules;
          vm.hasRecord = true;
          vm.enableCall = true;
          vm.timeOptions = vm.times.slice();
          vm.tableHeight = $(".wrapper").height() - 490;
          if (vm.currentDate == "") {
            vm.currentDate = vm.current;
          }
          vm.currentAdminId = res.data.current_admin_id
          $("#spinner").addClass("ng-hide");
        }
      });
    }
  };

  vm.setOptions = function(from, to) {
    vm.fromSelected = from;
    vm.toSelected = to;
  };

  vm.setCurrent = function(date) {
    return (vm.year + "/" + date) == vm.currentDate ? "current" : "";
  };

  vm.next = function() {
    vm.change = 1;
    vm.callData(vm.current, vm.month, vm.week + 1);
  };

  vm.prev = function() {
    vm.change = 1;
    vm.callData(vm.current, vm.month, vm.week - 1);
  };

  vm.nextYear = function() {
    vm.change = 1;
    vm.callData(vm.current, vm.month + 1, vm.week);
  };

  vm.prevYear = function() {
    vm.change = 1;
    vm.callData(vm.current, vm.month - 1, vm.week);
  };

  vm.setStyle = function(training_schedule) {
    return "height: " + training_schedule.height*vm.height + "px;";
  };

  vm.setMaxHeight = function(training_schedule) {
    return "max-height: " + (training_schedule.height*vm.height - vm.height + 5) + "px;";
  };

  vm.addOne = function(keyDate, from) {
    vm.add(keyDate, from, calculateEndTime(from));
  };

  vm.add = function(keyDate, from, to) {
    vm.current = vm.system_days_of_week[keyDate]
    vm.setOptions(from, to);
    vm.locationSelected = vm.searchParams.location_ids.length > 0 ? vm.searchParams.location_ids[0] : vm.locationOptions[0][1].toString();
    vm.showGroupModal();
  };

  vm.selectDays = function($event, index) {
    var element = $($event.currentTarget);
    var input = element.next();
    if (input.val() == "") {
      input.val(index);
      element.addClass("selected");
      vm.daySelected[index] = index;
    } else {
      input.val("");
      element.removeClass("selected");
      vm.daySelected[index] = "";
    }
  };

  vm.confirmDelete = function() {
    if (vm.enableCall) {
      $("#confirm-modal").modal("show");
    }
  };

  vm.destroy = function() {
    if (vm.enableCall) {
      vm.enableCall = false;
      trainingScheduleService.destroy(vm.currentId).then(function(res) {
        if (res.status == 200 && res.data.status) {
          $("#details-modal").modal("hide");
          vm.enableCall = true;
          vm.callData(vm.current, vm.month, vm.week);
        }
        else {
          toaster.pop("error", "", res.data.message);
        }
      });
    }
  };

  vm.setClass = function(training_schedule, count, index) {
    var column = 12;
    var columnR = 12 - 1;
    var offset = index * Math.floor(columnR/count);
    if(count > 0){
      column = columnR -  offset;
    }
    column = column + " " + colorClassText(training_schedule) + " col-md-offset-" + offset;
    return "col-md-" + column;
  };

  vm.showTime = function(time){
    var show = time.indexOf(":00") >= 0;
    return show;
  };

  vm.openDownloadModal = function() {
    vm.downloadParams.started_from = vm.year + "/" + _.first(vm.days_of_week);
    vm.downloadParams.started_to = vm.year + "/" + _.last(vm.days_of_week);
    $("#download-modal").modal("show");
  }

  vm.checkValidDate = function(field) {
    vm.downloadParams[field] = checkValidDateFunction.checkValidDate(vm.downloadParams[field]) || vm.downloadParams[field];
  }

  vm.exportTrainingSchedules = function(e) {
    $.asyncDownload(e, "training_schedule_applicants", JSON.stringify({condition: _.merge(vm.searchParams, vm.downloadParams)}), "xlsx", {});
  };

  function hasApplied(training_schedule) {
    return !_.isEmpty(training_schedule.applieds_names);
  }

  function colorClassText(training_schedule) {
    var location = _.find(vm.locationOptions, function(option) {
      return option[0] == training_schedule.location_name;
    })
    var colorText = ((_.isEmpty(location)) ? LOCATION_COLORS[0] : location[2]);
    var hasAppliedText = (hasApplied(training_schedule)) ? "" : "no_apply_";
    return hasAppliedText + colorText + "_color";
  }

  // Training Group Functions
  vm.showGroupModal = function() {
    $("#group-form-modal").modal("show");
    setTrainingGroupParams();
    setDatePicker();
  };

  vm.closeGroupModal = function() {
    $("#group-form-modal").modal("hide");
    resetTrainingGroupParams();
  };

  vm.isDisabledBtnCreateGroup = function() {
    if (vm.trainingGroup.selectedDates.length == 0) return true;
  }

  vm.removeSchedule = function(index) {
    vm.trainingGroup.params.training_schedules.splice(index, 1);
  };

  vm.duplicateSchedule = function(schedule) {
    vm.trainingGroup.params.training_schedules.push(_.extend({}, schedule));
  };

  vm.assignToMe = function(index) {;
    var item = _.nth(vm.trainingGroup.params.training_schedules, index);
    _.set(item, 'person_in_charge_id', vm.currentAdminId);
    angular.element(document).ready(function() {
      var $element = $("#select2-person-in-charge-for-" + index);
      $element.val(vm.currentAdminId);
      $element.trigger("change");
    });
  }

  vm.createGroup = function(e) {
    addLoading(e.currentTarget);
    if (vm.enableCall) {
      vm.enableCall = false;
      trainingScheduleService.create(vm.trainingGroup.params).then(function(res) {
        removeLoading(e.currentTarget);
        if (res.data.status) {
          $("#group-form-modal").modal("hide");
          resetTrainingGroupParams();
          vm.enableCall = true;
          vm.callData(vm.current, vm.month, vm.week);
        } else {
          vm.enableCall = true;
          if (res.data.errors.length > 0) {
            _.map(res.data.errors, function(message) {
              toaster.pop("error", "", message);
            })
          } else {
            toaster.pop("error", "", res.data.message);
          }
        }
      });
    }
  };
  // ------

  // Functions
  function setDatePicker() {
    setTimeout(function() {
      $("#group-form-modal #training-group-datepicker").datepicker({
        format: 'mm/dd',
        clearBtn: true,
        todayBtn: true,
        todayHighlight: true,
        multidate: true,
        multidateSeparator: ", ",
      }).on("changeDate", function(e) {
        vm.trainingGroup.selectedDates = e.dates;
        vm.trainingGroup.params.dates = e.dates.map(function(date) {
          return moment(date).format("YYYY/MM/DD");
        })
      });
    }, 0);
  }

  function calculateEndTime(startTime) {
    var hours = startTime.split(":")[0];
    var minutes = startTime.split(":")[1];
    hours = parseInt(hours, 10);
    hours += 7;
    if (hours >= MAXIMUM_TRAINING_HOUR) {
      hours = MAXIMUM_TRAINING_HOUR;
      minutes = 0;
    }
    var endTimeString = hours.toString().padStart(2, "0") + ":" + minutes.toString().padStart(2, "0");
    return endTimeString;
  }

  function resetTrainingGroupParams() {
    vm.trainingGroup = _.extend({}, DEFAULT_TRAINING_GROUP_PARAMS);
    $("#group-form-modal #training-group-datepicker").datepicker("clearDates").trigger("change");
  }

  function setTrainingGroupParams() {
    var default_schedule = {
      location_id: vm.locationSelected,
      start_time: vm.fromSelected,
      end_time: vm.toSelected,
      total_portion: vm.candidateSelected,
    };
    var formattedDate = formatDateAndMonth(vm.current);
    vm.trainingGroup = _.extend({}, {
      selectedDates: [formattedDate],
      params: {
        training_schedules: [default_schedule],
        dates: [vm.current],
      }
    });
  }

  function addLoading(element) {
    angular.element(element).addClass("loading-skeleton");
    angular.element(element).prop("disabled", true);
  }

  function removeLoading(element) {
    angular.element(element).removeClass("loading-skeleton");
    angular.element(element).prop("disabled", false);
  }

  function formatDaysOfWeek(originalDates) {
    var formattedDates = originalDates.map(function (date) {
      return formatDateAndMonth(date);
    })

    return formattedDates
  }

  function formatDateAndMonth(dateString) {
    var dateComponent = dateString.split('/');
    var month = dateComponent[1];
    var day = dateComponent[2];

    return month + '/' + day;
  }
}
