'use strict'

angular.module('adminApp')
  .controller('RegistrationDetailsController', RegistrationDetailsController)

RegistrationDetailsController.$inject = ["$scope", "$controller", "registrationListService", "postalCodeService",  "$window", "toaster"];

function RegistrationDetailsController($scope, $controller, registrationListService, postalCodeService,  $window, toaster) {
  var vm = this;
  var staffDetails = $controller("StaffDetailsController", {$scope: $scope});
  vm.staff = {};
  vm.params = {};
  angular.extend(vm, staffDetails);
  vm.original_staff = angular.copy(vm.staff);
  vm.authenticated = false;
  vm.verify_password_error = false;
  var DATA_HOLDER_IDS = ['is_enabled_op_send_link_entry', 'is_enabled_op_confirm', 'is_enabled_reject',
    'staff_level_options', 'updated_profile', 'staff_recruitment_process'];
  var REGISTERED = 'registered';
  var RECRUITED = 'recruited';
  var DISABLED_TAB = 'admin-staff-tab-disabled';
  var SPECIAL_CHARACTERS = '********';
  var PERSONAL_IMAGES_ATTRS = ['residence_img_front', 'residence_img_back', 'certificate_img'];
  vm.disabledTab = function() {
    if (vm.status_id == REGISTERED || vm.status_id == RECRUITED ) {
      return DISABLED_TAB;
    }
  }

  vm.enabledWhenUpdatedProfile = function() {
    if (!vm.updated_profile) {
      return DISABLED_TAB;
    }
  }

  vm.initRegistration = function(staffId) {
    vm.staffId = staffId;
    vm.init();
    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });
    vm.initSuccess = true;
    vm.staff.birthday = SPECIAL_CHARACTERS;
    iniIndustries();
  }

  function initModalConfirm() {
    resetModal();
    if(vm.actionType == 'send-mail') {
      vm.modalTitle = I18n.t("admin.staff.details.send_correction_request");
      vm.modalCommentLabel = I18n.t("admin.staff.details.modal.evaluation_comment");
      vm.submitBtn = I18n.t("common.buttons.send");
    } else {
      vm.modalTitle = I18n.t("admin.staff.details.modal.approval");
      vm.modalCommentLabel = I18n.t("admin.staff.details.modal.evaluation_comment");
      if(vm.actionType == "op-approve") vm.submitBtn = I18n.t("common.buttons.save");
    }

    vm.commentError = false;
    vm.entryComment = "";
  }

  function resetModal() {
    vm.modalTitle = "";
    vm.modalEmailAddr = "";
    vm.modalCommentLabel = "";
  }

  vm.openConfirmModal = function(popupType) {
    vm.popupType = popupType;
    vm.popupTitle = '';
    vm.popupBody = '';
    if (popupType == 'approveSendMail') {
      vm.popupTitle = I18n.t("admin.staff.details.op_approved_registration");
      vm.popupBody = I18n.t("admin.staff.details.modal.recruit");
    } else if (popupType == 'rejectStaff') {
      vm.popupTitle = I18n.t("admin.staff.details.not_adopted");
      vm.popupBody = I18n.t("admin.staff.details.modal.reject");
    } else if (popupType == 'sendAuthenticateMail') {
      vm.popupTitle = I18n.t("admin.staff.details.send_auth_mail");
      vm.popupBody = I18n.t("admin.staff.details.modal.resend_mail");
    }
    angular.element("#confirm-popup").modal("show");
  }

  vm.executeAction = function(){
    angular.element("#confirm-popup").modal("hide");
    if (vm.popupType == 'approveSendMail') {
      vm.approveSendMail();
      return;
    }
    if (vm.popupType == 'rejectStaff') {
      vm.rejectStaff();
      return;
    }
    if (vm.popupType == 'sendAuthenticateMail') {
      vm.sendAuthenticateMail();
      return;
    }
  }

  vm.approveSendMail = function(){
    $("#spinner").removeClass("ng-hide");
    registrationListService.approveTemporaryStaff(vm.staffId).then(function(res){
      $("#spinner").addClass("ng-hide");
      var res = res.data;
      if (res.redirect_path)  {
        $window.location.href = res.redirect_path;
      } else {
        toaster.pop("error", "", res.message);
      }
    });
  }

  vm.rejectStaff = function() {
    $("#spinner").removeClass("ng-hide");
    registrationListService.rejectTemporaryStaff(vm.staffId).then(function(res){
      var res = res.data;
      $("#spinner").removeClass("ng-hide");
      $window.location.href = res.redirect_path;
    });
  }

  vm.checkForApproval = function() {
    vm.currentStep = "update-step";
    $("#admins-edit-registrations").submit();
    angular.element("#comment-confirm-modal").modal("hide");
  }

  vm.checkForApprovalOrSendMail = function() {
    if(_.includes(vm.actionType, "approve")) {
      vm.checkForApproval();
    } else if (vm.actionType == "send-mail") {
      vm.sendUpdateRegisteredProfileMail();
    }
  }

  vm.openApproveModal = function(status) {
    vm.status = status;
    vm.actionType = "op-approve";
    vm.$scope.$watch(vm.status, function() {
      vm.currentStep = "confirm-step";
      $("#admins-edit-registrations").submit();
    });
  };

  vm.sendAuthenticateMail = function() {
    registrationListService.sendAuthenticateMail({id: vm.staffId}).then(function(res) {
      location.reload();
    });
  };

  vm.openSendCorrectionRequestModal = function() {
    vm.actionType = "send-mail";
    initModalConfirm();
    angular.element("#comment-confirm-modal").modal("show");
  };

  vm.sendUpdateRegisteredProfileMail = function() {
    registrationListService.sendUpdateRegisteredProfileMail({id: vm.staff_id, comment: vm.entryComment})
      .then(function(res) {
      location.reload();
    });
  };

  $("#admins-edit-registrations").on("submit", function(e) {
    e.preventDefault();
    var actionURL = "/registrations/" + vm.staff_id;
    if (vm.currentStep == "confirm-step") {
      actionURL = "/registrations/"+vm.staff_id+"/check_update_staff";
    }
    $(".btn-op-confirm").prop('disabled', true);
    $.ajax({
      url: actionURL,
      method: 'PUT',
      dataType: 'json',
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        if (response.redirect_path) {
          $window.location.href = response.redirect_path;
        }
        var extraErrorElements = {
          base: ".base-staff-check-item",
          japanese_level: ".japanese-level-staff-check-item",
          working_other_place: ".working-other-place-staff-check-item",
          working_time: ".working-time-staff-check-item",
          is_request_seal: ".is-request-seal-staff-check-item",
          base_staff_expectation: ".base-staff-expectation"
        };
        $(".btn-op-confirm").prop('disabled', false);
        $.lawsonAjaxStaffEditForm(response, extraErrorElements, true);
      }, complete: function(res) {
        if(res.responseJSON.status && _.isEqual(vm.currentStep, "confirm-step")) {
          $.resetFormError();
          initModalConfirm();
          vm.$scope.$apply(function() {
            angular.element("#comment-confirm-modal").modal("show");
          });
        } else if(!_.isUndefined(res.responseJSON.success) && !res.responseJSON.success) {
          location.reload();
        }
      }
    })
  });

  vm.verifyAdminPassword = function() {
    $("#verify-admin-password-modal").modal("show");
  }

  vm.viewStaffInfos = function(e) {
    var submitParams = {
      password: vm.verify_admin_password,
      staff_id: vm.staff_id
    }
    $("#spinner").removeClass("ng-hide");
    registrationListService.viewRegistrationInfos(submitParams).then(function(res){
      var res = res.data;
      if (res.status) {
        $("#verify-admin-password-modal").modal("hide");
        vm.verify_admin_password = "";
        vm.authenticated = true;
        var personalInfos = res.personal_infos.staff;
        var registrationAnswer = res.personal_infos.registration_answer;
        vm.staff.birthday = personalInfos.birthday;
        setTimeout(function() {
          unLockedPersonalInfos(personalInfos)
          unLockedRegistrationAnswer(registrationAnswer);
        }, 0);
      } else if (res.redirect_path){
        $window.location.href = res.redirect_path;
      } else {
        vm.verify_password_error = true;
        vm.verify_password_error_class = "has-error";
      }
      $("#spinner").addClass("ng-hide");
    });
  }

  vm.handleInputPassword = function(e) {
    var keyCode = e.keyCode || e.which;
    if (keyCode === 13) {
      vm.viewStaffInfos(e);
      return false;
    }
  }

  function unLockedPersonalInfos(staffInfos){
    _.forEach(staffInfos, function(info, key){
      if (_.includes(PERSONAL_IMAGES_ATTRS, key)) {
        var imageElement = $(".personal_"+key);
        var jpImageElement = $(".jp_personal_"+key);
        imageElement.attr("src", info.url);
        jpImageElement.attr("src", info.url);
      }
      var element = $("[name$='staff[" + key + "]']");
      element.attr("value", info);
    });
  }

  function unLockedRegistrationAnswer(answer){
    if (answer == null || answer.upload_file_1.url == null) {
      return
    }
    $(".upload_file_1").attr("src", answer.upload_file_1.url);
  }

  vm.confirmSave = function() {
    $("#confirm-save").modal("show");
  }

  vm.updateRegistration = function() {
    $("#confirm-save").modal("hide");
    $("#spinner").removeClass("ng-hide");

    var params = {staff: vm.staff}
    params.staff["staff_recruitment_process_attributes"] = vm.staff_recruitment_process;
    if (!vm.skip_registration_answer) {
      if (!vm.registration_answer.has_lawson_experience) {
        vm.registration_answer.answer_4 = vm.registration_answer.answer_4 || null;
        vm.registration_answer.answer_5 = vm.registration_answer.answer_5 || null;
      }
      vm.registration_answer.answer_3 = vm.profession_ids.join(",");
      params["registration_answer"] = vm.registration_answer;
    }
    registrationListService.updateRegistration(vm.staff.id, params).then(function(res) {
      $("#spinner").addClass("ng-hide");
      res = res.data;
      $(".has-error").removeClass("has-error");
      $(".help-block").remove();
      if (res.status) {
        vm.original_staff = vm.staff;
        toaster.pop("success", "", res.messages[0]);
      } else if (res.redirect_path) {
        $window.location.href = res.redirect_path
      } else {
        handleFormErrors(res.errors.staff);
        handleFormErrors(res.errors.registration_answer);
        toaster.pop("error", "", res.messages[0]);
      }
    });
  }

  function handleFormErrors(errors) {
    if (errors) {
      $.each(errors, function(column, errorMessage) {
        var inputElement = $("body").find("[name$='[" + column +"\]']")[0];
        var element = $(inputElement).closest('.form-group');
        element.addClass("has-error");
        var $inputBox = $(inputElement).closest('.input-box');
        $inputBox.append("<span class='help-block err-msg'>" + errorMessage[0] + "</span>");
      });
    }
  }

  vm.$scope.$watch("vm.staff.prefecture_id", function() {
    registrationListService.getStations({prefecture_id: vm.staff.prefecture_id}).then(function(res) {
      vm.homeStations = res.data.stations;
    });
  });

  vm.$scope.$watch("vm.staff.staff_account_transfer.bank_id", function() {
    if (vm.staff.staff_account_transfer != undefined &&
        vm.staff.staff_account_transfer.bank_id) {
      vm.getBankBranches(vm.staff.staff_account_transfer.bank_id);
    } else {
      vm.bank_branches = [];
    }
  });

  vm.getBankBranches = function(bankId){
    registrationListService.getBankBranches({bank_id: bankId}).then(function(res) {
      vm.bank_branches = res.data.bank_branches;
    });
  }

  vm.initRegistrationPostalCode = function(e) {
    $(".open-modal-postal-code").data("modal-active", 0);
    $(e.target).data("modal-active", 1);
    vm.currentPage = 1;

    vm.params = {
      page: 1,
      postal_code: ""
    };

    vm.refresh();
  }

  vm.refresh = function() {
    vm.params.page = vm.currentPage;
    vm.params.postal_code = vm.postalCode;
    postalCodeService.getPostalCode(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.postal_codes.length);
      vm.noRecord = !vm.hasRecord;
      vm.params.limit = res.data.pagination_limit;
    }, function(error) {
    });
  }

  function iniIndustries() {
    var registrationAnswer = angular.element("#registration_answer").data("infos");
    vm.business_circles = angular.element("#business_circles").data("infos");
    vm.business_circle_id = angular.element("#business_circle_id").data("infos");
    vm.industry_id = angular.element("#industry_id").data("infos");
    vm.industries = angular.element("#industries").data("infos");
    vm.profession_ids = angular.element("#profession_ids").data("infos");
    vm.professions = angular.element("#professions").data("infos");
    vm.original_industry_id = angular.copy(vm.industry_id);
    vm.groupedIndustries = _.groupBy(vm.industries, "business_circle_id");
    vm.groupedProfessions = _.groupBy(vm.professions, "industry_id");
    if (!!registrationAnswer) {
      vm.skip_registration_answer = false;
      vm.registration_answer = registrationAnswer;
    } else {
      vm.skip_registration_answer = true;
      vm.registration_answer = {
        answer_1: null,
        answer_2: null,
        answer_4: null,
        answer_5: null,
        answer_3: null,
        has_lawson_experience: null
      };
    }
    refreshCurrentIndustries();
    refreshCurrentProfessions(false);
  }

  vm.checkProfessionOption = function(id) {
    var isChecked = document.getElementById("answer_3_" + id).checked;
    if (!_.includes(vm.profession_ids, id) && isChecked) {
      vm.profession_ids.push(id);
    } else if (!isChecked) {
      vm.profession_ids = _.filter(vm.profession_ids, function(profId) {
        return profId != id;
      });
    }
  };

  vm.changeBusinessCircle = function() {
    refreshCurrentIndustries();
    refreshCurrentProfessions(true);
  };

  vm.changeIndustry = function() {
    refreshCurrentProfessions(true);
  };

  function refreshCurrentIndustries() {
    if (!vm.business_circle_id) {
      vm.currentIndustries = [];
    } else {
      vm.currentIndustries = vm.groupedIndustries[vm.business_circle_id];
    }
  }

  function refreshCurrentProfessions(isReset) {
    if (!!isReset) {vm.profession_ids = []};
    if (!vm.industry_id) {
      vm.currentProfessions = [];
    } else {
      vm.currentProfessions = vm.groupedProfessions[vm.industry_id];
    }
  }
}