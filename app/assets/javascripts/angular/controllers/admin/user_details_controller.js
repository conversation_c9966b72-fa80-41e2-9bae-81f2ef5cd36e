"use strict";

angular.module("adminApp").controller("UserDetailsController", UserDetailsController);

UserDetailsController.$inject = ["userDetailsService"];

function UserDetailsController(userDetailsService){
  var vm = this;
  vm.verify_password_error = false;
  vm.authenticated = false;
  vm.isSubmitting = false;

  vm.init = function(actionName, userId){
    vm.isNewObject = actionName == "new";
    vm.userId = userId;
  }

  vm.authenticate = function(){
    if (vm.isNewObject) {
      return false;
    }
    return vm.authenticated;
  }

  vm.verifyAdminPassword = function() {
    $("#verify-admin-password-modal").modal("show");
  }

  vm.viewOwnerInfos = function() {
    if (vm.isNewObject) {
      return
    }
    var submitParams = {
      password: vm.verify_admin_password,
      user_id: vm.userId
    }
    $("#spinner").removeClass("ng-hide");
    userDetailsService.viewOwnerInfos(submitParams).then(function(res){
      var res = res.data;
      if (res.status) {
        $("#verify-admin-password-modal").modal("hide");
        vm.verify_admin_password = "";
        vm.authenticated = true;
        $("#user_account_email").attr("value", res.email);
        $("#user_account_email").prop("disabled", false);
      } else {
        vm.verify_password_error = true;
        vm.verify_password_error_class = "has-error";
      }
      $("#spinner").addClass("ng-hide");
    });
  }

  vm.handleInputPassword = function(e) {
    var keyCode = e.keyCode || e.which;
    if (keyCode === 13) {
      vm.viewOwnerInfos();
      return false;
    }
  }

  vm.sendRemindLoginMail = function() {
    $("#confirmation-modal").modal("show");
  }

  vm.confirmedSubmitDetail = function() {
    vm.isSubmitting = true;
    userDetailsService.sendRemindLoginMail({user_id: vm.userId}).then(function() {
      location.reload();
    });
  }

  $('#admins-edit-user').on('keyup keypress', function(e) {
    var keyCode = e.keyCode || e.which;
    if (keyCode === 13) { 
      e.preventDefault();
      return false;
    }
  });
}