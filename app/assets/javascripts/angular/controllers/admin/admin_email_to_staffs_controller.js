"use strict";

angular.module("adminApp").controller("AdminEmailToStaffsController", AdminEmailToStaffsController);
AdminEmailToStaffsController.$inject = ["$location", "$scope", "$window", "AdminEmailToStaffService", "checkValidDateFunction", "toaster"];

function AdminEmailToStaffsController($location, $scope, $window, AdminEmailToStaffService, checkValidDateFunction, toaster) {
  var $listResult = $("#email-list-result");
  var $fromDateInput = $("#sent-date-from-date");
  var $toDateInput = $("#sent-date-to-date");
  var $modalRemoveEmail = $("#modal-remove-email");
  var $confirmSaveFormANS = $("#confirm-save-admin-email-to-staff");
  var $emailInformation = $("#email-information")
  var $notValid = $("#not-valid-popup");
  var $successPopup = $("#success-popup");
  var $reloadBtn = $("#reload-information");
  var STAFF_STATUSES = ["active", "inactive", "required_re_training"];
  var STAFF_LEVELS = ["before_debut", "debut", "not_require_training", "oboj"];
  var STAFF_REGISTRATION_TYPES = ["single_haken", "haken", "matchbox", ""];
  var DEFAULT_AUDIENCE = "to_staff";
  var DEFAULT_TARGET = "excluded_ids";

  var vm = this;
  vm.$scope = $scope;
  vm.error_message = "";
  vm.params_create = {};
  vm.params_create.to_audience = DEFAULT_AUDIENCE;
  vm.params_create.staff_status = STAFF_STATUSES;
  vm.params_create.staff_levels = STAFF_LEVELS;
  vm.params_create.registration_types = STAFF_REGISTRATION_TYPES;
  vm.params_create.target_type = DEFAULT_TARGET;
  vm.params_create.target_staff_ids = [];
  vm.submited = false;
  var requestParams = $location.search();
  var currentDateTime = moment();
  vm.hasRecord = false;
  vm.params = {};
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.admin_email_to_staffs = [];
  vm.admin_email_to_staff = {};
  vm.review_email = {};
  vm.total_items = 0;
  vm.step = "input";
  var formatDatePicker = {
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true
  };
  $fromDateInput.datepicker(formatDatePicker);
  $toDateInput.datepicker(formatDatePicker);
  vm.params.department_ids = [];
  vm.params.prefecture_ids = [];
  if (!!requestParams.department_ids) {
    vm.params.department_ids = requestParams.department_ids.split(",");
  }
  if (!!requestParams.prefecture_ids) {
    vm.params.prefecture_ids = requestParams.prefecture_ids.split(",");
  }
  vm.params.to_audience = DEFAULT_AUDIENCE;
  if (!!requestParams.to_audience) {
    vm.params.to_audience = requestParams.to_audience;
  }
  vm.params.page = requestParams.page || 1;
  vm.params.per_page = requestParams.page || vm.perPageSettings[0];
  vm.params.from_date = requestParams.from_date || "";
  vm.params.to_date = requestParams.to_date || "";

  $modalRemoveEmail.on("hidden.bs.modal", function (e) {
    vm.emailRemove = undefined;
  });

  vm.init = function() {
    vm.search();
    vm.initCheckbox();
  };

  vm.search = function(isResetPage) {
    if (!!isResetPage) {vm.params.page = 1;}
    var searchParams = {
      search: vm.params,
      page: vm.params.page,
      per_page: vm.params.per_page
    };
    AdminEmailToStaffService.searchEmail(searchParams).then(function(res) {
      vm.total_items = 0;
      vm.admin_email_to_staffs = [];
      if (res.status === 200) {
        angular.extend(vm, res.data);
        vm.params.page = res.data.page;
      }
      vm.hasRecord = vm.admin_email_to_staffs.length > 0;
      vm.copyParams = angular.copy(vm.params);
      vm.copyParams.department_ids = vm.params.department_ids.joins(",");
      vm.copyParams.prefecture_ids = vm.params.prefecture_ids.joins(",");
      $location.search(vm.copyParams).replace();
    });
  };

  vm.checkValidDataDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  };

  vm.changePerPage = function() {
    vm.search();
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $listResult.offset().top
    }, 1000);
  };

  vm.confirmCreate = function() {
    vm.review_email = {};
    AdminEmailToStaffService.reviewEmail({admin_email_to_staff: vm.params_create}).then(function(response) {
      vm.review_email = response.data
      if (vm.review_email.status) {
        vm.step = "review";
        vm.error_message = "";
        $confirmSaveFormANS.modal("show");
      } else {
        vm.step = "input";
        vm.error_message = vm.review_email.message;
        $notValid.show();
        setTimeout(function() {
          $notValid.hide();
        }, 4000);
      }
    })
  }

  vm.reviewEmail = function() {
    vm.review_email = {};
    AdminEmailToStaffService.reviewEmail({admin_email_to_staff: vm.params_create}).then(function(response) {
      vm.review_email = response.data
      if (vm.review_email.status) {
        vm.error_message = "";
        vm.step = "review";
      } else {
        vm.step = "input";
        vm.error_message = vm.review_email.message;
        $notValid.show();
        setTimeout(function() {
          $notValid.hide();
        }, 4000);
      }
    })
  }

  vm.togglesChkbox = function(modelName, value) {
    var valueIndex = modelName.indexOf(value);
    if (valueIndex !== -1) {
      modelName.splice(valueIndex, 1);
    } else {
      modelName.push(value);
    }
  }

  vm.initCheckbox = function(){
    if (vm.params_create.staff_status.length == 0) {
      vm.params_create.staff_status = STAFF_STATUSES;
    }
    if (vm.params_create.staff_levels.length == 0) {
      vm.params_create.staff_levels = STAFF_LEVELS;
    }
  }

  vm.backToStep1 = function() {
    vm.step = "input";
    vm.review_email = {};
  }

  vm.currentSelectClass = function() {
    return vm.params_create.to_audience == DEFAULT_AUDIENCE ? 'select-staff-ids' : 'select-registration-ids';
  }

  vm.createAdminMailToStaff = function() {
    $confirmSaveFormANS.modal("hide");
    vm.submited = true;
    AdminEmailToStaffService.createEmail({admin_email_to_staff: vm.params_create}).then(function(response) {
      var data = response.data
      if (!data.status) {
        vm.submited = false;
        $notValid.show();
        vm.error_message = data.message;
        setTimeout(function() {
          $notValid.hide();
        }, 4000);
      } else {
        vm.submited = true;
        vm.error_message = "";
        $notValid.hide();
        $successPopup.show();
        setTimeout(function() {
          $window.location.href = data.redirect_path;
        }, 1000);
      }
    })
  }

  vm.showEmailInfomation = function(admin_email_to_staff, showModal) {
    vm.admin_email_to_staff = admin_email_to_staff;
    if (!showModal) {$reloadBtn.attr("disabled", true);}
    AdminEmailToStaffService.showEmailInfomation(admin_email_to_staff.id).then(function(res) {
      if (res.status == 200) {
        vm.admin_email_to_staff = res.data.data;
        if (showModal) {
          $emailInformation.modal("show");
        } else {
          $reloadBtn.removeAttr("disabled");
        }
      }
    })
  }

  vm.getInformation = function(admin_email_to_staff_id) {
    vm.params_create = {};
    AdminEmailToStaffService.showEmailInfomation(admin_email_to_staff_id).then(function(res) {
      if (res.status == 200) {
        vm.params_create = res.data.data;
        vm.initCheckbox();
      }
    })
  }

  vm.disableCreate = function() {
    var validData = false;
    if (vm.params_create.to_audience == 'to_staff') {
      validData = !!vm.params_create.department_ids;
    } else {
      validData = !!vm.params_create.prefecture_ids;
    }
    validData =  validData && !!vm.params_create.subject_mail && !!vm.params_create.content;
    return vm.submited || !validData;
  }

  vm.pauseSendEmail = function(admin_email_to_staff) {
    AdminEmailToStaffService.pauseSendEmail(admin_email_to_staff.id).then(function(res) {
      var data = res.data;
      if (data.status) {
        vm.search();
      }
    });
  };

  vm.continueSendEmail = function(admin_email_to_staff) {
    AdminEmailToStaffService.continueSendEmail(admin_email_to_staff.id).then(function(res) {
      var data = res.data;
      if (data.status) {
        vm.search();
      }
    });
  };
}
