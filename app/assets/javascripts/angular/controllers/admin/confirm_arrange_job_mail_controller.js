"use strict";

angular.module("adminApp")
  .controller("ConfirmArrangeJobMailController", ConfirmArrangeJobMailController);
ConfirmArrangeJobMailController.$inject = ["arrangementService", "$scope"];

function ConfirmArrangeJobMailController(arrangementService, $scope) {
  var vm = this;
  var $progressElm = angular.element("#send-confirm-job-mail-status div[role=progressbar]");
  var $statusElm = angular.element("#send-confirm-job-mail-status");
  var $confirmArrangeJobMailModal = angular.element("#confirm-arrange-job-mail-modal");
  var $sendingModal = angular.element("#confirm-arrange-mail-sending-modal");
  var $sentMsgModal = angular.element("#confirm-arrange-mail-sent-msg-modal");
  var $errorStopArrangementModal = angular.element("#error-stop-arrangement-modal");
  var STAFF_ARRANGEMENT_TYPE = {"lack_of_submission": 1, "attendance_ng": 2, "grooming_ng": 3, "arrangement_stop": 4, "automatic_matching_ng": 5};
  var MAX_FAILED_NUMBER = 1;
  vm.isShowErrorStopArrangement = false;

  vm.$scope = $scope;
  vm.$scope.$watch("vm.$scope.$parent.confirmArrangeJobMailModal", function() {
    init();
    if (vm.$scope.$parent.confirmArrangeJobMailModal) {
      vm.init = false;
      loadArrangements();
    }
  });

  init();

  vm.loadMore = function() {
    if (!vm.init) return;
    if (vm.arrangements.length < vm.totalItems && vm.params.page == vm.params.currentPage) {
      vm.params.page++;
      loadArrangements();
    }
  };

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
    }
    vm.params.page = 1;
    vm.isSorting = true;
    loadArrangements();
  };

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !vm.params.desc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !!vm.params.desc
    };
  };

  vm.changeCheckedStateOfArrangements = function(state, arrangements) {
    arrangements.forEach(function(arrangement) {
      arrangement.is_check = angular.copy(state);
    });
    return arrangements;
  };

  vm.isDisabledSendBtn = function() {
    var idx = vm.arrangements.findIndex(function(arrangement) {
      return !!arrangement.is_check;
    });
    return idx === -1;
  };

  vm.selectedArrangement = function() {
    return vm.arrangements.filter(function(arrangement) {
      return !!arrangement.is_check;
    });
  };

  vm.hasStopArrangementStaff = function() {
    vm.isShowErrorStopArrangement = false;
    vm.arrangements.filter(function(arrangement) {
      var arrangement_type = arrangement.staff.arrangement_type;
      if (!!arrangement.is_check && arrangement_type.includes(STAFF_ARRANGEMENT_TYPE.arrangement_stop)) {
        vm.isShowErrorStopArrangement = true;
        return;
      }
    });
  };

  vm.selectedTxt = function() {
    var numCurrentSelectedArrange = vm.selectedArrangement().length;
    vm.hasStopArrangementStaff();
    var numNotLoadArrangement = vm.totalItems - vm.arrangements.length;
    var numSelected = vm.usedToCheckedAll ? numCurrentSelectedArrange + numNotLoadArrangement : numCurrentSelectedArrange;
    return I18n.t("admin.arrangements.modal.confirm_arrange_job_mail.selected",
      {num_selected: numSelected, total: vm.totalItems})
  };

  vm.checkOrUncheckAll = function() {
    vm.changeCheckedStateOfArrangements(vm.isCheckAll, vm.arrangements);
    vm.usedToCheckedAll = !!vm.isCheckAll;
  };

  vm.send = function() {
     if (vm.isShowErrorStopArrangement) {
      showModal($errorStopArrangementModal);
      return;
    }

    var isSentForNotYetLoadedArrangements = vm.usedToCheckedAll && vm.arrangements.length < vm.totalItems;
    var formatedArrangements = vm.arrangements.map(function(arrangement) {
      return {
        id: arrangement.id,
        store_note: arrangement.store_note,
        arrange_comment: arrangement.arrange_comment,
        is_check: arrangement.is_check,
        is_change: arrangement.is_change
      }
    });
    var params = {is_send_all: isSentForNotYetLoadedArrangements,
      arrangements: formatedArrangements};
    vm.isSubmiting = true;
    showModal($sendingModal);
    arrangementService.sendConfirmArrangeJobMail(params).then(function(res) {
      var jobId = res.data.jid;
      var intervalName = "job_" + jobId;
      var arrangedArrangementIds = res.data.arrangement_ids;
      $statusElm.toggleClass("hidden");
      _.forEach(arrangedArrangementIds, function(arrangementId) {
        angular.element("#arrangement-history-" + arrangementId).removeClass("ng-hide");
      });
      window[intervalName] = setInterval(function() {
        getExportJobStatus(jobId, intervalName);
      }, 5000);
    }, function(err) {
      hideModal($sendingModal);
    });
  };

  vm.rowChange = function(arrangement) {
    if (!_.isEqual(arrangement.store_note, arrangement.original_store_note) ||
      !_.isEqual(arrangement.arrange_comment, arrangement.original_arrange_comment)) {
      arrangement.is_change = true;
    }
  };

  vm.addCheckChangeField = function(arrangements) {
    arrangements.forEach(function(arrangement) {
      arrangement.original_store_note = angular.copy(arrangement.store_note);
      arrangement.original_arrange_comment = angular.copy(arrangement.arrange_comment);
    });
    return arrangements;
  };

  vm.closeAllModal = function(){
    hideModal($sentMsgModal);
    hideModal($confirmArrangeJobMailModal);
  };

  function loadArrangements() {
    vm.isLoading = true;
    angular.extend(vm.params, arrangementService.searchParams);
    arrangementService.getTemporaryArrangeArrangements({search: vm.params}).then(function(res) {
      var newArrangements = vm.changeCheckedStateOfArrangements(vm.usedToCheckedAll, res.data.arrangements);
      if (vm.isSorting) {
        vm.arrangements = newArrangements;
        vm.isSorting = false;
      } else {
        vm.arrangements = vm.arrangements.concat(newArrangements);
      }
      initValueOfArrangmenent();
      vm.totalItems = res.data.total_items;
      vm.params.currentPage = res.data.page;
      vm.init = true;
      vm.isLoading = false;
    });
  }

  function initValueOfArrangmenent() {
    vm.arrangements.forEach(function(arrangement){
      arrangement.store_note = arrangement.location_caution_to_staff_mail;
      arrangement.is_change = true;
    });
  }

  function getExportJobStatus(jobId, intervalName) {
    arrangementService.getArrangeJobMailWorkerStatus({job_id: jobId}).then(function(res) {
      var percentage = res.data.percentage;

      if (res.data.status === "complete") {
        setTimeout(function() {
          deleteIntervalJob(intervalName);
          $statusElm.toggleClass("hidden");
        }, 500);
        hideModal($sendingModal);
        vm.$scope.$parent.vm.getStatisticData();
        vm.sentFailed = false;
        showModal($sentMsgModal);
      } else if ((res.data.status === "retrying" || res.data.status === "queued" || res.data.status === "failed") && res.data.percentage == null) {
        vm.failedRequestNumber += 1;

        if (vm.failedRequestNumber >= MAX_FAILED_NUMBER) {
          deleteIntervalJob(intervalName);
          $statusElm.toggleClass("hidden");
          hideModal($sendingModal);
          vm.sentFailed = true;
          showModal($sentMsgModal);
        }
      }
    }, function(err) {
      deleteIntervalJob(intervalName);
      $statusElm.toggleClass("hidden");
      hideModal($sendingModal);
      vm.sentFailed = true;
      showModal($sentMsgModal);
    });
  }

  function deleteIntervalJob(intervalName) {
    clearInterval(window[intervalName]);
    delete window[intervalName];
    vm.failedRequestNumber = 0;
  }

  function showModal($modal) {
    $modal.modal("show");
  }

  function hideModal($modal) {
    $modal.modal("hide");
  }

  function init() {
    vm.params = {page: 1};
    vm.arrangements = [];
    vm.usedToCheckedAll = false;
    vm.isCheckAll = false;
    vm.failedRequestNumber = 0;
    vm.isSubmiting = false;
    vm.sentFailed = false;
  };
}
