"use strict";

angular.module("adminApp").controller("CreateOrderController", CreateOrderController);
CreateOrderController.$inject = ["$scope", "$sce", "checkValidDateFunction", "orderService",
  "postalCodeService"];

function CreateOrderController($scope, $sce, checkValidDateFunction, orderService, postalCodeService) {
  var VIOLATION_DAYS = 60;
  var MIN_STAFF = 1;
  var MAX_STAFF = 100;
  var NUMBER_BASE = 10;
  var MAX_REST_TIMES = 3;
  var MIN_REST_TIMES = 1;
  var REST_TIMES = [1, 2, 3];
  var DEFAULT_ORDER_SEGMENT_ID = "haken";
  var MIDNIGHT = "00:00";
  var vm = this;
  var picsData = ["claim", "haken_destination", "mandator", "order",
    "is_valid_order_info", "violation_day", "days_to_violation_day", "order_pic_tel",
    "prefecture_id", "is_approval_required", "closed_day_error_message", "transaction_error_message",
    "location_not_survey_error_message"];
  var paramsStep1 = ["definition_id", "corporation_id", "corporation_group_id", "organization_id",
    "location_id", "haken_destination_pic_id", "haken_destination_pic_position", "haken_destination_pic_tel",
    "mandator_id", "mandator_position", "mandator_tel", "claim_pic_id", "claim_pic_position",
    "claim_pic_tel", "order_pic_id", "order_pic_email", "order_pic_tel", "note", "order_segment_id",
    "is_fixed_term_project", "is_limited_day"];
  var CORPORATION_BILLING_ATTRS = ["billing_name", "billing_postal_code",
    "billing_organization_id", "billing_prefecture_id", "billing_city", "billing_street_number",
    "billing_building", "billing_tel", "billing_fax", "is_use_current_billing_address", "billing_organizations"];
  var BREAK_TIME_ATTRS = ["break_time", "rest1_started_at", "rest1_ended_at", "rest2_started_at", "rest2_ended_at",
    "rest3_started_at", "rest3_ended_at"];
  var ORDER_BRANCH_DATA = ["started_at", "working_start_time", "working_end_time", "staff_count",
    "is_time_changable", "is_urgent", "is_special_offer", "special_offer_fee", "special_offer_note"];
  var COLLECTIVE_ORDER_DATA = ["selected_days", "is_except_holiday", "week_count", "is_time_changable", "is_urgent"];
  vm.orderBranchDays = ["is_sunday", "is_monday", "is_tuesday", "is_wednesday", "is_thursday",
    "is_friday", "is_saturday"];
  var ORDER_LOCATION_DATA = ["postal_code", "tel", "fax", "is_store_parking_area_usable"];
  var TEMPLATE_ATTRS = ["billing_basic_unit_price", "billing_night_unit_price", "area_allowance", "short_allowance",
    "absence_discount", "tax_exemption", "payment_basic_unit_price", "payment_night_unit_price", "transportation_fee"];
  var SEGMENT_TRAINING = "training";
  var TRAINING_NOT_APPLICABLE = "training_not_applicable";
  var TRAINING_NOT_SCHEDULED = "training_not_scheduled";

  vm.$scope = $scope;
  vm.originLocationData = {};
  vm.dayNames = I18n.t("date.abbr_day_names");
  vm.params = {};
  vm.selectedLocation = {};
  vm.params.is_used_existing_pic = true;
  vm.params.is_use_current_billing_address = true;
  vm.params.is_fixed_term_project = false;
  vm.params.is_limited_day = false;
  vm.params.over_limit_portion = false;
  vm.params.under_minimum_wage = false;
  vm.selectedType = "individual_order";
  vm.totalCollectiveOrder = vm.totalIndividualOrder = 0;
  vm.steps = {
    step1: true,
    step2: false,
    step3: false
  };
  vm.isEditOrder = false;
  vm.location = [];
  vm.selectedLocation = {isValidLocation: true, haken_destination: [], claim: [], mandator: [], order: []}
  _.forEach(paramsStep1, function(attr) {
      vm.params[attr] = "";
      if (attr === "order_segment_id") {
        vm.params[attr] = DEFAULT_ORDER_SEGMENT_ID;
      }
  });
  vm.orderData = {};
  vm.templates = {};

  vm.params.individual_order = [
    {
      order_number: 1,
      break_time: 0,
      is_special_offer: false,
      rest_times: [1]
    }
  ];

  vm.orderDataList = {};

  vm.params.collective_order = [
    {
      order_number: 1,
      break_time: 0,
      week_count: 1,
      is_special_offer: false,
      rest_times: [1]
    }
  ];

  vm.params.bill_paym_temp = {};

  vm.breakTimeOptions = [0, 15, 30, 45, 60, 75, 90, 99];
  vm.durationOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
  vm.specialOfferFeeOptions = SPECIAL_OFFER_FEE;
  vm.estimateData = [];
  vm.totalEstimation = {individual_order: [], collective_order: []};
  vm.canSubmitStep1 = true;
  vm.canSubmitStep3 = true;
  vm.isValidLocation = true;
  vm.selectedCorporationGroup = {};
  vm.corporations = angular.element(".corporation-options").data("corporation-options");
  vm.billingPrefectures = angular.element(".billing-prefectures").data("billing-prefectures");
  vm.types = {"batch": "collective_order", "template": "template_order", "individual": "individual_order"}
  vm.isSelectedLocation = false;
  vm.violationDayUnchanged = true;
  vm.locationSurveyUnchanged = true;
  vm.isNotOneManOperation = true;

  vm.init = function (orderData) {
    vm.params.is_used_existing_pic = false;
    var orderDetail = orderData.order;

    if (orderData) {
      vm.isCopy = true;
      vm.params.definition_id = orderDetail.definition_id;
      vm.params.corporation_id = orderDetail.corporation_id;
      vm.locations = orderData.locations;
      vm.organizations = orderData.organizations;

      vm.params.organization_id = orderDetail.organization_id.toString();
      vm.params.organization_full_name = orderDetail.organization_full_name;
      vm.params.location_id = orderDetail.location_id.toString();
      vm.selectedLocation.prefecture_id = orderData.location_pics.prefecture_id;
      vm.is_lawson_staff = orderData.is_lawson_staff;

      loadLocationData()
      _.forEach(picsData, function(pic_data) {
        vm.selectedLocation[pic_data] = orderData.location_pics[pic_data];
      });
      vm.checkLocationIsValid();
      vm.checkViolationDay();
      vm.checkSubmitStep1Condition();
      if (vm.selectedLocation.isValidLocation) {
        vm.initLocationPicData(orderDetail);
      }
      vm.loadBillingData();
      vm.params.type_id = orderDetail.type_id;
      vm.params.is_used_existing_pic = orderDetail.is_used_existing_pic;
      if (!vm.is_lawson_staff) {
        if (_.isNull(orderDetail.is_use_current_billing_address)) {
          vm.params.is_use_current_billing_address = true;
        } else {
          vm.params.is_use_current_billing_address = orderDetail.is_use_current_billing_address;
        }
      }
      vm.params.note = orderDetail.note;
      vm.params.copied_from_id = orderDetail.copied_from_id;
      vm.params.order_segment_id = orderDetail.order_segment_id;
      vm.params.training_session_code = orderDetail.training_session_code;
      vm.params.training_schedule_code = orderDetail.training_schedule_code;
      vm.params.is_fixed_term_project = !!orderDetail.is_fixed_term_project;
      vm.params.is_limited_day = !!orderDetail.is_limited_day;

      initEstimateOrder(orderDetail);
    } else if (!_.isUndefined(vm.organizationOptions) && vm.organizationOptions[0]) {
      vm.params.organization_id = vm.organizationOptions[0][1].toString();
      vm.changeLocationOptions();
    };

    vm.initSaveOrderValues(false);
  };

  function initEstimateOrder(order) {
    var typeId = (order.type_id === "individual") ? order.type_id + "_order" : "collective_order";
    vm.selectedType = typeId;
    vm.estimateData = vm.totalEstimation[typeId];

    orderService.orderBranchesDetail({order_id: order.copied_from_id, is_copied: true}).then(function(res) {
      var orderBranches = res.data;
      _.forEach(Object.keys(orderBranches), function(key) {
        orderBranches[key] = vm.initOrderBranchRestTime(orderBranches[key]);
      });
      vm.params[vm.selectedType] = orderBranches;
      vm.params[vm.selectedType] = _.map(res.data, function(order) {
        delete order.id;
        return order;
      });
      initOrderBranchDetail();
    });

    if (typeId === "collective_order") {
      $("div #single-order").removeClass("active in");
      $(".individual_order").closest("li").removeClass("active in");
      $("div #multi-order").addClass("active in");
      $(".collective_order").closest("li").addClass("active in");
    }
  }


  vm.loadLocations = function() {
    if (_.isNaN(vm.params.corporation_id)) {
      vm.locations = {};
      vm.selectedLocation = {isValidLocation: true};
      vm.initLocationPicData();
      vm.canSubmitStep1 = true;
      return;
    }
    orderService.loadLocations({
      corporation_id: vm.params.corporation_id
    }).then(function(res) {
      angular.extend(vm, res.data);
      vm.selectedLocation = {};
      vm.selectedLocation.isValidLocation = true;
      vm.canSubmitStep1 = true;
    }, function(error) {
    });
  };

  vm.initLocationPicData = function(order) {
    var types = ["haken_destination", "claim"];
    var isDeleted = false;
    
    if (order) {
      isDeleted = !_.find(vm.selectedLocation.order, function(o) {
        return o.id == order.order_pic_id;
      });
    }

    if (isDeleted) {
      vm.params["order_pic_id"] = vm.params["order_pic_email"] = "";
    } else if (order) {
      vm.params["order_pic_id"] = order.order_pic_id;
      vm.params["order_pic_email"] = order.order_pic_email;
    } else if (vm.isCopy) {
      vm.params["order_pic_id"] = getSelectedLocationProperty("order", "id");
      vm.params["order_pic_email"] = getSelectedLocationProperty("order", "email");
    } else {
      vm.params["order_pic_id"] = vm.params["order_pic_email"] = "";
    };

    _.forEach(types, function(type) {
      if (order) {
        vm.params[type + "_pic_id"] = order[type + "_pic_id"];
        vm.params[type + "_pic_position"] = order[type + "_pic_position"];
        vm.params[type + "_pic_email"] = order[type + "_pic_email"];
        vm.params[type + "_pic_tel"] = order[type + "_pic_tel"];
      } else {
        vm.params[type + "_pic_id"] = getSelectedLocationProperty(type, "id");
        vm.params[type + "_pic_position"] = getSelectedLocationProperty(type, "position");
        vm.params[type + "_pic_tel"] = getSelectedLocationProperty(type, "tel");
      }
    });
    vm.initMandator(order);

    var selectedType = vm.selectedType;
    if (!_.isUndefined(vm.params[selectedType][0]["started_at"])) {
      var targetTab = selectedType == "individual_order" ? "#single-order" : "#multi-order";

      $.each($(targetTab).find("[name*='started_at']"), function(key, element) {
        var elementIndex = $(element).scope().$index;
        vm.estimateOrderPrice(selectedType, vm.params[selectedType][elementIndex]);
      });
    }
  };

  function getSelectedLocationProperty(type, key) {
    if (!_.isEmpty(vm.selectedLocation) && vm.selectedLocation.isValidLocation && vm.selectedLocation[type] &&
      vm.selectedLocation[type][0][key]) {
      return vm.selectedLocation[type][0][key];
    }
    return "";
  };

  vm.initMandator = function(order) {
    if (order) {
      vm.params.mandator_id = order["mandator_id"];
      vm.params.mandator_position = order["mandator_position"];
      vm.params.mandator_tel = order["mandator_tel"];
      vm.params.order_pic_tel = _.isEmpty(vm.selectedLocation) ? "" : vm.selectedLocation.order_pic_tel;
    } else {
      vm.params.mandator_id = getSelectedLocationProperty("mandator", "id");
      vm.params.mandator_position = getSelectedLocationProperty("mandator", "position");
      vm.params.mandator_tel = getSelectedLocationProperty("mandator", "tel");
      vm.params.order_pic_tel = _.isEmpty(vm.selectedLocation) ? "" : vm.selectedLocation.order_pic_tel;
    }
  };

  vm.nextToStep2 = function() {
    vm.enableStep(2);
  };

  vm.backToStep1 = function() {
    vm.enableStep(1);
  };

  vm.nextToStep3 = function() {
    vm.enableStep(3);
  };

  vm.backToStep2 = function() {
    vm.enableStep(2);
  };

  vm.enableStep = function(step) {
    angular.forEach([1, 2, 3], function(index) {
      vm.steps["step" + index] = (step === index);
    });
    window.scrollTo(0, 0);
  };

  vm.checkSelectedLocation = function() {
    vm.isSelectedLocation = !_.isEmpty(vm.params.location_id.toString());
  }

  vm.loadPicOptions = function() {
    vm.checkSelectedLocation();
    if (!vm.isSelectedLocation) {
      vm.selectedLocation = {isValidLocation: true};
      vm.initLocationPicData();
      vm.canSubmitStep1 = true;
      return vm.selectedLocation;
    }
    loadLocationData();
    if (!_.isEmpty(vm.originLocationData) && vm.location.id === vm.originLocationData.id) {
      vm.location = vm.originLocationData;
      vm.originLocationData = {};
    };
    orderService.loadPicOptions({location_id: vm.params.location_id, id: vm.params.id}).then(function(res) {
      var results = res.data;
      _.forEach(picsData, function(pic_data) {
        vm.selectedLocation[pic_data] = results[pic_data];
      });
      vm.checkLocationIsValid();
      vm.checkViolationDay();
      vm.checkSubmitStep1Condition();
      vm.params.organization_id = results.organization_id;
      vm.params.organization_full_name = results.organization_full_name;
      vm.locationSurveySubmittedTime = results.location_survey_submitted_at
      vm.loadBillingData();
      if (vm.selectedLocation.isValidLocation) {
        vm.initLocationPicData();
      }
    }, function(error) {
    });
  };

  vm.loadJobCategories = function() {
    vm.checkSelectedLocation();
    if (!vm.isSelectedLocation) {
      return;
    }
    orderService.loadLocationJobCategories(vm.params.location_id).then(function(res) {
      vm.location_job_categories = res.data;
    });
  }

  vm.initInvoiceTarget = function() {
    if (!vm.isSelectedLocation) {
      return;
    }
    vm.invoice_target = vm.location.default_invoice_target;
  }

  vm.initJobCategory = function() {
    vm.jobCategory = {}
    vm.checkSelectedLocation();
    if (!vm.isSelectedLocation) {
      vm.location_job_categories = []
      return;
    }
    vm.jobCategory.thumbnail_path =  vm.location.thumbnail_path;
    vm.jobCategory.job_category_name =  vm.location.job_category_name;
    vm.jobCategory.personal_things = vm.location.personal_things;
    vm.jobCategory.clothes = vm.location.clothes;
    vm.jobCategory.job_content = vm.location.job_content;
    vm.jobCategory.special_note = vm.location.special_note;
  }

  vm.loadJobCategoryData = function() {
    if (_.isEmpty(vm.location_job_category_id.toString())) {
      vm.initJobCategory();
      return;
    }
    vm.jobCategory = _.find(vm.location_job_categories, function(ljc) {
      return ljc.id === parseInt(vm.location_job_category_id);
    });
  }

  vm.checkLocationIsValid = function() {
    vm.selectedLocation.isValidLocation = true;
    var requiresInfo = [vm.selectedLocation.claim, vm.selectedLocation.haken_destination,
      vm.selectedLocation.mandator];
    if (!_.isEmpty(vm.params.location_id)) {
      _.forEach(requiresInfo, function(locationPic) {
        if (_.isUndefined(locationPic) || !vm.selectedLocation.is_valid_order_info) {
          vm.params.order_pic_tel = "";
          vm.selectedLocation.isValidLocation = false;
        }
      });
    }
  };

  vm.checkViolationDay = function() {
    var days = vm.selectedLocation.days_to_violation_day;
    var message = "";

    if (!_.isInteger(days)) {
      message = I18n.t("corporation.order.step1.violation_day.blank");
    } else if (days <= 0) {
      message = I18n.t("corporation.order.step1.violation_day.error_message");
    } else if (days <= 30) {
      message = I18n.t("corporation.order.step1.violation_day.notice_message_day_30", {days: days});
    } else if (days >= 31 && days <= VIOLATION_DAYS) {
      message = I18n.t("corporation.order.step1.violation_day.notice_message_day_31", {days: days});
    };
    vm.selectedLocation.violation_day_error_message = $sce.trustAsHtml(message);
  };

  vm.showMessageError = function() {
    vm.canSubmitStep3 = vm.canSubmitStep1 && isValidStartDate();
    var error = [];
    if (!vm.isValidLocation) {
      error.push(I18n.t("corporation.order.messages.invalid_location"));
    };
    if (!isValidStartDate()) {
      error.push(I18n.t("corporation.order.messages.invalid_start_date"))
    };
    error.push(vm.selectedLocation.transaction_error_message);
    return $sce.trustAsHtml(error.join("<br>"));
  };

  function isValidStartDate() {
    return moment().isSameOrBefore(vm.orderData.overall_started_at, "day");
  };

  vm.showLocationErrorMessage = function() {
    return I18n.t("corporation.order.step1.location_notice");
  };

  vm.submitCreateOrderStep1 = function() {
    $(".disable-submit-btn").prop("disabled", true);
    $('.has-error').removeClass('has-error');
    $('.help-block').remove();

    orderService.createOrderStep1({order: vm.params, id: vm.params.id}).then(function(res) {
      $.lawsonAjax(res.data);
      if (res.data.status) {
        if (vm.isEditOrder) {
          vm.enableStep(3)
        } else {
          vm.enableStep(2);
        }
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  };

  vm.submitCreateOrderStep2 = function() {
    vm.params.over_limit_portion = false;
    vm.params.under_minimum_wage = false;
    $(".disable-submit-btn").prop("disabled", true);
    $('.has-error').removeClass('has-error');
    $('.help-block').remove();

    var $form = vm.isEditOrder ? $("form#form-edit-create-order") : $("form#form-create-order");
    var orderParams = $form.serializeJSON();
    orderParams.id = orderParams.order.id = vm.params.id;
    orderParams.bill_paym_template_id = vm.params.bill_paym_temp.id;
    orderParams.prefecture_id = vm.selectedLocation.prefecture_id;
    orderService.createOrderStep2(orderParams).then(function(res) {
      $.lawsonAjaxOrderFormAdminStep2(res.data);
      vm.params.over_limit_portion = res.data.over_limit_portion;
      vm.params.under_minimum_wage = res.data.under_minimum_wage;
      vm.showErrorMessagesWorkingStartTime(vm.selectedType);
      if (res.data.status && vm.notExistsErrors()) {
        vm.orderData = res.data.order;
        vm.orderData.overall_started_at = formatDate(vm.orderData.overall_started_at, ORDER_DATEPICKER_FORMAT);
        vm.orderData.overall_ended_at = formatDate(vm.orderData.overall_ended_at, ORDER_DATEPICKER_FORMAT);
        vm.enableStep(3);
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  };

  vm.changeNumberOfEmployees = function(add, type, order) {
    var numberOfEmployees = order["staff_count"];
    if (add) {
      order["staff_count"] = numberOfEmployees >= MAX_STAFF ? MAX_STAFF : numberOfEmployees + 1;
    } else {
      order["staff_count"] = numberOfEmployees <= MIN_STAFF ? MIN_STAFF : numberOfEmployees - 1;
    }
    vm.estimateOrderPrice(type, order);
  };

  vm.estimateOrderPrice = function(type, order) {
    if(invalidEstimateParams(order)) {
      return;
    }

    var submitParams = vm.estimateOrderParams(type, order);
    submitParams.id = vm.params.id;
    submitParams.corporation_id = vm.params.corporation_id;
    submitParams.bill_paym_template_id = vm.params.bill_paym_temp.id;
    orderService.estimateOrderPrice(submitParams).then(function(res) {
      var orderIndex = vm.params[type].indexOf(order);
      var response_data = res.data;
      vm.params[type][orderIndex]["estimate_data"] = response_data;
      vm.estimateData[orderIndex] = {data: order.estimate_data};
      vm.checkMidNight(orderIndex, type, 'is_mid_night', vm.params[type][orderIndex]["estimate_data"]);
      vm.selectedType = type;
      if (type === "collective_order") {
        var summaryOrderBranch = _.sumBy(response_data, "summary");
        var summaryStaff = _.sumBy(response_data, "staff_count");
        var collectiveData = {
          selected_days: convertToDay(submitParams.selected_days),
          week_count: convertToDateFormat(submitParams),
          total_employees: summaryStaff,
          total_estimation: summaryOrderBranch
        }
        vm.params[type][orderIndex]["total_estimation"] = summaryOrderBranch;
        _.merge(vm.estimateData[orderIndex], collectiveData);
        vm.totalCollectiveOrder = _.sumBy(vm.estimateData, "total_estimation");
      } else {
        vm.totalIndividualOrder = _.sumBy(vm.estimateData, "data[summary]");
      }
    }, function(error) {
    });
  };

  function invalidEstimateParams(order) {
    var dateRegex = /^([0-9]{2})\:([0-9]{2})$/;

    return _.isEmpty(order.started_at) || _.isEmpty(order.working_start_time) || _.isEmpty(order.working_end_time)
    || (!_.isEmpty(order.rest1_started_at) && (!moment(order.rest1_started_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest1_started_at)))
    || (!_.isEmpty(order.rest2_started_at) && (!moment(order.rest2_started_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest2_started_at)))
    || (!_.isEmpty(order.rest3_started_at) && (!moment(order.rest3_started_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest3_started_at)))
    || (!_.isEmpty(order.rest1_ended_at) && (!moment(order.rest1_ended_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest1_ended_at)))
    || (!_.isEmpty(order.rest2_ended_at) && (!moment(order.rest2_ended_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest2_ended_at)))
    || (!_.isEmpty(order.rest3_ended_at) && (!moment(order.rest3_ended_at, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.rest3_ended_at)))
    || (!_.isEmpty(order.started_at) && (!moment(order.started_at).isValid()
    || !CHECK_DATE_REGEX.test(order.started_at)))
    || (!_.isEmpty(order.working_start_time) && (!moment(order.working_start_time, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.working_start_time)))
    || (!_.isEmpty(order.working_end_time) && (!moment(order.working_end_time, TIME_PICKER_FORMAT).isValid()
    || !dateRegex.test(order.working_end_time)))
  }

  function loadLocationData() {
    vm.location = _.find(vm.locations, function(lo) {
      return lo.id === parseInt(vm.params.location_id);
    }) || [];
  };

  vm.estimateOrderParams = function(type, order) {
    var orderBranchData = {};
    var data = {
      type: type,
      prefecture_id: vm.selectedLocation.prefecture_id,
      location_id: vm.params.location_id
    };
    _.forEach(_.concat(ORDER_BRANCH_DATA, BREAK_TIME_ATTRS), function(key) {
      data[key] = order[key];
    });
    if (type === "individual_order" && order["started_at"] && order["working_start_time"]
      && order["working_end_time"] && order["staff_count"]) {
      orderBranchData = _.extend({}, data);
    } else if (type === "collective_order") {
      var selectedDays = [];
      _.forEach(vm.orderBranchDays, function(day, index) {
        if (order[day]) {
          selectedDays.push(index);
        }
      });
      order.selected_days = selectedDays.join();
      _.forEach(COLLECTIVE_ORDER_DATA, function(key) {
        data[key] = order[key];
      });
      orderBranchData = _.extend({}, data);
    }
    return orderBranchData;
  };

  vm.isDisplayPriceColumn = function(orderData, field) {
    return _.sumBy(orderData, field) !== 0;
  }

  vm.isDisplaySpeacialCharge = function(orderData, field) {
    return _.sumBy(orderData, field) !== 0;
  }

  vm.changePosition = function(type) {
    var picID = vm.params[type + "_pic_id"];
    var locationPic = {};
    if (_.isNaN(picID)) {
      locationPic = {
        position: "",
        email: "",
        tel: ""
      }
    } else {
      var currentPics = vm.selectedLocation[type];
      locationPic = _.find(currentPics, function(pic) {return pic.id === parseInt(picID)});
    }
    changePicValue(type, locationPic, false);
  };

  vm.getViolationDay = function() {
    var violationDay = vm.selectedLocation.violation_day;
    if (violationDay) {
      return violationDay.split("(")[0];
    };
  };

  function changePicValue(type, locationPic, isMadator) {
    if (_.isUndefined(locationPic)) {
      return;
    }
    var validLocation = vm.selectedLocation.isValidLocation;
    if (isMadator) {
      vm.params[type + "_position"] = validLocation ? locationPic.position : "";
      vm.params[type + "_tel"] = validLocation ? locationPic.tel : "";
    } else {
      vm.params[type + "_pic_position"] = validLocation ? locationPic.position : "";
      vm.params[type + "_pic_email"] = validLocation ? locationPic.email : "";
      if (type === "order") {
        vm.params.user_locations_pic_tel = validLocation ? locationPic.tel : "";
      } else {
        vm.params[type + "_pic_tel"] = validLocation ? locationPic.tel : "";
      }
    }
  };

  vm.changePositionMandator = function() {
    var picID = vm.params.mandator_id;
    var locationPic = {};
    if (_.isNaN(picID)) {
      locationPic = {
        position: "",
        tel: ""
      }
    } else {
      locationPic = _.find(vm.selectedLocation.mandator, function(pic) {return pic.id === parseInt(picID)});
    }
    changePicValue("mandator", locationPic, true);
  };

  vm.selectOrderBranchDays = function(day, order) {
    order[vm.orderBranchDays[day]] = !order[vm.orderBranchDays[day]];
    vm.estimateOrderPrice('collective_order', order);
  };

  // Edit Order
  vm.initEditOrder = function(paramsData) {
    vm.enableStep(3);
    vm.isEditOrder = true;
    var locationPics = paramsData.location_pics;
    _.forEach(picsData, function(picData) {
      vm.selectedLocation[picData] = locationPics[picData];
    });
    vm.locations = paramsData.locations;
    var orderParams = paramsData.order;
    var orderType = vm.types[orderParams.type_id];
    vm.estimateData = vm.totalEstimation[orderType];
    vm.selectedType = orderType;

    angular.element("." + orderType).trigger("click");
    _.forEach(_.concat(paramsStep1, CORPORATION_BILLING_ATTRS), function(attr) {
      vm.params[attr] = orderParams[attr];
    });
    vm.params.is_used_existing_pic = orderParams.is_used_existing_pic;
    vm.params.corporation_id = String(vm.params.corporation_id);
    vm.params.organization_id = orderParams.organization_id;
    vm.params.organization_full_name = orderParams.organization_full_name;
    vm.params.training_session_code = orderParams.training_session_code;
    vm.params.training_schedule_code = orderParams.training_schedule_code;
    vm.params.location_id = String(vm.params.location_id);
    vm.params.id = orderParams.id;
    vm.loadBillingData();
    loadLocationData();
    initLocationData(orderParams);
    vm.checkViolationDay();
    vm.checkSubmitStep1Condition();
    vm.searchPostalCode();
    vm.selectedLocation.violation_day = formatDate(orderParams.violation_day, ORDER_DATEPICKER_FORMAT);
    vm.orderData.overall_started_at = formatDate(orderParams.overall_started_at, ORDER_DATEPICKER_FORMAT);
    vm.orderData.overall_ended_at = formatDate(orderParams.overall_ended_at, ORDER_DATEPICKER_FORMAT);
    if (!vm.is_lawson_staff) {
      if (_.isNull(orderParams.is_use_current_billing_address)) {
        vm.initBillingData();
      } else {
        vm.params.is_use_current_billing_address = orderParams.is_use_current_billing_address;
      }
      vm.params.billing_organization_id = vm.params.organization_id;
    }
    orderService.orderBranchesDetail({order_id: orderParams.id}).then(function(res) {
      vm.params[vm.selectedType] = res.data;
      var orderBranches = res.data;
      _.forEach(Object.keys(orderBranches), function(key) {
        orderBranches[key] = vm.initOrderBranchRestTime(orderBranches[key]);
      });
      vm.params[vm.selectedType] = orderBranches;
      initOrderBranchDetail();
    });
  };

  function convertTemplateToDateFormat(params, firstCase) {
    var startDate = params.started_at;
    var endDate = params.ended_at;
    if (startDate === endDate) {return firstCase.date};
    return [startDate.toString(), "～", endDate.toString()].join("");
  };

  function initOrderBranchDetail() {
    vm.estimateData = _.map(vm.params[vm.selectedType], function(order){
      var extendData = {};

      if (vm.selectedType === "collective_order"  || vm.selectedType === "template_order") {
        var weekCount = "";
        if (!_.isUndefined(order.started_at)) {
          weekCount = convertToDateFormat({week_count: order.week_count, started_at: order.started_at});
        }

        extendData = {
          total_estimation: _.sumBy(order.estimate_data, "summary"),
          selected_days: convertToDay(order.selected_days ? order.selected_days : ""),
          week_count: weekCount,
          total_employees: _.sumBy(order.estimate_data, "staff_count")
        }
        if(vm.selectedType === "template_order"){
          extendData["selected_days"] = convertTemplateToDateFormat({started_at: order.started_at, ended_at: order.ended_at}, order.estimate_data[0])
        }
      }
      return angular.extend({data: order.estimate_data}, extendData);
    });

    if (vm.selectedType === "collective_order" || vm.selectedType === "template_order") {
      vm.totalCollectiveOrder = _.sumBy(vm.estimateData, "total_estimation");
    } else {
      vm.totalIndividualOrder = _.sumBy(vm.estimateData, "data[summary]");
    }
  }

  vm.isStoreParking = function() {
    var isStoreParking = vm.location.is_store_parking_area_usable;
    if (!_.isNull(isStoreParking)) {
      return I18n.t("corporation.order.store_parking." + isStoreParking);
    }
  };

  vm.getObjectName = function(typeData, typeId) {
    var object = _.find(vm.selectedLocation[typeData], function(o) {
      return o.id === vm.params[typeId];
    });
    if (object) {
      return object.name;
    }
  };

  var isDeletedOrderPic = function(typeData, picId) {
    if (typeData !== "order") { return; }
    var object = _.find(vm.selectedLocation[typeData], function(o) {
      return o.id === vm.params[picId];
    });
    if (object) {
      return !!object.deleted_at;
    }
    return true;
  };

  vm.addNewOrder = function(orderType) {
    var orders = vm.params[orderType];
    var orderNumber = orders.length + 1;
    if (orderType === "individual_order") {
      orders.push({order_number: orderNumber, break_time: 0, is_special_offer: false,
        rest_times: [1]});
    } else {
      orders.push({order_number: orderNumber, break_time: 0, week_count: 1,
        is_special_offer: false, rest_times: [1]});
    }
  };

  vm.removeOrder = function(type, index) {
    var orderBranchId = vm.params[type][index].id;
    if (orderBranchId) {
      var orderBranch = "order[order_branches_attributes][" + 1000 + orderBranchId + "]";
      var destroyElement = "<input type='hidden' name=" + orderBranch + "[id] value=" + orderBranchId + " /> \
        <input type='hidden' name=" + orderBranch + "['_destroy'] value=true />";
      angular.element(".hidden-order-nested").append(destroyElement);
    };
    vm.params[type].splice(index, 1);
    vm.estimateData.splice(index, 1);
    if (type === "collective_order") {
      vm.totalCollectiveOrder = _.sumBy(vm.estimateData, "total_estimation");
    } else {
      vm.totalIndividualOrder = _.sumBy(vm.estimateData, "data[summary]");
    }
  };

  vm.duplicateOrder = function(type, order) {
    var newOrder = _.extend({}, order);
    newOrder.id = "";
    newOrder.order_number = vm.params[type].length + 1;
    if (!order.is_special_offer) {
      newOrder.special_offer_fee = "";
      newOrder.special_offer_note = "";
    }
    vm.params[type].push(newOrder);
    vm.estimateOrderPrice(type, newOrder);
  };

  vm.changeOrderMethod = function(type) {
    vm.estimateData = vm.totalEstimation[type];
    vm.selectedType = type;

    initOrderBranchDetail();
  };

  function convertToDay(dayNumbers) {
    var numberArr = dayNumbers.split(",");
    return _.map(numberArr, function(number) {
      return vm.dayNames[number];
    }).join("");
  }

  function convertToDateFormat(params) {
    var duration = params.week_count;
    var startDate = params.started_at;
    return [startDate.toString(), "～", duration, I18n.t("corporation.order.step3.detail_multi_modal.week")].join("");
  }

  vm.setOrderStatus = function() {
    var status = "waiting_approved";
    if (!vm.isNeedConfirmOrder) {
      status = vm.selectedLocation.is_approval_required ? "op_checking" : "confirmed";
    }
    return status;
  };

  vm.initSaveOrderValues = function(isNeedConfirmOrder) {
    vm.isNeedConfirmOrder = isNeedConfirmOrder;
    var strKey = isNeedConfirmOrder ? "submit_to_superior" : "agree_and_send_create_order";
    vm.saveOrderStr = I18n.t("corporation.order.button")[strKey];
  }

  vm.getLocationPic = function(typeData) {
    var type = typeData + "_pic";
    if (typeData === "mandator") {
      type = typeData;
    };
    var picName = vm.getObjectName(typeData, type + "_id");
    var picPosition = type === "order_pic" ? vm.params[type + "_email"] : vm.params[type + "_position"];
    var isDeleted = isDeletedOrderPic(typeData, type + "_id") ? I18n.t("corporation.order.step3.deleted_order_pic") : "";
    return [picName, picPosition, vm.params[type + "_tel"], isDeleted].join(" ");
  };

  function formatDate(date, formatType) {
    if (_.isEmpty(date)) return;
    return moment.parseZone(date).format(formatType);
  };

  function initLocationData(orderParams) {
    angular.extend(vm.originLocationData, vm.location);
    _.forEach(ORDER_LOCATION_DATA, function(attr) {
      vm.location[attr] = orderParams["location_" + attr];
    });
    vm.location.full_address = orderParams.full_address;
  };

  vm.checkSubmitStep1Condition = function() {
    var days = vm.selectedLocation.days_to_violation_day;
    var isValidClosedDay = _.isEmpty(vm.selectedLocation.closed_day_error_message);
    var isLocationNotSurvey = _.isEmpty(vm.selectedLocation.location_not_survey_error_message);
    vm.isValidLocation = vm.selectedLocation.isValidLocation && days > 0 && isValidClosedDay && isLocationNotSurvey;
    vm.canSubmitStep1 = vm.isValidLocation && _.isEmpty(vm.selectedLocation.transaction_error_message);
  };

  vm.inputNumberOfEmployees = function(type, order) {
    var numberOfEmployees = parseInt(order["staff_count"], NUMBER_BASE);
    if (!_.isNaN(numberOfEmployees) && _.isNumber(numberOfEmployees)) {
      if (numberOfEmployees >= MAX_STAFF) {
        order["staff_count"] = MAX_STAFF;
      } else if (numberOfEmployees <= MIN_STAFF) {
        order["staff_count"] = MIN_STAFF;
      } else {
        order["staff_count"] = numberOfEmployees;
      }
    } else {
      order["staff_count"] = MIN_STAFF;
    }
    vm.estimateOrderPrice(type, order);
  };

  vm.isDisableStaffCountBtn = function(add, staffCount) {
    if (add) {
      return staffCount >= MAX_STAFF;
    } else {
      return staffCount <= MIN_STAFF;
    }
  };

  vm.loadOrganizations = function() {
    if (!_.isNumber(parseInt(vm.params.corporation_id))) {
      vm.organizations = {};
      vm.is_lawson_staff = false;
      return ;
    }
    orderService.loadOrganizations({corporation_id: vm.params.corporation_id, id: vm.params.id}).then(function(res) {
      angular.extend(vm, res.data);
      if (_.isNull(vm.is_lawson_staff)) {
        vm.is_lawson_staff = false;
      }
    }, function(error) {
    });
  };

  vm.searchPostalCode = function() {
    vm.params.page = vm.currentPage;
    vm.params.postal_code = vm.postalCode;
    postalCodeService.getPostalCode(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.postal_codes.length);
      vm.noRecord = !vm.hasRecord;
      vm.params.limit = res.data.pagination_limit;
    }, function(error) {
    });
  };

  vm.loadBillingData = function() {
    if (_.isEmpty(vm.params.organization_id.toString()) || vm.is_lawson_staff) {
      vm.selectedCorporationGroup = {};
      vm.initBillingData();
      return;
    };
    var submitParams = {organization_id: vm.params.organization_id, id: vm.params.id};
    orderService.loadCorporationGroupData(submitParams).then(function(res) {
      _.forEach(CORPORATION_BILLING_ATTRS, function(attr) {
        vm.selectedCorporationGroup[attr] = res.data.corporation_group[attr];
      });
      if (vm.params.is_use_current_billing_address) {
        vm.initBillingData();
      } else {
        vm.params.billing_organizations = vm.selectedCorporationGroup.billing_organizations;
      }
    }, function(error) {
    });
  };

  vm.initBillingData = function() {
    _.forEach(CORPORATION_BILLING_ATTRS, function(attr) {
      vm.params[attr] = vm.selectedCorporationGroup[attr];
    });
    if (vm.is_lawson_staff) return;
    if (_.isUndefined(vm.selectedCorporationGroup.billing_organization_id)) {
      vm.params.billing_organization_id = vm.params.organization_id;
    }
    vm.params.is_use_current_billing_address = true;
  };

  vm.getBillingNameArr = function(billingData, key) {
    if (_.isUndefined(billingData)) return;
    var billingData = _.find(billingData, function(data) {
      return data[1] === parseInt(vm.params[key]);
    })
    if (_.isUndefined(billingData)) return;
    return billingData[0];
  };

  vm.getBillingNameObject = function(billingData, key) {
    if (_.isUndefined(billingData)) return;
    var billingData = _.find(billingData, function(data) {
      return data.id === parseInt(vm.params[key]);
    })
    if (_.isUndefined(billingData)) return;
    return billingData.full_name;
  };

  vm.getFullBillingAddress = function() {
    var prefectureName = vm.getBillingNameArr(vm.billingPrefectures, "billing_prefecture_id");
    var fullAddress = [prefectureName, vm.params.billing_city, vm.params.billing_street_number,
      vm.params.billing_building].join("");
    return fullAddress;
  };

  vm.deleteOrder = function() {
    orderService.deleteOrder(vm.params.id).then(function(res) {
      $.lawsonAjax(res.data);
    }, function(success) {
    });
  };

  vm.showSpinner = function() {
    $("#spinner").removeClass("ng-hide");
  };

  vm.submitCreateOrder = function(status) {
    vm.showSpinner();
    vm.buttonsDisabled = true;
    var orderParams = $("form#form-create-order").serializeJSON();
    var formatedParam = formatParamsBeforeSubmit(orderParams);
    _.extend(formatedParam.order, {status_id: status});
    orderService.createOrder(formatedParam).then(function(res) {
      $.lawsonAjax(res.data);
      vm.createErrors = JSON.parse(res.data.all_errors);
      $("#spinner").addClass("ng-hide");
      if (!_.isEmpty(vm.createErrors)) {
        window.scrollTo(0, 0);
      }
    });
  };

  function formatParamsBeforeSubmit(orderParams) {
    var resultsParam = orderParams.order.order_branches_attributes;
    Object.keys(resultsParam).forEach(function(key) {
      if (!resultsParam[key].is_special_offer) {
        delete resultsParam[key].special_offer_fee;
        delete resultsParam[key].special_offer_note;
      }
      if (resultsParam[key].order_branch_type !== vm.selectedType) {
        delete resultsParam[key];
      }
    });
    orderParams.order.order_branches_attributes = resultsParam;
    orderParams.order.bill_paym_temp = vm.params.bill_paym_temp;
    orderParams["location_job_category_id"] = vm.location_job_category_id;
    orderParams["invoice_target"] = vm.invoice_target;
    orderParams["is_violation_day_unchanged"] = vm.violationDayUnchanged;
    orderParams["is_location_survey_unchanged"] = vm.locationSurveyUnchanged;
    orderParams["is_not_one_man_operation"] = vm.isNotOneManOperation;
    return orderParams;
  }

  vm.getOrderSegmentId = function() {
    if (vm.params.order_segment_id == SEGMENT_TRAINING) {
      return I18n.t('corporation.order.segment_ids.' + vm.params.order_segment_id) + " (" +
        I18n.t('admin.order.order_form.step1.' + vm.params.training_session_code) + ")";
    }
    return I18n.t('corporation.order.segment_ids.' + vm.params.order_segment_id);
  }

  vm.setDefaultSpecialOfferFee = function(type, order) {
    var orderIndex = vm.params[type].indexOf(order);
    if (order.is_special_offer) {
      if (!_.includes(vm.specialOfferFeeOptions, order.special_offer_fee)) {
        vm.params[type][orderIndex]["special_offer_fee"] = SPECIAL_OFFER_FEE[0];
      }
    } else {
      vm.params[type][orderIndex]["is_special_offer"] = false;
      vm.params[type][orderIndex]["special_offer_fee"] = "";
      vm.params[type][orderIndex]["special_offer_note"] = "";
    }
  }

  vm.addRestTime = function(type, order) {
    var orderIndex = vm.params[type].indexOf(order);
    var restTimes = vm.params[type][orderIndex]["rest_times"].slice();
    var restTimeCount = restTimes.length;
    if (restTimeCount === MAX_REST_TIMES) return;
    restTimes.push(restTimeCount + 1);
    vm.params[type][orderIndex]["rest_times"] = restTimes;
  }

  vm.removeRestTime = function(type, order, restTime) {
    var orderIndex = vm.params[type].indexOf(order);
    var restTimes = vm.params[type][orderIndex]["rest_times"].slice();
    var restTimeCount = restTimes.length;
    if (restTimeCount === MIN_REST_TIMES) return;
    if (restTime === 2) {
      vm.params[type][orderIndex]["rest2_started_at"] = vm.params[type][orderIndex]["rest3_started_at"];
      vm.params[type][orderIndex]["rest2_ended_at"] = vm.params[type][orderIndex]["rest3_ended_at"];
    }
    vm.params[type][orderIndex]["rest3_started_at"] = vm.params[type][orderIndex]["rest3_ended_at"] = "";
    restTimes.pop();
    vm.params[type][orderIndex]["rest_times"] = restTimes;
  }

  vm.countRestTime = function(type, order, rest_time) {
    var startTime = order["rest" + rest_time + "_started_at"];
    var endTime = order["rest" + rest_time + "_ended_at"];
    if (_.isEmpty(startTime) || _.isEmpty(endTime)) return;
    var start = moment(startTime, TIME_PICKER_FORMAT);
    var end = moment(endTime, TIME_PICKER_FORMAT);
    if (end.isBefore(start)) end.add(1, "day");
    return I18n.t("corporation.order.step2.total_minutes", {minutes: end.diff(start, "minutes")});
  }

  vm.initOrderBranchRestTime = function(order_branch) {
    var rest_times = [];
    _.forEach(REST_TIMES, function(rest_time) {
      var startTime = order_branch["rest" + rest_time + "_started_at"];
      var endTime = order_branch["rest" + rest_time + "_ended_at"];
      if (_.isEmpty(order_branch["rest" + rest_time + "_started_at"])) return;
      order_branch["rest" + rest_time + "_started_at"] = formatDate(startTime, TIME_PICKER_FORMAT);
      order_branch["rest" + rest_time + "_ended_at"] = formatDate(endTime, TIME_PICKER_FORMAT);
      rest_times.push(rest_time);
    });
    order_branch["rest_times"] = rest_times;
    return order_branch;
  }

  vm.displayEstimateByStaff = function(price, staffCount) {
    return I18n.t("corporation.order.step3.detail_modal.price_per_staff", {price: price, staff_count: staffCount});
  }

  vm.isTrue = function(value) {
    return (value == true) || (value == "true");
  }

  vm.checkValidDate = function(type, index, order) {
    vm.params[type][index]["started_at"] = checkValidDateFunction.checkValidDate(vm.params[type][index]["started_at"]) ||
      vm.params[type][index]["started_at"];
    vm.estimateOrderPrice(type, order);
  }

  vm.checkValidTime = function(index, type, order, field) {
    vm.params[type][index][field] = checkValidDateFunction.checkValidTime(vm.params[type][index][field]) ||
      vm.params[type][index][field];
    vm.estimateOrderPrice(type, order);
  }

  vm.checkMidNight = function(index, type, field, estimateData) {
    var params = vm.params[type][index];
    if (_.isUndefined(params.started_at) || _.isUndefined(params.working_start_time) ||
      params.working_start_time != MIDNIGHT){
        vm.params[type][index]['is_mid_night'] = false;
        vm.params[type][index]['agree_to_be_on_time'] = true;
      return
    }
    var startDate = vm.getMinStartDate(estimateData);
    var submitParams = {start_date: startDate, working_start_time: params.working_start_time};
    orderService.checkMidNight(submitParams).then(function(res){
      var res = res.data;
      vm.params[type][index][field] = res.is_mid_night;
      if (!_.isUndefined(vm.params[type][index]['checked_to_be_on_time'])) {
        vm.params[type][index]['agree_to_be_on_time'] = vm.params[type][index]['checked_to_be_on_time'];
      } else {
        vm.params[type][index]['agree_to_be_on_time'] = !res.is_mid_night;
      }
      if (res.is_mid_night) {
        vm.params[type][index]['show_error_wk_start_time'] = false;
      }
      vm.params[type][index]['warning_working_start_time'] = res.warning_message;
    });
  }

  vm.getMinStartDate = function(estimateData) {
    if (vm.selectedType == 'collective_order') {
      var selectedDates = _.map(estimateData, function(estimate){
        return estimate['original_date'];
      });
      var tmp = _.map(selectedDates, function(date){
        return Date.parse(date);
      });
      var indexOfMinDate = _.indexOf(tmp, _.min(tmp));
      return selectedDates[indexOfMinDate];
    } else {
      return estimateData.original_date;
    }
  }

  vm.agreeToBeOnTime = function(index, type) {
    vm.params[type][index]['checked_to_be_on_time'] = vm.params[type][index]['agree_to_be_on_time'];
    if (vm.params[type][index]['agree_to_be_on_time']) {
      vm.params[type][index]['show_error_wk_start_time'] = false;
    } else {
      vm.params[type][index]['show_error_wk_start_time'] = true;
    }
  }

  vm.notExistsErrors = function() {
    var params = vm.params[vm.selectedType];
    return vm.isAgreeToBeOnTime(params);
  }

  vm.isAgreeToBeOnTime = function(params) {
    var isAgreed = _.find(params, function(p) { 
      return !_.isUndefined(p.agree_to_be_on_time) && !p.agree_to_be_on_time;
    });
    return _.isUndefined(isAgreed)
  }

  vm.isShowMidnightError = function(agree) {
    return !agree && !_.isUndefined(agree);
  }

  vm.showErrorMessagesWorkingStartTime = function(type) {
    _.forEach(vm.params[type], function(param, index){
      if (!param.agree_to_be_on_time) {
        vm.params[type][index]['show_error_wk_start_time'] = true;
      }
    })
  }

  vm.showBillingPaymentValue = function(field) {
    var normalTxt = I18n.t("admin.order.order_form.new_step3.normal_calculation");
    return !!vm.params.bill_paym_temp[field] || vm.params.bill_paym_temp[field] == 0 ? vm.params.bill_paym_temp[field] : normalTxt;
  }

  vm.changeLocation = function() {
    vm.loadPicOptions();
    vm.loadBillingData();
    vm.initJobCategory();
    vm.initInvoiceTarget();
    vm.loadJobCategories();
    vm.loadLocationTemplates();
  }

  vm.loadLocationTemplates = function() {
    if (_.isEmpty(vm.params.location_id.toString())) return;
    orderService.loadLocationTemplates({location_id: vm.params.location_id}).then(function(res) {
      if (!!res.data.templates) {
        vm.templates = angular.copy(res.data.templates);
      }
    });
  }

  vm.changeTemplate = function() {
    var template = _.find(vm.templates, function(template) {
      return template.id == vm.selectedTemplate;
    });
    if (_.isUndefined(template)) {
      resetBillingPaymentValues();
      return;
    }
    vm.selectedTemplateName = template.name;
    vm.params.bill_paym_temp.id = template.id;
    _.each(TEMPLATE_ATTRS, function(fieldName) {
      vm.params.bill_paym_temp[fieldName] = template[fieldName];
    });
  }

  function resetBillingPaymentValues() {
    vm.params.bill_paym_temp = {};
  }

  vm.$scope.$watch("vm.params.order_segment_id", function() {
    if(vm.params.order_segment_id != SEGMENT_TRAINING) {
      vm.params.training_session_code = TRAINING_NOT_APPLICABLE;
      vm.params.training_schedule_code = TRAINING_NOT_SCHEDULED;
    }
  });

  // $(document).ready(function() {
  //   $("#confirm-violation-date-unchanged, #confirm-location-survey-unchanged").change(function() {
  //     var checkedViolationDate = $("#confirm-violation-date-unchanged").is(":checked");
  //     var checkedLocationSurvey = $("#confirm-location-survey-unchanged").is(":checked");

  //     vm.unchangedTerm = checkedViolationDate && checkedLocationSurvey;
  //   });
  // });
}
