'use strict';

angular.module('adminApp').controller('AdminsController', AdminsController);
AdminsController.$inject = ['$location', 'adminService'];

function AdminsController($location, adminService) {
  var vm = this;
  var requestParams = $location.search();
  var searchParamKeys = ['keyword'];
  var SEARCH_CONDITION_TYPE = "admin_search_conditions"
  vm.hasRecord = vm.noRecord = false;
  vm.params = {};
  vm.params.page = requestParams.page;
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  _.forEach(searchParamKeys, function(searchParamKey) {
    vm.params[searchParamKey] = requestParams[searchParamKey];
  });

  init();

  vm.search = function(resetPage, isSaveCondition) {
    if (resetPage) {
      vm.params.page = 1;
    };
    vm.params.per_page = vm.params.per_page || vm.perPageSettings[0];
    vm.params.can_view_my_number = vm.params.can_view_my_number || "all";
    vm.params.can_view_user_details = vm.params.can_view_user_details || "all";
    vm.params.is_insurance_mail_receiver = vm.params.is_insurance_mail_receiver || "all";
    adminService.searchAdmin(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.admins.length);
      vm.noRecord = !vm.hasRecord;
      if(isSaveCondition){
        var searchConditionParams = _.omit(vm.params, "page", "limit");
        adminService.createSearchCondition({search: searchConditionParams, search_condition: SEARCH_CONDITION_TYPE});
      }
      $location.search(vm.params).replace();
    }, function (error) {
    });
  };

  vm.changePerPage = function() {
    vm.search(false, true);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".admin-list-result").offset().top
    }, 1000);
  };

  function init() {
    adminService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
      }
      vm.search(true);
    });
  }
}
