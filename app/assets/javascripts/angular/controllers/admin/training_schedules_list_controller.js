"use strict";

angular.module("adminApp").controller("TrainingSchedulesListController", TrainingSchedulesListController);

TrainingSchedulesListController.$inject = ["$location", "$scope", "trainingScheduleService", "checkValidDateFunction", "toaster"];

function TrainingSchedulesListController($location, $scope, trainingScheduleService, checkValidDateFunction, toaster) {
  var vm = this;
  vm.$scope = $scope;

  var XLSX_TYPE       = "xlsx";
  var SELECT_FIELDS   = ["location_ids", "person_in_charge_ids", "staff_id"];
  var DATETIME_FIELDS = ["training_date_from", "training_date_to"];

  vm.haveDeletedSchedules = false;
  vm.hasRecord            = false;
  vm.perPageSettings      = [10, 15, 30, 50];
  vm.isCheckAll           = false;
  vm.isSelected           = false;
  vm.toasterTimeout       = 6200;
  vm.scheduleErrors       = {};

  var DEFAULT_SEARCH_PARAMS = {
    training_session: "",
    order_key: "schedule_id",
    desc: true,
    per_page: vm.perPageSettings[3],
    page: 1,
  };
  vm.params = {};
  // ------

  vm.init = function() {
    setDatePicker();
    if (_.isEmpty(getQueryString())) {
      fetchData({});
    } else {
      fetchData({search: getQueryString()});
    }
  };

  vm.checkOrUncheck = function() {
    var checkedArr = vm.getSelectedSchedules();
    var deletedArr = getDeletedSchedules();
    vm.isSelected  = Boolean(checkedArr.length);
    vm.isCheckAll  = checkedArr.length === deletedArr.length;
  };

  vm.checkOrUncheckAll = function() {
    var deletedArr = getDeletedSchedules();
    _.map(deletedArr, function(schedule) {
      schedule.checked = vm.isCheckAll;
      return schedule;
    });
    vm.isSelected = vm.isCheckAll;
  };

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  };

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !vm.params.desc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !!vm.params.desc
    }
  };

  vm.sort = function(column) {
    // column ['schedule_id']
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
    }
    vm.refresh(false);
    vm.scrollToResult();
  };

  vm.refresh = function(resetPage) {
    var searchParams = angular.copy(vm.params)
    if (resetPage) {searchParams.page = 1;}
    if (!vm.params.per_page) {searchParams.per_page = 10;}
    fetchData({search: searchParams});
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".staff-search-result").offset().top
    }, 1000);
  };

  vm.changePerPage = function() {
    vm.refresh(true);
    vm.scrollToResult();
  };

  vm.clearSearchCondition = function() {
    clearQueryString();
    vm.params = angular.copy(DEFAULT_SEARCH_PARAMS);
    setTimeout(function() {
      angular.element("select.select2, select.select2-multiple").trigger("change");
    }, 20);
  };

  vm.exportTrainingSchedules = function(e) {
    var searchParams = angular.copy(vm.params);
    searchParams.total_items = vm.total_items;
    $.asyncDownload(e, "training_schedules", JSON.stringify(searchParams), XLSX_TYPE, {});
  };

  vm.showRestoreModal = function() {
    $("#confirm-modal").modal("show");
  }

  vm.closeRestoreModal = function() {
    vm.scheduleErrors = {};
    $("#confirm-modal").modal("hide");
  }

  vm.restore = function() {
    var selected_ids = _.map(vm.getSelectedSchedules(), function(schedule) {
      return schedule.schedule_id;
    });
    trainingScheduleService.restore({schedule_ids: JSON.stringify(selected_ids)}).then(function(res) {
      if (res.status == 200 && res.data.status) {
        toaster.pop("success", "", res.data.message);
        $("#confirm-modal").modal("hide");
        vm.refresh(false);
        vm.scrollToResult();
      } else {
        if (!_.isEmpty(res.data.errors)) {
          _.map(res.data.errors, function(schedule) {
            return vm.scheduleErrors[schedule.schedule_id] = schedule;
          })
        }
      }
    });
  }

  vm.getSelectedSchedules = function() {
    return _.filter(vm.training_schedules, function(schedule) {
      return schedule.deleted_at.date && schedule.checked;
    });
  };

  // -- Func
  function fetchData(inputParams) {
    addLoading();
    addTableLoading();
    trainingScheduleService.list(inputParams).then(function(res) {
      angular.extend(vm, res.data);
      setData(res.data);
    }, function(error) {
      vm.training_schedules = [];
    }).finally(function() {
      removeLoading();
      removeTableLoading();
    });
  }

  function setData(resData) {
    vm.hasRecord = Boolean(resData.training_schedules.length);
    vm.params = angular.copy(resData.search_condition);
    vm.training_schedules = _.map(vm.training_schedules, function(schedule) {
      schedule.checked = false;
      return schedule;
    });
    vm.deleted_schedules = getDeletedSchedules();
    vm.haveDeletedSchedules = Boolean(vm.deleted_schedules.length);
    vm.isCheckAll = false;
    triggerChangeFields(vm.params);
  }

  function getDeletedSchedules() {
    return _.filter(vm.training_schedules, function(schedule) {
      return schedule.deleted_at.date;
    });
  }

  function triggerChangeFields(params) {
    DATETIME_FIELDS.forEach(function(field) {
      if (params[field]) {
        $("[name='" + field + "']").datepicker("setDate", params[field]);
      };
    });
    SELECT_FIELDS.forEach(function(field) {
      $("#" + field).val(params[field]).trigger("change.select2");
    });
  }

  function setDatePicker() {
    setTimeout(function() {
      $(".training-date-search-datepicker").datepicker({
        autoclose: true,
        format: DEFAULT_DATE_TIME_FORMAT,
        clearBtn: true,
        todayBtn: true,
        todayHighlight: true,
      });
    }, 0);
  };

  function validateParams(inputParams) {
    var allowedKeys = [
      "location_ids", "person_in_charge_ids", "staff_id", "training_date_from", "training_date_to","schedule_id",
      "deleted_by", "training_session", "sort_by", "order_by", "per_page", "page"
    ]
    var filteredParams = _.pick(inputParams, allowedKeys);
    if (Object.keys(filteredParams).length !== Object.keys(inputParams).length) return false;
    return true;
  }

  function getQueryString() {
    var searchParams = $location.search();
    if (validateParams(searchParams)) return searchParams;
    return {};
  }

  function clearQueryString() {
    $location.search({});
  }

  function addLoading() {
    $("#spinner").removeClass("ng-hide");
  }

  function removeLoading() {
    $("#spinner").addClass("ng-hide");
  }

  function addTableLoading() {
    $(".staff-search-result").find(".horizontal-scroll").addClass("loading-skeleton");
  }

  function removeTableLoading() {
    $(".staff-search-result").find(".horizontal-scroll").removeClass("loading-skeleton");
  }
  //
}
