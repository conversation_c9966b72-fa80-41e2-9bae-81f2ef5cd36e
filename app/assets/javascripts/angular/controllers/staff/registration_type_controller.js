"use strict";

angular.module("staffApp")
  .controller("RegistrationTypeController", RegistrationTypeController);

RegistrationTypeController.$inject = ["$scope", "$window", "staffEntryFrmConsts", "staffEntryFrmCommonService"];

function RegistrationTypeController($scope, $window, staffEntryFrmConsts, staffEntryFrmCommonService) {
  var vm = this;
  
  vm.$scope = $scope;
  var ALLOWED_IMG_TYPES = staffEntryFrmConsts.ALLOWED_IMG_TYPES;
  var REQUIRED_RADIO_FIELDS = ["answer_2"];
  var REQUIRED_FILE_FIELDS = ["upload_file_1"];
  vm.fileSizeWithMb = FILE_SIZE_IN_MB + "MB";
  vm.acceptedImgTypes = ALLOWED_IMG_TYPES.join(", ");
  vm.hasImage = true;
  vm.formError = false;
  vm.currentIndustries = [];
  vm.currentProfessions = [];

  vm.params = {
    answer_2: "1",
    upload_file_1: ""
  };

  vm.initAnswer = function(answer, isSingleHaken) {
    if(answer.id) {
      vm.params.answer_2 = answer.answer_2;
      vm.params.upload_file_1 = answer.upload_file_1["url"];
    }
    vm.isSingleHaken = isSingleHaken;
  }

  vm.validateBeforeSubmit = function() {
    if (vm.isSingleHaken) { 
      vm.submitForm();
      return
    }
    validateImageBeforeSubmit();
    if (isFormValid()) {
      vm.submitForm();
    } else {
      window.scrollTo(0, 0);
      $(".disable-submit-btn").prop("disabled", false);
    }
  };

  vm.onClickFileField = function(input) {
    vm.fileInput = input;
    document.body.onfocus = checkFileField;
  }

  vm.isAbleToEditProfile = function(ableEditProfile) {
    return !ableEditProfile;
  }

  vm.backUrl = function() {
    $window.location.href = "/" + I18n.locale + "/profiles";
  };

  vm.fileChanged = function(event) {
    if (!event.target.files[0]) {return;}
    var previewElm = event.target.getAttribute("target-preview");
    var field = event.target.getAttribute("model-name");
    var fileSizeInMb = event.target.files[0].size / 1024 / 1024; 

    if (!_.includes(ALLOWED_IMG_TYPES, event.target.files[0].type)) {
      vm.$scope.$apply(function() {
        $("#" + field + "_file_field").val("");
        if (_.includes(REQUIRED_FILE_FIELDS, field)) {
          vm.answerForm["answer[" + field + "]"].$error = {"file-not-selected": true}
          vm.answerForm["answer[" + field + "]"].$valid = false;
          vm.params[field] = "";
        }
        $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
      });
      return;
    }

    vm.$scope.$apply(function() {
      if (!_.isEqual(event.target.files.length, 0)) {
        eval("vm.params")[field] = event.target.files[0].name;
        staffEntryFrmCommonService.previewImg(event, previewElm);
        if (fileSizeInMb > FILE_SIZE_IN_MB) {
          vm.params[field] = "";
          vm.answerForm["answer[" + field + "]"].$error = {"file-max-size": true}
          vm.answerForm["answer[" + field + "]"].$valid = false;
        } else {
          vm.answerForm["answer[" + field + "]"].$error = {}
          vm.answerForm["answer[" + field + "]"].$valid = true;
        }
      } else {
        $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
      }
    });
  };

  vm.errorClassForFileField = function(inputName) {
    return {
      "file-field-error-notice": vm.conditionForDispErrFileField(inputName)
    };
  };

  vm.conditionForDispErrFileField = function(inputName) {
    var condition = vm["" + inputName + "_touched"] && !vm.params[inputName];
    return condition;
  };

  vm.requireAnswer = function(inputName) {
    return _.isEmpty(vm.params[inputName]);
  };

  vm.displayImageField = function(hasImage) {
    vm.hasImage = hasImage;
    if (vm.hasImage) {
      $("html,body").animate({
        scrollTop: $("#upload-file-field-position").offset().top
      }, 1000);
    } else {
      REQUIRED_FILE_FIELDS.forEach(function(input) {
        vm.answerForm["answer[" + input + "]"].$error = {}
        vm.answerForm["answer[" + input + "]"].$valid = true;
        vm["" + input + "_touched"] = false;
      });
    }
  };

  vm.setTouchedSingleHakenCondition = function() {
    var checked = vm.params.is_single_haken_condition;
    vm.answerForm["answer[is_single_haken_condition]"].$touched = checked;
    if (checked) {
      vm.answerForm["answer[is_single_haken_condition]"].$error = {};
    } else {
      vm.answerForm["answer[is_single_haken_condition]"].$error = {"required": true};
    }
  }

  vm.showModalSingleHakenCondition = function() {
    $("#single-haken-condition").modal("show");
  }

  vm.submitForm = function() {
    $(".disable-submit-btn").prop("disabled", true);
    $("#spinner").removeClass("ng-hide");
    var form = $("#staff-regist-answer-form");
    var formData = new FormData(form[0]);
    $.ajax({
      url: "/profiles/update_registration_type",
      method: "POST",
      dataType: "json",
      data: formData,
      processData: false,
      contentType: false,
      success: function(response) {
        if(response.status) {
          $window.location.href = response.redirect_path;
        } else {
          vm.$scope.$apply(function() {
            $("#spinner").addClass("ng-hide");
            window.scrollTo(0, 0);
            vm.formError = true;
            $(".disable-submit-btn").prop("disabled", false);
          });
        }
      }
    });
  }

  function checkFileField() {
    vm.$scope.$apply(function() {
      vm["" + vm.fileInput + "_touched"] = true;
    });
    if(_.isEmpty($("#" + vm.fileInput + "_file_field").val())) {
      vm.$scope.$apply(function() {
        if (_.includes(REQUIRED_FILE_FIELDS, vm.fileInput) &&
          _.isEmpty(vm.answerForm["answer[" + vm.fileInput + "]"].$error)) {
          setValueAttr(vm.fileInput);
        }
      });
    }
    document.body.onfocus = null;
  }

  function setValueAttr(field) {
    vm.answerForm["answer[" + field + "]"].$error = {"file-not-selected": true};
    vm.answerForm["answer[" + field + "]"].$valid = false;
  }

  function isFormValid() {
    var flag = true;
    var requiredFields = REQUIRED_RADIO_FIELDS.concat(REQUIRED_FILE_FIELDS);
    requiredFields.forEach(function(attr) {
      if (attr == "upload_file_1" && !vm.hasImage) { return; }
      vm.answerForm["answer[" + attr + "]"].$touched = true;
      if (!vm.answerForm["answer[" + attr + "]"].$valid) {flag = false;}
    });
    if (!vm.answerForm['answer[is_single_haken_condition]'].$touched) {
      flag = false;
      vm.answerForm["answer[is_single_haken_condition]"].$error = {"required": true};
    }
    vm.formError = flag ? false : true;
    return flag;
  }

  function validateImageBeforeSubmit() {
    if (!vm.hasImage) { return; }
    REQUIRED_FILE_FIELDS.forEach(function(input) {
      vm["" + input + "_touched"] = true;
      var fileFieldElement = $("#" + input + "_file_field");
      if (!_.isEmpty(fileFieldElement.attr("old-value"))) {
        return
      }
      if(_.isEmpty(fileFieldElement.val())) {
        if (_.includes(REQUIRED_FILE_FIELDS, input) &&
          _.isEmpty(vm.answerForm["answer[" + input + "]"].$error)) {
          setValueAttr(input);
        }
      }
    });
  }

  function disableEnter(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 13) {
      e.preventDefault();
      $("input").blur();
    }
  };

  if (!_.isNull($(".staff-regist-answer-form"))) {
    $(".staff-regist-answer-form").keypress(function(e) {
      disableEnter(e);
    });
  }
}