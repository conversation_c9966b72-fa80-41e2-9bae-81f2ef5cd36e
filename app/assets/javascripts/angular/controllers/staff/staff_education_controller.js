"use strict";

angular.module("staffApp")
  .controller("StaffEducationController", StaffEducationController);
StaffEducationController.$inject = ["staffEducationService", "staffProfileService", "$scope"];

function StaffEducationController(staffEducationService, staffProfileService, $scope) {
  var vm = this;
  var DATA_HOLDER_IDS = ["education_background_types", "graduation_types", "study_time_types",
    "education_backgrounds", "min_record_education"];
  var EDUCATION_FIELDS = ["entry_school_date", "graduation_date", "school_name", "education_background_type", "graduation_type"];
  var DATE_FIELDS = ["entry_school_date", "graduation_date"];

  var TEMPLATE_EDUCATION_BG = {
    id: null,
    entry_school_date: null,
    graduation_date: null,
    education_background_type: null,
    graduation_type: null,
    school_name: null,
    study_department_name: null,
    study_subject_name: null,
    study_time_type: null,
    note: null,
    _destroy: false};
  vm.isInitialized = false;
  vm.isDisabled = false;
  vm.arrItemIndex = [1];

  vm.initData = function() {
    setTimeout(function() {
      window.scrollTo(0, 0);
    }, 500);
    vm.education_backgrounds = [];
    staffEducationService.getDataTabEducations().then(function mySuccess(res){
      if (res.data.status) {
        DATA_HOLDER_IDS.forEach(function(id) {
          vm[id] = JSON.parse(res.data[id]);
        });
        for (var i = 0; i < vm.education_backgrounds.length; i++) vm.arrItemIndex[i] = i + 1;
        vm.isInitialized = true;
        addEvent();
      }
      else
        vm.isDisabled = true;

      if (_.isEmpty(vm.education_backgrounds) && !vm.isDisabled) {
        vm.education_backgrounds.push(angular.copy(TEMPLATE_EDUCATION_BG));
      }
      setBtnStatus();
    });
  }

  vm.submitForm = function() {
    $("#btn-submit-education").prop("disabled", true);
    var params = {};
    vm.isDuringSubmit = true;
    _.forEach(angular.copy(vm.education_backgrounds), function(values, key) {
      _.forEach(DATE_FIELDS, function(key_date) {
        if (!_.isEmpty(values[key_date])) values[key_date] = values[key_date] + "/01";
      });
      _.forEach(EDUCATION_FIELDS, function(key) {
        if(_.isUndefined(values[key])) values[key] = "";
      });
      params[key] = values;
    });
    params = {staff: {staff_education_backgrounds_attributes: params}}
    staffEducationService.updateEducations(params).then(function mySuccess(res){
      if (res.data.status) location.reload();
      else vm.setFormTouched();
    });
  }

  vm.checkIsDate = function(fieldName, index) {
    if (!Date.parse(vm.education_backgrounds[index][fieldName])) {
      vm.education_backgrounds[index][fieldName] = "";
    }
  }

  vm.addItem = function() {
    vm.education_backgrounds.push(angular.copy(TEMPLATE_EDUCATION_BG));
    addEvent();
    var index = 0;
    for(var i = 0; i < vm.arrItemIndex.length; i++){
      if (vm.arrItemIndex[i] != -1) index = vm.arrItemIndex[i];
    }
    vm.arrItemIndex.push(index + 1);
    setBtnStatus();
  }

  vm.destroyItem = function(index) {
    vm.education_backgrounds[index]._destroy = true;
    vm.arrItemIndex[index] = -1;
    for (var i = index + 1; i < vm.arrItemIndex.length; i++) {
      if (vm.arrItemIndex[i] != -1) vm.arrItemIndex[i] -= 1;
    }
    setBtnStatus();
  };

  vm.conditionError = function(index, name, type) {
    if (_.isEqual(type, "error")){
      return vm.educationForm['staff[staff_education_backgrounds_attributes][' + index + '][' + name + ']'].$error;
    } else {
      return vm.educationForm['staff[staff_education_backgrounds_attributes][' + index + '][' + name + ']'].$touched;
    }
  }

  vm.setFormTouched = function() {
    _.forEach(EDUCATION_FIELDS, function(key) {
      _.forEach(vm.education_backgrounds, function(values, index) {
        if (!values._destroy) {
          vm.educationForm['staff[staff_education_backgrounds_attributes][' + index + '][' + key + ']'].$touched = true;
          vm['education_background_type_' + index + '_blur'] = true;
          vm['graduation_type_' + index + '_blur'] = true;
        }
      });
    });
    $("#btn-submit-education").prop("disabled", false);
    vm.isDuringSubmit = false;
  }

  vm.errorClassForInput = function(key, index) {
    var inputName = 'staff[staff_education_backgrounds_attributes][' + index + '][' + key + ']';
    return !vm.educationForm[inputName].$valid && vm.educationForm[inputName].$touched;
  };

  vm.checkEndTime = function(end_time, start_time){
    if (end_time) {
      return (end_time < start_time)
    }
  }

  function addEvent(){
    $scope.$watchGroup(["vm.education_backgrounds", "vm.isInitialized"], function() {
      staffProfileService.setFormatDate();
    });
  }

  function setBtnStatus() {
    var educations = _.filter(vm.education_backgrounds, function(education){
      return !education._destroy
    });
    if (_.isEqual(educations.length, vm.min_record_education)) vm.isDisabledDelBtn = true;
    else vm.isDisabledDelBtn = false;
  }
}
