"use strict";

angular.module("staffApp").controller("RegisteredProfileController", RegisteredProfileController);
RegisteredProfileController.$inject = ["$scope", "bankBranchService", "registeredProfileService", "$window",
  "staffEntryFrmConsts", "staffEntryFrmCommonService", "stepsOverviewService"];
 
function RegisteredProfileController($scope, bankBranchService, registeredProfileService, $window, 
  staffEntryFrmConsts, staffEntryFrmCommonService, stepsOverviewService) {
  var vm = this;

  vm.$scope = $scope;
  var LOCALE = "ja";

  var DATA_HOLDER_IDS = ["edited_staff", "edited_staff_account_transfer", "edited_staff_expectation",
    "nationalities", "residence_statuses", "residence_permissions", "banks", "uniform_sizes", "pant_sizes",
    "shoes_sizes", "relationships", "registration_histories", "prefectures", "japan", "other_nationality",
    "specific_activity_residence_statuses", "has_residence_permission", "is_single_haken", "is_new_form",
    "has_experience"];
  var ADDRESS_ATTRS = staffEntryFrmConsts.ADDRESS_ATTRS;
  var STEP_1_ALL_ATTRS = staffEntryFrmConsts.STEP_1_ALL_ATTRS;
  var STEP_1_DATETIME_FIELDS = staffEntryFrmConsts.STEP_1_DATETIME_FIELDS;
  var STEP_1_REQUIRED_INPUT_FIELDS = staffEntryFrmConsts.STEP_1_REQUIRED_INPUT_FIELDS;
  var STEP_1_RESIDENCE_FILE_FIELDS = staffEntryFrmConsts.STEP_1_RESIDENCE_FILE_FIELDS;
  var STEP_1_JP_RESIDENCE_FILE_FIELDS = staffEntryFrmConsts.STEP_1_JP_RESIDENCE_FILE_FIELDS;
  var STEP_1_RESIDENCE_INPUT_FIELDS = staffEntryFrmConsts.STEP_1_RESIDENCE_INPUT_FIELDS;
  var STEP_1_REQUIRED_SELECT_FIELDS = staffEntryFrmConsts.STEP_1_REQUIRED_SELECT_FIELDS;
  var STEP_1_RESIDENCE_DATETIME_FIELDS = staffEntryFrmConsts.STEP_1_RESIDENCE_DATETIME_FIELDS;
  var STEP_1_RESIDENCE_SELECT_FIELDS = staffEntryFrmConsts.STEP_1_RESIDENCE_SELECT_FIELDS;
  var STEP_2_ALL_EXPECTATION_ATTRS = staffEntryFrmConsts.STEP_2_ALL_EXPECTATION_ATTRS;
  var STEP_2_REQUIRED_SELECT_FIELDS = staffEntryFrmConsts.STEP_2_REQUIRED_SELECT_FIELDS;
  var STEP_3_ALL_ATTRS = staffEntryFrmConsts.STEP_3_ALL_ATTRS;
  var STEP_3_REQUIRED_INPUT_FIELDS = staffEntryFrmConsts.STEP_3_REQUIRED_INPUT_FIELDS;
  var STEP_3_FILE_FIELDS = staffEntryFrmConsts.STEP_3_FILE_FIELDS;
  var STEP_3_REQUIRED_SELECT_FIELDS = staffEntryFrmConsts.STEP_3_REQUIRED_SELECT_FIELDS;
  var STEP_4_ALL_ATTRS = staffEntryFrmConsts.STEP_4_ALL_ATTRS;
  var STEP_4_REQUIRED_INPUT_FIELDS = staffEntryFrmConsts.STEP_4_REQUIRED_INPUT_FIELDS;
  var DATA_SELECT_BOX_ATTR = staffEntryFrmConsts.DATA_SELECT_BOX_ATTR;
  var ERROR_KEY_FROM_SERVER_MAPPINGS = staffEntryFrmConsts.ERROR_KEY_FROM_SERVER_MAPPINGS;
  var ERROR_FORM_STEP = staffEntryFrmConsts.ERROR_FORM_STEP;
  var ALLOWED_IMG_TYPES = staffEntryFrmConsts.ALLOWED_IMG_TYPES;
  var DEFAULT_BIRTHDAY = null;
  var POSTAL_CODE_REGEX = /^\d{7}$/;
  var FIRST_SOCIAL_ATTRIBUTE_JP = "day_student";
  var FIRST_SOCIAL_ATTRIBUTE_FOREIGNER = "international_student";
  var STEP_2_TIME_ATTRS = ["monday_start_time_1", "monday_end_time_1", "tuesday_start_time_1", "tuesday_end_time_1",
    "wednesday_start_time_1", "wednesday_end_time_1", "friday_start_time_1", "friday_end_time_1", "thursday_start_time_1",
    "thursday_end_time_1", "saturday_start_time_1", "saturday_end_time_1", "sunday_start_time_1", "sunday_end_time_1"];
  
  var STEPS = {1: "step1", 2: "step2", 3: "step3", 4: "step4"};

  var $residenceExpDateLabel = angular.element("label[for=residence_expiration_date] span");
  vm.OTHER_KEY = "other";
  vm.staff = {};
  vm.homeStations = [];
  vm.schoolStations = [];
  vm.bankBranches = [];
  vm.mappingErrors = ERROR_KEY_FROM_SERVER_MAPPINGS;
  vm.isInitedHomeStation = false;
  vm.isInitedSchoolStation = false;
  vm.isInitedBankBranch = false;
  vm.formError = {};
  vm.acceptedImgTypes = ALLOWED_IMG_TYPES.join(", ");
  vm.fileSizeWithMb = FILE_SIZE_IN_MB + "MB";
  vm.base = false;
  vm.isPreloadResidenceForm = false;
  vm.isJapanese = true;
  vm.currentAccMiddleNameLength = 0;
  vm.currentAccMiddleNameKanaLength = 0;

  initData();

  vm.$scope.$watch("vm.staff.is_emergency_address_differ_current_address", function() {
    staffEntryFrmCommonService.watchDifferCurrentAddr(vm, "staff");
  });

  vm.$scope.$watch("vm.staff.residence_permission", function() {
    staffEntryFrmCommonService.watchResidencePermission(vm, "staff");
  });

  vm.$scope.$watch("vm.staff.prefecture_id", function() {
    staffEntryFrmCommonService.watchStation(vm, "staff");
  });

  vm.$scope.$watch("vm.staff.staff_account_transfer_attributes.bank_id", function() {
    if (vm.staff.staff_account_transfer_attributes.bank_id) {
      bankBranchService.getBankBranch({bank_id: vm.staff.staff_account_transfer_attributes.bank_id}).then(function(res) {
        vm.bankBranches = res.data.bank_branches;
      });
    } else {
      vm.staff.staff_account_transfer_attributes.bank_branch_id = "";
      vm.bankBranches = [];
    }
    if (_.isEmpty(vm.edited_staff_account_transfer)) {
      vm.staff.staff_account_transfer_attributes.bank_branch_id = "";
    } else {
      if (vm.isInitedBankBranch) {
        vm.staff.staff_account_transfer_attributes.bank_branch_id = "";
      } else {
        vm.isInitedBankBranch = true;
      }
    }
  });

  vm.$scope.$watch("vm.staff.birthday", function() {
    staffEntryFrmCommonService.watchBirthday(vm, "staff");
  });

  vm.$scope.$watch("vm.staff.nationality", function() {
    if (vm.staff.nationality != vm.other_nationality)
      vm.staff.other_nationality = "";
    else {
      vm.staff.other_nationality = !vm.is_new_form ? vm.edited_staff.other_nationality : "";
      vm.entryForm['staff[other_nationality]'].$touched = false;
    }
  });

  vm.handlePatternTel = (function() {
    return {
      test: function(value) {
        var length = value.replace(/-/g, "").length;
        return STAFF_TEL_REGEX.test(value) && _.includes(STAFF_TEL_LENGTH, length);
      }
    };
  })();

  vm.handlePatternResidentNumber = (function() {
    return {
      test: function(value) {
        return RESIDENCE_NUMBER_REGEX.test(value);
      }
    };
  })();

  vm.currentStep = function() {
    return vm.overviewSteps.filter(function(step) {
      return step.active;
    })[0].id;
  };

  vm.setSelectedItem = function(attr, id, selectedVal, notExecute) {
    if (notExecute) {return;}
    vm.staff[attr] = id;
    vm.staff["selected_" + attr] = selectedVal;
  };

  vm.searchPostalCodeByExactlyCode = function(field) {
    staffEntryFrmCommonService.searchPostalCodeByExactlyCode(vm, "staff", field);
  };

  vm.enableAccInputMiddleName = function() {
    vm.isEnableAccountMiddleName = true;
    if (!vm.currentAccMiddleNameKanaLength) vm.currentAccMiddleNameKanaLength = 1;
    if (!vm.currentAccMiddleNameLength) vm.currentAccMiddleNameLength = 1;
  };

  vm.removeAccMiddleName = function(position, field, isName) {
    var counter;
    if (isName) {
      counter = angular.copy(vm.currentAccMiddleNameLength);
      vm.currentAccMiddleNameLength--;
    } else {
      counter = angular.copy(vm.currentAccMiddleNameKanaLength);
      vm.currentAccMiddleNameKanaLength--;
    }
    for (var i = position; i <= counter - 1; i++) {
      vm.staff.staff_account_transfer_attributes[field + "_" + i] = $("#" + field + "_" + (i + 1)).val();
    }
    vm.staff.staff_account_transfer_attributes[field + "_" + counter] = "";
  };

  vm.addAccMiddleName = function(suffix) {
    vm["currentAccMiddleName" + suffix + "Length"]++;
  };

  vm.errorClassForInput = function(inputName) {
    if (!vm.entryForm[inputName]) return;
    return {
      "form-error": !vm.entryForm[inputName].$valid && vm.entryForm[inputName].$touched
    };
  };

  vm.errorClassForSocialAttrOther = function(socialAttributeOther) {
    return {
      "form-error": !vm.entryForm['staff[social_attribute_other]'].$valid &&
        vm.entryForm['staff[social_attribute_other]'].$touched &&
        vm.staff.social_attribute == socialAttributeOther
    };
  };

  vm.errorClassForFileField = function(inputName, isNeedSpecActivities) {
    return {
      "file-field-error-notice": vm.conditionForDispErrFileField(inputName, isNeedSpecActivities)
    };
  };

  vm.errorClassForInputAccName = function() {
    return {
      "form-error": (!vm.entryForm["staff[staff_account_transfer_attributes][account_first_name]"].$valid && vm.entryForm["staff[staff_account_transfer_attributes][account_first_name]"].$touched) ||
        (!vm.entryForm["staff[staff_account_transfer_attributes][account_last_name]"].$valid && vm.entryForm["staff[staff_account_transfer_attributes][account_last_name]"].$touched) ||
        (!vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_1]"].$valid && vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_1]"].$touched) ||
        (!vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_2]"].$valid && vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_2]"].$touched) ||
        (!vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_3]"].$valid && vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_3]"].$touched)
    };
  };

  vm.conditionForDispErrFileField = function(inputName, isNeedSpecActivities) {
    var condition = vm["staff_" + inputName + "_touched"] && !vm.staff[inputName];
    if (vm.staff.nationality == vm.japan) {
      condition = condition && vm.is_new_form ||
      !!vm.entryForm["staff[" + inputName + "]"].$error["file-max-size"]
    }
    if (isNeedSpecActivities) {
      condition = (condition && (_.includes(vm.specific_activity_residence_statuses, vm.staff.residence_status)) ||
        vm.certificateImgError || !!vm.entryForm["staff[" + inputName + "]"].$error["file-max-size"]);
    }
    return condition;
  };

  vm.fileChanged = function(event) {
    if (!event.target.files[0]) {return;}
    var previewElm = event.target.getAttribute("target-preview");
    var argument = event.target.getAttribute("argument");
    var editedArgument = event.target.getAttribute("edited-argument");
    var field = event.target.getAttribute("model-name");
    var fileSizeInMb = event.target.files[0].size / 1024 / 1024;
    var allFileFields = STEP_1_RESIDENCE_FILE_FIELDS.concat(STEP_1_JP_RESIDENCE_FILE_FIELDS);

    if (!_.includes(ALLOWED_IMG_TYPES, event.target.files[0].type)) {
      vm.$scope.$apply(function() {
        $("#staff_" + field + "_file_field").val("");
        if (_.includes(allFileFields, field)) {
          var src = angular.element("#" + field + "_old").data("value");
          $("." + field + "_preview").attr("src", src);
          vm.entryForm["staff[" + field + "]"].$error = {"file-not-selected": true}
          vm.entryForm["staff[" + field + "]"].$valid = false;
          vm.staff[field] = vm.is_new_form ? "" : vm.edited_staff[field];
        } else {
          vm.staff.staff_account_transfer_attributes.passbook_pic = "";
          $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
          eval("vm." + argument)[$(previewElm).attr("data-name")] = _.isEmpty(eval("vm." + editedArgument)) ? "" :
            eval("vm." + editedArgument)[$(previewElm).attr("data-name")];
          vm.staff.staff_account_transfer_attributes.passbook_pic_over_size = false;
        }
      });
      return;
    }

    vm.$scope.$apply(function() {
      if (!_.isEqual(event.target.files.length, 0)) {
        eval("vm." + argument)[field] = event.target.files[0].name;
        staffEntryFrmCommonService.previewImg(event, previewElm);
        if (fileSizeInMb > FILE_SIZE_IN_MB) {
          if (_.includes(allFileFields, field)) {
            vm.staff[field] = "";
            vm.entryForm["staff[" + field + "]"].$error = {"file-max-size": true}
            vm.entryForm["staff[" + field + "]"].$valid = false;
          } else {
            vm.staff.staff_account_transfer_attributes.passbook_pic = "";
            vm.staff.staff_account_transfer_attributes.passbook_pic_over_size = true;
          }
        } else {
          if (_.includes(allFileFields, field)) {
            if (field == "certificate_img") {vm.certificateImgError = false;}
            vm.entryForm["staff[" + field + "]"].$error = {}
            vm.entryForm["staff[" + field + "]"].$valid = true;
          } else {
            vm.staff.staff_account_transfer_attributes.passbook_pic_over_size = false;
          }
        }
      } else {
        $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
        eval("vm." + argument)[$(previewElm).attr("data-name")] = _.isEmpty(eval("vm." + editedArgument)) ? "" :
          eval("vm." + editedArgument)[$(previewElm).attr("data-name")];
      }
    });
  };

  vm.otherFieldsValid = function() {
    var flag = true;

    setTouchedForSelectFields();

    if (!isStep1SelectFieldsValid()) {return false;}

    if (vm.staff.nationality != vm.japan) {
      for (var idx in STEP_1_RESIDENCE_FILE_FIELDS) {
        var field = STEP_1_RESIDENCE_FILE_FIELDS[idx];
        vm["staff_" + field + "_touched"] = true;
        if (_.isEqual("certificate_img", field)) {
          if (vm.conditionForDispErrFileField(field, true)) {
            flag = false;
            break;
          }
        } else {
          if (vm.conditionForDispErrFileField(field)) {
            flag = false;
            break;
          }
        }
      };
    } else {
      for (var idx in STEP_1_JP_RESIDENCE_FILE_FIELDS) {
        var field = STEP_1_JP_RESIDENCE_FILE_FIELDS[idx];
        vm["staff_" + field + "_touched"] = true;
        if (vm.conditionForDispErrFileField(field)) {
          flag = false;
          break;
        }
      }
    }
    return flag;
  };

  vm.validateStep1 = function() {
    $(".disable-submit-btn").prop("disabled", true);
    vm.staff.career_up_id = staffEntryFrmCommonService.removeFalseItem(vm.staff.career_up_ids);
    vm.checkTelNumber();

    if (vm.is_new_form) { vm.setErrorForImg(); }
    if (isStep1InputFieldsValid() && vm.otherFieldsValid()) {
      if (vm.staff.is_emergency_address_differ_current_address == 0) {
        ADDRESS_ATTRS.forEach(function(attr) {
          vm.staff["emergency_" + attr] = angular.copy(vm.staff[attr]);
        });
      }
      var params = {staff: vm.staff};
      if (!vm.is_new_form) { params["edit"] = true; }
      registeredProfileService.validateStep1(params).then(function mySuccess(response) {
        if (response.data.status) {
          vm.nextStep();
          vm.formError.is_display_error_step1 = false;
          $(".disable-submit-btn").prop("disabled", false);
        } else {
          setFormTouchedStep1(vm.entryForm);
          vm.formError.is_display_error_step1 = true;
          if (!!response.data.errors["certificate_img"]) {
            vm.certificateImgError = true;
            setValueAttr("certificate_img");
          }
          window.scrollTo(0, 0);
          $(".disable-submit-btn").prop("disabled", false);
        }
      }, function myError(response) {
        location.reload();
      });
    } else {
      setFormTouchedStep1(vm.entryForm);
      vm.formError.is_display_error_step1 = true;
      window.scrollTo(0, 0);
      $(".disable-submit-btn").prop("disabled", false);
    }
  };

  vm.validateStep2 = function(){
    $(".disable-submit-btn").prop("disabled", true);
    vm.is_check();
    if (isStep2InputFieldsValid()) {
      registeredProfileService.validateStep2({staff: vm.staff}).then(function mySuccess(res) {
        if (res.data.status) {
          vm.nextStep();
          vm.formError.is_display_error_step2 = false;
          $(".disable-submit-btn").prop("disabled", false);
        } else {
          var errors = JSON.parse(res.data.errors);
          angular.forEach(errors, function(errorArr, field) {
            vm[field] = true;
          });
          setFormTouchedStep2(vm.entryForm);
          vm.formError.is_display_error_step2 = true;
          window.scrollTo(0, 0);
          $(".disable-submit-btn").prop("disabled", false);
        }
      }, function myError(response) {
        location.reload();
      });
    } else {
      setFormTouchedStep2(vm.entryForm);
      vm.formError.is_display_error_step2 = true;
      window.scrollTo(0, 0);
      $(".disable-submit-btn").prop("disabled", false);
    }
  };

  vm.validateStep3 = function() {
    $(".disable-submit-btn").prop("disabled", true);
    if (isStep3InputFieldsValid()) {
      var step_3_params = {
        staff: {staff_account_transfer_attributes: vm.staff.staff_account_transfer_attributes, staff_code: vm.staff.staff_code}
      }
      registeredProfileService.validateStep3(step_3_params).then(function mySuccess(res) {
        if (res.data.status) {
          vm.entryForm.$valid = true;
          vm.nextStep();
          vm.formError.is_display_error_step3 = false;
          $(".disable-submit-btn").prop("disabled", false);
        } else {
          var errors = res.data.errors;
          setFormTouchedStep3(vm.entryForm);
          vm.entryForm.$valid = false;
          angular.forEach(errors, function(errorArr, field) {
            setErrorInput(errorArr[0].error, "staff[staff_account_transfer_attributes][" + field + "]");
          });
          vm.formError.is_display_error_step3 = true;
          window.scrollTo(0, 0);
          $(".disable-submit-btn").prop("disabled", false);
        }
      }, function myError(response) {
        location.reload();
      });
    } else {
      setFormTouchedStep3(vm.entryForm);
      vm.formError.is_display_error_step3 = true;
      window.scrollTo(0, 0);
      $(".disable-submit-btn").prop("disabled", false);
    }
  };

  vm.validateStep4 = function() {
    $(".disable-submit-btn").prop("disabled", true);
    if(vm.entryForm.$valid) {
      registeredProfileService.validateStep4({staff: vm.staff}).then(function mySuccess(response) {
        if(response.data.status) {
          vm.openModalConfirm();
          vm.formError.is_display_error_step4 = false;
          $(".disable-submit-btn").prop("disabled", false);
        } else {
          setFormTouchedStep4(vm.entryForm);
          vm.formError.is_display_error_step4 = true;
          window.scrollTo(0, 0);
          $(".disable-submit-btn").prop("disabled", false);
        }
      }, function myError(response) {
        location.reload();
      });
    } else {
      setFormTouchedStep4(vm.entryForm);
      vm.formError.is_display_error_step4 = true;
      window.scrollTo(0, 0);
      $(".disable-submit-btn").prop("disabled", false);
    }
  };

  vm.submitForm = function() {
    $("#spinner").removeClass("ng-hide");
    $("#staff-entry-form-submit").trigger("click");
  };

  vm.setActiveFirstStep = function() {
    var currentStep = vm.overviewSteps[vm.currentStep() - 1];
    var firstStep = vm.overviewSteps[0];

    currentStep.active = false;
    currentStep.done = false;
    firstStep.active = true;
  };

  vm.nextStep = function() {
    var currentStep = vm.overviewSteps[vm.currentStep() - 1];
    var nextStep = vm.overviewSteps[vm.currentStep()];
    currentStep.active = false;
    currentStep.done = true;
    nextStep.active = true;
    window.scrollTo(0, 0);
  }

  vm.previousStep = function() {
    var currentStep = vm.overviewSteps[vm.currentStep() - 1];
    var prevStep = vm.overviewSteps[vm.currentStep() - 2];
    currentStep.active = false;
    prevStep.active = true;
    prevStep.done = false;
    window.scrollTo(0, 0);
    $(".disable-submit-btn").prop("disabled", false);
  };

  vm.openModalConfirm = function() {
    angular.element("#confirm-staff-entry").modal("show");
  }

  vm.errorClassForFileFieldStep3 = function(inputName) {
    return {
      "file-field-error-notice": vm["staff_" + inputName + "_touched"] &&
        !vm.staff.staff_account_transfer_attributes[inputName]
    };
  };

  vm.checkIsDate = function(fieldName, nestedField) {
    if (_.isUndefined(nestedField)) {
      if (!Date.parse(vm.staff[fieldName])) {
        vm.staff[fieldName] = "";
      }
    } else {
      if (!Date.parse(vm.staff[nestedField][fieldName])) {
        vm.staff[nestedField][fieldName] = "";
      }
    }
  };

  vm.checkIsTime = function(fieldName, nestedField) {
    var timeFomat = /^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/
    if (_.isUndefined(nestedField)) {
      if (!timeFomat.test(vm.staff[fieldName])) {
        vm.staff[fieldName] = "";
      }
    } else {
      if (!timeFomat.test(vm.staff[nestedField][fieldName])) {
        vm.staff[nestedField][fieldName] = "";
      }
    }
  }

  vm.compareTime = function(fieldName, fieldStart, nestedField) {
    if (_.isEmpty(vm.staff[nestedField][fieldName]) || _.isEmpty(vm.staff[nestedField][fieldStart])) return;
    var startTime = Date.parse('01/01/2001 ' + vm.staff[nestedField][fieldStart]);
    var endTime = Date.parse('01/01/2001 ' + vm.staff[nestedField][fieldName]);
    if (startTime >= endTime) {
      return true;
    }
  }

  vm.resetResidenceFields = function(isRemoveAll, notExecute) {
    if (notExecute) {return;}
    vm.staff_nationality_blur = false;

    STEP_1_RESIDENCE_FILE_FIELDS.forEach(function(field) {
      vm.staff[field] = !vm.is_new_form && !isRemoveAll ? vm.edited_staff[field] : "";
      vm["staff_" + field + "_touched"] = false;
      var src = angular.element("#" + field + "_old").data("value");
      $("." + field + "_preview").attr("src", src);
      $("#staff_" + field + "_file_field").val("");
      if (_.isEqual("certificate_img", field)) { vm.staff["remove_" + field] = isRemoveAll; }
    });

    STEP_1_RESIDENCE_INPUT_FIELDS.forEach(function(field) {
      vm.staff[field] = !vm.is_new_form && !isRemoveAll ? vm.edited_staff[field] : "";
      vm.entryForm["staff[" + field + "]"].$touched = false;
    })

    STEP_1_RESIDENCE_SELECT_FIELDS.forEach(function(field) {
      vm.staff[field] = !vm.is_new_form && !isRemoveAll ? vm.edited_staff[field] : "";
      vm["staff_" + field + "_blur"] = false;
    });

    STEP_1_RESIDENCE_DATETIME_FIELDS.forEach(function(field) {
      vm.staff[field] = !vm.is_new_form && !!vm.edited_staff[field] && !isRemoveAll ?
        moment(vm.edited_staff[field]).format(ORDER_DATEPICKER_FORMAT) : "";
      if (vm.is_new_form) {
        $("input#datepicker_" + field).siblings("label").removeClass("label-ontop");
      }
    });
  };

  vm.resetNationality = function() {
    if (vm.is_new_form) {
      vm.setSelectedItem("nationality", "", "", vm.staff.nationality != vm.japan);
    } else {
      var nation = vm.nationalities.filter(function(nationality) {
        return nationality.id == vm.edited_staff.nationality;
      })[0];
      if (nation) {
        vm.setSelectedItem("nationality", nation.id, nation.name, vm.staff.nationality != vm.japan);
      } else {
        vm.setSelectedItem("nationality", "", "");
      }
    }
  };

  vm.resetStartEndTime = function(field) {
    if (!vm.staff.staff_expectation_attributes["is_" + field]){
      $("#" + field + "_end_time_1").data("datetimepicker").clear();
      $("#" + field + "_start_time_1").data("datetimepicker").clear();
      vm.entryForm["staff[staff_expectation_attributes][" + field + "_start_time_1]"].$touched = false;
      vm.entryForm["staff[staff_expectation_attributes][" + field + "_end_time_1]"].$touched = false;
    } else {
      vm.staff.staff_expectation_attributes[field + "_start_time_1"] = _.isEmpty(vm.edited_staff_expectation) ? "" :
        vm.edited_staff_expectation[field + "_start_time_1"];
      vm.staff.staff_expectation_attributes[field + "_end_time_1"] = _.isEmpty(vm.edited_staff_expectation) ? "" :
        vm.edited_staff_expectation[field + "_end_time_1"];
    }
  }

  vm.resetResidentDateTimeFields = function() {
    STEP_1_RESIDENCE_DATETIME_FIELDS.forEach(function(attr) {
      $("#" + attr).data("datetimepicker").clear();
    });
  };

  vm.resetSocialAttrOther = function() {
    vm.staff.social_attribute_other = vm.is_new_form ? "" : vm.edited_staff.social_attribute_other;
    vm.entryForm['staff[social_attribute_other]'].$touched = false;
  };

  vm.resetForeignersInfos = function() {
    vm.resetResidenceFields(false, vm.staff.nationality != vm.japan);
    vm.resetNationality();
    vm.setErrorForImg();
    vm.resetJapanResidenceFileFields(true);
    setSocialAttrByNationality(false);
  };

  vm.setTouchedContractAndHandlingChk = function(fieldName) {
    if (_.isUndefined(fieldName)) {
      _.forEach(STEP_4_REQUIRED_INPUT_FIELDS, function(fieldName) {
        vm.entryForm["staff[" + fieldName + "]"].$touched = true;
      });
    } else {
      vm.entryForm["staff[" + fieldName + "]"].$touched = true;
    }
  };

  vm.setTouchedSingleHakenCondition = function(fieldName) {
    vm.entryForm["staff[" + fieldName + "]"].$touched = true;
  }

  vm.showModalSingleHakenCondition = function() {
    $("#single-haken-condition").modal("show");
  }

  vm.disableForeignersInfos = function() {
    vm.setErrorForImg();
    vm.resetResidenceFields(true);
    vm.resetJapanResidenceFileFields(false, vm.staff.nationality == vm.japan);
    vm.resetResidentDateTimeFields();
    setSocialAttrByNationality(true);
    vm.staff.is_partime_experience = false
  };

  function setSocialAttrByNationality(isJapanese) {
    vm.isJapanese = isJapanese;
    if(isJapanese) {
      vm.staff.social_attribute = FIRST_SOCIAL_ATTRIBUTE_JP;
    } else {
      vm.staff.social_attribute = FIRST_SOCIAL_ATTRIBUTE_FOREIGNER;
    }
  }

  vm.resetJapanResidenceFileFields = function(isRemoveAll, notExecute) {
    if (notExecute) { return; }
    STEP_1_JP_RESIDENCE_FILE_FIELDS.forEach(function(field) {
      vm.staff[field] = !vm.is_new_form && !isRemoveAll ? vm.edited_staff[field] : "";
      vm["staff_" + field + "_touched"] = false;
      var src = angular.element("#" + field + "_old").data("value");
      $("." + field + "_preview").attr("src", src);
      $("#staff_" + field + "_file_field").val("");
    });
  };

  vm.isChosen = staffEntryFrmCommonService.isChosen;
  vm.checkTimeWorked = staffEntryFrmCommonService.checkTimeWorked;

  function setFormTouchedStep1(form) {
    if (vm.staff.nationality == vm.japan) {
      STEP_1_JP_RESIDENCE_FILE_FIELDS.forEach(function(field) {
        vm["staff_" + field + "_touched"] = true;
      });
    } else {
      STEP_1_RESIDENCE_FILE_FIELDS.forEach(function(field) {
        vm["staff_" + field + "_touched"] = true;
      });
    }

    vm.entryForm["staff[emergency_tel]"].$touched = true;
    if (vm.staff.social_attribute == vm.OTHER_KEY) {
      vm.entryForm["staff[social_attribute_other]"].$touched = true;
    }

    setTouchedForSelectFields();
    var stepInfo = staffEntryFrmCommonService.getStepInfo("step1");
    if (_.isEqual(vm.staff.social_attribute, vm.OTHER_KEY)){
      stepInfo.requiredFields.push("social_attribute_other");
    }
    setTouchedForInputFields(form, stepInfo);
  }

  function setFormTouchedStep2(form) {
    var stepInfo = staffEntryFrmCommonService.getStepInfo("step2");
    setTouchedForInputFields(form, stepInfo);
    _.forEach(STEP_2_REQUIRED_SELECT_FIELDS, function(fieldName) {
      vm.entryForm["staff[staff_expectation_attributes][" + fieldName + "]"].$touched = true;
    });
  }

  function setFormTouchedStep3(form) {
    STEP_3_FILE_FIELDS.forEach(function(field) {
      vm["staff_" + field + "_touched"] = true;
    });

    setTouchedForSelectFieldsStep3();
    var stepInfo = staffEntryFrmCommonService.getStepInfo("step3");
    setTouchedForInputFields(form, stepInfo);
  }

  function setFormTouchedStep4(form) {
    setTouchedForInputFields(form, "step4");
    vm.setTouchedContractAndHandlingChk();
    if (vm.is_single_haken) {
      vm.setTouchedSingleHakenCondition('is_single_haken_condition');
    }
  }

  function setTouchedForInputFields(form, stepInfo) {
    if (form.hasOwnProperty("$submitted")) {
      angular.forEach(form.$error, function (errorType) {
        angular.forEach(errorType, function (prop) {
          var requiredInputFields = _.flatMap(stepInfo.requiredFields, eval(stepInfo.stepFunc));
          if (prop.hasOwnProperty("$touched") && _.includes(requiredInputFields, prop.$name)) prop.$setTouched();
          setFormTouchedStep1(prop);
        });
      });
    }
  }

  function setTouchedForSelectFields() {
    STEP_1_REQUIRED_SELECT_FIELDS.forEach(function(field) {
      vm["staff_" + field + "_blur"] = true;
    });
  }

  function setTouchedForSelectFieldsStep3() {
    STEP_3_REQUIRED_SELECT_FIELDS.forEach(function(field) {
      vm["staff_" + field + "_blur"] = true;
    });
  }

  function isStep1SelectFieldsValid() {
    var condition = !vm.staff.prefecture_id || !vm.staff.nationality || !vm.staff.home_station_id ||
      !vm.staff.entry_uniform_size || !vm.staff.entry_pant_size || !vm.staff.entry_shoes_size ||
      (!!vm.staff.nationality && vm.staff.nationality != vm.japan && !vm.staff.residence_status);
    if (condition) return false;
    return true;
  }

  function isStep1InputFieldsValid() {
    var flag = true;
    for (var idx in STEP_1_REQUIRED_INPUT_FIELDS) {
      if (STEP_1_REQUIRED_INPUT_FIELDS[idx] == "residence_number" && vm.staff.nationality === vm.japan) {
        continue;
      } else {
        var field = "staff[" + STEP_1_REQUIRED_INPUT_FIELDS[idx] + "]";
        if (vm.entryForm[field] && !vm.entryForm[field].$valid) {
          flag = false;
          break;
        }
      }
    }
    if (flag) {
      flag = vm.entryForm["staff[residence_expiration_date]"].$valid;
    }
    if (flag) {
      flag = vm.entryForm["staff[social_attribute_other]"].$valid;
    }
    return flag;
  }

  function isStep2InputFieldsValid() {
    var flag = true;
    _.forEach(STEP_2_REQUIRED_SELECT_FIELDS, function(field) {
      var fieldName = "staff[staff_expectation_attributes][" + field + "]";
      if (!_.isUndefined(vm.entryForm[fieldName]) && !vm.entryForm[fieldName].$valid) {
        flag = false;
      }
    });
    return flag;
  }

  function isStep3InputFieldsValid() {
    var flag = true;
    for (var idx in STEP_3_REQUIRED_INPUT_FIELDS) {
      if (!vm.entryForm["staff[staff_account_transfer_attributes][" + STEP_3_REQUIRED_INPUT_FIELDS[idx] + "]"].$valid) {
        flag = false;
      }
    }
    return flag;
  }

  function setErrorInput(error, inputName) {
    if (!_.isUndefined(vm.entryForm[inputName])) {
      vm.entryForm[inputName].$error[vm.mappingErrors[error]] = true;
      vm.entryForm[inputName].$valid = false;
      vm.entryForm[inputName].$touched = true;
    }
  }

  function initData() {
    moment.locale(LOCALE);

    vm.staff.staff_account_transfer_attributes = {};
    vm.staff.staff_expectation_attributes = {};

    _.forEach(STEP_1_ALL_ATTRS, function(attr) {
      if (_.isEqual(attr, "birthday")) {
        vm.staff[attr] = DEFAULT_BIRTHDAY;
        return;
      }
      vm.staff[attr] = "";
    });

    vm.staff.career_up_ids = {};

    _.forEach(STEP_2_ALL_EXPECTATION_ATTRS, function(attr) {
      vm.staff.staff_expectation_attributes[attr] = "";
    });

    _.forEach(STEP_3_ALL_ATTRS, function(attr) {
        vm.staff.staff_account_transfer_attributes[attr] = "";
    });

    _.forEach(STEP_4_ALL_ATTRS, function(attr) {
      if (_.isEqual(attr, "payment_request")) {
        vm.staff[attr] = "";
        return;
      }
      vm.staff[attr] = true;
    });

    _.forEach(STEP_4_REQUIRED_INPUT_FIELDS, function(attr) {
      if (!_.isEqual(attr, "is_expectation_condition")) vm.staff[attr] = false;
    });

    _.forEach(ERROR_FORM_STEP, function(attr) {
      vm.formError[attr] = false;
    });

    DATA_SELECT_BOX_ATTR.forEach(function(id) {
      vm[id] = [];
    });

    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });

    initOverview();

    vm.staff.staff_expectation_attributes.expectation_employment_type = "haken";
    vm.staff.staff_expectation_attributes.is_working_car = false;
    var days = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"];
    _.forEach(days, function(day) {
      vm.staff.staff_expectation_attributes['is_' + day] = false;
    });

    formatDataForEdit();
    vm.staff.nationality == '' ? vm.staff.nationality = vm.japan : '';
  }

  vm.initTel = function(tel) {
    vm.staff.tel = tel;
  }

  function formatDataForEdit() {
    _.forEach(STEP_1_ALL_ATTRS, function(attr) {
      vm.staff[attr] = vm.edited_staff[attr];
    });

    _.forEach(STEP_1_DATETIME_FIELDS, function(field) {
      if (moment(vm.staff[field]).isValid()) {
        vm.staff[field] = moment(vm.staff[field]).format(ORDER_DATEPICKER_FORMAT);
      }
    });

    vm.staff.career_up_ids = vm.edited_staff.career_up_id.reduce(function(res, id) {
      res[id] = true;
      return res;
    }, {});

    if (vm.is_new_form) {
      vm.staff.is_emergency_address_differ_current_address = "0";
      return;
    }

    _.forEach(STEP_2_ALL_EXPECTATION_ATTRS, function(attr) {
      vm.staff.staff_expectation_attributes[attr] = vm.edited_staff_expectation[attr];
      if (_.includes(STEP_2_TIME_ATTRS, attr) && moment(vm.edited_staff_expectation[attr]).isValid()) {
        vm.staff.staff_expectation_attributes[attr] = moment.parseZone(vm.edited_staff_expectation[attr]).format(TIME_PICKER_FORMAT);
      }
    });

    _.forEach(STEP_3_ALL_ATTRS, function(attr) {
      vm.staff.staff_account_transfer_attributes[attr] = vm.edited_staff_account_transfer[attr];
    });

    formatAccountFirstMiddleLastName();

    _.forEach(STEP_4_ALL_ATTRS, function(attr) {
      vm.staff[attr] = vm.edited_staff[attr];
    });
  }

  var getFormData = function(form) {
    var $inputs = $("input[type='file']:not([disabled])", form);
    $inputs.each(function(_, input) {
      if (input.files.length > 0) return;
      $(input).prop("disabled", true);
    });
    var formData = new FormData(form);
    $inputs.prop("disabled", false);
    return formData;
  };

  $(".staff-entry-form").on("submit", function(e) {
    e.preventDefault();
    $("#spinner").removeClass("ng-hide");

    $.ajax({
      url: "/" + LOCALE + "/registered_profiles/update",
      method: "POST",
      dataType: "json",
      data: getFormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        if(response.status) {
          $window.location.href = response.redirect_path
        } else {
          window.scrollTo(0, 0);
          $("#confirm-staff-entry").modal("hide");
          $("#spinner").addClass("ng-hide");
          vm.$scope.$apply(function() {
            initOverview();
            vm.formError.is_display_error_step1 = true;
            setFormTouchedStep1(vm.entryForm);
          });
        }
      },
      error: function(){
        location.reload();
      }
    });
  });

  vm.showError = function(condition, value, server){
    return (condition && !value) || server
  }

  vm.checkEndTime = function(end_time, start_time, server){
    if (end_time) {
      return (end_time < start_time) || server
    }
  }

  vm.conditionForDispErrForm = function() {
    return vm.formError["is_display_error_step" + vm.currentStep()] == true ? true : false
  }

  vm.onClickFileField = function(input) {
    vm.fileInput = input;
    document.body.onfocus = checkFileField;
  }

  function checkFileField() {
    var allFileFields = STEP_1_RESIDENCE_FILE_FIELDS.concat(STEP_1_JP_RESIDENCE_FILE_FIELDS);
    vm.$scope.$apply(function() {
      vm["staff_" + vm.fileInput + "_touched"] = true;
    });
    if(_.isEmpty($("#staff_" + vm.fileInput + "_file_field").val())) {
      vm.$scope.$apply(function() {
        if (_.includes(allFileFields, vm.fileInput) &&
          _.isEmpty(vm.entryForm["staff[" + vm.fileInput + "]"].$error)) {
          setValueAttr(vm.fileInput);
        }
      });
    }
    document.body.onfocus = null;
  }

  vm.setErrorForImg = function() {
    if (vm.staff.nationality == vm.japan) {
      _.forEach(STEP_1_JP_RESIDENCE_FILE_FIELDS, function(field) {
        setValueAttr(field);
      })
    } else {
      _.forEach(STEP_1_RESIDENCE_FILE_FIELDS, function(field) {
        setValueAttr(field);
      })
    }
  };

  function setValueAttr(field){
    vm.entryForm["staff[" + field + "]"].$error = {"file-not-selected": true};
    vm.entryForm["staff[" + field + "]"].$valid = false;
  }

  $("input.datetimepicker-input").on("input", function() {
    var field = $(this).attr("id");
    var value = $(this).val();
    if (_.includes(STEP_1_DATETIME_FIELDS, field)) {
      vm.staff[field] = value;
    }
  });

  vm.is_check = function(){
    vm.base = $('.date-expectation-check:checked').length == 0;
  }

  vm.postalCodePattern = (function() {
    return {
      test: function(value) {
        return POSTAL_CODE_REGEX.test(value.replace(/\-/g, ""));
      }
    }
  })();

  function formatAccountFirstMiddleLastName() {
    var nameArr = vm.staff.staff_account_transfer_attributes.account_name.split(" "),
    nameKanaArr = vm.staff.staff_account_transfer_attributes.account_name_kana.split(" ");

    vm.staff.staff_account_transfer_attributes.account_last_name = nameArr.splice(0, 1).join("");
    vm.staff.staff_account_transfer_attributes.account_first_name = nameArr.splice(nameArr.length - 1, 1).join("");
    var middle_names = nameArr.filter(function(item){return item != ''});

    vm.staff.staff_account_transfer_attributes.account_last_name_kana = nameKanaArr.splice(0, 1).join("");
    vm.staff.staff_account_transfer_attributes.account_first_name_kana = nameKanaArr.splice(nameKanaArr.length - 1, 1).join("");
    var middle_name_kanas = nameKanaArr.filter(function(item){return item != ''});
    [1, 2, 3].forEach(function(idx) {
      vm.staff.staff_account_transfer_attributes["account_middle_name_" + idx] = middle_names[idx - 1];
      vm.staff.staff_account_transfer_attributes["account_middle_name_kana_" + idx] = middle_name_kanas[idx - 1];
    });
    vm.currentAccMiddleNameLength = middle_names.length;
    vm.currentAccMiddleNameKanaLength = middle_name_kanas.length;

    if (!_.isEmpty(middle_names) || !_.isEmpty(middle_name_kanas)) {
      vm.currentAccMiddleNameLength = vm.currentAccMiddleNameLength == 0 ? 1 : vm.currentAccMiddleNameLength;
      vm.currentAccMiddleNameKanaLength = vm.currentAccMiddleNameKanaLength == 0 ? 1 : vm.currentAccMiddleNameKanaLength;
      if (_.isEmpty(middle_name_kanas)) vm.staff.staff_account_transfer_attributes["account_middle_name_kana_1"] = "";
      vm.isEnableAccountMiddleName = true;
    }
  }

  vm.$scope.$watch("vm.staff.residence_expiration_date", function() {
    if (vm.staff.nationality == vm.japan) return;
    staffEntryFrmCommonService.watchResidenceExpirationDate(vm, "staff");
  });

  vm.$scope.$watchGroup(["vm.staff.emergency_tel"], function() {
    vm.checkTelNumber();
  });

  vm.isResidenseExpDateRequired = function(){
    return (vm.staff.residence_status != NOT_REQUIRED_RESIDENSE_STATUS_ID);
  };

  vm.checkResidenseExpDateRequired = function() {
    if (vm.isResidenseExpDateRequired()){
      if($residenceExpDateLabel.hasClass('d-none')){
        $residenceExpDateLabel.removeClass('d-none');
      }
    }else{
      if(!$residenceExpDateLabel.hasClass('d-none')){
        $residenceExpDateLabel.addClass('d-none');
      }
    }
  };

  vm.checkTelNumber = function() {
    if (_.isEmpty(vm.staff.emergency_tel)) { return; }
    if (vm.staff.tel == vm.staff.emergency_tel) {
      vm.entryForm["staff[emergency_tel]"].$error = {"same-tel-number": true};
      vm.entryForm["staff[emergency_tel]"].$valid = false;
    } else {
      vm.entryForm["staff[emergency_tel]"].$error = {};
      vm.entryForm["staff[emergency_tel]"].$valid = true;
    }
  };

  vm.requireResidencePermission = function() {
    return vm.currentStep() == 1 && vm.staff.nationality != vm.japan && vm.staff.residence_permission== vm.has_residence_permission;
  };

  function initOverview() {
    // TODO(Phuong): When modify registered_profiles, both cases use static steps;
    vm.overviewSteps = staffEntryFrmCommonService.initOverViewSteps();
    vm.overviewStaticSteps = stepsOverviewService.initNonExpSteps(2);
  };

  function disableEnter(e, step) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 13) {
      e.preventDefault();
      $("input").blur();
      switch (step) {
        case STEPS[1]:
          vm.validateStep1();
          break;
        case STEPS[2]:
          vm.validateStep2();
          break;
        case STEPS[3]:
          vm.validateStep3();
          break;
        case STEPS[4]:
          vm.validateStep4();
      }
    }
  };

  _.forEach(STEPS, function(step) {
    $("#step-form--" + step).keypress(function(e) {
      disableEnter(e, step);
    })
  });
}
