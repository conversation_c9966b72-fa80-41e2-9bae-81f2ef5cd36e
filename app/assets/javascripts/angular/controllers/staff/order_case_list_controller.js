"use strict";

angular.module("staffApp")
  .controller("OrderCaseListController", OrderCaseListController);
OrderCaseListController.$inject = ["$location", "$scope", "orderCaseListService", "stationService",
  "searchConditionFunction", "orderCaseService", "$window"];

function OrderCaseListController($location, $scope, orderCaseListService, stationService,
  searchConditionFunction, orderCaseService, $window) {
  var vm = this;
  vm.params = $location.search();
  var SEARCH_CONDITION_TYPE = "staff_order_case_search_conditions";
  var END_OF_DAY = "23:59";
  var FIELD_NOT_CLEAR = ["start_date", "order_key", "order_type", "page", "per_page"];
  var BACK_BUTTON_NAVIGATION_NUMBER = 2;
  var FROM_OUTSIDE_PARAMS = ["crew-matchbox", "crew-matchbox/", "crew-lawson", "crew-workz"];
  vm.isFromOutSide = _.includes(FROM_OUTSIDE_PARAMS, vm.params.source);
  vm.selectedPrefecturesName = "";
  var $modalBackDrop = $("#modal-backdrop");
  vm.init = function(prefectures, staff_number, sign_in_count, staff_signed_in) {
    vm.staff_signed_in = staff_signed_in;
    if (!vm.staff_signed_in) {
      localStorage.removeItem("current_visited_job");
    }
    vm.staff_number = staff_number;
    vm.sign_in_count = sign_in_count;
    vm.initWelcomeModal();
    vm.shouldResetPaging = window.performance.navigation.type != BACK_BUTTON_NAVIGATION_NUMBER;
    var existLastCondition = false;
    vm.lastVisitPage = vm.params.page;
    vm.params.page = 1;
    vm.params.per_page = 20;
    vm.stationParam = {};
    initWorkingHour();
    vm.orderKeys = [
      {key: "created_datetime", type: "desc", label: I18n.t("staff.order_cases.order_keys.deadline_apply_date")},
      {key: "case_start", type: "asc", label: I18n.t("staff.order_cases.order_keys.case_started_at")},
      {key: "staff_apply_count", type: "asc", label: I18n.t("staff.order_cases.order_keys.staff_apply_count")},
    ];
    vm.prefectures = prefectures;
    if (vm.params.ignore_condition) {
      if (vm.params.is_open) {
        vm.params.is_open = true;
      }
      vm.disableFreeWord = false;
      vm.free_word = vm.params.free_word;
      vm.params.free_word_selected = true;
      initSort(false);
      vm.disableStation = true;
      vm.params.station_selected = false;
      vm.params.designated_period_selected = false;
      vm.disableDesignatedPeriod = true;
      vm.refresh(false, false, true);
    } else if (vm.params.batch_search) {
      vm.params.is_open = true;
      vm.disableFreeWord = true;
      vm.params.free_word_selected = false;
      initSort(false);
      vm.disableStation = true;
      vm.params.station_selected = false;
      if (vm.params.start_time) {
        vm.start_time = vm.params.start_time.timeNumberToHalfWidth();
        vm.end_time = END_OF_DAY;
        vm.params.designated_period_selected = true;
      } else {
        vm.disableDesignatedPeriod = true;
      }
      vm.params.search_by_prefecture = true;
      initPrefectures();
      vm.refresh(false, false, true);
    } else if (vm.params.ignore_last_search) {
      vm.storedDataStartDateFrom = vm.params.start_date_from;
      vm.storedDataStartDateTo = vm.params.start_date_to;
      vm.params.is_open = true;
      vm.params.search_by_prefecture = true;
      initSort(false);
      initPrefectures();
      vm.refresh(false, false, true);
    } else {
      if (!vm.shouldResetPaging) {
        vm.storedDataStartDateFrom = vm.params.start_date_from;
        vm.storedDataStartDateTo = vm.params.start_date_to;
      }
      existLastCondition = false;
      if (vm.staff_signed_in) {
        searchConditionFunction.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE})
          .then(function(res) {
          if (!vm.params.batch_search && res.data.status) {
            vm.params = JSON.parse(res.data.last_conditions);
            existLastCondition = true;
          }
          vm.disableFreeWord = !vm.params.free_word_selected;
          vm.disableStation = !vm.params.station_selected;
          vm.disableDesignatedPeriod = !vm.params.designated_period_selected;
          initSort(existLastCondition);
          initWorkingTimeCondition(existLastCondition);
          initFreeWord();
          initStartDate(existLastCondition);
          initPrefectures();
          vm.refresh(false, true, true);
        });
      } else {
        existLastCondition = false;
        vm.disableFreeWord = !vm.params.free_word_selected;
        vm.disableStation = !vm.params.station_selected;
        vm.disableDesignatedPeriod = !vm.params.designated_period_selected;
        initSort(false);
        initWorkingTimeCondition(false);
        initFreeWord();
        initStartDate(false);
        initPrefectures();
        vm.refresh(false, false, true);
      }
    }
  };

  vm.initDataDate = function() {
    if (moment(vm.storedDataStartDateFrom, FULL_DATE_FORMAT, true).isValid()) {
      vm.params.start_date_from = vm.storedDataStartDateFrom;
    } else {
      if (_.isEmpty(vm.storedDataStartDateFrom) ||
        moment(vm.storedDataStartDateFrom, DATE_FORMAT_MOBILE, true).isBefore(moment())) {
        vm.storedDataStartDateFrom = moment.parseZone(Date(Date.now())).format(FULL_DATE_FORMAT);
        vm.params.start_date_from = vm.storedDataStartDateFrom;
      }
      if (moment(vm.storedDataStartDateFrom, DATE_FORMAT_MOBILE, true).isValid()) {
        vm.storedDataStartDateFrom = vm.params.start_date_from;
      }
    }
    if (moment(vm.storedDataStartDateTo, FULL_DATE_FORMAT, true).isValid()) {
      vm.params.start_date_to = vm.storedDataStartDateTo;
    } else {
      if (vm.storedDataStartDateTo === "") {
        vm.params.start_date_to = vm.storedDataStartDateTo;
      }
      if (moment(vm.storedDataStartDateTo, DATE_FORMAT_MOBILE, true).isValid()) {
        vm.storedDataStartDateTo = vm.params.start_date_to;
      }
    }
  };

  vm.displayDataDate = function() {
    if (screen.width < MOBILE_MAX_WIDTH) {
      if (vm.storedDataStartDateFrom) {
        $("div#st-work-filter-datepicker-start").data("datetimepicker").date(vm.storedDataStartDateFrom);
        vm.storedDataStartDateFrom = vm.params.start_date_from.slice(5, 10);
      }
      if (vm.storedDataStartDateTo) {
        $("div#st-work-filter-datepicker-end").data("datetimepicker").date(vm.storedDataStartDateTo);
        vm.storedDataStartDateTo = vm.params.start_date_to.slice(5, 10);
      }
    }
  };

  vm.checkValidDate = function() {
    if (screen.width >= MOBILE_MAX_WIDTH) {
      if (vm.storedDataStartDateFrom && !moment(vm.storedDataStartDateFrom, FULL_DATE_FORMAT, true).isValid()) {
        vm.storedDataStartDateFrom = moment.parseZone(Date(Date.now())).format(FULL_DATE_FORMAT);
      }
      if (vm.storedDataStartDateTo && !moment(vm.storedDataStartDateTo, FULL_DATE_FORMAT, true).isValid()) {
        vm.storedDataStartDateTo = moment.parseZone(Date(Date.now())).format(FULL_DATE_FORMAT);
      }
      if (_.isEmpty(vm.storedDataStartDateFrom) || moment(vm.storedDataStartDateFrom).isBefore(moment())) {
        vm.storedDataStartDateFrom = moment.parseZone(Date(Date.now())).format(FULL_DATE_FORMAT);
      }
      if (moment(vm.storedDataStartDateFrom).isAfter(vm.storedDataStartDateTo, "day")) {
        vm.changeStartDateTo();
      }
    } else {
      vm.initDataDate();
      if (moment(vm.params.start_date_from).isAfter(vm.params.start_date_to, "day")) {
        vm.changeStartDateTo();
      }
    }
  };

  vm.changeStartDateTo = function() {
    vm.storedDataStartDateTo = vm.storedDataStartDateFrom;
  };

  vm.refresh = function(isUseStaffCondition, isSave, isInit, isResetPage) {
    vm.initDataDate();
    formatBeforeSendParams(isInit);
    vm.params = _.omit(vm.params, "is_tomorrow");
    var params = isUseStaffCondition ? formatPerPageParam(vm.currentParams) : vm.params;
    if (!vm.shouldResetPaging) {
      params.page = vm.lastVisitPage;
    }
    if (isResetPage) {
      params.page = 1;
    }
    orderCaseListService.getOrderCases({search: params}).then(function(res) {
      if (res.data.status) {
        vm.order_cases = res.data.order_cases;
        vm["total_items"] = res.data.total_items;
        angular.extend(vm, res.data);
        vm.hasRecord = Boolean(vm.order_cases.length);
      } else {
        vm.order_cases = [];
        vm.hasRecord = false;
        vm["total_items"] = 0;
      }
      if (res.data.status && isSave && !vm.params.ignore_condition && vm.shouldResetPaging && vm.staff_signed_in) {
        searchConditionFunction.createSearchCondition({search: vm.params, search_condition: SEARCH_CONDITION_TYPE});
      };
      vm.shouldResetPaging = true;
      vm.sort(params.order_key, params.order_type);
      $location.search(params).replace();
      vm.currentParams = angular.copy(params);
    });
    vm.displayDataDate();
    vm.scrollToResult();
  };

  vm.initMap = function() {
    if (vm.order_cases.length > 0) {
      initMap(vm.order_cases);
    } else if ($(".st-work-result__map").hasClass("show-map")) {
      if ($(".js-filter-mobile").css("display") !== "none") {
        $(".js-filter-mobile").hide();
        $(".js-map-mobile").show();
      };
      $(".js-map-switch").click();
    };
  };

  vm.initWelcomeModal = function() {
    if (vm.staff_signed_in) {
      return;
    }
    var isRead = JSON.parse(localStorage.getItem("read_welcome_modal"));
    if (!isRead){
      $("#modal-welcome-workz").show();
      $modalBackDrop.addClass("modal-backdrop fade show");
    }
  }

  vm.closeWelcomeModal = function() {
    $("#modal-welcome-workz").hide();
    localStorage.setItem("read_welcome_modal", true);
    $modalBackDrop.removeClass("modal-backdrop fade show");
  }

  function formatPerPageParam(currentParams) {
    currentParams.per_page = vm.params.per_page;
    return currentParams;
  }

  vm.keepOrderCase = function(orderCaseId) {
    if (!vm.staff_signed_in) {
      location.href = "/" + I18n.locale + "/login";
      return;
    }
    orderCaseListService.createStaffKeepOrderCase({order_case_id: orderCaseId});
    if (_.includes(vm.keep_order_case_ids, orderCaseId)) {
      vm.keep_order_case_ids.splice(vm.keep_order_case_ids.indexOf(orderCaseId), 1);
    } else {
      vm.keep_order_case_ids.push(orderCaseId);
    };
  };

  vm.moveToDetail = function(orderCase) {
    $window.location.href = "/" + I18n.locale + "/order_cases/" + orderCase.id;
  };

  vm.isKeeped = function(orderCaseId) {
    return _.includes(vm.keep_order_case_ids, orderCaseId);
  };

  vm.disableOrderCase = function(orderCase) {
    return orderCase.timeout_apply_before_2_hour || _.includes(vm.rejected_order_case_ids, orderCase.id) ||
      vm.registrationStaffApplyTimeout(orderCase);
  };

  vm.registrationStaffApplyTimeout = function(orderCase) {
    if (vm.no_limit_registration_apply) { return false; }
    var workingStart = moment(orderCase.started_at.substring(0, 10) + " " + orderCase.working_time.split("~")[0]);
    var currentTime = moment(moment().format(DATE_HOUR_MINUTE_FORMAT));
    var difference = moment.duration(workingStart.diff(currentTime)).asHours();
    return difference < 10; 
  };

  function formatBeforeSendParams(isInit) {
    formatFreeWordParam();
    formatStationParam(isInit);
    formatWorkingTimeParams();
    formatPrefecturesParams();
  }

  vm.toggleFreeWord = function() {
    vm.disableFreeWord = !vm.disableFreeWord;
    formatFreeWordParam();
  }

  function formatFreeWordParam() {
    if (!vm.disableFreeWord && vm.free_word) {
      vm.params.free_word = vm.free_word;
    } else {
      delete vm.params.free_word;
    }
  }

  function initFreeWord() {
    if (!vm.disableFreeWord && vm.params.free_word) {
      vm.free_word = vm.params.free_word;
    }
  }

  vm.getFeeFlag = function(order_case) {
    return orderCaseListService.getAdditionFeeByWages(order_case, vm.wages_fees);
  };

  vm.toggleStation = function() {
    vm.disableStation = !vm.disableStation;
  }

  function getWorkingHourSelected() {
    return _.filter(vm.workingHours, function(working) {return !!working.selected;});
  }

  function formatWorkingTimeParams() {
    if (!vm.disableDesignatedPeriod) {
      vm.start_time = !!vm.start_time ? vm.start_time.timeNumberToHalfWidth() : vm.start_time;
      vm.end_time = !!vm.end_time ? vm.end_time.timeNumberToHalfWidth() : vm.end_time;
      vm.params.start_time = vm.start_time;
      vm.params.end_time = vm.end_time;
    } else {
      delete vm.params.start_time;
      delete vm.params.end_time;
    }

    vm.params.working_time = _.map(getWorkingHourSelected(), function(working) {
      return working.value;
    }).join(",");

    if (_.isEqual(vm.params.working_time, "") || _.isEqual(vm.params.working_time, "-") ||
      _.isEqual(vm.params.working_time, ",")) {
      delete vm.params.working_time;
    }
  }

  vm.toggleChangeDesignatedPeriod = function() {
    vm.disableDesignatedPeriod = !vm.disableDesignatedPeriod;
  }

  vm.clearSearchCondition = function() {
    var paramsBeforeClear = vm.params;
    vm.params = {};
    vm.disableFreeWord = vm.params.free_word_selected;
    vm.disableStation = !vm.params.station_selected;
    vm.disableDesignatedPeriod = vm.params.designated_period_selected;
    vm.toggleFreeWord();
    vm.free_word = "";
    initWorkingHour();
    vm.toggleChangeDesignatedPeriod();
    formatWorkingTimeParams();
    initWorkingTimeCondition(true);
    _.forEach(FIELD_NOT_CLEAR, function(field) {
      vm.params[field] = paramsBeforeClear[field];
    });
  }

  vm.chooseStation = function(name, id) {
    vm.station = {name: name, id: id};
    vm.stationParam.name = name;
    vm.params.station_ids = id;
  }

  function formatStationParam(isInit) {
    if (!isInit) {
      if (_.isEmpty(vm.station)) {
        delete vm.params.station_ids;
      } else {
        vm.params.station_ids = vm.station.id;
      }
    }
    if(vm.disableStation) {
      delete vm.params.station_ids;
    }
  }

  vm.searchStations = function() {
    stationService.getStationByName(vm.stationParam).then(function(res) {
      vm.stationsByName = res.data.stations;
      vm.station = _.find(vm.stationsByName, function(element){
        return element.name == vm.stationParam.name;
      });
    });
  };

  vm.initAfterClearTime = function(period_time) {
    if (_.isEmpty(vm[period_time])) {
      vm[period_time] = moment().format(TIME_PICKER_FORMAT);
    }
  };

  vm.initAfterClearStartDateFrom = function() {
    if (_.isEmpty(vm.storedDataStartDateFrom)) {
      vm.storedDataStartDateFrom = moment().format(ORDER_DATEPICKER_FORMAT);
      vm.params.start_date_from = vm.storedDataStartDateFrom;
    }
  };

  vm.initAfterClearStartDateTo = function() {
    if (_.isEmpty(vm.storedDataStartDateTo)) {
      vm.storedDataStartDateTo = moment().format(ORDER_DATEPICKER_FORMAT);
      vm.params.start_date_to = vm.storedDataStartDateTo;
    }
  };

  function initWorkingTimeCondition(paramStatus) {
    if (paramStatus) {
      vm.start_time = !!vm.params.start_time ? vm.params.start_time.timeNumberToHalfWidth() : vm.params.start_time;
      vm.end_time = !!vm.params.end_time ? vm.params.end_time.timeNumberToHalfWidth() : vm.params.end_time;
      if (vm.params.working_time) {
        var workingTimes = vm.params.working_time === "-" ? [] : vm.params.working_time.split(",");
        workingTimes.forEach(function(time) {
          _.find(vm.workingHours, function(o) {return _.includes(o.value, time);}).selected = true;
        });
        vm.chkAllWorkingHour = getWorkingHourSelected().length === vm.workingHours.length;
      }
    }
  }

  function initStartDate(paramStatus) {
    if (!!vm.params.is_tomorrow) {
      vm.params.start_date_from = moment().add(1,'days').format(ORDER_DATEPICKER_FORMAT);
      vm.params.start_date_to = vm.params.start_date_from;
    }
    var isBatchSearch = !_.isEmpty(vm.params.start_date_from) && (vm.params.batch_search || paramStatus || vm.isFromOutSide)
    if (isBatchSearch && (moment().isAfter(vm.params.start_date_from, "day"))) {
      vm.params.start_date_from = moment().format(ORDER_DATEPICKER_FORMAT);
      vm.storedDataStartDateFrom = vm.params.start_date_from;
      vm.changeStartDateTo();
      return;
    }
    if (isBatchSearch) {
      vm.storedDataStartDateFrom = vm.params.start_date_from;
      if (vm.isFromOutSide && _.isEmpty(vm.params.start_date_to)) {
        vm.changeStartDateTo();
      } else {
        vm.storedDataStartDateTo = vm.params.start_date_to;
      }
      return;
    }
    if (vm.params.start_date_from != "") {
      if (!vm.params.is_tomorrow) {
        vm.params.start_date_from = moment().format(ORDER_DATEPICKER_FORMAT);
      } else {
        vm.params.start_date_from = moment().add(1,'days').format(ORDER_DATEPICKER_FORMAT);
      }
      vm.storedDataStartDateFrom = vm.params.start_date_from;
      vm.changeStartDateTo();
      return;
    }
    vm.storedDataStartDateFrom = vm.params.start_date_from;
    vm.storedDataStartDateTo = vm.params.start_date_to;
  }

  vm.sort = function(field, type, isRefresh) {
    vm.params.order_key = field;
    vm.params.order_type = type;
    if (isRefresh) {
      vm.refresh(false, true);
    }

    vm.order_key_name = _.filter(vm.orderKeys, function(element) {
      return element.key == vm.params.order_key;
    })[0].label;
  };

  function initSort(paramStatus) {
    var shouldReFilter = false;
    if (!paramStatus) {
      vm.params.order_key = "created_datetime";
      vm.params.order_type = "desc";
      shouldReFilter = true;
    }
    if (!!vm.params.order_key) {
      vm.params.order_key = "created_datetime";
      shouldReFilter = true;
    }
    if (!!vm.params.order_type) {
      vm.params.order_type = "desc";
      shouldReFilter = true;
    }
    if (shouldReFilter) {
       vm.order_key_name = _.filter(vm.orderKeys, function(element) {
        return element.key == vm.params.order_key;
      })[0].label;
    }
  }

  function initWorkingHour() {
    vm.workingHours = [
      {name: "morning", value: "06:00-08:59", label: I18n.t("corporation.order_case.order_case_list_page.early_morning")},
      {name: "lunch", value: "09:00-16:59", label: I18n.t("corporation.order_case.order_case_list_page.noon")},
      {name: "evening", value: "17:00-21:59", label: I18n.t("corporation.order_case.order_case_list_page.evening")},
      {name: "night", value: "22:00-23:59,00:00-05:59", label: I18n.t("corporation.order_case.order_case_list_page.late_night")}
    ];
  }

  function initMap(orderCases) {
    var markers = [], activeInfoWindow, acitiveMarker, currentOrderCaseId;
    var iconMarkerUrl = angular.element("#ic-marker").data("ic-marker");
    var iconMarkerActiveUrl = angular.element("#ic-marker").data("ic-marker-active");
    var map = new google.maps.Map(document.getElementById("map"));
    _.forEach(orderCases, function(orderCase) {
      if (_.isNull(orderCase.location_latitude) || _.isNull(orderCase.location_longitude) || orderCase.timeout_apply_before_2_hour) {
        return;
      };
      var latLngObj = new google.maps.LatLng(orderCase.location_latitude, orderCase.location_longitude);
      var marker = new google.maps.Marker({
        position: latLngObj,
        map: map,
        animation: google.maps.Animation.DROP,
        icon: iconMarkerUrl
      });
      markers.push(marker)

      var contentString = "<div id='content'>" +
        "<div id='infoClose'><i class='fas fa-times'></i></div>" +
        "<div id='siteNotice'></div><div id='bodyContent'><div class='detail-plan'>" +
        "<div class='detail-plan__date'><i class='color--st-mint fas fa-calendar-alt'></i>" + orderCase.started_at_format_month_day + "</div>" +
        "<div class='detail-plan__time'><i class='color--st-mint far fa-clock'></i>" + orderCase.working_time + "</div> "+
        "<p>" + orderCase.site_name + "</p></div></div></div>";

      var infoWindow = new google.maps.InfoWindow({
        content: contentString,
        maxWidth: 200
      });

      google.maps.event.addListener(marker, "click", function() {
        if (activeInfoWindow) {
          activeInfoWindow.close();
          acitiveMarker.setIcon(iconMarkerUrl);
        };
        marker.setIcon(iconMarkerActiveUrl);
        infoWindow.open(map, marker);
        activeInfoWindow = infoWindow;
        acitiveMarker = marker;
        currentOrderCaseId = orderCase.id;
        $(".gm-style-iw").each(function(){
          $(this).prev("div").remove();
          $(this).next("div").remove();
        });
      });

      google.maps.event.addListener(map, "click", function() {
        infoWindow.close();
        marker.setIcon(iconMarkerUrl);
      });

      $("body").delegate("#infoClose", "click", function() {
        infoWindow.close();
        marker.setIcon(iconMarkerUrl);
      });

      $("body").delegate("#bodyContent", "click", function() {
        if (currentOrderCaseId) {
          window.location.href = "/" + I18n.locale + "/order_cases/" + currentOrderCaseId;
        };
      });
    });
    setBoundMap(map, markers);
  };

  function setBoundMap(map, markers) {
    var MAX_ROOM = 15;
    var bounds = new google.maps.LatLngBounds();
    for (var i = 0; i < markers.length; i++) {
      bounds.extend(markers[i].getPosition());
    };
    map.setCenter(bounds.getCenter());
    map.fitBounds(bounds);
    google.maps.event.addListenerOnce(map, "bounds_changed", function(event) {
      if (this.getZoom() > MAX_ROOM) {
        this.setZoom(MAX_ROOM);
      };
    });
  };

  function checkChangeLocationInput() {
    var selectedLocations = vm.locations.filter(function(location) {
      return !!location.selected;
    });
    if (vm.chkAllLocationState && selectedLocations.length === vm.locations.length) {
      vm.locationInp = CHECK_ALL_TXT;
    } else {
      vm.checkAllLocation = vm.chkAllLocationState = false;
      vm.locationInp = selectedLocations.map(function(location) {
        return location.name;
      }).join(COMMA);
    }
    $("#location-input").val(vm.locationInp).trigger("input");
  }

  vm.togglePrefectureChkbox = function(prefecture) {
    prefecture.selected = !prefecture.selected;
    if (prefecture.selected) {
      vm.prefectureIdsArr.push(prefecture.id);
      vm.isCheckAllPrefectures = vm.prefectureIdsArr.length == vm.prefectures.length;
    } else {
      _.pull(vm.prefectureIdsArr, prefecture.id);
      vm.isCheckAllPrefectures = false;
    }
    setSelectedPrefecturesName();
    formatPrefecturesParams();
    vm.changeSearchByPrefecture();
  };

  vm.isSelectedPrefecture = function(prefectureId) {
    return _.includes(vm.prefectureIdsArr, prefectureId);
  }

  function formatPrefecturesParams() {
    if (_.isEmpty(vm.prefectureIdsArr)) {
      vm.params.location_prefecture_id = "";
    } else {
      vm.params.location_prefecture_id = vm.prefectureIdsArr.join();
    };
  }

  function setSelectedPrefecturesName() {
    if (_.isEmpty(vm.prefectureIdsArr)) {
      vm.selectedPrefecturesName = "";
    } else {
      vm.selectedPrefecturesName = _.map(vm.prefectureIdsArr, function(prefectureId) {
        var prefecture = _.find(vm.prefectures, function(prefecture) {
          return prefectureId == prefecture.id;
        });
        return prefecture.name;
      }).join("、");
    }
    $("#select-prefectures").val(vm.selectedPrefecturesName).trigger("input");
  }

  function initPrefectures() {
    if (!_.isEmpty(vm.params.location_prefecture_id)) {
      vm.prefectureIdsArr = vm.params.location_prefecture_id.split(",").map(Number);
      vm.selectedPrefecturesName = _.forEach(vm.prefectureIdsArr, function(prefectureId) {
        var prefecture = _.find(vm.prefectures, function(prefecture) {
          return prefectureId == prefecture.id;
        });
        prefecture.selected = true;
      }).join("、");
    } else {
      vm.prefectureIdsArr = [];
    }
    vm.isCheckAllPrefectures = vm.prefectureIdsArr.length == vm.prefectures.length;
    setSelectedPrefecturesName();
  }

  vm.changeSearchByPrefecture = function() {
    if (vm.params.search_by_prefecture && !_.isEmpty(vm.params.location_prefecture_id)) {
      vm.stationParam.prefecture_id = vm.params.location_prefecture_id;
    } else {
      delete vm.stationParam.prefecture_id
    }
  }

  vm.toggleCheckAllPrefectureChkbox = function() {
    vm.isCheckAllPrefectures = !vm.isCheckAllPrefectures;
    setPrefectureChkbox();
    formatPrefecturesParams();
    vm.changeSearchByPrefecture();
  }

  function setPrefectureChkbox() {
    _.forEach(vm.prefectures, function(prefecture) {
      if (vm.isCheckAllPrefectures) {
        prefecture.selected = true;
        if (!_.includes(vm.prefectureIdsArr)) {
          vm.prefectureIdsArr.push(prefecture.id);
        }
      } else {
        prefecture.selected = false;
        _.pull(vm.prefectureIdsArr, prefecture.id);
      }
    });
    setSelectedPrefecturesName();
  }

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $("head").offset().top
    }, 1000);
  };

  vm.bgImageForLocation = function(locationType, thumbnailPath) {
    return orderCaseService.bgImageForLocation(locationType, thumbnailPath);
  };

  vm.bgForNotLawson = function(locationType, locationImage, thumbnailPath) {
    return orderCaseService.bgForNotLawson(locationType, locationImage, thumbnailPath);
  };

  String.prototype.timeNumberToHalfWidth = function() {
    return this.replace(/[０-９：]/g, function(s) {
      return String.fromCharCode(s.charCodeAt(0) - 0xFEE0)
    });
  };
}
