"use strict";

angular.module("staffApp")
  .controller("workHistoryListController", workHistoryListController);

workHistoryListController.$inject = ["$location", "$scope", "workListService"];

function workHistoryListController($location, $scope, workListService) {
  var vm = this;
  var DATA_HOLDER_IDS = ["year_options", "current_year", "current_month"];
  var LOCATION_TYPE = {
    not_lawson: "not_lawson",
    lawson: "ローソン",
    nature_lawson: "ナチュラルローソン",
    lawson_store_100: "ローソンストア100"
  };
  var BG_CLASS = {
    not_lawson: "bg-not-lawson",
    nature_lawson: "bg-nature-lawson-work-list",
    lawson_store_100: "bg-lawson-store-work-list"
  };

  vm.$scope = $scope;
  vm.params = $location.search();
  vm.params.per_page = 20;
  vm.params.page = vm.params.page ? vm.params.page : 1;
  vm.init = false;
  vm.total_record = 0;
  vm.total_hour = 0;
  vm.total_salary = 0;
  vm.total_payroll_record = 0;
  vm.hasRecord = false;

  initData();

  vm.$scope.$watch("vm.selected_year", function() {
    if (vm.selected_year) {
      vm.month_options = getMonthOptions(vm.selected_year);
      if (vm.selected_month == vm.month_options[0].id) loadDataWithNewTimeRange();
      if (!vm.init) {
        vm.selected_month = vm.params.month || vm.current_month;
      } else {
        vm.selected_month = vm.month_options[0].id;
      }
      vm.init = true;
    }
  });

  vm.$scope.$watch("vm.selected_month", function() {
    if (vm.selected_month) loadDataWithNewTimeRange();
  });

  function loadDataWithNewTimeRange() {
    var selectedMonth = vm.month_options.filter(function(item) {
      return item.id == vm.selected_month;
    })[0];
    vm.params.start_time = selectedMonth.start_time;
    vm.params.end_time = selectedMonth.end_time;
    vm.loadData();
    formatWorkAchivementModalTimeLbl();
  }

  vm.loadData = function() {
    workListService.loadDataWorkHistoryList(vm.params).then(function(res) {
      var paramSearch = paramsSearch();
      $location.search(paramSearch).replace();
      angular.extend(vm, res.data);
      formatWorkAchivement();
      vm.hasRecord = vm.total_record != 0;
    });
  };

  vm.getFeeFlag = function(order_case_id) {
    var order_case_by_staff_fee = _.find(vm.order_case_by_staff_fees, function(order_case_by_staff_fee) {
      return order_case_by_staff_fee.order_case_id == order_case_id;
    });
    var fee_flags = [];
    if (!_.isUndefined(order_case_by_staff_fee)) {
      if (!_.isUndefined(order_case_by_staff_fee.fee)) {
        fee_flags = order_case_by_staff_fee.fee;
      }
    }
    return fee_flags;
  };

  vm.showWorkAchiementModal = function() {
    angular.element(".work-achievement-modal").modal("show");
  };

  vm.moveToDetail = function(orderCaseId) {
    location.href = "/" + I18n.locale + "/order_cases/" + orderCaseId;
  };

  function initData() {
    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });
    vm.selected_year = angular.copy(vm.params.year) || angular.copy(vm.current_year);
  }

  function getMonthOptions(yearId) {
    return vm.year_options.filter(function(item) {
      return item.id == yearId;
    })[0].months;
  }

  function formatWorkAchivement() {
    if (_.isNull(vm.total_time)) vm.total_time = 0;
    vm.total_hour = formatWorkTime(vm.total_time);
    vm.total_salary = Math.round(vm.total_salary).toLocaleString();
  }

  function formatWorkAchivementModalTimeLbl() {
    var monthTxt = vm.month_options.filter(function(month) {
      return month.id == vm.selected_month;
    })[0].name;
    var yearTxt = vm.year_options.filter(function(year) {
      return year.id == vm.selected_year;
    })[0].name;
    vm.workAchivementModalTimeLbl = yearTxt + " " + monthTxt;
  }

  function paramsSearch() {
    return {year: vm.selected_year, month: vm.selected_month, page: vm.params.page};
  }

  function formatWorkTime(timeInMinutes){
    var workHour = Math.floor(timeInMinutes / 60);
    var workMinute = timeInMinutes % 60;
    if (workMinute < 10) workMinute = "0".concat(workMinute);
    return (workHour + ":" + workMinute);
  };

  vm.bgImageForLocation = function(locationType, thumbnailPath) {
    if ((!!thumbnailPath && thumbnailPath != "") ||  locationType === LOCATION_TYPE.not_lawson){
      return BG_CLASS.not_lawson;
    }
    if (locationType === LOCATION_TYPE.lawson_store_100) {
      return BG_CLASS.lawson_store_100;
    }
    if (locationType === LOCATION_TYPE.nature_lawson) {
      return BG_CLASS.nature_lawson;
    }
  };

  vm.showButton = function(arrangement) {
    var orderCase = arrangement.order_case;
    var condition = arrangement.status_arrangement || orderCase.status_id;
    if(condition == "not_input" || condition == "pending_approve_and_staff_confirming" || condition == "prepared_going_work"){
      return true;
    };
    return false;
  };

  vm.showButtonText = function(arrangement) {
    var orderCase = arrangement.order_case;
    var condition = arrangement.status_arrangement || orderCase.status_id;
    if(condition == "not_input") {
      return I18n.t("staff.work_list.button.not_input");
    }else if(condition == "pending_approve_and_staff_confirming") {
      return I18n.t("staff.work_list.button.pending_approve");
    }else if(condition == "prepared_going_work") {
      return I18n.t("staff.work_list.button.prepared_going_work");
    }
  };
}
