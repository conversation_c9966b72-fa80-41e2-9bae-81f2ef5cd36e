"use strict";

angular.module("staffApp")
  .controller("HistoryWorkLocationListController", HistoryWorkLocationListController);

HistoryWorkLocationListController.$inject = ["$location", "orderCaseService", "orderCaseListService"];

function HistoryWorkLocationListController($location, orderCaseService, orderCaseListService) {
  var vm = this;
  vm.params = $location.search();
  var HAKEN_ORDER_CASE_SEGMENT = "haken";
  var CONTRACT_ORDER_CASE_SEGMENT = "contract";
  var REGULAR_ORDER_CASE_SEGMENT = "regular_order";

  vm.init = function() {
    vm.params.page = 1;
    vm.params.per_page = 20;
    vm.hasRecord = true;
    vm.orderKeys = [
      {key: "case_start", type: "asc", label: I18n.t("staff.history_work_location.order_keys.case_start")},
      {key: "created_datetime", type: "desc", label: I18n.t("staff.history_work_location.order_keys.created_datetime")},
      {key: "case_start", type: "desc", label: I18n.t("staff.history_work_location.order_keys.work_day")}
    ];
    if (_.isUndefined(vm.params.order_key)) {
      vm.params.order_key = vm.orderKeys[0].key;
      vm.params.order_type = vm.orderKeys[0].type;
    }
    vm.order_key_name = _.find(vm.orderKeys, function(element) {
      return element.key == vm.params.order_key && element.type == vm.params.order_type;
    }).label;
    vm.refresh();
  }

  vm.keepOrderCase = function(orderCaseId) {
    orderCaseListService.createStaffKeepOrderCase({order_case_id: orderCaseId});
    if (_.includes(vm.keep_order_case_ids, orderCaseId)) {
      vm.keep_order_case_ids.splice(vm.keep_order_case_ids.indexOf(orderCaseId), 1);
    } else {
      vm.keep_order_case_ids.push(orderCaseId);
    };
  };

  vm.moveToDetail = function(orderCase) {
    location.href = "/" + I18n.locale + "/order_cases/" + orderCase.id;
  };

  vm.refresh = function() {
    orderCaseService.getHistoryWorkLocations({search: vm.params}).then(function(res) {
      vm.order_cases = res.data.order_cases;
      vm.locationOrderCases = _.groupBy(vm.order_cases, "location_id");
      vm.recentLocations = res.data.recent_locations;
      vm["total_items"] = res.data.total_items;
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.recentLocations);
      $location.search(vm.params).replace();
    });
  };

  vm.isKeeped = function(orderCaseId) {
    return _.includes(vm.keep_order_case_ids, orderCaseId);
  };

  vm.disableOrderCase = function(orderCase) {
    return orderCase.timeout_apply_before_2_hour || _.includes(vm.rejected_order_case_ids, orderCase.id);
  }

  vm.getFeeFlag = function(order_case) {
    if (order_case.segment_id === REGULAR_ORDER_CASE_SEGMENT) {
      return feeFlagRegular(vm.order_case_wages[order_case.id]);
    }

    if (order_case.segment_id === HAKEN_ORDER_CASE_SEGMENT || order_case.segment_id === CONTRACT_ORDER_CASE_SEGMENT) {
      return feeFlagHaken(vm.order_case_wages[order_case.id]);
    }
  };

  vm.sort = function(field, type) {
    var $owl = $(".owl-carousel-custom");
    $owl.trigger("destroy.owl.carousel");
    $owl.html($owl.find(".owl-stage-outer").html()).removeClass("owl-loaded");
    vm.params.order_key = field;
    vm.params.order_type = type;
    vm.order_key_name = _.find(vm.orderKeys, function(element) {
      return element.key == vm.params.order_key && element.type == vm.params.order_type;
    }).label;
    vm.refresh();
  };

  vm.submitLikeLocation = function(locationId) {
    orderCaseService.submitLikeLocation({location_id: locationId}).then(function(res) {
      if (res.data.status) {
        vm.liked_location_ids.push(locationId);
      } else {
        _.remove(vm.liked_location_ids, function(location_id) {
          return location_id === locationId;
        });
      }
    });
  }

  vm.isLikedLocation = function(locationId) {
    return _.includes(vm.liked_location_ids, locationId);
  }

  vm.viewAll = function(locationName, locationId) {
    var queryParams = {
      free_word_selected: true,
      ignore_condition: true,
      location_id: locationId,
      is_open: true,
      search_only_location: true,
      free_word: locationName
    }

    var queryString = _.map(queryParams, function(value, key) {
        return key + "=" + value;
    }).join("&");
    var url = "/" + I18n.locale + "/order_cases/?" + queryString;
    if (/Mobile|webOS/i.test(navigator.userAgent)) {
      url = "/" + I18n.locale + "/locations/" + locationId + "/jobs";
    }
    return url;
  }

  vm.isSelectedOrderKey = function(key, type) {
    return vm.params.order_key == key && vm.params.order_type == type;
  }

  vm.bgImageForLocation = function(locationType, thumbnailPath) {
    return orderCaseService.bgImageForLocation(locationType, thumbnailPath);
  }

  vm.bgForNotLawson = function(locationType, locationImage, thumbnailPath) {
    return orderCaseService.bgForNotLawson(locationType, locationImage, thumbnailPath);
  };

  function feeFlagRegular(order_case_wage) {
    var data_fee = [];

    if (!_.isUndefined(order_case_wage.payment_urgent_price) && order_case_wage.payment_urgent_price > 0) {
      data_fee.push(orderCaseListService.feeFlagByKey(order_case_wage.payment_urgent_price));
    }

    return data_fee
  }

  function feeFlagHaken(order_case_wage) {
    var data_fee = [];

    var addition_fee = 0;
    if (!_.isUndefined(order_case_wage.other_addition_fee)) {
      addition_fee = order_case_wage.other_addition_fee;
    }
    if (!_.isUndefined(order_case_wage.total_unit_price_addition) && order_case_wage.total_unit_price_addition > 0) {
      data_fee.push(orderCaseListService.feeFlagByKey(order_case_wage.total_unit_price_addition));
    }
    if (order_case_wage.total_order_case_addition > 0 || addition_fee > 0) {
      var amount = order_case_wage.total_order_case_addition + addition_fee;
      data_fee.push(orderCaseListService.feeFlagByKey(amount, "offer_fee_by_order_rate"));
    }

    return data_fee;
  }
}
