"use strict";

angular.module("staffApp")
  .controller("StaffPaymentController", StaffPaymentController);

StaffPaymentController.$inject = ["staffPaymentService", "$scope", "$location", "$window", "toaster"];

function StaffPaymentController(staffPaymentService, $scope, $location, $window, toaster) {
  var vm = this;
  var LOCALE = I18n.locale;

  vm.isAbleToRequest = true, vm.chkboxAllState = false, vm.isRequested = false;
  vm.tempTotalPrice = 0;
  vm.sortBy = {request_payment: {}, history: []};
  vm.params = {};
  vm.params.history_per_page = 10;
  vm.params.history_page = vm.params.history_page ? vm.params.hitsory_page : 1;
  vm.toasterTimeout = 6200;
  vm.overviewTabs = [
    {
      id: 1,
      active: true,
      hashVal: "request_payment",
      name: I18n.t("staff.payments.tab_panel_title.request_payment")
    },
    {
      id: 2,
      active: false,
      hashVal: "history",
      name: I18n.t("staff.payments.tab_panel_title.history")
    }
  ];

  $scope.formatCurrency = function(price, optionalChracter) {
    var currency = I18n.t("common.currency");
    price = price.toLocaleString();
    if(_.isUndefined(optionalChracter)) return price + currency;
    else {
      return "<b class='font-weight-bold'>" + price + "</b>" + currency;
    }
  };

  vm.setTab = function(tabId) {
    var currentTab = vm.currentTab();
    if (!_.isUndefined(vm.overviewTabs[tabId])) {
      vm.overviewTabs[currentTab - 1].active = false;
      vm.overviewTabs[tabId].active = true;
    }
  };

  vm.currentTab = function() {
    return vm.overviewTabs.filter(function(step) {
      return step.active;
    })[0].id;
  };

  vm.refresh = function(options) {
    initTab();
    loadData(options);
  };

  vm.toggleAllArrangementChkboxes = function() {
    if(_.isUndefined(vm.arrangements)) return ;
    vm.arrangements.forEach(function(arrangement) {
      arrangement.selected = vm.chkboxAllState;
    });
    calcTempTotalPrice();
    checkStateForRequestBtn();
  };

  vm.checkStateForChkboxAll = function(arrangement) {
    if(vm.chkboxAllState && !arrangement.selected)
      vm.chkboxAllState = !vm.chkboxAllState;
    calcTempTotalPrice(arrangement);
    checkStateForRequestBtn();
  };

  vm.confirmRequest = function() {
    $(".disable-submit-btn").prop("disabled", true);
    var selectedArrangements = _.map(getSelectedArrangement(vm.arrangements), function(arrangement) {
      return arrangement.id;
    });
    var requestParams = [selectedArrangements, vm.sortBy, vm.params];
    $window.localStorage.setItem("requestParams", JSON.stringify(requestParams));

    location.href = "/" + LOCALE + "/payments/confirm_request?arrangement_ids=" + _.join(selectedArrangements, ",");
  };

  vm.submitRequest = function() {
    vm.isRequested = true;
    var arrangements = _.map(getSelectedArrangement(vm.arrangements), function(arrangement) { return arrangement.id });
    var request_params = {
      request_amount: vm.totalPrice,
      transfer_fee: vm.transferFee,
      usage_fee: vm.systemFee,
      arrangements: arrangements
    };

    staffPaymentService.submitRequest(request_params).then(function mySuccess(res){
      if(res.data.status) $("#sokubarai-modal-confirm").modal("hide");

      location.href = "/" + LOCALE + "/payments";
    });
  };

  vm.getHistoryItemClass = function(status) {
    if(_.isUndefined(status)) return;

    var historyItemClass = "", historyItemMsg = "";
    var isAutoOpenItem = false;
    switch(status) {
      case "in_progress":
        historyItemClass = "st-exchange__status--processing";
        break;
      case "trade_success":
        historyItemClass = "st-exchange__status--complete";
        isAutoOpenItem = false;
        break;
      case "trade_failed":
        historyItemClass = "st-exchange__status--processing processing-failed";
        break;
    }
    historyItemMsg = I18n.t("staff.payments.history.transfer_" + status);
    return [isAutoOpenItem, historyItemClass, historyItemMsg];
  };

  vm.sort = function(column, tab, index) {
    var history = getHistoryByIndex(index);
    var targetObj = (tab == "history") ? history : vm.sortBy[tab];

    if(_.isEmpty(targetObj) && tab == "history") {
      vm.sortBy[tab].push({id: index, column: column, reverse: "true"});
      targetObj = getHistoryByIndex(index);
    }

    targetObj.reverse = (column !== null && targetObj.column == column) ? !targetObj.reverse : "true";
    targetObj.column = column;
  };

  vm.sortByArray = function(index) {
    var history = getHistoryByIndex(index);

    if(_.isUndefined(history)) return ["work_achievement.working_started_at_format", "false"];
    else return [history.column, history.reverse];
  };

  vm.classForSortColumn = function(column, tab, index) {
    var history = getHistoryByIndex(index);
    var targetObj = (tab == "history") ? history : vm.sortBy[tab];

    if(_.isEmpty(targetObj)) return "fa-sort";
    else return getClassWhenSort(targetObj, column);
  };

  function loadData(options) {
    if (!_.isUndefined(options)) vm.params.pagination = true;
    vm.params.is_previous_month = $location.search().is_previous_month;
    staffPaymentService.loadDataArrangements(vm.params).then(function mySuccess(res){
      angular.extend(vm, res.data);
      extendPreviousData();
      if(!vm.params.pagination) $window.localStorage.removeItem("requestParams");
    });
  }

  function extendPreviousData() {
    var previousData = JSON.parse($window.localStorage.getItem("requestParams"));

    if(!_.isUndefined(previousData)) {
      var selectedIds = previousData[0];
      var previousSorts = previousData[1];
      var previousPage = previousData[2];

      if(!_.isUndefined(selectedIds)) initPreviousArrangements(selectedIds);
      if(!_.isUndefined(previousSorts)) initPreviousSort(previousSorts);
      if(!_.isUndefined(previousPage)) angular.extend(vm.params, previousPage);
    }
  }

  function initPreviousArrangements(selectedIds) {
    _.forEach(selectedIds, function(id) {
      var selectedArrangement = _.find(vm.arrangements, function(arrangement) { return arrangement.id == parseInt(id) });
      if(!_.isUndefined(selectedArrangement)) {
        selectedArrangement.selected = true;
        calcTempTotalPrice(selectedArrangement);
        checkStateForRequestBtn();
      }
    });

    if(selectedIds.length == vm.arrangements.length) {
      vm.chkboxAllState = true;
      calcTempTotalPrice();
      checkStateForRequestBtn();
    }
  }

  function initPreviousSort(previousSorts) {
    _.forEach(previousSorts, function(data, tabName) {
      if(!_.isEmpty(data)) {
        if(tabName == "history") {
          angular.extend(vm.sortBy[tabName], data);
        } else {
          vm.sortBy[tabName].reverse = data.reverse;
          vm.sortBy[tabName].column = data.column;
        }
      }
    });
  }

  function calcTempTotalPrice(arrangement) {
    if(_.isUndefined(arrangement)) calcTempTotalPriceForChkboxAll();
    else calcTempTotalPriceForChkbox(arrangement);

    vm.tempTotalPrice = _.round(vm.tempTotalPrice, 3);
  }

  function calcTempTotalPriceForChkboxAll() {
    if(!vm.chkboxAllState) vm.tempTotalPrice = 0;
    else {
      var payment_requests = _.map(vm.arrangements, function(arrangement) {
        return arrangement.arrange_payment.payment_request_calc;
      });
      vm.tempTotalPrice = getTotalPrice(payment_requests);
    }
  }

  function calcTempTotalPriceForChkbox(arrangement) {
    var price = parseFloat(formatPrice(arrangement.arrange_payment.payment_request_calc));

    if(arrangement.selected) vm.tempTotalPrice += price;
    else vm.tempTotalPrice -= price;
  }

  function formatPrice(price) {
    return _.replace(price, "円", "");
  }

  function checkStateForRequestBtn() {
    if(vm.chkboxAllState) vm.isAbleToRequest = false;
    else {
      vm.isAbleToRequest = !_.some(vm.arrangements, function(arrangement) {
        return arrangement.selected;
      });
    }
  }

  function getSelectedArrangement() {
    return _.filter(vm.arrangements, function(arrangement) { return arrangement.selected; });
  }

  function getTotalPrice(numbers) {
    var formatedNumbers = _.map(numbers, function(number) {
      return parseFloat(formatPrice(number));
    });

    return _.sum(formatedNumbers);
  }

  function initTab() {
    var hashTabVal = $location.hash();
    var tab = _.find(vm.overviewTabs, function(tab) { return tab.hashVal == hashTabVal });
    if(tab) vm.setTab(tab.id - 1);
  }

  function getHistoryByIndex(index) {
    return _.find(vm.sortBy["history"], function(history) {
      return history.id == index;
    });
  }

  function getClassWhenSort(object, column) {
    return {
      "fa-sort": !_.isEqual(object.column, column),
      "fa-sort-up": _.isEqual(object.column, column) && !object.reverse,
      "fa-sort-down": _.isEqual(object.column, column) && !!object.reverse
    };
  }

  vm.initPageConfirm = function() {
    vm.arrangeRequestIds = angular.element("#arrangment_request_ids").data("infos").toString();
  }

  vm.submitRequestPayments = function() {
    vm.isSubmitting = true;
    $("#spinner").removeClass("ng-hide");
    staffPaymentService.submitRequest({arrangement_ids: vm.arrangeRequestIds}).then(function mySuccess(res){
      $("#spinner").addClass("ng-hide");
      $("#modal-confirm-payments").modal("hide");
      if(res.data.status) {
        $("#sokubarai-modal-confirm").modal("show");
        vm.pageRedirectAfterRequest = res.data.redirect_page;
      } else {
        toaster.pop("error", "", res.data.message);
        vm.isSubmitting = false;
      }
    });
  }

  vm.redirectPage = function(url) {
    if (vm.isRedirecting) return;
    vm.isRedirecting = true;
    location.href = url;
  }
}
