"use strict";

angular.module("staffApp")
  .controller("accountTransferController", accountTransferController);
accountTransferController.$inject = ["$location", "$scope", "bankBranchService", "$window", "staffEntryFrmConsts", "staffEntryFrmCommonService"];

function accountTransferController($location, $scope, bankBranchService, $window, staffEntryFrmConsts, staffEntryFrmCommonService) {
  var vm = this;

  vm.$scope = $scope;
  var ACCOUNT_TRANSFER_ATTRIBUTES = ["staff_id", "bank_id", "bank_branch_id", "account_name",
    "account_name_kana", "account_type", "account_number"];
  var DATA_HOLDER_IDS = staffEntryFrmConsts.DATA_HOLDER_IDS;
  var ALLOWED_IMG_TYPES = staffEntryFrmConsts.ALLOWED_IMG_TYPES;
  var FILE_FIELDS = staffEntryFrmConsts.STEP_3_FILE_FIELDS;
  var REQUIRED_SELECT_FIELDS = staffEntryFrmConsts.STEP_3_REQUIRED_SELECT_FIELDS;

  vm.staff = {};
  vm.bankBranches = [];
  vm.formError = {};
  vm.acceptedImgTypes = ALLOWED_IMG_TYPES.join(", ");
  vm.fileSizeWithMb = FILE_SIZE_IN_MB + "MB";
  vm.currentBankId = null;

  initData();

  vm.$scope.$watch("vm.currentBankId", function() {
    if (vm.currentBankId) {
      bankBranchService.getBankBranch({bank_id: vm.currentBankId}).then(function(res) {
        vm.bankBranches = res.data.bank_branches;
      });
    } else {
      vm.staff.staff_account_transfer_attributes.bank_branch_id = "";
      vm.bankBranches = [];
    }
    if (_.isEmpty(vm.edited_staff_account_transfer)) {
      vm.staff.staff_account_transfer_attributes.bank_branch_id = "";
    } else {
      if (vm.isInitedBankBranch) {
        vm.staff.staff_account_transfer_attributes.bank_branch_id = "";
      } else {
        vm.isInitedBankBranch = true;
      }
    }
  });

  vm.errorClassForFileFieldStep3 = function(inputName) {
    return {
      "file-field-error-notice": vm["staff_" + inputName + "_touched"] &&
        !vm.staff.staff_account_transfer_attributes[inputName]
    };
  };

  vm.fileChanged = function(event) {
    if (!event.target.files[0]) {return;}
    var previewElm = event.target.getAttribute("target-preview");
    var argument = event.target.getAttribute("argument");
    var editedArgument = event.target.getAttribute("edited-argument");
    var field = event.target.getAttribute("model-name");
    var fileSizeInMb = event.target.files[0].size / 1024 / 1024;

    if (!_.includes(ALLOWED_IMG_TYPES, event.target.files[0].type)) {
      vm.$scope.$apply(function() {
        vm.staff_passbook_pic_touched = true;
        vm.staff.staff_account_transfer_attributes.passbook_pic = "";
        vm.staff.staff_account_transfer_attributes.passbook_pic_over_size = false;
        $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
      });
      return;
    }

    vm.$scope.$apply(function() {
      if (!_.isEqual(event.target.files.length, 0)) {
        eval("vm." + argument)[field] = event.target.files[0].name;
        staffEntryFrmCommonService.previewImg(event, previewElm);
        if (fileSizeInMb > FILE_SIZE_IN_MB) {
          vm.staff.staff_account_transfer_attributes.passbook_pic = "";
          vm.staff.staff_account_transfer_attributes.passbook_pic_over_size = true;
          vm.staff_passbook_pic_touched = true;
        } else {
          vm.staff.staff_account_transfer_attributes.passbook_pic_over_size = false;
          vm.staff_passbook_pic_touched = false;
        }
      } else {
        $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
        eval("vm." + argument)[$(previewElm).attr("data-name")] = _.isEmpty(eval("vm." + editedArgument)) ? "" :
        eval("vm." + editedArgument)[$(previewElm).attr("data-name")];
      }
    });
  };

  vm.accountTransferSubmit = function(argument) {
    $("#staff-edit-bank-form").submit();
  };

  vm.enableAccInputMiddleName = function() {
    vm.isEnableAccountMiddleName = true;
    if (!vm.currentAccMiddleNameKanaLength) vm.currentAccMiddleNameKanaLength = 1;
    if (!vm.currentAccMiddleNameLength) vm.currentAccMiddleNameLength = 1;
  };

  vm.errorClassForInputAccName = function() {
    return {
      "form-error": (!vm.entryForm["staff[staff_account_transfer_attributes][account_first_name]"].$valid && vm.entryForm["staff[staff_account_transfer_attributes][account_first_name]"].$touched) ||
        (!vm.entryForm["staff[staff_account_transfer_attributes][account_last_name]"].$valid && vm.entryForm["staff[staff_account_transfer_attributes][account_last_name]"].$touched) ||
        (!vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_1]"].$valid && vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_1]"].$touched) ||
        (!vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_2]"].$valid && vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_2]"].$touched) ||
        (!vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_3]"].$valid && vm.entryForm["staff[staff_account_transfer_attributes][account_middle_name_3]"].$touched)
    };
  };

  vm.errorClassForInput = function(inputName) {
    return {
      "form-error": !vm.entryForm[inputName].$valid && vm.entryForm[inputName].$touched
    };
  };

  vm.removeAccMiddleName = function(position, field, isName) {
    var counter;
    if (isName) {
      counter = angular.copy(vm.currentAccMiddleNameLength);
      vm.currentAccMiddleNameLength--;
    } else {
      counter = angular.copy(vm.currentAccMiddleNameKanaLength);
      vm.currentAccMiddleNameKanaLength--;
    }
    for (var i = position; i <= counter - 1; i++) {
      vm.staff.staff_account_transfer_attributes[field + "_" + i] = angular.copy(vm.staff.staff_account_transfer_attributes[field + "_" + (i + 1)]);
    }
    vm.staff.staff_account_transfer_attributes[field + "_" + counter] = "";
  };

  vm.addAccMiddleName = function(suffix) {
    vm["currentAccMiddleName" + suffix + "Length"]++;
  };

  vm.backUrl = function() {
    $(".disable-submit-btn").prop("disabled", true);
    $window.location.href = "/" + I18n.locale + "/profiles#account_transfer";
  };

  function initData() {
    vm.staff.staff_account_transfer_attributes = {};
    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });
    if (!_.isEmpty(vm.edited_staff_account_transfer)) {
      formatDataForEdit();
      formatAccountFirstMiddleLastName();
      validateBank();
    }
  }

  function formatDataForEdit() {
    _.forEach(ACCOUNT_TRANSFER_ATTRIBUTES, function(attr) {
      vm.staff.staff_account_transfer_attributes[attr] = vm.edited_staff_account_transfer[attr];
    });
  }

  var getFormData = function(form) {
    var $inputs = $("input[type='file']:not([disabled])", form);
    $inputs.each(function(_, input) {
      if (input.files.length > 0) return;
      $(input).prop("disabled", true);
    });
    var formData = new FormData(form);
    $inputs.prop("disabled", false);
    return formData;
  };

  function setTouchedForSelectFields() {
    REQUIRED_SELECT_FIELDS.forEach(function(field) {
      vm["staff_" + field + "_blur"] = true;
    });
  }

  function setFormTouched(form) {
    FILE_FIELDS.forEach(function(field) {
      vm["staff_" + field + "_touched"] = true;
    });

    setTouchedForSelectFields();
    var stepInfo = staffEntryFrmCommonService.getStepInfo("step3");
    setTouchedForInputFields(form, stepInfo);
  }

  function setTouchedForInputFields(form, stepInfo) {
    if (form.hasOwnProperty("$submitted")) {
      angular.forEach(form.$error, function (errorType) {
        angular.forEach(errorType, function (prop) {
          var requiredInputFields = _.flatMap(stepInfo.requiredFields, eval(stepInfo.stepFunc));
          if (prop.hasOwnProperty("$touched") && _.includes(requiredInputFields, prop.$name)) prop.$setTouched();
          setFormTouched(prop);
        });
      });
    }
  }

  function formatAccountFirstMiddleLastName() {
    var nameArr = vm.staff.staff_account_transfer_attributes.account_name.split(" "),
    nameKanaArr = vm.staff.staff_account_transfer_attributes.account_name_kana.split(" ");
     vm.staff.staff_account_transfer_attributes.account_last_name = nameArr.splice(0, 1).join("");
    vm.staff.staff_account_transfer_attributes.account_first_name = nameArr.splice(nameArr.length - 1, 1).join("");
    var middle_names = nameArr.filter(function(item){return item != ''});
     vm.staff.staff_account_transfer_attributes.account_last_name_kana = nameKanaArr.splice(0, 1).join("");
    vm.staff.staff_account_transfer_attributes.account_first_name_kana = nameKanaArr.splice(nameKanaArr.length - 1, 1).join("");
    var middle_name_kanas = nameKanaArr.filter(function(item){return item != ''});
    [1, 2, 3].forEach(function(idx) {
      vm.staff.staff_account_transfer_attributes["account_middle_name_" + idx] = middle_names[idx - 1];
      vm.staff.staff_account_transfer_attributes["account_middle_name_kana_" + idx] = middle_name_kanas[idx - 1];
    });
    vm.currentAccMiddleNameLength = middle_names.length;
    vm.currentAccMiddleNameKanaLength = middle_name_kanas.length;
     if (!_.isEmpty(middle_names) || !_.isEmpty(middle_name_kanas)) {
      vm.currentAccMiddleNameLength = vm.currentAccMiddleNameLength == 0 ? 1 : vm.currentAccMiddleNameLength;
      vm.currentAccMiddleNameKanaLength = vm.currentAccMiddleNameKanaLength == 0 ? 1 : vm.currentAccMiddleNameKanaLength;
      if (_.isEmpty(middle_name_kanas)) vm.staff.staff_account_transfer_attributes["account_middle_name_kana_1"] = "";
      vm.isEnableAccountMiddleName = true;
    }
  }

  $("#staff-edit-bank-form").on("submit", function(e) {
    e.preventDefault();
    var actionURL = $(this).attr("action");
    $.ajax({
      url: actionURL,
      method: "PATCH",
      dataType: "json",
      data: getFormData(this),
      processData: false,
      contentType: false,
      beforeSend: function() {
        $(".disable-submit-btn").prop("disabled", true);
      },
      success: function(response) {
        if(response.status){
          $window.location.href = response.redirect_path;
        }else{
          vm.$scope.$apply(function() {
            setFormTouched(vm.entryForm);
            window.scrollTo(0, 0);
            $(".disable-submit-btn").prop("disabled", false);
          });
        }
      },
      error: function(){
        location.reload();
      }
    });
  });

  function validateBank() {
    vm.currentBankId = getCurrentBankId(vm.staff.staff_account_transfer_attributes.bank_id);
  }

  function getCurrentBankId(bank_id) {
    var bank = _.find(vm.banks, {id: bank_id});

    if (_.isUndefined(bank)) return;

    return bank.id;
  }
}
