"use strict";

angular.module("staffApp")
  .controller("StaffProfileWorkExpController", StaffProfileWorkExpController);
StaffProfileWorkExpController.$inject = ["staffProfileService"];

function StaffProfileWorkExpController(staffProfileService) {
  var vm = this;
  var DATA_HOLDER_IDS = ["industry_types", "employment_types", "businesses", "work_experiences",
    "min_record_work_experience"];
  var LOCALE = "ja";
  var INPUT_FIELDS = ["work_start_date", "work_end_date", "office_name"];
  var SELECT_FIELDS = ["work_start_date", "work_end_date"];
  var workExpTemplate = {
    id: null,
    work_start_date: null,
    work_end_date: null,
    office_name: null,
    industry_type: null,
    affiliation_department_name: null,
    position: null,
    salary: null,
    employment_type: null,
    use_software: null,
    job_content1: null,
    career_type1: null,
    job_content2: null,
    career_type2: null,
    job_content3: null,
    career_type3: null,
    retirement_reason: null,
    _destroy: false
  };

  initData();

  vm.isDisabled = false;
  vm.isDisableSubmitBtn = false;

  vm.addItem = addItem;

  vm.destroyItem = function(index) {
    vm.work_experiences[index]._destroy = true;
    setOrder(vm.work_experiences);
    setBtnStatus();
  };

  vm.updateWorkExperiences = function() {
    if (vm.checkFormValid(vm.workExpForm, vm.work_experiences)) {
      vm.isDisableSubmitBtn = true;
      staffProfileService.updateWorkExperiences({
        staff: {staff_work_experiences_attributes: formatDate(vm.work_experiences, FULL_DATE_FORMAT)}}
      ).then(function(res) {
        if (res.data.status) {
          location.reload();
        } else {
          vm.isDisableSubmitBtn = false;
          setFormTouched(vm.workExpForm, vm.work_experiences);
        }
      });
    } else {
      setFormTouched(vm.workExpForm, vm.work_experiences);
    }
  };

  vm.errorClassForInput = function(inputName) {
    return {
      "form-error": !vm.workExpForm[inputName].$valid && vm.workExpForm[inputName].$touched
    };
  };

  vm.checkEndTimeValid = function(index){
    checkEndTimeValid(vm.work_experiences, index, vm.workExpForm);
  };

  vm.checkSalaryValidString = function(salary, name_with_index) {
    var regexInt = /^[0-9]+$/;
    if (!_.isEmpty(salary) && !regexInt.test(salary) && vm.workExpForm[name_with_index].$touched &&
      !vm.checkSalaryValidNumberGreateThanZero(salary, name_with_index)) {
      vm.workExpForm[name_with_index].$valid = false;
      return true;
    }
  };

  vm.checkSalaryValidNumberGreateThanZero = function(salary, name_with_index) {
    if (!_.isEmpty(salary) && !isNaN(Number(salary)) && _.isInteger(Number(salary)) && Number(salary) <= 0 &&
      vm.workExpForm[name_with_index].$touched) {
      vm.workExpForm[name_with_index].$valid = false;
      return true;
    }
  };

  vm.checkFormValid = function(form, workExperiences) {
    var flag = true;
    _.forEach(workExperiences, function(workExp, index) {
      if (workExp._destroy) {return;}
      vm.checkEndTimeValid(index);
      _.forEach(INPUT_FIELDS, function(field) {
        var isValid = vm.workExpForm[field + "_" + index].$valid;
        if (!isValid) {
          flag = false;
          return false;
        }
      });
      if (!workExp.industry_type || !workExp.employment_type) {
        flag = false;
        return false;
      }
      if (!flag) {return false;}
    });

    return flag;
  };

  function initData() {
    setTimeout(function() {
      window.scrollTo(0, 0);
    }, 500);
    staffProfileService.getDataTabWorkExperiences().then(function(res) {
      DATA_HOLDER_IDS.forEach(function(id) {
        vm[id] = res.data[id];
      });
      vm.isDisabled = !res.data.status;

      if (_.isEmpty(vm.work_experiences)) {
        addItem();
      } else {
        formatDate(vm.work_experiences, DATE_MONTH_YEAR_FORMAT);
        setOrder(vm.work_experiences);
        addEventForDatePicker();
      }
      setBtnStatus();
    });
  }

  function checkEndTimeValid(workExperiences, index, workExpForm) {
    var workExp = workExperiences[index];
    if (!workExp.work_start_date || !workExp.work_end_date) {return;}
    var startDate = moment(new Date(workExp.work_start_date));
    var endDate = moment(new Date(workExp.work_end_date));

    if (endDate < startDate) {
      workExpForm["work_end_date_" + index].$valid = false;
      workExpForm["work_end_date_" + index].$error = {"date-gt-than": true};
    } else {
      workExpForm["work_end_date_" + index].$valid = true;
      workExpForm["work_end_date_" + index].$error = {};
    }
  }

  function addItem() {
    var template = angular.copy(workExpTemplate);
    template.order = vm.work_experiences.length + 1;
    vm.work_experiences.push(template);
    addEventForDatePicker();
    setOrder(vm.work_experiences);
    setBtnStatus();
  }

  function addEventForDatePicker() {
    setTimeout(function() {
      $(".js-only-month").datetimepicker({
        locale: LOCALE,
        format: DATE_MONTH_YEAR_FORMAT,
        viewMode: DATE_MONTH_YEAR_VIEW_MODE
      });
    }, 500);
  }

  function formatDate(workExperiences, format) {
    workExperiences.forEach(function(workExp) {
      workExp.work_start_date = moment(new Date(workExp.work_start_date)).format(format);
      workExp.work_end_date = moment(new Date(workExp.work_end_date)).format(format);
    });
    return workExperiences;
  }

  function setOrder(workExperiences) {
    var order = 1;
    workExperiences.forEach(function(workExp) {
      if (!workExp._destroy) workExp.order = order++;
    });
    return workExperiences;
  }

  function setFormTouched(form, workExperiences) {
    if (form.hasOwnProperty("$submitted")) {
      angular.forEach(form.$error, function (field) {
        angular.forEach(field, function(errorField){
          errorField.$setTouched();
        })
      });
    }
    workExperiences.forEach(function(workExp) {
      workExp.industry_type_blur = true;
      workExp.employment_type_blur = true;
    });
  }

  function setBtnStatus() {
    var workExperiences = _.filter(vm.work_experiences, function(workExperience){
      return !workExperience._destroy
    });
    if (_.isEqual(workExperiences.length, vm.min_record_work_experience)) vm.isDisabledDelBtn = true;
    else vm.isDisabledDelBtn = false;
  }
}
