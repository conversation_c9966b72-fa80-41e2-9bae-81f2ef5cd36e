"use strict"

angular.module("staffApp").controller("TrainingScheduleController", TrainingScheduleController);
TrainingScheduleController.$inject = ["$scope", "$window", "$interval", "trainingScheduleService",
  "stepsOverviewService", "otpVerificationService", "toaster"]

function TrainingScheduleController($scope, $window, $interval, trainingScheduleService,
  stepsOverviewService, otpVerificationService, toaster) {
  var vm = this;
  var RESCHEDULE_DATA_HOLDERS = [
    "training_center_id", "prefecture_name", "location_name",
    "training_centers", "single_session", "surveys",
    "single_session_schedules",
    "current_schedule_id"
  ];
  var DETAILS_DATA_HOLDERS = [
    "first_session",
    "second_session",
    "single_session"
  ];
  vm.$scope = $scope;
  vm.isReschedule = false;
  vm.formError = false;
  vm.errorMessage = "";
  vm.scheduleErrors = {};
  vm.overviewSteps = stepsOverviewService.initNonExpSteps(2);
  vm.toasterTimeout = 6200;
  vm.disableRescheduleBtn = false;
  vm.params = {
    training_center_id: "",
    first_session_id: "",
    second_session_id: "",
    single_session_id: ""
  }

  // Email verification
  vm.staff = {email: ""};
  vm.otpSuccessMessage = null;
  vm.otpErrorMessage = null;
  vm.otpParams = {
    otp: "",
    token: "",
    login_id: "",
  };
  vm.canResend = false;
  vm.otpInitSuccess = false;
  // EO Email Verification

  // Cancel Survey
  vm.cancelSurveyFormError = false
  // EO Cancel Survey
  vm.able_to_absent = false;
  vm.cancelApplicantIds = [];

  vm.$scope.$watch("vm.params.training_center_id", function() {
    if (vm.params.training_center_id == vm.currentTrainingCenterId) {
      return;
    }
    vm.currentTrainingCenterId = vm.params.training_center_id;
    vm.params.first_session_id = "";
    vm.params.second_session_id = "";
    vm.params.single_session_id = "";
    vm.resetSchedulesList();
  });

  function getScheduleIds(arr1, arr2) {
    return _.union(
      _.map(arr1, function(val) { return val.id; }),
      _.map(arr2, function(val) { return val.id; })
    );
  }

  vm.$scope.$watchGroup(["vm.params.first_session_id", "vm.params.second_session_id"], function(newVal, oldVal) {
    // Check falsey values
    if (_.some(newVal, _.negate(Boolean))) return vm.disableRescheduleBtn = true;

    // Check if params schedule is in the list of available schedules
    var scheduleIds = getScheduleIds(vm.first_session_schedules, vm.second_session_schedules);
    var diff = _.difference(newVal, scheduleIds);
    if (diff.length > 0) return vm.disableRescheduleBtn = true;

    vm.disableRescheduleBtn = false;
  });

  vm.$scope.$watch("vm.params.single_session_id", function(newVal, _oldVal) {
    if (vm.params.single_session_id == vm.current_schedule_id) return vm.disableRescheduleBtn = true;

    // Check falsey values
    if (_.some(newVal, _.negate(Boolean))) return vm.disableRescheduleBtn = true;

    // Check if params schedule is in the list of available schedules
    var availableScheduleIds = _.map(vm.single_session_schedules, function(val) { return val.id; });
    var diff = _.difference(newVal, availableScheduleIds);
    if (diff.length > 0) return vm.disableRescheduleBtn = true;

    vm.disableRescheduleBtn = false;
  });

  vm.init = function() {
    vm.training_centers = angular.element("#training_centers").data("infos");
    vm.requireEmail = angular.element("#require_email").data("infos");
    vm.currentTrainingCenterId = vm.params.training_center_id;
    if (vm.requireEmail) { openVerifyEmailModal(); }
  }

  vm.initReschedule = function() {
    vm.isReschedule = true;
    _.each(RESCHEDULE_DATA_HOLDERS, function(fieldName) {
      vm[fieldName] = angular.element("#" + fieldName).data("infos");
    });
    vm.currentTrainingCenterId    = vm.params.training_center_id = vm.training_center_id;
    vm.params.single_session_id   = vm.current_schedule_id;
    vm.single_session_schedules = _.get(vm, "single_session_schedules", "");
  }

  vm.initDetails = function() {
    vm.currentTrainingCenterId = "";
    _.each(DETAILS_DATA_HOLDERS, function(fieldName) {
      vm[fieldName] = angular.element("#" + fieldName).data("infos")
    })
  }

  vm.getAnswerByType = function(type) {
    return _.filter(vm.surveys.answers, {type: type})
  }

  vm.getAnswerById = function(id) {
    return _.filter(vm.surveys.answers, {id: id})
  }

  vm.resetSchedulesList = function() {
    $("#spinner").removeClass("ng-hide");
    var params = {training_center_id: vm.currentTrainingCenterId, current_training_id: vm.current_schedule_id};
    trainingScheduleService.trainingJobs(params).then(function(res) {
      $("#spinner").addClass("ng-hide");
      if (!!res.data) {
        vm.first_session_schedules = res.data.first_session_schedules;
        vm.second_session_schedules = res.data.second_session_schedules;
        vm.single_session_schedules = res.data.single_session_schedules
        vm.params.single_session_id = vm.current_schedule_id
      }
    })
  }

  vm.resetErrorMessage = function() {
    vm.formError = false;
    vm.errorMessage = "";
    vm.scheduleErrors = {};
  }

  vm.checkAbleToAbsent = function() {
    var training_schedule_applicants = {
      first_session_id:  _.get(vm, "first_session.id", ""),
      second_session_id: _.get(vm, "second_session.id", ""),
      single_session_id: _.get(vm, "single_session.id", "")
    }
    $("#spinner").removeClass("ng-hide");
    trainingScheduleService.checkAbleToAbsent(training_schedule_applicants).then(function (res) {
      $("#spinner").addClass("ng-hide");

      if (!!res.data.able_to_absent) {
        $window.location.href = res.data.redirect_path;
      }
      else {
        $("#modal-warning-reschedule-training").modal("show");
      }
    });
  }

  vm.showModalConfirmCancelTraining = function() {
    vm.resetErrorMessage();
    var training_schedule_applicants = {
      first_session_id:  _.get(vm, "first_session.id", ""),
      second_session_id: _.get(vm, "second_session.id", ""),
      single_session_id: _.get(vm, "single_session.id", "")
    }

    $("#spinner").removeClass("ng-hide");
    trainingScheduleService.getCancellationInfo(training_schedule_applicants).then(function (res) {
      $("#spinner").addClass("ng-hide");

      if (!!res.data.able_to_absent) {
        vm.able_to_absent = res.data.able_to_absent;
        vm.surveys = res.data.surveys;
        vm.surveyAnswers = vm.surveys.answers
        vm.cancelApplicantIds = res.data.cancel_applicant_ids;
        vm.modalMsgContent = res.data.message_content;
        $("#modal-confirm-cancel-training").modal("show");
      }
      else {
        $("#modal-warning-absent-training").modal("show");
      }
    });
  }

  vm.closePopupConfirm = function() {
    $("#modal-confirm-cancel-training").modal("hide");
  }

  vm.cancelTraining = function() {
    vm.validateCancelSurvey();

    if(!vm.cancelSurveyFormError) {
      $("#spinner").removeClass("ng-hide");
      var surveyAnswerResponse = vm.surveyResponse.content;
      
      if (vm.surveyResponse.type == "text") {
        surveyAnswerResponse = vm.surveyAnswerResponse;
      } 

      var cancelTrainingParams = {
        applicant_ids: vm.cancelApplicantIds,
        consider_dropping_out: vm.staffConsiderDroppingOut,
        survey_answer_id: vm.surveyResponse.id,
        survey_answer_response: surveyAnswerResponse,
        survey_question_id: vm.surveys.id
      };

      trainingScheduleService.cancel(cancelTrainingParams).then(function(response) {
        var res = response.data;
        $("#modal-confirm-cancel-training").modal("hide");
        $("#spinner").addClass("ng-hide");
        if(!!res.status) {
          $window.location.reload();
        } else {
          toaster.pop("error", "", res.message, vm.toasterTimeout, 'trustedHtml');
        }
      });
    }
  }

  vm.validateCancelSurvey = function() {
    var flag = false;
    if(!_.isUndefined(vm.cancelSurveyForm)) {
      ["consider_dropping_out", "cancel_reason", "cancel_reason_response"].forEach(function(attr) {
        if(_.isUndefined(vm.cancelSurveyForm[attr])) return;
        vm.cancelSurveyForm[attr].$touched = true;
        if (!vm.cancelSurveyForm[attr].$valid) {flag = true;}
      });
    }
    vm.cancelSurveyFormError = flag;
  }

  vm.validateBeforeSubmit = function() {
    vm.validateCancelSurvey();
    if(!vm.cancelSurveyFormError) {
      vm.resetErrorMessage();
      if (vm.canSubmitForm()) {
        vm.submitForm();
      } else {
        vm.formError = true;
        vm.errorMessage = I18n.t("staff.training_schedule.errors.must_select_training_session");
        window.scrollTo(0, 0);
        $(".disable-submit-btn").prop("disabled", false);
      }
    }
  }

  vm.canSubmitForm = function() {
    if (!!vm.params.single_session_id) {return true;}
    return false;
  }

  function handleSurveyAnswer() {
    if (!vm.surveys) return;

    if(!_.isUndefined(vm.cancelSurveyForm)) {
      vm.params.survey_answer_id = vm.surveyAnswerId;
      var surveyAnswerResponse = '';
      if(vm.surveyAnswerId == vm.getAnswerByType('text')[0].id) {
        surveyAnswerResponse = vm.surveyAnswerResponse;
      } else {
        var answer = vm.getAnswerById(vm.surveyAnswerId)[0];
        surveyAnswerResponse = (answer) ? answer.content : '';
      }

      vm.params.survey_answer_response = surveyAnswerResponse;
      vm.params.survey_question_id = vm.surveys.id;
    }
  }

  vm.submitForm = function() {
    $("#spinner").removeClass("ng-hide");
    $(".disable-submit-btn").prop("disabled", true);
    var serviceFunction = vm.isReschedule ? trainingScheduleService.reschedule : trainingScheduleService.book;

    handleSurveyAnswer();

    serviceFunction(vm.params).then(function(response) {
      $("#spinner").addClass("ng-hide");
      var res = response.data;
      if (res.status) {
        $window.location.href = res.redirect_path;
      } else {
        vm.setErrorMessage(res);
        vm.formError = true;
        vm.resetSchedulesList();
        window.scrollTo(0, 0);
        $(".disable-submit-btn").prop("disabled", false);
      }
    });
  }

  vm.setErrorMessage = function(res) {
    if (!!res.error) {
      vm.errorMessage = res.error;
      return;
    }
  }

  function getErrorString(applyConditionMessage) {
    if (_.isUndefined(applyConditionMessage)) return "";
    return Object.values(applyConditionMessage)[0][0];
  }

  function disableEnter(e) {
    var key = e.charCode || e.keyCode || 0;
    if (key == 13) {
      e.preventDefault();
      $("input").blur();
    }
  };

  if (!_.isNull($(".st-training-schedule-form"))) {
    $(".st-training-schedule-form").keypress(function(e) {
      disableEnter(e);
    });
  }

  vm.emptyTrainingCenterId = function() {
    return vm.params.training_center_id == "" || _.isUndefined(vm.params.training_center_id);
  }

  vm.submitEmail = function() {
    $("#spinner").removeClass("ng-hide");
    var params = {email: vm.staff.email};
    if (!validateEmail()) {
      $("#spinner").addClass("ng-hide");
      return;
    }
    trainingScheduleService.submitEmail(params).then(function(res) {
      var data = res.data;
      if (data.status) {
        transitionToOtpStep();
        vm.resetOtpMessage();
        vm.otpParams.token = data.token;
        vm.otpParams.login_id = data.login_id;
        vm.remainSeconds = OTP_RESEND_INTERVAL;
        vm.otpInitSuccess = true;
        startCountdown();
      } else {
        setResponseError(data.errors);
      }
      $("#spinner").addClass("ng-hide");
    });
  }

  vm.errorClassForSurveyInput = function(inputName) {
    if (!vm.cancelSurveyForm[inputName]) return;
    return {
      "form-error": !vm.cancelSurveyForm[inputName].$valid && vm.cancelSurveyForm[inputName].$touched
    }
  };

  vm.errorClassForInput = function(inputName) {
    if (!vm.verifyEmailForm["staff[" + inputName + "]"]) return;
    return {
      "form-error": !vm.verifyEmailForm["staff[" + inputName + "]"].$valid && vm.verifyEmailForm["staff[" + inputName + "]"].$touched
    }
  };

  vm.verifyOtp = function() {
    $("#spinner").removeClass("ng-hide");
    vm.resetOtpMessage();
    otpVerificationService.staffVerifyOtp(vm.otpParams).then(function(res) {
      if (res.data.status) {
        vm.requireEmail = false;
        closeVerifyEmailModal();
      } else {
        vm.otpSuccessMessage = res.data.message;
        vm.otpErrorMessage = res.data.error;
      }
      $("#spinner").addClass("ng-hide");
    });
  }

  vm.resendOtp = function() {
    var resendOtpParams = _.omit(vm.otpParams, "otp");
    vm.canResend = false;
    vm.remainSeconds = OTP_RESEND_INTERVAL;
    startCountdown();
    vm.resetOtpMessage();
    otpVerificationService.staffResendOtp(resendOtpParams).then(function(res) {
      vm.otpSuccessMessage = res.data.message;
      vm.otpErrorMessage = res.data.error;
    });
  }

  vm.resetOtpMessage = function() {
    vm.otpSuccessMessage = null;
    vm.otpErrorMessage = null;
  }

  vm.isOtpValid = function() {
    var isEmpty = _.isEmpty(vm.params.otp.trim());
    return vm.params.otp.match(OTP_REGEX) && !isEmpty
  }

  vm.backToEmail = function() {
    vm.staff.email = "";
    vm.verifyEmailForm["staff[email]"].$touched = false;
    $("#otp-section").hide();
    $("#email-section").show();
  }

  function openVerifyEmailModal() {
    $("#modal-verify-email").modal("show");
  }

  function closeVerifyEmailModal() {
    $("#modal-verify-email").modal("hide");
  }

  function transitionToOtpStep() {
    $("#email-section").slideUp("slow");
    $("#otp-section").slideDown("slow");
  }

  function validateEmail() {
    vm.verifyEmailForm["staff[email]"].$touched = true;
    if (!!vm.verifyEmailForm["staff[email]"].$error.uniq) {
      vm.verifyEmailForm["staff[email]"].$error = {};
      vm.verifyEmailForm["staff[email]"].$valid = true;
    }
    return vm.verifyEmailForm["staff[email]"].$valid;
  }

  function setResponseError(errors) {
    if (!!errors.email && errors.email[0].error === "taken") {
      vm.verifyEmailForm["staff[email]"].$valid = false;
      vm.verifyEmailForm["staff[email]"].$error = {uniq: true};
      return;
    }
  }

  function startCountdown() {
    if (vm.canResend) return;
    var resendCycle = $interval(function() {
      if (vm.remainSeconds > 0) {
        vm.remainSeconds -= 1;
      } else {
        $interval.cancel(resendCycle);
        vm.canResend = true;
      }
    }, 1000);
  }

  $("#form-verify-email, #form-verify-otp").keydown(function(event) {
    if(event.keyCode == 13) {
      event.preventDefault();
    }
  });

  $("#form-verify-email, #form-verify-otp").on("submit", function(e) {
    e.preventDefault();
  });
}
