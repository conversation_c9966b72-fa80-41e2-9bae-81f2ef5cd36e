"use strict"

angular.module("staffApp").controller("RegistrationAnswerController", RegistrationAnswerController);
RegistrationAnswerController.$inject = ["$scope", "$window", "staffEntryFrmCommonService", "staffEntryFrmConsts",
  "stepsOverviewService"]

function RegistrationAnswerController($scope, $window, staffEntryFrmCommonService, staffEntryFrmConsts,
  stepsOverviewService) {
  var vm = this;
  
  vm.$scope = $scope;
  var ALLOWED_IMG_TYPES = staffEntryFrmConsts.ALLOWED_IMG_TYPES;
  var REQUIRED_RADIO_FIELDS = ["answer_1", "answer_2"];
  var HAS_EXP_REQUIRED_RADIO_FIELDS = ["answer_4", "answer_5"];
  var REQUIRED_FILE_FIELDS = ["upload_file_1"];
  var MATCHBOX_URL = "https://matchbox.jp/login";
  vm.fileSizeWithMb = FILE_SIZE_IN_MB + "MB";
  vm.acceptedImgTypes = ALLOWED_IMG_TYPES.join(", ");
  vm.hasImage = false;
  vm.hasLawsonExperience = false;
  vm.formError = false;
  vm.goToMatchbox = false;
  vm.currentIndustries = [];
  vm.currentProfessions = [];
  vm.submittedForm = false;
  vm.overviewSteps = [];

  vm.params = {
    answer_1: "1",
    answer_2: "1",
    answer_4: "",
    answer_5: "",
    upload_file_1: "",
    create_matchbox_account: true
  };

  vm.$scope.$watchGroup(["vm.params.answer_1", "vm.params.answer_2"], function() {
    vm.isTypeMatchbox = vm.params.answer_1 == "2" && vm.params.answer_2 == "4";
    if (vm.isTypeMatchbox) {
      vm.openLinkToMatchboxModal();
    }
  });

  vm.$scope.$watch("vm.params.lawson_experience_code", function() {
    if (_.isEmpty(vm.params.lawson_experience_code)) { return; }
    vm.hasLawsonExperience = vm.params.lawson_experience_code == "has_pos_experience";
  });

  vm.init = function() {

    vm.business_circles = angular.element("#business_circles").data("infos");
    vm.industries = angular.element("#industries").data("infos");
    vm.professions = angular.element("#professions").data("infos");
    vm.staff_id = angular.element("#staff_id").data("infos");
    vm.hasLawsonExperience = angular.element("#has_pos_experience").data("infos");
    vm.newAnswer = angular.element("#new_answer").data("infos");
    vm.groupedIndustries = _.groupBy(vm.industries, "business_circle_id");
    vm.groupedProfessions = _.groupBy(vm.professions, "industry_id");

    vm.selectedBusinessCircleId = "";
    vm.selectedProfessionIds = [];

    vm.overviewSteps = vm.hasLawsonExperience ? stepsOverviewService.initExpRegistrationSteps(1) :
      stepsOverviewService.initNonExpSteps(1);

    refreshCurrentIndustries();
    refreshCurrentProfessions();
  }

  vm.validateBeforeSubmit = function() {
    vm.submittedForm = true;
    validateImageBeforeSubmit();
    if (isFormValid()) {
      vm.openLinkToMatchboxModal();
    } else {
      window.scrollTo(0, 0);
      $(".disable-submit-btn").prop("disabled", false);
    }
  };

  vm.onClickFileField = function(input) {
    vm.fileInput = input;
    document.body.onfocus = checkFileField;
  }

  vm.fileChanged = function(event) {
    var previewElm = event.target.getAttribute("target-preview");
    var field = event.target.getAttribute("model-name");

    if (!event.target.files[0]) {
      vm.$scope.$apply(function() {
        $("#" + field + "_file_field").val("");
        if (_.includes(REQUIRED_FILE_FIELDS, field)) {
          setValueAttr(field);
          vm.params[field] = "";
        }
        $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
      });
      return;
    }

    var fileSizeInMb = event.target.files[0].size / 1024 / 1024; 

    if (!_.includes(ALLOWED_IMG_TYPES, event.target.files[0].type)) {
      vm.$scope.$apply(function() {
        $("#" + field + "_file_field").val("");
        if (_.includes(REQUIRED_FILE_FIELDS, field)) {
          setValueAttr(field);
          vm.params[field] = "";
        }
        $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
      });
      return;
    }

    vm.$scope.$apply(function() {
      if (!_.isEqual(event.target.files.length, 0)) {
        eval("vm.params")[field] = event.target.files[0].name;
        staffEntryFrmCommonService.previewImg(event, previewElm);
        if (fileSizeInMb > FILE_SIZE_IN_MB) {
          vm.params[field] = "";
          vm.answerForm["answer[" + field + "]"].$error = {"file-max-size": true}
          vm.answerForm["answer[" + field + "]"].$valid = false;
        } else {
          vm.answerForm["answer[" + field + "]"].$error = {}
          vm.answerForm["answer[" + field + "]"].$valid = true;
        }
      } else {
        $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
      }
    });
  };

  vm.errorClassForFileField = function(inputName) {
    return {
      "file-field-error-notice": vm.conditionForDispErrFileField(inputName)
    };
  };

  vm.conditionForDispErrFileField = function(inputName) {
    var condition = vm["" + inputName + "_touched"] && !vm.params[inputName];
    return condition;
  };

  vm.requireAnswer = function(inputName) {
    return _.isEmpty(vm.params[inputName]) && vm.submittedForm;
  };

  vm.displayImageField = function(hasImage) {
    vm.hasImage = hasImage;
    if (vm.hasImage) {
      $("html,body").animate({
        scrollTop: $("#upload-file-field-position").offset().top
      }, 1000);
    } else {
      REQUIRED_FILE_FIELDS.forEach(function(input) {
        vm.answerForm["answer[" + input + "]"].$error = {}
        vm.answerForm["answer[" + input + "]"].$valid = true;
        vm["" + input + "_touched"] = false;
      });
    }
  };

  vm.openInstructionModal = function() {
    $("#instruction-modal").modal("show");
  };

  vm.openLinkToMatchboxModal = function() {
    if (vm.isTypeMatchbox) {
      $("#link-to-matchbox-modal").modal("show");
    } else {
      vm.submitForm(false);
    }
  };

  vm.closeLinkToMatchboxModal = function() {
    $("#link-to-matchbox-modal").modal("hide");
  };

  vm.checkIfLinkToMatchbox = function() {
    if (vm.params.create_matchbox_account) {
      $(".disable-matchbox-btn").prop("disabled", false);
    } else {
      $(".disable-matchbox-btn").prop("disabled", true);
    }
  }

  $(".staff-regist-answer-form").on("submit", function(e) {
    $("#spinner").removeClass("ng-hide");
    e.preventDefault();
    var formData = formattedFormData(this);

    $.ajax({
      url: "/registration_answers",
      method: "POST",
      dataType: "json",
      data: formData,
      processData: false,
      contentType: false,
      success: function(response) {
        $("#spinner").addClass("ng-hide");
        if(response.status) {
          $window.location.href = response.redirect_path;
        } else {
          vm.$scope.$apply(function() {
            window.scrollTo(0, 0);
            vm.formError = true;
            $(".disable-submit-btn").prop("disabled", false);
          });
        }
      }
    });
  });

  vm.submitForm = function(goToMatchbox) {
    vm.goToMatchbox = goToMatchbox;
    $(".disable-submit-btn").prop("disabled", true);
    $("#staff-regist-answer-form-submit").click();
  }

  vm.changeBusinessCircle = function() {
    refreshCurrentIndustries();
    refreshCurrentProfessions();
  };

  vm.changeIndustry = function() {
    refreshCurrentProfessions();
  };

  vm.checkProfessionOption = function(id) {
    var isChecked = document.getElementById("answer_3_" + id).checked;
    if (!_.includes(vm.selectedProfessionIds, id) && isChecked) {
      vm.selectedProfessionIds.push(id);
    } else if (!isChecked) {
      vm.selectedProfessionIds = _.filter(vm.selectedProfessionIds, function(profId) {
        return profId != id;
      });
    }
  };

  vm.removeProfession = function(id) {
    vm.selectedProfessionIds = _.filter(vm.selectedProfessionIds, function(selectedId) {
      return selectedId !== id;
    })
  };

  vm.displaySelectedProfession = function(id) {
    var profession = _.find(vm.professions, {"id": id});
    if (!profession) { return ""; }
    var industry = _.find(vm.industries, {"id": profession.industry_id});
    return industry.name + "・" + profession.name;
  }

  vm.redirectToMatchbox = function() {
    $("#spinner").removeClass("ng-hide");
    location.href = MATCHBOX_URL;
    $("#spinner").addClass("ng-hide");
  }

  function refreshCurrentIndustries() {
    vm.selectedIndustryId = "";
    if (vm.selectedBusinessCircleId == "") {
      vm.isIndustryDisabled = true;
      vm.currentIndustries = [];
    } else {
      vm.isIndustryDisabled = false;
      vm.currentIndustries = vm.groupedIndustries[vm.selectedBusinessCircleId];
    }
  }

  function refreshCurrentProfessions() {
    vm.currentProfessions = vm.groupedProfessions[vm.selectedIndustryId];
  }

  function formattedFormData(form) {
    var formData = new FormData(form);
    formData.append("answer[answer_3]", vm.selectedProfessionIds);
    formData.append("go_to_matchbox", vm.goToMatchbox);
    return formData;
  }

  function checkFileField() {
    vm.$scope.$apply(function() {
      vm["" + vm.fileInput + "_touched"] = true;
    });
    if(_.isEmpty($("#" + vm.fileInput + "_file_field").val())) {
      vm.$scope.$apply(function() {
        if (_.includes(REQUIRED_FILE_FIELDS, vm.fileInput) &&
          _.isEmpty(vm.answerForm["answer[" + vm.fileInput + "]"].$error)) {
          setValueAttr(vm.fileInput);
        }
      });
    }
    document.body.onfocus = null;
  }

  function setValueAttr(field) {
    vm.answerForm["answer[" + field + "]"].$error = {"file-not-selected": true};
    vm.answerForm["answer[" + field + "]"].$valid = false;
  }

  function isFormValid() {
    var flag = true;
    var requiredFields = REQUIRED_RADIO_FIELDS.concat(REQUIRED_FILE_FIELDS);
    if (vm.hasLawsonExperience) {
      requiredFields = requiredFields.concat(HAS_EXP_REQUIRED_RADIO_FIELDS);
    }
    if (vm.newAnswer) {
      requiredFields.push("lawson_experience_code");
    }
    requiredFields.forEach(function(attr) {
      if (attr == "upload_file_1" && !vm.hasImage) { return; }
      vm.answerForm["answer[" + attr + "]"].$touched = true;
      if (!vm.params[attr]){
        vm.answerForm["answer[" + attr + "]"].$valid = false;
        vm.answerForm["answer[" + attr + "]"].$error = {"required": true};
        flag = false;
      } else {
        vm.answerForm["answer[" + attr + "]"].$valid = true;
        vm.answerForm["answer[" + attr + "]"].$error = {};
      }
      vm["" + attr + "_touched"] = true;
    });
    vm.formError = flag ? false : true;
    return flag;
  }

  function validateImageBeforeSubmit() {
    if (!vm.hasImage) { return; }
    REQUIRED_FILE_FIELDS.forEach(function(input) {
      vm["" + input + "_touched"] = true;
      if(_.isEmpty($("#" + input + "_file_field").val())) {
        if (_.includes(REQUIRED_FILE_FIELDS, input) &&
          _.isEmpty(vm.answerForm["answer[" + input + "]"].$error)) {
          setValueAttr(input);
        }
      }
    });
  }

  function disableEnter(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 13) {
      e.preventDefault();
      $("input").blur();
    }
  };

  if (!_.isNull($(".staff-regist-answer-form"))) {
    $(".staff-regist-answer-form").keypress(function(e) {
      disableEnter(e);
    });
  }
}