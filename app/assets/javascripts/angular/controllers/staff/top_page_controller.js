"use strict";

angular.module("staffApp")
  .controller("TopPageController", TopPageController);
TopPageController.$inject = ["$scope", "topPageService", "orderCaseListService", "orderCaseService"];

function TopPageController($scope, topPageService, orderCaseListService, orderCaseService) {
  var vm = this;
  var $adminNotificationPopUp = $("#modal-notification-info");
  var $notificationPopup = $("#notification-popup");
  var $body = $("body");
  vm.have_new_notification = $notificationPopup.data("haveNewNotification");
  vm.payment_requests = {};
  vm.order_cases = [];
  vm.locations = [];
  vm.isRegistrationStaff = false;
  vm.adminNotificationData = {};
  vm.downloadUrl = "#";
  vm.notification = {
    params: {
      page: 1,
      limit: 5,
      total: 0
    },
    notifications: []
  };

  vm.new_notification = {
    params: {
      page: 1,
      limit: 5,
      total: 0
    },
    notifications: []
  };

  vm.init = function(isRegistrationStaff) {
    vm.isRegistrationStaff = !!isRegistrationStaff;
    if (vm.have_new_notification) {
      vm.displayNewNotififications();
    }
    vm.loadNotification();
    topPageService.getData().then(function(res) {
      angular.extend(vm, res.data);
      vm.displayIndividualNumberModal();
      vm.getOrderCasesData();
    });
  }

  vm.getOrderCasesData = function() {
    topPageService.getOrderCasesData().then(function(res) {
      angular.extend(vm, res.data);
      vm.getLocationsData();
    });
  }

  vm.getLocationsData = function() {
    topPageService.getLocationsData().then(function(res) {
      angular.extend(vm, res.data);
    });
  }

  vm.loadNotification = function() {
    vm.notification.params.is_registration = vm.isRegistrationStaff;
    topPageService.getNotifications(vm.notification.params).then(function(res) {
      vm.notification.notifications = _.concat(vm.notification.notifications, res.data.notifications);
      vm.notification.params.total = res.data.total_item;
      vm.notification.notif_work_achieve = res.data.notif_work_achieve;

      vm.is_notice_work_achieve = !!vm.notification.notif_work_achieve;
      vm.notification_length = vm.is_notice_work_achieve ? 1 : 0;
      vm.notification_length += vm.notification.notifications.length;
    });
  }

  vm.loadNewNotifications = function() {
    topPageService.getNewNotifications(vm.new_notification.params).then(function(res) {
      vm.new_notification.notifications = _.concat(vm.new_notification.notifications, res.data.notifications);
      vm.new_notification.params.total = res.data.total_item;
      vm.new_notification.notif_work_achieve = res.data.notif_work_achieve;
      vm.is_notice_work_achieve_new = !!vm.new_notification.notif_work_achieve;
      vm.new_notification_length = vm.is_notice_work_achieve_new ? 1 : 0;
      vm.new_notification_length += vm.new_notification.notifications.length;
    });
  };

  vm.displayNewNotififications = function() {
    vm.loadNewNotifications();
    $notificationPopup.show();
  };

  vm.closeNotificationPopup = function() {
    $notificationPopup.hide();
    $body.removeClass("modal-open");
    topPageService.turnOffNotificationPopup();
  };

  vm.formatMessages = function(messages) {
    var urlRegex =/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
    return messages.replace(urlRegex, function(url) {
      return '<a href="' + url + '" target="_blank">' + url + '</a>';
    });
  }
  
  vm.getFeeFlag = function(order_case) {
    var flags = [];
    var wage = vm.wages_fees[order_case.id];

    if (!_.isUndefined(wage.total_unit_price_addition) && wage.total_unit_price_addition > 0) {
      flags.push(orderCaseListService.feeFlagByKey(wage.total_unit_price_addition));
    }
    if (!_.isUndefined(wage.total_order_case_addition) && wage.total_order_case_addition > 0) {
      flags.push(orderCaseListService.feeFlagByKey(wage.total_order_case_addition, "offer_fee_by_order_rate"));
    }

    return flags;
  };

  vm.loadMoreNotification = function() {
    vm.notification.params.page +=1;
    vm.loadNotification();
  }

  vm.loadMoreNewNotification = function() {
    vm.new_notification.params.page +=1;
    vm.loadNewNotifications();
  };

  vm.isKeeped = function(orderCaseId) {
    return _.includes(vm.keep_order_case_ids, orderCaseId);
  };

  vm.disableOrderCase = function(orderCase) {
    return orderCase.timeout_apply_before_2_hour || _.includes(vm.rejected_order_case_ids, orderCase.id);
  }

  vm.keepOrderCase = function(orderCaseId) {
    orderCaseListService.createStaffKeepOrderCase({order_case_id: orderCaseId}).then(function(){
      location.reload();
    });
  };

  vm.moveToDetail = function(orderCase) {
    location.href = "/" + I18n.locale + "/order_cases/" + orderCase.id;
  };

  vm.locationUrl = function(location) {
    var queryParams = {
      free_word_selected: true,
      ignore_condition: true,
      location_id: location.id,
      is_open: true,
      search_only_location: true
    }

    var queryString = _.map(queryParams, function(value, key) {
        return key + "=" + value;
    }).join("&");
    var url = "/" + I18n.locale + "/order_cases/?" + queryString;
    if (/Mobile|webOS/i.test(navigator.userAgent)) {
      url = "/" + I18n.locale + "/locations/" + location.id + "/jobs";
    }
    return url;
  };

  vm.bgImageForLocation = function(locationType, thumbnailPath) {
    return orderCaseService.bgImageForLocation(locationType, thumbnailPath);
  };

  vm.bgForNotLawson = function(locationType, locationImage, thumbnailPath) {
    return orderCaseService.bgForNotLawson(locationType, locationImage, thumbnailPath);
  };

  vm.openNotification = function(notification, event) {
    event.preventDefault();
    vm.adminNotificationData = {};
    vm.downloadUrl = "#";
    var type = notification.notification_type;
    if (type == "admin_notification_staff" || type == "notify_working_time" || type == "remind_update_contract") {
      $adminNotificationPopUp.hide();
      $body.removeClass("modal-open");
      vm.showAdminNotification(notification);
      return;
    }
    if (type == "update_rank_or_level") {
      topPageService.removeNotifications({remove_notification_update_rank_or_level: true});
    }
    if (type != "admin_notification_staff"){
      location.href = notification.link_url;
    }
  };

  vm.showAdminNotification = function(notification) {
    var type = notification.notification_type;
    if (type == "admin_notification_staff") {
      vm.adminNotificationData = notification.data_link_url;
      vm.downloadUrl = "/" + I18n.locale + "/top_pages/download_notification_attachment?notification_id=" + vm.adminNotificationData.admin_notification_staff_id;
    }
    if (type == "notify_working_time" || type == "remind_update_contract") {
      vm.adminNotificationData = {
        title: notification.message_content,
        messages: notification.message_body
      };
    }
    $adminNotificationPopUp.show();
    $body.addClass("modal-open");
  };

  vm.closeAdminNotificationPopUp = function() {
    vm.adminNotificationData = {};
    vm.downloadUrl = "#";
    $adminNotificationPopUp.hide();
    $body.removeClass("modal-open");
  };

  $scope.formatCurrency = function(price) {
    if (typeof price === "undefined") return;
    return price.toLocaleString() + I18n.t("common.currency");
  };

  vm.goToRoomUrl = function(room){
    window.open(room.url, "_blank");
  };

  vm.displayIndividualNumberModal = function() {
    if (vm.my_number_status != "no_number" && vm.my_number_status != "returned_number") {return;}
    $("#individual-number-modal").modal("show");
  };

  vm.goToIndividualNumberRegistration = function() {
    location.href = "/" + I18n.locale + "/individual_number_registrations/new";
  };
}
