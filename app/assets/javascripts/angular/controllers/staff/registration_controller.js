"use strict"

angular.module("staffApp").controller("RegistrationController", RegistrationController);
RegistrationController.$inject = ["$scope", "$window", "$interval", "registrationService", "birthdayService"]

function RegistrationController($scope, $window, $interval, registrationService, birthdayService) {
  var vm = this;
  vm.$scope = $scope;
  vm.staff = {};
  vm.birthday = {};
  vm.formError = false;
  vm.initSuccess = false;
  var TEXT_FIELDS = ["name", "name_kana", "birthday", "postal_code", "tel", "password","password_confirmation"];
  var RADIO_FIELDS = ["lawson_experience_code"];
  var POSTAL_CODE_REGEX = /^\d{7}$/;
  vm.postalCodePattern = (function() {
    return {
      test: function(value) {
        return POSTAL_CODE_REGEX.test(value.replace(/\-/g, ""));
      }
    }
  })();

  vm.isValidTelFormat = false;
  vm.isValidOtpFormat = false
  vm.step = 1;
  vm.otpParams = {};
  vm.optError = null;
  vm.optSuccess = null;
  vm.canResend = false;

  vm.init = function() {
    watchingChanges();
    birthdayService.initBirthday($scope);
    vm.staff.birthday = formatDate(vm.staff.birthday);
    $("#registration-body").removeClass("d-none");
    vm.initSuccess = true;
  };

  vm.checkTelError = function() {
    vm.telForm["staff[tel]"].$touched  = true;
    vm.isValidTelFormat = !!vm.staff.tel && !vm.telForm["staff[tel]"].$error.pattern;
  };

  vm.errorTelForInput = function() {
    return {
      "form-error": !vm.telForm["staff[tel]"].$valid && vm.telForm["staff[tel]"].$touched
    }
  };

  vm.resetOptMessage = function() {
    vm.optError = null;
    vm.optSuccess = null;
  }

  vm.sendOtpToTel = function() {
    vm.resetOptMessage();
    $("#spinner").removeClass("ng-hide");
    registrationService.registerPhone({staff: {tel: vm.staff.tel}}).then(function(res) {
      $("#spinner").addClass("ng-hide");
      if (vm.step != 2) {
        vm.step = 2;
        return;
      }
      vm.optError = res.data.errors;
      vm.optSuccess = res.data.message;
    });
  }

  vm.openNewPosExplainModal = function() {
    $("#new-pos-explain-modal").modal("show");
  };

  vm.checkOtpError = function() {
    vm.otpForm["staff[otp]"].$touched  = true;
    vm.isValidOtpFormat = !!vm.otpParams.otp && !vm.otpForm["staff[otp]"].$error.pattern;
  };

  function startCountdown() {
    if (vm.canResend) return;
    var resendCycle = $interval(function() {
      if (vm.remainSeconds > 0) {
        vm.remainSeconds -= 1;
      } else {
        $interval.cancel(resendCycle);
        vm.canResend = true;
      }
    }, 1000);
  }

  vm.verifyOtp = function() {
    $("#spinner").removeClass("ng-hide");
    vm.resetOptMessage();
    vm.otpParams.tel = vm.staff.tel;
    registrationService.verifyPhone({staff: vm.otpParams}).then(function(res) {
      $("#spinner").addClass("ng-hide");
      if (res.data.status) {
        vm.submitRegistration(res.data.verify_id);
      } else {
        vm.optSuccess = res.data.message;
        vm.optError = res.data.errors;
      }
    });
  }

  vm.resendOtp = function() {
    vm.remainSeconds = OTP_RESEND_INTERVAL;
    vm.canResend = false;
    startCountdown();
    vm.sendOtpToTel();
  }

  vm.errorClassForInput = function(inputName) {
    if (!vm.entryForm["staff[" + inputName + "]"]) return;
    return {
      "form-error": !vm.entryForm["staff[" + inputName + "]"].$valid && vm.entryForm["staff[" + inputName + "]"].$touched
    }
  };

  vm.checkExistedPostalCode = function() {
    if (!_.isEmpty(vm.staff.postal_code) && !vm.entryForm["staff[postal_code]"].$error.pattern) {
      registrationService.validateExistedPostalCode({postal_code: vm.staff.postal_code}).then(function(res) {
        if (res.data.exist) {
          vm.entryForm["staff[postal_code]"].$valid = true;
          vm.entryForm["staff[postal_code]"].$error = {};
        } else {
          vm.entryForm["staff[postal_code]"].$valid = false;
          vm.entryForm["staff[postal_code]"].$error = {invalid: true};
        }
      });
    } else {
      vm.entryForm["staff[postal_code]"].$error.invalid = false;
    }
  };

  vm.resetPostalCodeError = function() {
    if (vm.entryForm["staff[postal_code]"].$error.invalid) {
      vm.entryForm["staff[postal_code]"].$valid = true;
      vm.entryForm["staff[postal_code]"].$error = {};
    }
  };

  vm.checkValidPassword = function() {
    if (!_.isEmpty(vm.staff.password) && vm.staff.password.length >= 8 && vm.staff.password.length <= 32) {
      vm.entryForm["staff[password]"].$valid = true;
      vm.entryForm["staff[password]"].$error = {};
    } else {
      vm.entryForm["staff[password]"].$valid = false;
      vm.entryForm["staff[password]"].$error = {"invalid-password": true};
    }
  };

  vm.checkValidPasswordConfirmation = function() {
    if (vm.staff.password == vm.staff.password_confirmation) {
      vm.entryForm["staff[password_confirmation]"].$valid = true;
      vm.entryForm["staff[password_confirmation]"].$error = {};
    } else {
      vm.entryForm["staff[password_confirmation]"].$valid = false;
      vm.entryForm["staff[password_confirmation]"].$error = {"not-same-password": true};
    }
  };

  vm.validateRegistrationInfo = function() {
    var flag = false;
    _.concat(TEXT_FIELDS, RADIO_FIELDS).forEach(function(attr) {
      vm.entryForm["staff[" + attr + "]"].$touched = true;
      if (!vm.entryForm["staff[" + attr + "]"].$valid) {flag = true;}
    });
    vm.formError = flag;
  }

  vm.goToOtpVerification = function() {
    vm.validateRegistrationInfo();
    if (vm.formError) {
      window.scrollTo(0, 0);
    } else {
      vm.remainSeconds = OTP_RESEND_INTERVAL;
      startCountdown();
      vm.sendOtpToTel();
    }
  };

  vm.submitRegistration = function(verifyId) {
    $("#spinner").removeClass("ng-hide");
    vm.staff.verify_id = verifyId;
    registrationService.register({staff: vm.staff}).then(function(response) {
      $("#spinner").addClass("ng-hide");
      var res = response.data;
      vm.passwordErrors = {};
      if(res.status) {
        $window.location.href = res.redirect_path;
      } else {
        setTouchedForInputFields(vm.entryForm);
        setResponseError(res.errors, res.staff_number_not_unique);
        vm.step = 1;
        window.scrollTo(0, 0);
      }
    });
  }

  vm.changeYear = function() {
    vm.entryForm["staff[birthday]"].$touched = true;
    birthdayService.changeYear($scope);
  }

  vm.changeMonth = function() {
    vm.entryForm["staff[birthday]"].$touched = true;
    birthdayService.changeMonth($scope);
  }

  vm.changeDay = function() {
    vm.entryForm["staff[birthday]"].$touched = true;
    birthdayService.assignBirthday($scope);
  }

  vm.formatMonthAndDay = function(value) {
    return birthdayService.formatMonthAndDay(value);
  }

  function formatDate(date) {
    if (!isValidDate(date)) return;
    return moment.parseZone(date).format(FULL_DATE_FORMAT);
  }

  function watchingChanges() {
    vm.$scope.$watch("vm.staff.birthday", function() {
      if (!vm.entryForm["staff[birthday]"]) return;
      if(!Date.parse(vm.staff.birthday)) {
        vm.entryForm["staff[birthday]"].$valid = false;
        vm.entryForm["staff[birthday]"].$error = {"required": true};
      } else if(isAgeLessThan15(vm.staff.birthday)) {
        vm.entryForm["staff[birthday]"].$valid = false;
        vm.entryForm["staff[birthday]"].$error = {"age-must-over15": true};
      } else if (!isValidDate(vm.staff.birthday)) {
        vm.entryForm["staff[birthday]"].$valid = false;
        vm.entryForm["staff[birthday]"].$error = {"invalid": true};
      } else {
        vm.entryForm["staff[birthday]"].$valid = true;
        vm.entryForm["staff[birthday]"].$error = {};
      }
    });  

    vm.$scope.$watch("vm.birthday.year", function(newVal, oldVal) {
      if (oldVal === newVal) return;
      birthdayService.changeYear($scope);
    });
  
    vm.$scope.$watch("vm.birthday.month", function(newVal, oldVal) {
      if (oldVal === newVal) return;
      birthdayService.changeMonth($scope);
    });
  
    vm.$scope.$watch("vm.birthday.day", function(newVal, oldVal) {
      if (oldVal === newVal) return;
      birthdayService.changeDay($scope);
    });  
  };

  function setTouchedForInputFields(form) {
    if (form.hasOwnProperty("$submitted")) {
      angular.forEach(form.$error, function (errorType) {
        angular.forEach(errorType, function (prop) {
          var requiredInputFields = _.flatMap(_.concat(TEXT_FIELDS, RADIO_FIELDS), getAttributeName);
          if (prop.hasOwnProperty("$touched") && _.includes(requiredInputFields, prop.$name)) prop.$setTouched();
        });
      });
    }
  }

  function setResponseError(errors, notUnique) {
    if (errors.tel) {
      vm.entryForm["staff[tel]"].$valid = false;
      vm.entryForm["staff[tel]"].$error = {uniq: true};
    }
    if (errors.postal_code) {
      vm.entryForm["staff[postal_code]"].$valid = false;
      vm.entryForm["staff[postal_code]"].$error = {invalid: true};
    }
    if (errors.password) {
      vm.entryForm["staff[password]"].$valid = false;
      vm.entryForm["staff[password]"].$error = {"invalid-password": true};
    }
    if (errors.password_confirmation) {
      vm.entryForm["staff[password_confirmation]"].$valid = false;
      vm.entryForm["staff[password_confirmation]"].$error = {"not-same-password": true};
    }
    if (!!notUnique) {
      vm.formError = true;
    }
  }

  function isValidDate (date) {
    return moment(date, FULL_DATE_FORMAT).isValid();
  }

  function isAgeLessThan15(birthday) {
    var age = moment().diff(birthday, "years", true);
    if (age < 15) return true;
    return false;
  }


  function getAttributeName(attr) {
    return "staff[" + attr + "]";
  }

  function disableEnter(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 13) {
      e.preventDefault();
      $("input").blur();
    }
  };

  if (!_.isNull($(".staff-registration-form"))) {
    $(".staff-registration-form").keypress(function(e) {
      disableEnter(e);
    });
  }

  if (!_.isNull($(".staff-register-phone"))) {
    $(".staff-register-phone").keypress(function(e) {
      disableEnter(e);
    });
  }
}