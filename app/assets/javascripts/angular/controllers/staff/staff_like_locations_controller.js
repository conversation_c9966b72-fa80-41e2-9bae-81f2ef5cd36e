"use strict";

angular.module("staffApp")
  .controller("StaffLikeLocationsController", StaffLikeLocationsController);
StaffLikeLocationsController.$inject = ["$location", "orderCaseService"];

function StaffLikeLocationsController($location, orderCaseService) {
  var vm = this;

  vm.initData = function() {
    vm.sortField = "case_started_at";

    var searchField = location.search.split("=")[1];
    vm.sortByCaseStartedAt = searchField == "case_started_at";

    if (_.isEmpty(searchField)) {
      vm.sortByCaseStartedAt = true;
      vm.displayTextSearch = I18n.t("staff.favorite_stores.filter_fields.case_started_at");
    } else {
      vm.displayTextSearch = I18n.t("staff.favorite_stores.filter_fields." + searchField);
    }
  }

  vm.keepOrderCase = function(orderCaseId) {
    orderCaseService.createStaffKeepOrderCase({order_case_id: orderCaseId}, I18n.locale).then(function(res) {
    });
  };

  vm.sortBy = function(sortField) {
    vm.displayTextSearch = I18n.t("staff.favorite_stores.filter_fields." + sortField);
    location.href = $location.search({sort_field: sortField}).replace().$$absUrl;
  };

  vm.openRemoveModal = function(locationId) {
    vm.currentLocationId = locationId;
    angular.element("#modal-favorite-store").modal("show");
  }

  vm.confirmRemoveLikeLocation = function() {
    orderCaseService.removeStaffLikeLocation(vm.currentLocationId).then(function(res) {
      location.reload();
    });
  }

  vm.viewAll = function(locationName, locationId) {
    var queryParams = {
      free_word_selected: true,
      ignore_condition: true,
      location_id: locationId,
      is_open: true,
      search_only_location: true,
      free_word: locationName
    }

    var queryString = _.map(queryParams, function(value, key) {
        return key + "=" + value;
    }).join("&");

    var url = "/" + I18n.locale + "/order_cases/?" + queryString;
    if (/Mobile|webOS/i.test(navigator.userAgent)) {
      url = "/" + I18n.locale + "/locations/" + locationId + "/jobs";
    }
    return url;
  }

  vm.moveToDetail = function(orderCaseId) {
    location.href = "/" + I18n.locale + "/order_cases/" + orderCaseId;
  };

  vm.bgImageForLocation = function(locationType, thumbnailPath) {
    return orderCaseService.bgImageForLocation(locationType, thumbnailPath);
  };

  vm.bgForNotLawson = function(locationType, locationImage, thumbnailPath) {
    return orderCaseService.bgForNotLawson(locationType, locationImage, thumbnailPath);
  };
}
