"use strict"

angular.module("staffApp").controller("InterviewScheduleController", InterviewScheduleController);
InterviewScheduleController.$inject = ["$scope", "$window", "interviewScheduleService",
  "stepsOverviewService", "toaster", "$interval", "otpVerificationService"]

function InterviewScheduleController($scope, $window, interviewScheduleService,
  stepsOverviewService, toaster, $interval, otpVerificationService) {
  var vm = this;

  vm.$scope = $scope;
  vm.formError = false;
  vm.prefectureError = false;
  vm.interviewError = false;
  vm.errorMessage = "";
  vm.params = {
    prefecture_id: "",
    interview_id: ""
  }
  vm.cancelInterviewParams = {
    staff_apply_interview_id: "",
    staff_recruitment_process_id: ""
  }
  vm.overviewSteps = stepsOverviewService.initExpRegistrationSteps(2);
  vm.toasterTimeout = 6200;

  vm.otpSuccessMessage = null;
  vm.otpErrorMessage = null;
  vm.otpParams = {
    otp: "",
    token: "",
    login_id: "",
  };
  vm.canResend = false;
  vm.otpInitSuccess = false;

  vm.init = function(initOptions) {
    vm.prefectures = angular.element("#prefectures").data("infos");
    vm.interviews = angular.element("#interviews").data("infos");
    if (!!initOptions) {
      vm.oldInterviewId = initOptions[0];
      vm.oldPrefectureId = initOptions[1];
    }
    vm.params.prefecture_id = !!vm.oldPrefectureId ? vm.oldPrefectureId : "";
    vm.params.interview_id = !!vm.oldInterviewId  ? vm.oldInterviewId  : "";
    openVerifyEmailModal();
  }

  vm.initDetail = function(staffApplyInterviewId, staffRecruitmentProcessId) {
    vm.cancelInterviewParams["staff_apply_interview_id"] = staffApplyInterviewId
    vm.cancelInterviewParams["staff_recruitment_process_id"] = staffRecruitmentProcessId
  }

  vm.showModalConfirmCancelInterview = function() {
    $("#modal-confirm-cancel-interview").modal("show");
  }

  vm.closePopupConfirm = function() {
    $("#modal-confirm-cancel-interview").modal("hide");
  }

  vm.validateBeforeSubmit = function() {
    vm.clearError();
    if (vm.validInput() && !vm.cannotReschedule()) {
      vm.submitForm();
      return;
    }
    vm.formError = true;
    window.scrollTo(0, 0);
    $(".disable-submit-btn").prop("disabled", false);
  }

  vm.clearError = function() {
    vm.formError = false;
    vm.prefectureError = false;
    vm.interviewError = false;
    vm.errorMessage = "";
  }

  vm.cannotReschedule = function() {
    if ((!!vm.oldInterviewId || !!vm.oldPrefectureId) &&
        (vm.oldInterviewId == vm.params.interview_id && vm.oldPrefectureId == vm.params.prefecture_id)) {
      vm.errorMessage = "変更前と違う日付をお選びください。";
      return true;
    }
    return false;
  }

  vm.cancelInterview = function() {
    $("#spinner").removeClass("ng-hide");
    interviewScheduleService.cancel(vm.cancelInterviewParams).then(function(response) {
      var res = response.data;
      $("#modal-confirm-cancel-interview").modal("hide");
      $("#spinner").addClass("ng-hide");
      if(!!res.status) {
        $window.location.reload();
      } else {
        toaster.pop("error", "", res.message, vm.toasterTimeout, 'trustedHtml');
      }
    });
  }

  vm.validInput = function() {
    if (!!vm.params.prefecture_id && !!vm.params.interview_id) {return true;}
    if (vm.params.prefecture_id === "") {vm.prefectureError = true;}
    if (vm.params.interview_id === "") {vm.interviewError = true;}
    return false;
  }

  vm.submitForm = function() {
    $(".disable-submit-btn").prop("disabled", true);
    $("#spinner").removeClass("ng-hide");
    interviewScheduleService.schedule(vm.params).then(function(response) {
      var res = response.data;
      $("#spinner").addClass("ng-hide");
      if(!!res.status) {
        $window.location.href = res.redirect_path;
      } else {
        if (res.redirect_back) {
          return $window.location.href = res.redirect_path;
        }

        window.scrollTo(0, 0);
        vm.formError = true;
        vm.errorMessage = res.error;
        $(".disable-submit-btn").prop("disabled", false);
      }
    });
  }

  vm.submitEmail = function() {
    $("#spinner").removeClass("ng-hide");

    if (!validateEmail()) {
      $("#spinner").addClass("ng-hide");

      return
    }
    var params = {email: this.staffEmail}
    interviewScheduleService.submitEmail(params).then(function(res) {
      var data = res.data;
      if (data.status) {
        transitionToOtpStep();
        vm.resetOtpMessage();
        vm.otpParams.token = data.token;
        vm.otpParams.login_id = data.login_id;
        vm.remainSeconds = OTP_RESEND_INTERVAL;
        vm.otpInitSuccess = true;
        startCountdown();
      } else {
        setResponseError(data.errors);
      }
      $("#spinner").addClass("ng-hide");
    });
  }

  function disableEnter(e) {
    var key = e.charCode || e.keyCode || 0;
    if (key == 13) {
      e.preventDefault();
      $("input").blur();
    }
  };

  if (!_.isNull($(".st-interview-schedule-form"))) {
    $(".st-interview-schedule-form").keypress(function(e) {
      disableEnter(e);
    });
  }

  function openVerifyEmailModal() {
    $("#modal-verify-email").modal("show");
  }

  function closeVerifyEmailModal() {
    $("#modal-verify-email").modal("hide");
  }

  function transitionToOtpStep() {
    $("#email-section").slideUp("slow");
    $("#otp-section").slideDown("slow");
  }

  function validateEmail() {
    vm.verifyEmailForm["staff[email]"].$touched = true;

    if (!!vm.verifyEmailForm["staff[email]"].$error.uniq) {
      vm.verifyEmailForm["staff[email]"].$error = {};
      vm.verifyEmailForm["staff[email]"].$valid = true;
    }
    return vm.verifyEmailForm["staff[email]"].$valid;
  }

  $("#form-verify-email").on("submit", function(e) {
    e.preventDefault();
    vm.submitEmail();
  });

  function setResponseError(errors) {
    if (!!errors.email && errors.email[0].error === "taken") {
      vm.verifyEmailForm["staff[email]"].$valid = false;
      vm.verifyEmailForm["staff[email]"].$error = {uniq: true};
      return;
    }
  }

  vm.resetOtpMessage = function() {
    vm.otpSuccessMessage = null;
    vm.otpErrorMessage = null;
  }

  function startCountdown() {
    if (vm.canResend) return;
    var resendCycle = $interval(function() {
      if (vm.remainSeconds > 0) {
        vm.remainSeconds -= 1;
      } else {
        $interval.cancel(resendCycle);
        vm.canResend = true;
      }
    }, 1000);
  }

  vm.resendOtp = function() {
    var resendOtpParams = _.omit(vm.otpParams, "otp");
    vm.canResend = false;
    vm.remainSeconds = OTP_RESEND_INTERVAL;
    startCountdown();
    vm.resetOtpMessage();
    otpVerificationService.staffResendOtp(resendOtpParams).then(function(res) {
      vm.otpSuccessMessage = res.data.message;
      vm.otpErrorMessage = res.data.error;
    });
  }

  vm.backToEmail = function() {
    vm.staffEmail = "";
    vm.verifyEmailForm["staff[email]"].$touched = false;
    $("#otp-section").hide();
    $("#email-section").show();
  }

  vm.verifyOtp = function() {
    $("#spinner").removeClass("ng-hide");
    vm.resetOtpMessage();
    otpVerificationService.staffVerifyOtp(vm.otpParams).then(function(res) {
      if (res.data.status) {
        vm.requireEmail = false;
        closeVerifyEmailModal();
      } else {
        vm.otpSuccessMessage = res.data.message;
        vm.otpErrorMessage = res.data.error;
      }
      $("#spinner").addClass("ng-hide");
    });
  }

  vm.openPopupCannotReschedule = function() {
    $("#modal-cannot-reschedule").modal("show")
  }

  vm.closePopupCannotReschedule = function() {
    $("#modal-cannot-reschedule").modal("hide")
  }
}
