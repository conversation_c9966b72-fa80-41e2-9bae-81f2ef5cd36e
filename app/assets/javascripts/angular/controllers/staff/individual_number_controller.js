"use strict";

angular.module("staffApp").controller("IndividualNumberController", IndividualNumberController);
IndividualNumberController.$inject = ["$scope", "individualNumberService", "staffEntryFrmConsts", "staffEntryFrmCommonService"];

function IndividualNumberController($scope, individualNumberService, staffEntryFrmConsts, staffEntryFrmCommonService) {
  var vm = this;
  vm.$scope = $scope;

  var DATA_HOLDER_IDS = ["is_new_form", "old_registration"];
  var REGIST_FIELDS = ["submission_type", "identification_1_front", "identification_1_back", "identification_2_front",
    "identification_2_back", "individual_number"]
  var REQUIRED_FIELDS = ["submission_type", "individual_number"];
  var OPTION_1_FIELDS = ["identification_1_front", "identification_1_back"];
  var OPTION_2_FIELDS = ["identification_1_front", "identification_1_back", "identification_2_front",
    "identification_2_back"];
  var OPTION_3_FIELDS = ["identification_1_front", "identification_2_front", "identification_2_back"];
  var ALLOWED_IMG_TYPES = staffEntryFrmConsts.ALLOWED_IMG_TYPES;

  vm.registration = {};
  vm.old_registration = {};
  vm.submission_options = [];
  vm.fileSizeWithMb = FILE_SIZE_IN_MB + "MB";

  vm.init = function(partnerData) {
    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });

    vm.old_registration = !!partnerData ? partnerData : {};

    if (!vm.is_new_form) {
      _.forEach(REGIST_FIELDS, function(attr) {
        vm.registration[attr] = partnerData[attr];
      });
    }
  }

  vm.checkIndividualNumber = function() {
    individualNumberService.checkIndividualNumber({individual_number: vm.registration.individual_number})
      .then(function(res) {
      if (res.data.status) {
        vm.myNumberForm["registration[individual_number]"].$error = {};
        vm.myNumberForm["registration[individual_number]"].$valid = true;
      } else if (_.isEmpty(vm.myNumberForm["registration[individual_number]"].$error)) {
        vm.myNumberForm["registration[individual_number]"].$error = {invalid: true};
        vm.myNumberForm["registration[individual_number]"].$valid = false;
      }
    });
  };

  vm.getFieldText = function(fieldGroup) {
    if (!vm.registration.submission_type) {return "";}
    switch(vm.registration.submission_type) {
      case 1:
        if (fieldGroup == "identification_1") {
          return "individual_card";
        } else if (fieldGroup == "identificatioN_2") {
          return "";
        }
        break;
      case 2:
        if (fieldGroup == "identification_1") {
          return "notification_card";
        } else if (fieldGroup == "identification_2") {
          return "driver_license";
        }
        break;
      case 3:
        if (fieldGroup == "identification_1") {
          return "citizen_card";
        } else if (fieldGroup == "identification_2") {
          return "residence_card_driver_license";
        }
        break;
    }
  }

  vm.showImageField = function(field) {
    if (!vm.registration.submission_type) {return false;}
    var fileFields = getOptionFileFields(vm.registration.submission_type);
    return _.includes(fileFields, field) ? true : false;
  };

  vm.fileChanged = function(event) {
    var previewElm = event.target.getAttribute("target-preview");
    var field = event.target.getAttribute("model-name");
    var requiredFileFields = getOptionFileFields(vm.registration.submission_type);
    var type_changed = vm.registration.submission_type != vm.old_registration.submission_type;
    var src = type_changed ? angular.element("#" + field + "_blank").data("value") : angular.element("#" + field + "_old").data("value");

    if (!event.target.files[0]) {
      vm.$scope.$apply(function() {
        $("#" + field + "_file_field").val("");
        if (_.includes(requiredFileFields, field)) {
          vm.registration[field] = vm.is_new_form || type_changed ? "" : vm.old_registration[field];
          if (_.isEmpty(vm.registration[field])) {setValueAttr(field);}
        }
        $(previewElm).attr("src", src);
      });
      return;
    }

    var fileSizeInMb = event.target.files[0].size / 1024 / 1024; 

    if (!_.includes(ALLOWED_IMG_TYPES, event.target.files[0].type)) {
      vm.$scope.$apply(function() {
        $("#" + field + "_file_field").val("");
        if (_.includes(requiredFileFields, field)) {
          vm.registration[field] = vm.is_new_form || type_changed ? "" : vm.old_registration[field];
          if (_.isEmpty(vm.registration[field])) {setValueAttr(field);}
        }
        $(previewElm).attr("src", src);
      });
      return;
    }

    vm.$scope.$apply(function() {
      if (!_.isEqual(event.target.files.length, 0)) {
        eval("vm.registration")[field] = event.target.files[0].name;
        staffEntryFrmCommonService.previewImg(event, previewElm);
        if (fileSizeInMb > FILE_SIZE_IN_MB) {
          vm.registration[field] = "";
          vm.myNumberForm["registration[" + field + "]"].$error = {"file-max-size": true};
          vm.myNumberForm["registration[" + field + "]"].$valid = false;
        } else {
          vm.myNumberForm["registration[" + field + "]"].$error = {};
          vm.myNumberForm["registration[" + field + "]"].$valid = true;
        }
      } else {
        $(previewElm).attr("src", src);
      }
    });
  };

  vm.onClickFileField = function(input) {
    vm.fileInput = input;
    document.body.onfocus = checkFileField;
  };

  vm.resetImageFields = function() {
    var fileFields = ["identification_1_front", "identification_1_back", "identification_2_front",
      "identification_2_back"];
    var type_changed = vm.registration.submission_type != vm.old_registration.submission_type;
    fileFields.forEach(function(field) {
      vm.registration[field] = vm.is_new_form || type_changed ? "" : vm.old_registration[field];
      vm[field + "_touched"] = false;
      var src = type_changed ? angular.element("#" + field + "_blank").data("value") : angular.element("#" + field + "_old").data("value");
      $("." + field + "_preview").attr("src", src);
      $("#" + field + "_file_field").val("");
      if (!!vm.myNumberForm["registration[" + field + "]"]) {
        vm.myNumberForm["registration[" + field + "]"].$error = {};
        vm.myNumberForm["registration[" + field + "]"].$valid = true;
      }
    });
  };

  vm.errorClassForInput = function(field) {
    return {
      "form-error": !vm.myNumberForm["registration[" + field + "]"].$valid &&
        vm.myNumberForm["registration[" + field + "]"].$touched
    };
  };

  vm.errorClassForFileField = function(field) {
    return {
      "file-field-error-notice": vm.conditionForDispErrFileField(field)
    };
  };

  vm.conditionForDispErrFileField = function(field) {
    var condition = vm["" + field + "_touched"] && !vm.registration[field];
    return condition;
  };

  vm.registerMyNumber = function() {
    if (validateMyNumberForm()) {
      vm.submitForm();
    } else {
      window.scrollTo(0, 0);
      vm.formError = true;
      $(".disable-submit-btn").prop("disabled", false);
    }
  };

  vm.submitForm = function() {
    $(".disable-submit-btn").prop("disabled", true);
    $("#individual-number-form-submit").click();
  };

  $("#individual-number-form").on("submit", function(e) {
    $("#spinner").removeClass("ng-hide");
    e.preventDefault();
    var formData = formatFormData(this);

    $.ajax({
      url: "/individual_number_registrations",
      method: "POST",
      dataType: "json",
      data: formData,
      processData: false,
      contentType: false,
      success: function(res) {
        if(res.status) {
          vm.formError = false;
          $(".register-form").slideUp("slow");
          $(".register-finished").slideDown("slow");
          $("#spinner").addClass("ng-hide");
        } else {
          vm.$scope.$apply(function() {
            window.scrollTo(0, 0);
            vm.formError = true;
            $(".disable-submit-btn").prop("disabled", false);
            $("#spinner").addClass("ng-hide");
          });
        }
      }
    });
  });

  function validateMyNumberForm() {
    var flag = true;

    vm.submission_type_blur = true;
    var requiredFileFields = getOptionFileFields(vm.registration.submission_type);
    requiredFileFields.forEach(function(field) {
      vm["" + field + "_touched"] = true;
      if(_.isEmpty(vm.registration[field]) && _.isEmpty(vm.myNumberForm["registration[" + field + "]"].$error)) {
        setValueAttr(field);
      } else if (!_.isEmpty(vm.registration[field]) && !!vm.myNumberForm["registration[" + field + "]"].$error["file-not-selected"]) {
        vm.myNumberForm["registration[" + field + "]"].$error = {};
        vm.myNumberForm["registration[" + field + "]"].$valid = true;
      }
    });
    REQUIRED_FIELDS.concat(requiredFileFields).forEach(function(field) {
      vm.myNumberForm["registration[" + field + "]"].$touched = true;
      if (!vm.myNumberForm["registration[" + field + "]"].$valid) {
        flag = false;
      }
    });
    if (vm.registration.submission_type == 0) {
      flag = false;
    }
    return flag;
  };

  function setValueAttr(field) {
    vm.myNumberForm["registration[" + field + "]"].$error = {"file-not-selected": true};
    vm.myNumberForm["registration[" + field + "]"].$valid = false;
  }

  function checkFileField() {
    vm.$scope.$apply(function() {
      vm["" + vm.fileInput + "_touched"] = true;
    });
    if(_.isEmpty($("#" + vm.fileInput + "_file_field").val())) {
      vm.$scope.$apply(function() {
        var requiredFileFields = getOptionFileFields(vm.registration.submission_type);
        if (_.includes(requiredFileFields, vm.fileInput) &&
          _.isEmpty(vm.myNumberForm["registration[" + vm.fileInput + "]"].$error)) {
          setValueAttr(vm.fileInput);
        }
      });
    }
    document.body.onfocus = null;
  }

  function getOptionFileFields(option) {
    switch(option) {
      case 1:
        return OPTION_1_FIELDS;
      case 2:
        return OPTION_2_FIELDS;
      case 3:
        return OPTION_3_FIELDS;
      default:
        return [];
    }
  }

  function formatFormData(form) {
    var formData = new FormData(form);
    var requiredFileFields = getOptionFileFields(vm.registration.submission_type);
    var oldFiles = {};
    requiredFileFields.forEach(function(field) {
      oldFiles[field] = vm.old_registration[field]
    });
    formData.append("old_files", JSON.stringify(oldFiles));
    return formData;
  }

  function disableEnter(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 13) {
      e.preventDefault();
      $("input").blur();
    }
  };

  if (!_.isNull($(".individual-number-form"))) {
    $(".individual-number-form").keypress(function(e) {
      disableEnter(e);
    });
  }
}