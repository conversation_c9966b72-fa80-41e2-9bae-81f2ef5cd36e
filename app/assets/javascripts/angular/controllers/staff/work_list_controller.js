"use strict";

angular.module("staffApp")
  .controller("workListController", workListController);

workListController.$inject = ["$location", "$scope", "workListService"];

function workListController($location, $scope, workListService) {
  var vm = this;
  var LOCATION_TYPE = {
    not_lawson: "not_lawson",
    lawson: "ローソン",
    nature_lawson: "ナチュラルローソン",
    lawson_store_100: "ローソンストア100"
  };
  var BG_CLASS = {
    not_lawson: "bg-not-lawson",
    nature_lawson: "bg-nature-lawson-work-list",
    lawson_store_100: "bg-lawson-store-work-list"
  };
  var i18n_work_tag = I18n.t("staff.work_list.work_tag");
  var SORT_TYPES = ["work_time", "arranged_time"];
  var STAFF_APPLY_ORDER_CASE_REJECTED_STATUS = "rejected"

  vm.overviewTabs = [
    {
      id: 1,
      active: true,
      hashVal: "work_plan",
      name: I18n.t("staff.work_list.tab_panel_title.work_plan_list")
    },
    {
      id: 2,
      active: false,
      hashVal: "apply_history",
      name: I18n.t("staff.work_list.tab_panel_title.apply_history_list")
    },
  ];
  workListService.setCurrentTabName(vm.overviewTabs[0].hashVal);

  vm.$scope = $scope;

  vm.paramsWorkPlan = _.isEqual(workListService.getCurrentTabName(), vm.overviewTabs[0].hashVal) ? $location.search() : {};
  vm.paramsWorkPlan.per_page_work_list = 20;
  vm.paramsWorkPlan.page_work_list = vm.paramsWorkPlan.page ? vm.paramsWorkPlan.page : 1;
  vm.paramsWorkPlan.order_by = [{id: SORT_TYPES[0], name: I18n.t("staff.work_list.order_by.working_time")},
    {id: SORT_TYPES[1], name: I18n.t("staff.work_list.order_by.arrange_time")}];
  vm.paramsWorkPlan.order_type = _.includes(SORT_TYPES, vm.paramsWorkPlan.order_type) ? vm.paramsWorkPlan.order_type : SORT_TYPES[0];

  vm.paramsApplyHistory = _.isEqual(workListService.getCurrentTabName(), vm.overviewTabs[1].hashVal) ? $location.search() : {};
  vm.paramsApplyHistory.per_page_apply_history = 20;
  vm.paramsApplyHistory.page_apply_history = vm.paramsApplyHistory.page ? vm.paramsApplyHistory.page : 1;
  vm.paramsApplyHistory.order_by = [{id: SORT_TYPES[0], name: I18n.t("staff.work_list.order_by.working_time")},
    {id: SORT_TYPES[1], name: I18n.t("staff.work_list.order_by.arrange_time")}];
  vm.paramsApplyHistory.order_type = _.includes(SORT_TYPES, vm.paramsApplyHistory.order_type) ? 
    vm.paramsApplyHistory.order_type : SORT_TYPES[0];

  vm.initData = function() {
    var currentTabHash = $location.hash();
    var tab = vm.tabByHashVal(currentTabHash);
    if (tab) {
      vm.setTab(tab.id - 1);
    }
  };

  vm.tabByHashVal = function(hashVal) {
    return vm.overviewTabs.filter(function(step) {
      return _.isEqual(hashVal, step.hashVal)
    })[0];
  };

  vm.currentTab = function() {
    return vm.overviewTabs.filter(function(step) {
      return step.active;
    })[0].id;
  };

  vm.$scope.$watch(function () {
    return workListService.getCurrentTabName();
  }, function (name) {
    if (_.isEqual(name, vm.overviewTabs[0].hashVal)) $location.search(paramsWorkPlan()).replace();
    if (_.isEqual(name, vm.overviewTabs[1].hashVal)) $location.search(paramsApplyHistory()).replace();
  });

  vm.setTab = function(tabId) {
    var currentTab = vm.currentTab();
    if (!_.isUndefined(vm.overviewTabs[tabId])) {
      vm.overviewTabs[currentTab - 1].active = false;
      vm.overviewTabs[tabId].active = true;
      workListService.setCurrentTabName(vm.overviewTabs[tabId].hashVal);
    }
  }

  vm.$scope.$watch("vm.paramsWorkPlan.order_type", function() {
    vm.refreshWorkPlan();
  });

  vm.$scope.$watch("vm.paramsApplyHistory.order_type", function() {
    vm.refreshApplyHistory();
  });

  vm.needDisable = function(applyOrderCase) {
    var arrangement = applyOrderCase.arrangement;
    var isDisableStatus = !_.isUndefined(arrangement) &&
      _.includes(["absence", "cancel_after_arrange_has_insurance", "cancel_after_arrange_no_insurance","absence_has_alternative"], arrangement.status_arrangement);
    return vm.isRejected(applyOrderCase) || isDisableStatus
  }

  vm.isRejected = function(applyOrderCase) {
    return applyOrderCase.status_id === STAFF_APPLY_ORDER_CASE_REJECTED_STATUS ||
      applyOrderCase.is_cancel;
  };

  vm.setLayoutWorkList = function(applyOrderCase) {
    var arrangement = applyOrderCase.arrangement;
    var orderCase = applyOrderCase.order_case;
    if(_.isEmpty(arrangement)) {
      setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.apply"), "worklist-status--applying");
    } else {
      var condition = arrangement.status_arrangement || orderCase.status_id;
      switch (condition) {
        case "absence":
          setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.absence"), "worklist-status--canceled",
            null, i18n_work_tag);
          break;
        case "cancel_after_arrange_has_insurance":
          setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.cancel_after_arrange_has_insurance"), "worklist-status--canceled",
            null, i18n_work_tag);
          break;
        case "not_input":
          setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.not_input"), "worklist-status--time-enter",
            I18n.t("staff.work_list.button.not_input"), i18n_work_tag);
          break;
        case "pending_approve_and_not_staff_confirming":
          setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.pending_approve"), "worklist-status--wait-approve",
            null, i18n_work_tag);
          break;
        case "pending_approve_and_staff_confirming":
          setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.pending_approve"), "worklist-status--wait-approve",
            I18n.t("staff.work_list.button.pending_approve"), i18n_work_tag);
          break;
        case "prepared_going_work":
          setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.prepared_going_work"), "worklist-status--not-report",
            I18n.t("staff.work_list.button.prepared_going_work"), i18n_work_tag);
          break;
        case "arranged":
          setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.arranged"), "worklist-status--confirm");
          break;
        case "valid_status":
          setAttr(orderCase, I18n.t("corporation.order_case.order_case_list_page.status_ids.valid_status"), "worklist-status--confirm");
          break;
        case "cancel":
          setAttr(orderCase, I18n.t("corporation.order_case.order_case_list_page.status_ids.cancel"), "worklist-status--confirm");
          break;
        case "no_arrange":
          setAttr(orderCase, I18n.t("corporation.order_case.order_case_list_page.status_ids.no_arrange"), "worklist-status--work-done");
          break;
        case "cancel_after_arrange_no_insurance":
          setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.cancel_after_arrange_no_insurance"), "worklist-status--canceled",
            null, i18n_work_tag);
          break;
        case "absence_has_alternative":
          setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.absence_has_alternative"), "worklist-status--canceled",
            null, i18n_work_tag);
          break;
        case "applied":
          setAttr(orderCase, I18n.t("staff.work_list.status_arrangement.apply"), "worklist-status--applying");
          break;
      }
    }
  }

  function setAttr(orderCase, labelText, className, buttonText, workTag) {
    orderCase.label_text = labelText;
    orderCase.class_name = className;
    orderCase.button_text = buttonText;
    orderCase.work_tag = workTag;
  }

  vm.getWorkingTime = function(applyOrderCase) {
    if(_.isEmpty(applyOrderCase.arrangement)) {
      return applyOrderCase.order_case.working_time;
    } else {
      return applyOrderCase.arrangement.working_time;
    }
  }

  vm.refreshWorkPlan = function() {
    var paramSearch = paramsWorkPlan();
    workListService.loadDataArrangedWorkList(paramSearch).then(function mySuccess(res){
      angular.extend(vm, res.data)
      _.forEach(vm.arranged_apply_order_cases, function(applyOrderCase, _key) {
        vm.setLayoutWorkList(applyOrderCase);
      });
      if (_.isEqual(workListService.getCurrentTabName(), vm.overviewTabs[0].hashVal))
        $location.search(paramSearch).replace();
    });
  }

  vm.refreshApplyHistory = function() {
    var paramSearch = paramsApplyHistory();
    workListService.loadDataWorkList(paramSearch).then(function mySuccess(res){
      angular.extend(vm, res.data)
      _.forEach(vm.apply_order_cases, function(applyOrderCase, _key) {
        vm.setLayoutWorkList(applyOrderCase);
      });
      if (_.isEqual(workListService.getCurrentTabName(), vm.overviewTabs[1].hashVal))
        $location.search(paramSearch).replace();
    });
  }

  vm.getFeeFlag = function(order_case_id) {
    var currentTab = vm.currentTab();
    var order_case_by_staff_fees = vm.order_case_by_staff_fees_work_plan;
    if (currentTab != 1) {
      order_case_by_staff_fees = vm.order_case_by_staff_fees_apply_history;
    };
    var order_case_by_staff_fee = _.find(order_case_by_staff_fees, function(order_case_by_staff_fee) {
      return order_case_by_staff_fee.order_case_id == order_case_id;
    });
    var fee_flags = [];
    if (!_.isUndefined(order_case_by_staff_fee)) {
      if (!_.isUndefined(order_case_by_staff_fee.fee)) {
        fee_flags = order_case_by_staff_fee.fee;
      }
    }
    return fee_flags;
  };

  vm.moveToDetail = function(orderCase) {
    if (!orderCase.button_text) {
      location.href = "/" + I18n.locale + "/order_cases/" + orderCase.id;
    }
  };

  function paramsWorkPlan() {
    return {
      page: vm.paramsWorkPlan.page_work_list,
      per_page: vm.paramsWorkPlan.per_page_work_list,
      order_type: vm.paramsWorkPlan.order_type}
  };

  function paramsApplyHistory() {
    return {
      page: vm.paramsApplyHistory.page_apply_history,
      per_page: vm.paramsApplyHistory.per_page_apply_history,
      order_type: vm.paramsApplyHistory.order_type}
  };

  vm.bgImageForLocation = function(locationType, thumbnailPath) {
    if ((!!thumbnailPath && thumbnailPath != "") || locationType === LOCATION_TYPE.not_lawson) {
      return BG_CLASS.not_lawson;
    }
    if (locationType === LOCATION_TYPE.lawson_store_100) {
      return BG_CLASS.lawson_store_100;
    }
    if (locationType === LOCATION_TYPE.nature_lawson) {
      return BG_CLASS.nature_lawson;
    }
  };
}
