"use strict";

angular.module("staffApp")
  .controller("OrderCaseDetailController", OrderCaseDetailController);
OrderCaseDetailController.$inject = ["$location", "$scope", "orderCaseService", "$window"];

function OrderCaseDetailController($location, $scope, orderCaseService, $window) {
  var vm = this;
  vm.$scope = $scope;
  
  var LOCALE = I18n.locale;
  var CONTRACT_SEGMENT = "contract";
  var APPLY_CONDITION_CONTACT_OP = "apply_condition_contact_op";
  var mappingArrangementModals = {
    need_talking_count: "#work-modal-contact",
    has_problem_count: "#work-modal-contact",
    is_arrived: "#work-modal-confirm"
  }

  vm.orderCase = {};
  vm.apply_params = {};
  vm.apply_errors = {};
  vm.apply_warnings = {};
  vm.evaluation_errors = {};
  vm.apply_params.is_used_original_condition = true;
  vm.starViews = [{selected: true, value: 1}, {selected: false, value: 2}, {selected: false, value: 3},
    {selected: false, value: 4}, {selected: false, value: 5}];
  vm.maxEvaluation = 1;
  vm.isMobile = /Mobile|webOS/i.test(navigator.userAgent);
  vm.isHideSimilarOrderCases = $("#data-init").data("isHideSimilar")
  vm.similar_order_cases = []
  vm.apply_params.accepted_out_of_contract = false;
  vm.responseData = {};
  vm.insurance_params = {
    new_insurance: "new_insurance",
    insurance_number: ""
  };
  vm.disableApplyOrderCase = false;

  vm.$scope.$watch("vm.insurance_params.new_insurance", function() {
    if (vm.insurance_params.new_insurance == "new_insurance") {
      vm.insurance_params.insurance_number = "";
    }
  });

  vm.checkWarning = function() {
    if (_.isEmpty(vm.apply_warnings)) {
      vm.applyOrderCase();
    } else {
      $("#work-modal").modal("hide");
      $("#modal-warning-end-of-contract").modal("show");
    }
  };

  vm.showContactModal = function() {
    $("#modal-warning-end-of-contract").modal("hide");
    $("#modal-contact-op-center-contract-renewal").modal("show");
  };

  vm.applyOrderCase = function() {
    $("#spinner").removeClass("ng-hide");
    vm.apply_params.location_id = vm.orderCase.location_id;
    vm.apply_params.order_case_id = vm.orderCase.id;
    vm.apply_params.order_branch_id = vm.orderCase.order_branch_id;
    vm.apply_params.order_id = vm.orderCase.order_id;
    vm.responseData = {};

    orderCaseService.applyOrderCase({apply_params: vm.apply_params}, LOCALE).then(function(res) {
      if (res.data.status) {
        $("#modal-warning-work-over-time").modal("hide");
        $("#modal-warning-work-over-time-in-week").modal("hide");
        $("#modal-warning-end-of-contract").modal("hide");
        $("#work-modal").modal("hide");
        $("#spinner").addClass("ng-hide");
        $("#work-modal-finish").modal("show");
      } else {
        if (!_.isEmpty(res.data.errors)) {
          $("#spinner").addClass("ng-hide");
          vm.apply_errors = res.data.errors;
        } else if (!_.isEmpty(res.data.apply_condition_errors)) {
          vm.applyConditionMessage = res.data.apply_condition_errors;
          $("#work-modal").modal("hide");
          $("#spinner").addClass("ng-hide");
          $("#modal-apply-error").modal("show");
        } else if (!_.isEmpty(res.data.apply_warnings) || !res.data.warning_over_time.status) {
          $("#work-modal").modal("hide");
          vm.responseData = angular.copy(res.data);
          vm.displayWarnings();
        } else {
          $("#spinner").addClass("ng-hide");
        }
      }
    });
  }

  vm.displayWarnings = function() {
    if (!_.isEmpty(vm.responseData.apply_warnings.contract_warning) && !vm.apply_params.accepted_out_of_contract) {
      vm.apply_warnings = vm.responseData.apply_warnings.contract_warning[0];
      warningEndOfContract();
    }
    if (!vm.responseData.warning_over_time.status && 
      (_.isEmpty(vm.responseData.apply_warnings.contract_warning) || vm.apply_params.accepted_out_of_contract)) {
      var data = vm.responseData.warning_over_time;
      vm.apply_params.accepted_ot = _.isEmpty(data.warning_message);
      vm.apply_params.accepted_ot_in_week = _.isEmpty(data.warning_in_week_message);
      vm.warningOT = {
        warningMessage: data.warning_message,
        warningInWeekMessage: data.warning_in_week_message,
        mobileDevice: data.mobile_device, note: data.note,
        opCenterTel: data.op_center_tel 
      };
      warningOverTime(data);
    }
  }

  function warningEndOfContract() {
    $("#spinner").addClass("ng-hide");
    $("#modal-warning-end-of-contract").modal("show");
  }

  function warningOverTime(response) {
    $("#modal-warning-end-of-contract").modal("hide");
    $("#work-modal").modal("hide");
    $("#spinner").addClass("ng-hide");
    if (!_.isEmpty(response.warning_message)) {
      $("#modal-warning-work-over-time").modal("show");
    } else {
      $("#modal-warning-work-over-time-in-week").modal("show");
    }
  }

  vm.initOrderCase = function(orderCase, likedLocation, currentKeptId, locationEvaluation,
    isOpConfirm, isMatchbox, hideSimilarOrderCases, staffSignedIn) {
    vm.orderCase = orderCase;
    vm.likedLocation = likedLocation;
    vm.keptIds = currentKeptId;
    vm.locationEvaluation = locationEvaluation;
    vm.isOpConfirm = isOpConfirm;
    vm.isMatchbox = isMatchbox;
    vm.isHideSimilarOrderCases = hideSimilarOrderCases;
    vm.staffSignedIn = staffSignedIn;
    vm.saveCurrentVisited();
    vm.getSimilarOrderCases();
    if (vm.orderCase.is_midnight) vm.disableApplyOrderCase = true;
  };

  vm.saveCurrentVisited = function() {
    if (!vm.staffSignedIn) {
      localStorage.setItem("current_visited_job", vm.orderCase.id);
    } else {
      localStorage.removeItem("current_visited_job");
    }
  }

  vm.getSimilarOrderCases = function() {
    if (vm.isHideSimilarOrderCases) {
      return;
    }
    vm.similar_order_cases = []
    orderCaseService.getSimilarOrderCases(vm.orderCase.id, LOCALE).then(function(res) {
      if (res.status == 200) vm.similar_order_cases = res.data.similar_order_cases;
    });
  };

  vm.keepOrderCase = function(orderCaseId) {
    if (!vm.staffSignedIn) {
      location.href = "/" + I18n.locale + "/login";
      return;
    }
    orderCaseService.createStaffKeepOrderCase({order_case_id: orderCaseId}, LOCALE).then(function(res) {
      if (res.data.status) {
        vm.keptIds.push(orderCaseId);
      } else {
        _.remove(vm.keptIds, function (id) {return id === orderCaseId});
      }
      vm.checkLikedSimilarOrder(orderCaseId);
    });
  };

  vm.likeLocation = function() {
    if (!vm.staffSignedIn) {
      location.href = "/" + I18n.locale + "/login";
      return;
    }
    orderCaseService.createStaffLikeLocation({order_case_id: vm.orderCase.id}, LOCALE).then(function(res) {
      vm.likedLocation = res.data.status;
    });
  };

  vm.changeAgreeToBeOnTime = function() {
    vm.disableApplyOrderCase = !vm.agree_to_be_on_time;
  };

  vm.checkLikedSimilarOrder = function(orderCaseId) {
    return _.includes(vm.keptIds, orderCaseId);
  };

  vm.updateArrangement = function(updateField, isMobile) {
    $(".disable-submit-btn").prop("disabled", true);
    var updateParams = {};
    updateParams[updateField] = true;
    orderCaseService.updateArrangement({arrangement: updateParams}, vm.arrangementId).then(function(res) {
      if (res.data.status) {
        if (updateField === "is_prepared") {
          location.reload();
        } else {
          if (!isMobile) {
            $(mappingArrangementModals[updateField]).modal("show");
          }
          $(".disable-submit-btn").prop("disabled", false);
        }
      }
    });
  }

  vm.checkInsurance = function(achievementId) {
    vm.insurance_params.order_case_id = vm.orderCase.id;
    orderCaseService.checkInsuranceForWorkAchievement(vm.insurance_params).then(function(res) {
      if (res.data.status) {
        $("#modal-input-insurance").modal("show");
      } else {
        vm.redirectToInputWorkTime(achievementId);
      }
    });
  };

  vm.redirectToInputWorkTime = function(achievementId, saveInsurance) {
    setInsuranceNumberTouched();
    if(!validateInsuranceInput()) {return;}
    if (saveInsurance) {
      $('#spinner').removeClass('ng-hide');
      $(".disable-submit-btn").prop("disabled", true);
      orderCaseService.saveEmploymentInsurance(vm.insurance_params).then(function(res) {
        if (res.data.status) {
          location.href = "/" + LOCALE + "/work_achievements/" + achievementId + "/input_work_time";
        } else {
          $('#spinner').addClass('ng-hide');
          $(".disable-submit-btn").prop("disabled", false);
        }
      });
    } else {
      location.href = "/" + LOCALE + "/work_achievements/" + achievementId + "/input_work_time";
    }
  };

  vm.openInstructionModal = function() {
    $("#modal-input-insurance-instruction").modal("show");
  };

  vm.errorClassForInput = function(inputName) {
    if (!vm.employmentInsuranceForm[inputName]) return;
    return {
      "form-error": !vm.employmentInsuranceForm[inputName].$valid && vm.employmentInsuranceForm[inputName].$touched
    }
  };

  function setInsuranceNumberTouched() {
    vm.employmentInsuranceForm.employment_insurance_number.$touched = true;
  }

  function validateInsuranceInput() {
    if (vm.insurance_params.new_insurance == "input_insurance" && _.isEmpty(vm.insurance_params.insurance_number)) {
      return false;
    }
    return vm.employmentInsuranceForm.employment_insurance_number.$valid;
  }

  vm.redirectToConfirmWorkTime = function(achievementId) {
    location.href = "/" + LOCALE + "/work_achievements/" + achievementId + "/confirm_work_time";
  }

  vm.initMap = function(locationInfo) {
    var map;
    var locationLatLng = new google.maps.LatLng(locationInfo.latitude, locationInfo.longitude);
    var mapOptions = {
      zoom: 15,
      center: locationLatLng
    };
    map = new google.maps.Map(document.getElementById('location-map'), mapOptions);

    var marker = new google.maps.Marker({
      position: locationLatLng,
      map: map
    });

    var infowindow = new google.maps.InfoWindow({
      content: '<p>Marker Location:' + marker.getPosition() + '</p>'
    });

    google.maps.event.addListener(marker, 'click', function() {
      infowindow.open(map, marker);
    });
  }

  vm.createLocationEvaluation = function() {
    if (!vm.staffSignedIn) {
      return;
    }
    $(".disable-submit-btn").prop("disabled", true);
    var params = {arrangement_id: vm.arrangementId, evaluation: vm.maxEvaluation, evaluation_comment: vm.comment};
    orderCaseService.createLocationEvaluation({location_evaluation: params}).then(function(res) {
      var data = res.data;
      if (data.status) {
        vm.locationEvaluation = data.location_evaluation;
        vm.changeActivedStar();
        $("#work-modal-review").modal("hide");
        $(".disable-submit-btn").prop("disabled", false);
      } else {
        vm.evaluation_errors = data.errors;
        $(".disable-submit-btn").prop("disabled", false);
      }
    });
  }

  vm.reviewWork = function() {
    if (!_.isEmpty(vm.locationEvaluation)) {
      _.forEach(vm.starViews, function(currentStar) {
        currentStar.selected = currentStar.value <= vm.locationEvaluation.evaluation;
      });
      vm.comment = vm.locationEvaluation.evaluation_comment;
    }
  }

  vm.changeActivedStar = function(star) {
    if (_.isEmpty(vm.locationEvaluation)) {
      _.forEach(vm.starViews, function(currentStar) {
        currentStar.selected = currentStar.value <= star.value;
      });
    }
    calMaxEvaluation();
  }

  function calMaxEvaluation() {
    var maxEvaluation = _.maxBy(vm.starViews, function(star) {if (star.selected) return star.value;});
    vm.maxEvaluation = _.isEmpty(maxEvaluation) ? 1 : maxEvaluation.value;
  }

  vm.resetReviewModal = function() {
    _.forEach(vm.starViews, function(currentStar) {
      currentStar.selected = currentStar.value === 1;
    });
    vm.comment = "";
  }

  vm.moveToDetail = function(orderCaseId) {
    location.href = "/" + I18n.locale + "/order_cases/" + orderCaseId;
  };

  vm.backToList = function(queryString, locationId) {
    var url = "/" + I18n.locale + "/order_cases?" + queryString;
    if (vm.isMobile) {
      url = "/" + I18n.locale + "/locations/" + locationId + "/jobs";
    }
    location.href = url;
  };

  vm.updateWorkAchievement = function(workAchievementId) {
    var submitParams = {work_achievements: {is_added_calendar: true}};
    orderCaseService.updateWorkAchievement(workAchievementId, submitParams).then(function(res) {
      if (res.data.status) {
        location.reload();
      }
    });
  }

  vm.recruitingForm = function(orderCaseId) {
    $window.open("/" + LOCALE +  "/recruiting_form/" + orderCaseId);
  }

  vm.checkApplyCondition = function(orderCaseId) {
    if (!vm.staffSignedIn) {
      location.href = "/" + I18n.locale + "/login";
      return;
    }
    if(!vm.isOpConfirm && !vm.isMatchbox && vm.orderCase.segment_id != CONTRACT_SEGMENT){
      $("#work-modal").modal("show");
      return;
    }
    orderCaseService.checkApplyCondition({order_case_id: orderCaseId}).then(function(res) {
      if (res.data.status) {
        orderCaseService.checkInsuranceOfStaff({order_case_id: orderCaseId}).then(function(res){
          if (res.data.status) {
            vm.apply_errors = {};
            $("#work-modal").modal("show");
          } else {
            vm.insuranceErrorMessage = res.data.errors;
            vm.insuranceNotificationMessage = res.data.notification;
            $("#modal-insurance-error").modal("show");
          }
        });
      } else {
        vm.applyConditionMessage = res.data.errors;
        $("#modal-apply-error").modal("show");
      }
    });
  }

  vm.acceptJoinInsurance = function() {
    vm.apply_params.accepted_apply = true;
    $("#modal-insurance-error").modal("hide");
    $("#work-modal").modal("show");
  }

  vm.cancelJoinInsurance = function() {
    vm.apply_params.accepted_apply = false;
    $("#modal-insurance-error").modal("hide");
    $("#modal-insurance-notification").modal("show");
  }

  vm.closeModalJoinInsuranceError = function() {
    $("#modal-insurance-notification").modal("hide");
  }

  vm.refusesToWorkOT = function() {
    vm.apply_params.accepted_ot = false;
    vm.apply_params.accepted_out_of_contract = false;
    $("#modal-warning-work-over-time").modal("hide");
    $("#modal-contact-op-center").modal("show");
  }

  vm.acceptToWorkOT = function() {
    vm.apply_params.accepted_ot = true;
    if (!_.isEmpty(vm.warningOT.warningInWeekMessage)) {
      $("#modal-warning-work-over-time").modal("hide");
      $("#modal-warning-work-over-time-in-week").modal("show");
    } else {
      vm.applyOrderCase();
    }
  }

  vm.acceptToWorkOtInWeek = function() {
    vm.apply_params.accepted_ot_in_week = true;
    vm.applyOrderCase();
  }

  vm.refusesToWorkOtInWeek = function() {
    vm.apply_params.accepted_ot_in_week = false;
    vm.apply_params.accepted_out_of_contract = false;
    $("#modal-warning-work-over-time-in-week").modal("hide");
    $("#modal-contact-op-center").modal("show");
  }

  vm.acceptToApplyOutOfContract = function() {
    $("#modal-warning-end-of-contract").modal("hide");
    vm.apply_params.accepted_out_of_contract = true;
    if (vm.responseData.warning_over_time.status) {
      vm.applyOrderCase();
    } else {
      vm.displayWarnings();
    }
  }

  vm.refuseToApplyOutOfContract = function() {
    vm.apply_params.accepted_out_of_contract = false;
    $("#modal-warning-end-of-contract").modal("hide");
    $("#modal-contact-op-center-contract-renewal").modal("show");
  }

  vm.bgImageForLocation = function(locationType, categoryThumbnail) {
    return orderCaseService.bgImageForLocation(locationType, categoryThumbnail);
  };

  vm.bgForNotLawson = function(locationType, locationImage, categoryThumbnail) {
    return orderCaseService.bgForNotLawson(locationType, locationImage, categoryThumbnail);
  };

  vm.bgForNotLawsonBigSize = function(locationType, locationImage, categoryThumbnail) {
    return orderCaseService.bgForNotLawsonBigSize(locationType, locationImage, categoryThumbnail);
  }

  vm.openRegularTimeModal = function() {
    $("#regular-time-modal").modal("show");
  }

  vm.displayApplyDoneNote = function() {
    return !_.isEmpty(vm.apply_warnings);
  }

  vm.goBackToSearchResult = function() {
    location.href = "/" + I18n.locale + "/order_cases";
  }

  vm.isContactDisplayed = function() {
    if (_.isUndefined(vm.applyConditionMessage)) return false;
    var errorType = Object.keys(vm.applyConditionMessage)[0]
    return errorType == APPLY_CONDITION_CONTACT_OP;
  }

  vm.getErrorString = function() {
    if (_.isUndefined(vm.applyConditionMessage)) return "";
    return Object.values(vm.applyConditionMessage)[0][0];
  }
}
