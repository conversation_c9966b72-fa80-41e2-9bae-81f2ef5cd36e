"use strict";

angular.module("staffApp").controller("RegisteredProfileDetailController", RegisteredProfileDetailController);
RegisteredProfileDetailController.$inject = ["$location", "$scope"];
 
function RegisteredProfileDetailController($location, $scope) {
  var vm = this;

  vm.$scope = $scope;

  vm.overviewTabs = [
    {
      id: 1,
      active: true,
      hashVal: "basic_info",
      name: I18n.t("staff.profile.tab_panel_title.basic_info")
    },
    {
      id: 2,
      active: false,
      hashVal: "account_transfer",
      name: I18n.t("staff.profile.tab_panel_title.account_transfer")
    }
  ];

  vm.initData = function() {
    var currentTabHash = $location.hash();
    var tab = vm.tabByHashVal(currentTabHash);
    if (tab) {
      vm.setTab(tab.id - 1);
    }
  };

  vm.currentTab = function() {
    return vm.overviewTabs.filter(function(step) {
      return step.active;
    })[0].id;
  };

  vm.tabByHashVal = function(hashVal) {
    return vm.overviewTabs.filter(function(step) {
      return _.isEqual(hashVal, step.hashVal)
    })[0];
  };

  vm.setTab = function(tabId) {
    var currentTab = vm.currentTab();
    if (!_.isUndefined(vm.overviewTabs[tabId])) {
      vm.overviewTabs[currentTab - 1].active = false;
      vm.overviewTabs[tabId].active = true;
    }
  };
  
  vm.redirectPage = function(href) {
    $(".disable-submit-btn").prop("disabled", true);
    location.href = href;
  };
}
