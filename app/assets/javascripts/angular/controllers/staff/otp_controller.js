"use strict"

angular.module("staffApp").controller("OtpController", OtpController);
OtpController.$inject = ["$scope", "$window", "$interval", "otpVerificationService"]

function OtpController($scope, $window, $interval, otpVerificationService) {
  var vm = this;
  vm.$scope = $scope;
  vm.successMessage = null;
  vm.errorMessage = null;
  vm.params = {otp: ""};
  vm.canResend = false;
  vm.initSuccess = false;

  vm.init = function(token, login_id) {
    vm.params.token = token;
    vm.params.login_id = login_id;
    vm.remainSeconds = OTP_RESEND_INTERVAL;
    vm.initSuccess = true;
    startCountdown();
  }

  vm.resetMessage = function() {
    vm.successMessage = null;
    vm.errorMessage = null;
  }

  vm.verifyOtp = function() {
    $("#spinner").removeClass("ng-hide");
    vm.resetMessage();
    otpVerificationService.verifyOtp(vm.params).then(function(res) {
      if (res.data.status) {
        $window.location.href = res.data.redirect_path;
      } else {
        vm.successMessage = res.data.message;
        vm.errorMessage = res.data.error;
        $("#spinner").addClass("ng-hide");
      }
    });
  }

  vm.resendOtp = function() {
    var resendOtpParams = _.omit(vm.params, "otp");
    vm.canResend = false;
    vm.remainSeconds = OTP_RESEND_INTERVAL;
    startCountdown();
    vm.resetMessage();
    otpVerificationService.resendOtp(resendOtpParams).then(function(res) {
      vm.successMessage = res.data.message;
      vm.errorMessage = res.data.error;
    });
  }

  function startCountdown() {
    vm.remainSeconds = OTP_RESEND_INTERVAL;
    if (vm.canResend) return;
    var resendCycle = $interval(function() {
      if (vm.remainSeconds > 0) {
        vm.remainSeconds -= 1;
      } else {
        $interval.cancel(resendCycle);
        vm.canResend = true;
      }
    }, 1000);
  }

  vm.isOtpValid = function() {
    var isEmpty = _.isEmpty(vm.params.otp.trim());
    return vm.params.otp.match(OTP_REGEX) && !isEmpty
  }
}