"use strict";

angular.module("staffApp")
  .controller("StaffScheduleController", StaffScheduleController);
StaffScheduleController.$inject = ["$scope", "staffScheduleService", "toaster"];

function StaffScheduleController($scope, staffScheduleService, toaster) {
  var vm = this;
  var MAX_POS = 5, MIN_POS = 0;
  vm.MAX_POS = MAX_POS, vm.MIN_POS = MIN_POS;
  vm.currentPos = 1;
  vm.currentWeekSchedule = {};
  vm.toasterTimeout = 6200;

  vm.init = function() {
    staffScheduleService.list().then(function(res) {
      vm.allSchedules = JSON.parse(res.data.schedules);
      vm.editable = JSON.parse(res.data.editable)
      vm.currentWeekSchedule = vm.allSchedules[vm.currentPos];
    });
  };

  vm.prevWeek = function() {
    if (vm.currentPos > MIN_POS) {
      vm.currentPos--;
      vm.currentWeekSchedule = vm.allSchedules[vm.currentPos];
    }
  };

  vm.nextWeek = function() {
    if (vm.currentPos < MAX_POS) {
      vm.currentPos++;
      vm.currentWeekSchedule = vm.allSchedules[vm.currentPos];
    }
  };

  vm.scheduleClick = function(weekDaySchedule, time) {
    weekDaySchedule.data[time] = !weekDaySchedule.data[time];
    weekDaySchedule.data._destroy = !weekDaySchedule.data.is_morning && !weekDaySchedule.data.is_afternoon &&
      !weekDaySchedule.data.is_evening && !weekDaySchedule.data.is_night;
  };

  vm.copySchedule = function() {
    if (vm.currentPos == MIN_POS) return;
    var prevWeekSchedule = vm.allSchedules[vm.currentPos - 1];
    vm.currentWeekSchedule.week_days.forEach(function(weekDay, index) {
      if (!weekDay.data.disable_morning) weekDay.data.is_morning = prevWeekSchedule.week_days[index].data.is_morning;
      if (!weekDay.data.disable_afternoon) weekDay.data.is_afternoon = prevWeekSchedule.week_days[index].data.is_afternoon;
      if (!weekDay.data.disable_evening) weekDay.data.is_evening = prevWeekSchedule.week_days[index].data.is_evening;
      if (!weekDay.data.disable_night) weekDay.data.is_night = prevWeekSchedule.week_days[index].data.is_night;
      weekDay.data._destroy = !weekDay.data.is_morning && !weekDay.data.is_afternoon &&
        !weekDay.data.is_evening && !weekDay.data.is_night;
    });
  };

  vm.updateSchedules = function() {
    $(".disable-submit-btn").prop("disabled", true);
    var allSchedules = [];
    vm.allSchedules.forEach(function(weekSchedule) {
      allSchedules.push(weekSchedule.week_days);
    });
    allSchedules = _.flattenDeep(allSchedules);
    var scheduleParams = allSchedules.filter(function(schedule) {
      return !schedule.is_in_the_past && (!!schedule.data.id || schedule.data.is_morning || schedule.data.is_afternoon
        || schedule.data.is_evening || schedule.data.is_night);
    }).map(function(schedule) {
      return schedule.data;
    });

    staffScheduleService.update({staff: {staff_schedules_attributes: scheduleParams}}).then(function(res) {
      if (res.data.status) {
        vm.allSchedules = JSON.parse(res.data.schedules);
        vm.currentWeekSchedule = vm.allSchedules[vm.currentPos];
        toaster.pop("success", "", res.data.message);
      } else {
        toaster.pop("error", "", res.data.message);
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  };

  vm.disableAllDay = function(data) {
    return data.disable_morning && data.disable_afternoon && data.disable_evening && data.disable_night;
  };
}
