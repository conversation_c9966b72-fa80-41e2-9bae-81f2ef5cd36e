"use strict";

angular.module("staffApp")
  .controller("StaffContractsController", StaffContractsController);

StaffContractsController.$inject = ["$location", "staffContractService"];

function StaffContractsController($location, staffContractService) {
  var vm = this;
  var INPUT_FIELDS = ["monday_start_time_1", "monday_end_time_1", "tuesday_start_time_1", "tuesday_end_time_1",
    "wednesday_start_time_1", "wednesday_end_time_1", "friday_start_time_1", "friday_end_time_1", "thursday_start_time_1",
    "thursday_end_time_1", "saturday_start_time_1", "saturday_end_time_1", "sunday_start_time_1", "sunday_end_time_1"];
  var BOOLEAN_FIELDS = ["is_sunday", "is_monday", "is_tuesday", "is_wednesday", "is_thursday", "is_friday", "is_saturday"];
  var ALL_FIELDS = INPUT_FIELDS.concat(BOOLEAN_FIELDS).concat(["work_condition"]);
  var IS_REQUEST_UPDATE = Boolean($("#is_request_update").data("infos"));
  var HAKEN = "haken";
  var AGE_60 = 60;
  vm.contract_code = $("#contract_code").data("infos");
  vm.i18nWorkCondition = I18n.t("enum_label.staff_contract.work_conditions");
  vm.i18nWorkConditionOfNewStaff = I18n.t("enum_label.staff_contract.work_conditions_of_new_staff");
  vm.i18nInsurance = I18n.t("staff.staff_contract.insurance");
  vm.isDisabledSubmit = false;
  vm.staff_contract = {};
  vm.init_work_condition = "";
  vm.isEdit = false;
  vm.warningInsuranceMessage = "";
  vm.rejectInsuranceMessage = "";
  vm.warningAccepted = false;
  var $body = $("body");
  var $popupWarningInsurance = $("#modal-insurance-warning");
  var $popupRejectInsurance = $("#modal-insurance-reject");
  var $popupInsuranceNotification = $("#modal-insurance-notification");
  vm.overviewSteps = [
    {
      id: 1,
      active: true
    },
    {
      id: 2,
      active: false
    }
  ];

  vm.currentStep = function() {
    return vm.overviewSteps.filter(function(step) {
      return step.active;
    })[0].id;
  };

  vm.init = function() {
    staffContractService.getData(vm.contract_code).then(function mySuccess(res) {
      angular.extend(vm, res.data);
      vm.showModalConfirmAge();
      vm.init_work_condition = angular.copy(vm.staff_contract.work_condition);
    });
  }

  vm.initEdit = function() {
    staffContractService.getDataEdit(vm.contract_code).then(function mySuccess(res) {
      angular.extend(vm, res.data);
      _.forEach(ALL_FIELDS, function(field) {
        vm.staff_contract[field] = vm.staff_expectation[field];
      });
      vm.init_work_condition = angular.copy(vm.staff_contract.work_condition);
    });
  }

  vm.nextStep = function(isEdit) {
    vm.closePopupWarningInsurance();
    var currentStep = vm.overviewSteps[vm.currentStep() - 1];
    var nextStep = vm.overviewSteps[vm.currentStep()];
    currentStep.active = false;
    currentStep.done = true;
    nextStep.active = true;
    vm.warningAccepted = true;
    vm.isEdit = isEdit;
    window.scrollTo(0, 0);
  }

  vm.previousStep = function() {
    var currentStep = vm.overviewSteps[vm.currentStep() - 1];
    var prevStep = vm.overviewSteps[vm.currentStep() - 2];
    currentStep.active = false;
    prevStep.active = true;
    prevStep.done = false;
    window.scrollTo(0, 0);
  };

  vm.saveOrCancelStaffContract = function(is_update_contract, isMobileView) {
    vm.staff_contract.is_request_update = IS_REQUEST_UPDATE;
    vm.isDisabledSubmit = true;
    var is_need_approved = !_.isEqual(vm.staff_contract.work_condition, vm.init_work_condition);
    var params = {
      is_update_contract: is_update_contract,
      is_need_approved: is_need_approved
    };
    if (is_update_contract) params.contract = vm.staff_contract;
    staffContractService.saveStaffContract(params, vm.contract_code).then(function mySuccess(res) { 
      redirectAfterSave(res.data.status, isMobileView);
    }, function myError() {
      location.reload();
    });
  }

  vm.generatePdf = function() {
    var url = "/" + I18n.locale + "/contracts/" + vm.contract_code + "/detail";
    window.open(url);
  }

  vm.checkIsTime = function(fieldName) {
    var timeFomat = /^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/
    if (!timeFomat.test(vm.staff_contract[fieldName])) {
      vm.staff_contract[fieldName] = "";
    }
  }

  vm.compareTime = function(fieldName, fieldStart) {
    if (_.isEmpty(vm.staff_contract[fieldName]) || _.isEmpty(vm.staff_contract[fieldStart])) return;
    var startTime = Date.parse('01/01/2001 ' + vm.staff_contract[fieldStart]);
    var endTime = Date.parse('01/01/2001 ' + vm.staff_contract[fieldName]);
    if (startTime >= endTime) {
      return true;
    }
  }

  vm.errorClassForInput = function(inputName) {
    return {
      "form-error": !vm.contractForm[inputName].$valid && vm.contractForm[inputName].$touched
    };
  };

  vm.validateStep1 = function() {
    $(".disable-submit-btn").prop("disabled", true);
    vm.staff_contract.is_request_update = IS_REQUEST_UPDATE;
    var params = {contract: vm.staff_contract};
    staffContractService.validateStep1(params, vm.contract_code).then(function mySuccess(res) {
      $(".disable-submit-btn").prop("disabled", false);
      if (res.data.status){
        var isWarningMessage = res.data.insurance_warning_message;
        var isRejectMessage = res.data.insurance_reject_message
        if (isWarningMessage && isRejectMessage) {
          vm.warningInsuranceMessage = res.data.insurance_warning_message;
          vm.rejectInsuranceMessage = res.data.insurance_reject_message;
          vm.showPopupWarningInsurance();
        }
        else if (isWarningMessage && !isRejectMessage) {
          vm.warningInsuranceMessage = res.data.insurance_warning_message;
          vm.showPopUpInsuranceNotification();
        } else {
          vm.nextStep(true);
        }
      }
      else {
        var errors = JSON.parse(res.data.errors);
        _.forEach(errors, function(error, key) {
          if (_.isEqual(key, "base")) vm.base = true;
          else {
            vm.contractForm["staff_contract[" + key + "]"].$touched = true;
          }
        });
        setFormTouched();
      }
    });
  }

  vm.showPopupWarningInsurance = function() {
    $popupWarningInsurance.modal("show");
    $body.addClass("modal-open");
  };

  vm.closePopupWarningInsurance = function() {
    $popupWarningInsurance.modal("hide");
    $body.removeClass("modal-open");
  };

  vm.rejectWarningInsurance = function() {
    $popupWarningInsurance.modal("hide");
    $popupRejectInsurance.modal("show");
  };

  vm.closePopupRejectInsurance = function() {
    $popupRejectInsurance.modal("hide");
    $body.removeClass("modal-open");
  };

  vm.showPopUpInsuranceNotification = function() {
    $popupInsuranceNotification.modal("show");
    $body.addClass("modal-open");
  };

  vm.closePopupInsuranceNotification = function() {
    $popupInsuranceNotification.modal("hide");
    $body.removeClass("modal-open");
  };

  function setFormTouched() {
    _.forEach(INPUT_FIELDS, function(field) {
      vm.contractForm["staff_contract[" + field + "]"].$touched = true;
    });
  }

  vm.resetInputField = function(key) {
    if(vm.staff_contract["is_" + key]) {
      vm.staff_contract[key + "_start_time_1"] = vm.staff_expectation[key + "_start_time_1"];
      vm.staff_contract[key + "_end_time_1"] = vm.staff_expectation[key + "_end_time_1"];
    } else {
      vm.staff_contract[key + "_start_time_1"] = "";
      vm.staff_contract[key + "_end_time_1"] = "";
    }
    vm.contractForm["staff_contract[" + key + "_start_time_1]"].$touched = false;
    vm.contractForm["staff_contract[" + key + "_end_time_1]"].$touched = false;
    checkBaseError();
  }

  function checkBaseError() {
    var flag = false;
    _.forEach(BOOLEAN_FIELDS, function(field) {
      if (vm.staff_contract[field]) flag = true;
    })
    if (flag) {vm.base = false; return;};
    vm.base = true;
  }

  vm.updateRegistrationType = function() {
    staffContractService.updateRegistrationType({}, vm.contract_code).then(function(res) {
      var res = res.data;
      if (res.status) {
        vm.closePopupConfirmAge();
        vm.showPopupFinishedConfirmAge();
      } else {
        window.location.href = "/" + I18n.locale +"/contracts/" + vm.contract_code;
      }
    });
  }
  
  vm.showModalConfirmAge = function() {
    if (vm.staff_basic_info.registration_type == HAKEN && vm.staff_basic_info.age >= AGE_60) {
      $("#modal-confirm-age").modal("show");
    }
  }

  vm.closePopupConfirmAge = function() {
    $("#modal-confirm-age").modal("hide");
  }

  vm.showPopupFinishedConfirmAge = function() {
    $("#modal-finished-confirm-age").modal("show");
  }

  vm.closePopupFinishedConfirmAge = function(isRedirect) {
    $("#modal-finished-confirm-age").modal("hide");
    if (isRedirect) {
      window.location.href = "/" + I18n.locale;
    }
  }

  vm.openEditPage = function() {
    $(".disable-submit-btn").prop("disabled", true);
    location.href = "/" + I18n.locale + "/contracts/" + vm.contract_code + "/edit";
  }

  vm.cancelEditPage = function() {
    $(".disable-submit-btn").prop("disabled", true);
    location.href = "/" + I18n.locale + "/contracts/" + vm.contract_code;
  }

  vm.workCondition = function(isFromReleaseEntryV2){
    if (isFromReleaseEntryV2) {
      return vm.i18nWorkConditionOfNewStaff;
    }
    return vm.i18nWorkCondition;
  }

  function redirectAfterSave(success, isMobileView) {
    if (success && isMobileView) {
      location.href = "/" + I18n.locale +"/mobileview_contract_completion";
    } else if (success) {
      location.href = "/";
    } else {
      location.reload();
    }
  }
}
