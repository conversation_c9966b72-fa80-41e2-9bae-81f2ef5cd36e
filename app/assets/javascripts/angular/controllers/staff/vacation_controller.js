"use strict";

angular.module("staffApp")
  .controller("VacationsController", VacationsController);
VacationsController.$inject = ["$scope", "vacationService", "$window"];

function VacationsController($scope, vacationService, $window) {
  var vm = this;

  var ALLOWED_IMG_TYPES = ["image/jpeg", "image/png", "image/jpg"];
  var DATA_HOLDER_IDS = ["vacations", "default_image", "min_max_records"];
  var VACATION_FIELDS = ["start_date", "end_date"];
  var TEMPLATE_VACATION_BG = {
    id: null,
    start_date: null,
    end_date: null,
    vacation_certificate_img: null,
    vacation_certificate_img_2: null,
    _destroy: false
  };

  vm.$scope = $scope;
  vm.vacations = [];
  vm.arrItemIndex = [1];
  vm.acceptedImgTypes = ALLOWED_IMG_TYPES.join(", ");
  vm.fileSizeWithMb = FILE_SIZE_IN_MB + "MB";

  vm.initData = function() {
    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });
    for (var i = 0; i < vm.vacations.length; i++) vm.arrItemIndex[i] = i + 1;

    _.forEach(vm.vacations, function(values, key) {
      values["vacation_certificate_img"] = values["vacation_certificate_img_url"];
      values["vacation_certificate_img_2"] = values["vacation_certificate_img_url_2"];
    });

    if (_.isEmpty(vm.vacations)) {
      vm.vacations.push(angular.copy(TEMPLATE_VACATION_BG));
    }
    setBtnStatus();
  };

  addEventForDatePicker();

  vm.addItem = function() {
    vm.vacations.push(angular.copy(TEMPLATE_VACATION_BG));
    addEventForDatePicker();
    var index = 0;
    for(var i = 0; i < vm.arrItemIndex.length; i++){
      if (vm.arrItemIndex[i] != -1) index = vm.arrItemIndex[i];
    }
    vm.arrItemIndex.push(index + 1);
    setBtnStatus();
  }

  vm.destroyItem = function(index) {
    vm.vacations[index]._destroy = true;
    vm.arrItemIndex[index] = -1;
    for (var i = index + 1; i < vm.arrItemIndex.length; i++) {
      if (vm.arrItemIndex[i] != -1) vm.arrItemIndex[i] -= 1;
    }
    setBtnStatus();
  };

  function setBtnStatus() {
    var vacations = getVacationsDestroyFalse();
    if (vacations.length == vm.min_max_records.max_record)
      vm.isDisabledAddBtn = true;
    else {
      vm.isDisabledAddBtn = false;
      if (vacations.length == vm.min_max_records.min_record) vm.isDisabledDelBtn = true;
      else vm.isDisabledDelBtn = false;
    }
  }

  function getVacationsDestroyFalse() {
    var vacations = _.filter(vm.vacations, function(vacation){
      return !vacation._destroy
    });
    return vacations;
  }

  vm.sendLongVacation = function(){
    $("#vacation_form").submit();
  }

  $("#vacation_form").on("submit", function(e) {
    e.preventDefault();

    $.ajax({
      url: "/vacations",
      method: "POST",
      dataType: "json",
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        if(response.status) {
          $window.location.href = response.redirect_path;
        } else {
          vm.setFormTouched();
          $(".disable-submit-btn").prop("disabled", false);
        }
      },
      error: function(){
        location.reload();
      }
    });
  });

  vm.onClickFileField = function(input, index) {
    vm.fileInput = input + "_" + index;
    document.body.onfocus = checkFileField;
  }

  function checkFileField() {
    vm.$scope.$apply(function() {
      vm[vm.fileInput] = true;
    });
    document.body.onfocus = null;
  }

  vm.isTouchedFile = function(fieldName, index) {
    return vm[fieldName + "_" + index];
  }

  vm.conditionError = function(index, name, type) {
    if (_.isEqual(type, "error")){
      return vm.vacationForm["staff[staff_long_vacations_attributes][" + index + "][" + name + "]"].$error;
    } else {
      return vm.vacationForm["staff[staff_long_vacations_attributes][" + index + "][" + name + "]"].$touched;
    }
  }

  vm.conditionFormError = function(index, name) {
    return (vm.vacationForm["staff[staff_long_vacations_attributes][" + index + "][" + name + "]"].$touched &&
      !_.isEmpty(vm.vacationForm["staff[staff_long_vacations_attributes][" + index + "][" + name + "]"].$error)) ||
      !!vm.vacations[index]["error_" + name] || !!vm.vacations[index]["error_" + name + "_less_than_today"];
  }

  vm.errorClassForFileField = function(index, inputName) {
    return {
      "file-field-error-notice": (vm.isTouchedFile(inputName + "_touched", index) &&
        !vm.vacations[index][inputName] && !vm.vacations[index][inputName + "_url"]) || vm.vacations[index][inputName + "_over_size"]
    };
  };

  vm.setFormTouched = function() {
    vm.$scope.$apply(function() {
      _.forEach(VACATION_FIELDS, function(key) {
        _.forEach(vm.vacations, function(values, index) {
          if (!values._destroy) {
            vm.vacationForm["staff[staff_long_vacations_attributes][" + index + "][" + key + "]"].$touched = true;
            vm["vacation_certificate_img_touched_" + index] = true;
            vm["vacation_certificate_img_touched_2" + index] = true;
            vm.checkIsDate(index, "start_date");
            vm.checkIsDate(index, "end_date");
          }
        });
      });
    });
    window.scrollTo(0, 0);
  }

  function addEventForDatePicker() {
    setTimeout(function() {
      $(".js-datepicker").datetimepicker({
        locale: "ja",
        format: FULL_DATE_FORMAT
      });
    }, 500);
  }

  vm.fileChanged = function(event) {
    var previewElm = event.target.getAttribute("target-preview");
    var index = event.target.getAttribute("data-index");

    if (!event.target.files[0]) {
      var oldImage = $(previewElm).attr("data-old-image");

      $(previewElm).attr("src", oldImage);
      vm.$scope.$apply(function() {
        vm.vacations[index]["vacation_certificate_img"] = "";
      });
    } else {
      var reader = new FileReader();
      var fileSizeInMb = event.target.files[0].size / 1024 / 1024;

      if (fileSizeInMb > FILE_SIZE_IN_MB) {
        vm.$scope.$apply(function() {
          vm.vacations[index]["vacation_certificate_img_over_size"] = true;
        });
      } else {
        reader.onload = function(evt) {
          $(previewElm).attr("src", evt.target.result);
          vm.$scope.$apply(function() {
            vm.vacations[index]["vacation_certificate_img"] = evt.target.result;
            vm.vacations[index]["vacation_certificate_img_over_size"] = false;
          });
        }

        reader.readAsDataURL(event.target.files[0]);
      }
    }
  }

  vm.fileChanged2 = function(event) {
    var previewElm = event.target.getAttribute("target-preview");
    var index = event.target.getAttribute("data-index");

    if (!event.target.files[0]) {
      var oldImage = $(previewElm).attr("data-old-image");

      $(previewElm).attr("src", oldImage);
      vm.$scope.$apply(function() {
        vm.vacations[index]["vacation_certificate_img"] = "";
      });
    } else {
      var reader = new FileReader();
      var fileSizeInMb = event.target.files[0].size / 1024 / 1024;

      if (fileSizeInMb > FILE_SIZE_IN_MB) {
        vm.$scope.$apply(function() {
          vm.vacations[index]["vacation_certificate_img_2_over_size"] = true;
        });
      } else {
        reader.onload = function(evt) {
          $(previewElm).attr("src", evt.target.result);
          vm.$scope.$apply(function() {
            vm.vacations[index]["vacation_certificate_img_2"] = evt.target.result;
            vm.vacations[index]["vacation_certificate_img_2_over_size"] = false;
          });
        }

        reader.readAsDataURL(event.target.files[0]);
      }
    }
  }

  vm.checkEndTime = function(end_time, start_time){
    if (end_time) {
      return (end_time < start_time)
    }
  }

  vm.checkTime = function(index, fieldName) {
    _.forEach(vm.vacations, function(vacation, index) {
      vm.isChanged = {startDate: false, endDate: false};
      if(!vacation["_destroy"]) {
        _.forEach(vm.vacations, function(other_vacation, other_index) {
          if(!other_vacation["_destroy"] && other_index != index) {
            _.forEach(["start_date", "end_date"], function(dateStr) {
              var status = (moment(vacation[dateStr]) >= moment(other_vacation.start_date) &&
                moment(vacation[dateStr]) <= moment(other_vacation.end_date))
              if (!vm.isChanged[_.camelCase(dateStr)] || status) {
                vacation["error_" + dateStr] = status;
                vm.isChanged[_.camelCase(dateStr)] = true;
              }
            });
          }
        });
      }
    });
  }

  vm.checkIsDate = function(index, fieldName) {
    if (!Date.parse(vm.vacations[index][fieldName])) {
      return vm.vacations[index][fieldName] = "";
    };

    if (vm.vacations[index][fieldName] <= moment().format("YYYY/MM/DD")) {
      vm.vacations[index]["error_" + fieldName + "_less_than_today"] = true;
    } else {
      vm.vacations[index]["error_" + fieldName + "_less_than_today"] = false;
    };
  }
}
