"use strict";

angular.module("staffApp")
  .controller("WorkAchievementController", WorkAchievementController);
WorkAchievementController.$inject = ["workAchievementService", "toaster"];

function WorkAchievementController(workAchievementService, toaster) {
  var vm = this;
  vm.toasterTimeout = 6200;
  var REST_TIMES = [1, 2, 3];

  vm.init = function(workAchievement, arrangement, orderCase, orderBranch) {
    vm.workAchievement = workAchievement;
    vm.arrangement = arrangement;
    vm.orderCase = orderCase;
    vm.additionalRestingTime = 1;
    vm.orderBranch = orderBranch;
    vm.initWorkingTime();
  }

  vm.staffUpdateWorkAchievement = function(checkBreakTime, checkActualWkTime) {
    $(".disable-submit-btn").prop("disabled", true);
    var params = $(".staff-update-work-achievement-form").serializeJSON();
    _.merge(params, {check_break_time: checkBreakTime, check_acutal_wk_time: checkActualWkTime});
    workAchievementService.staffUpdateRestTime(vm.workAchievement.id, params).then(function(res) {
      if (res.data.status && res.data.break_time_warning) {
        $("#modal-confirm-break-time-warning").modal("show");
      } else if (res.data.status && res.data.actual_working_time_warning) {
        $("#modal-confirm-acutal-wk-time-warning").modal("show");
      } else if (res.data.switched_day_warning) {
        $("#modal-switched-to-same-day-warning").modal("show");
      } else {
        $.lawsonAjax(res.data);
        if (res.data.locked_message) {
          toaster.pop("error", "", res.data.locked_message);
        }
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  }

  vm.addRestTime = function() {
    if (vm.additionalRestingTime === 3) return;
    vm.additionalRestingTime += 1;
  }

  vm.removeRestTime = function(removedNumber) {
    if (!_.isEmpty(vm["rest" + removedNumber])) {
      vm["rest" + removedNumber]["started_at"] = "";
      vm["rest" + removedNumber]["ended_at"] = "";
    }

    if (removedNumber === 2 && vm.additionalRestingTime === 3 && !_.isEmpty(vm.rest2) && !_.isEmpty(vm.rest3)) {
      vm.rest2.started_at = vm.rest3.started_at;
      vm.rest2.ended_at = vm.rest3.ended_at;
      vm.rest3.started_at = "";
      vm.rest3.ended_at = "";
    }
    vm.additionalRestingTime -= 1;
  }

  vm.initWorkingTime = function() {
    vm.staff_working_started_at = formatTime(vm.orderBranch.working_start_time);
    vm.staff_working_ended_at = formatTime(vm.orderBranch.working_end_time);
    _.forEach(REST_TIMES, function(rest_time) {
      var startTime = vm.orderBranch["rest" + rest_time + "_started_at"];
      var endTime = vm.orderBranch["rest" + rest_time + "_ended_at"];
      if (_.isEmpty(startTime)) return;
      vm.additionalRestingTime = rest_time;
      vm["rest" + rest_time] = {
        started_at: formatTime(startTime),
        ended_at: formatTime(endTime)
      }
    });
  }

  vm.confirmApproveWorkingTime = function() {
    $("form#staff-approve-working-time").submit();
  }

  function formatTime(time) {
    if (_.isEmpty(time)) return;
    return moment.parseZone(time).format(TIME_PICKER_FORMAT);
  };
}
