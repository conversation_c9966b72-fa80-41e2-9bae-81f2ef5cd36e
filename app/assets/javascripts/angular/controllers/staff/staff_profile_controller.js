"use strict";

angular.module("staffApp")
  .controller("StaffProfileController", StaffProfileController);

StaffProfileController.$inject = ["$location", "$scope", "postalCodeService", "stationService",
  "staffService", "$window", "staffEntryFrmConsts", "staffEntryFrmCommonService"];

function StaffProfileController($location, $scope, postalCodeService, stationService,
  staffService, $window, staffEntryFrmConsts, staffEntryFrmCommonService) {
  var vm = this;

  vm.overviewTabs = [
    {
      id: 1,
      active: true,
      hashVal: "basic_info",
      name: I18n.t("staff.profile.tab_panel_title.basic_info")
    },
    {
      id: 2,
      active: false,
      hashVal: "education",
      name: I18n.t("staff.profile.tab_panel_title.education")
    },
    {
      id: 3,
      active: false,
      hashVal: "work_history",
      name: I18n.t("staff.profile.tab_panel_title.work_history")
    },
    {
      id: 4,
      active: false,
      hashVal: "account_transfer",
      name: I18n.t("staff.profile.tab_panel_title.account_transfer")
    }
  ];

  var $residenceExpDateLabel = angular.element("label[for=residence_expiration_date] span");

  vm.$scope = $scope;
  var LOCALE = I18n.locale;

  var DATA_HOLDER_IDS = ["is_able_to_edit_profile", "edited_staff", "prefectures", "uniform_sizes",
    "shoes_sizes", "pant_sizes",
    "relationships", "residence_statuses", "residence_permissions", "specific_activity_residence_statuses",
    "edited_staff_check_item", "japanese_levels", "is_read_alert_review", "review_status",
    "has_residence_permission", "verify_account_status"];
  var STEP_1_RESIDENCE_FILE_FIELDS = staffEntryFrmConsts.STEP_1_RESIDENCE_FILE_FIELDS;
  var ALLOWED_IMG_TYPES = staffEntryFrmConsts.ALLOWED_IMG_TYPES;
  var STEP_1_REQUIRED_INPUT_FIELDS = staffEntryFrmConsts.STEP_1_REQUIRED_INPUT_FIELDS;
  var STEP_1_REQUIRED_SELECT_FIELDS = staffEntryFrmConsts.STEP_1_REQUIRED_SELECT_FIELDS;
  // STEP_1_REQUIRED_SELECT_FIELDS = STEP_1_REQUIRED_SELECT_FIELDS.push("uniform_size").push("pant_size").push("shoes_size");
  var ADDRESS_ATTRS = staffEntryFrmConsts.ADDRESS_ATTRS;
  var STEP1_STAFF_CHECK_ITEMS = ["id", "staff_id", "japanese_level", "uniform_size", "pant_size", "shoes_size"];
  var PROFILE_STEP_1_DATETIME_FIELDS = ["birthday", "residence_expiration_date",
    "residence_permission_validity", "residence_validity"];
  var POSTAL_CODE_REGEX = /^\d{7}$/;
  var COPY_CURRENT_STAFF_FIELDS = ["last_name", "middle_name_1", "middle_name_2", "middle_name_3", "first_name",
    "last_name_kana", "middle_name_kana_1", "middle_name_kana_2", "middle_name_kana_3", "first_name_kana",
    "postal_code", "prefecture_id", "city", "street_number", "house_number", "building"];

  vm.currentStaff = {};
  vm.fileSizeWithMb = FILE_SIZE_IN_MB + "MB";
  vm.homeStations = [];
  vm.schoolStations = [];
  vm.acceptedImgTypes = ALLOWED_IMG_TYPES.join(", ");
  vm.edited_staff = {};
  vm.edited_staff_check_item = {};
  vm.formError = {};
  vm.postalCodeSearch = {
    page: 1,
    postal_code: ""
  };

  initData();

  vm.initData = function() {
    var currentTabHash = $location.hash();
    var tab = vm.tabByHashVal(currentTabHash);
    if (tab) {
      vm.setTab(tab.id - 1);
    }
  }

  vm.currentTab = function() {
    return vm.overviewTabs.filter(function(step) {
      return step.active;
    })[0].id;
  };

  vm.tabByHashVal = function(hashVal) {
    return vm.overviewTabs.filter(function(step) {
      return _.isEqual(hashVal, step.hashVal)
    })[0];
  };

  vm.setTab = function(tabId) {
    var currentTab = vm.currentTab();
    if (!_.isUndefined(vm.overviewTabs[tabId])) {
      vm.overviewTabs[currentTab - 1].active = false;
      vm.overviewTabs[tabId].active = true;
    }
  };

  vm.enableInputMiddleName = function() {
    vm.isEnableMiddleName = true;
    if (!vm.currentMiddleNameKanaLength) vm.currentMiddleNameKanaLength = 1;
    if (!vm.currentMiddleNameLength) vm.currentMiddleNameLength = 1;
  };

  vm.removeMiddleName = function(position, field, isName) {
    var counter;
    if (isName) {
      counter = angular.copy(vm.currentMiddleNameLength);
      vm.currentMiddleNameLength--;
    } else {
      counter = angular.copy(vm.currentMiddleNameKanaLength);
      vm.currentMiddleNameKanaLength--;
    }
    for (var i = position; i <= counter - 1; i++) {
      vm.edited_staff[field + "_" + i] = angular.copy(vm.edited_staff[field + "_" + (i + 1)]);
    }
    vm.edited_staff[field + "_" + counter] = "";
  };

  vm.addMiddleName = function(suffix) {
    vm["currentMiddleName" + suffix + "Length"]++;
  };

  vm.errorClassForInputName = function(isKana) {
    var suffix = isKana ? "_kana" : "";
    return {
      "form-error": (!vm.entryForm["edited_staff[first_name" + suffix + "]"].$valid && vm.entryForm["edited_staff[first_name" + suffix + "]"].$touched) ||
        (!vm.entryForm["edited_staff[last_name" + suffix + "]"].$valid && vm.entryForm["edited_staff[last_name" + suffix + "]"].$touched) ||
        (!vm.entryForm["edited_staff[middle_name" + suffix + "_1]"].$valid && vm.entryForm["edited_staff[middle_name" + suffix + "_1]"].$touched) ||
        (!vm.entryForm["edited_staff[middle_name" + suffix + "_2]"].$valid && vm.entryForm["edited_staff[middle_name" + suffix + "_2]"].$touched) ||
        (!vm.entryForm["edited_staff[middle_name" + suffix + "_3]"].$valid && vm.entryForm["edited_staff[middle_name" + suffix + "_3]"].$touched)
    };
  };

  vm.$scope.$watch("vm.verify_account_status", function() {
    if(vm.verify_account_status === 'error') {
      $("#bank_account_status").removeClass("notices-staff--note");
      $("#bank_account_status").addClass("notices-staff--error");
    }
  });

  function watchingChange() {
    vm.$scope.$watchGroup(["vm.edited_staff.is_emergency_address_differ_current_address", "vm.edited_staff.house_number"], function() {
      staffEntryFrmCommonService.watchDifferCurrentAddr(vm, "edited_staff");
    });

    vm.$scope.$watch("vm.edited_staff.prefecture_id", function() {
      staffEntryFrmCommonService.watchStation(vm, "edited_staff");
      vm.checkNameAddressChanged();
    });

    vm.$scope.$watch("vm.edited_staff.residence_permission", function() {
      staffEntryFrmCommonService.watchResidencePermission(vm, "edited_staff");
    });

    vm.$scope.$watch("vm.edited_staff.birthday", function() {
      staffEntryFrmCommonService.watchBirthday(vm, "edited_staff");
    });

    vm.$scope.$watch("vm.edited_staff.residence_expiration_date", function() {
      if (!vm.edited_staff['nationality_other_than_japan?']) return;
      staffEntryFrmCommonService.watchResidenceExpirationDate(vm, "edited_staff");
    });

    vm.$scope.$watchGroup(["vm.edited_staff.tel", "vm.edited_staff.emergency_tel"], function() {
      vm.checkTelNumber();
    });
  }

  var getFormData = function(form) {
    var $inputs = $("input[type='file']:not([disabled])", form);
    $inputs.each(function(_, input) {
      if (input.files.length > 0) return;
      $(input).prop("disabled", true);
    });
    var formData = new FormData(form);
    $inputs.prop("disabled", false);
    return formData;
  };

  $(".form-profile").on("submit", function(e) {
    e.preventDefault();
    var actionURL = $(this).attr("action");

    $.ajax({
      url: actionURL,
      method: "POST",
      dataType: "json",
      data: getFormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        if(response.status) {
          $window.location.href = response.redirect_path
        } else {
          vm.$scope.$apply(function() {
            setFormTouchedStep1(vm.entryForm);
            vm.formError.is_display_error_step1 = true;
            window.scrollTo(0, 0);
          });
        }
      },
      error: function(){
        location.reload();
      }
    });
  });

  function setFormTouchedStep1(form) {
    STEP_1_RESIDENCE_FILE_FIELDS.forEach(function(field) {
      vm["edited_staff_" + field + "_touched"] = true;
    });

    vm.entryForm["edited_staff[emergency_tel]"].$touched = true;
    if (vm.edited_staff.social_attribute == vm.OTHER_KEY) {
      vm.entryForm['edited_staff[social_attribute_other]'].$touched = true;
    }

    setTouchedForSelectFields();
    var stepInfo = staffEntryFrmCommonService.getStepInfo("profile");
    if (_.isEqual(vm.edited_staff.social_attribute, vm.OTHER_KEY)){
      stepInfo.requiredFields.push("social_attribute_other");
    }
    setTouchedForInputFields(form, stepInfo);
  }

  function setTouchedForInputFields(form, stepInfo) {
    if (form.hasOwnProperty("$submitted")) {
      angular.forEach(form.$error, function (errorType) {
        angular.forEach(errorType, function (prop) {
          var requiredInputFields = _.flatMap(stepInfo.requiredFields, eval(stepInfo.stepFunc));
          if (prop.hasOwnProperty("$touched") && _.includes(requiredInputFields, prop.$name)) prop.$setTouched();
          setFormTouchedStep1(prop);
        });
      });
    }
  }

  function setTouchedForSelectFields() {
    STEP_1_REQUIRED_SELECT_FIELDS.forEach(function(field) {
      vm["edited_staff_" + field + "_blur"] = true;
    });
    vm["staff_check_item_uniform_size"] = true;
    vm["staff_check_item_pant_size"] = true;
    vm["staff_check_item_shoes_size"] = true;
  }

  vm.isStatusConfirming = function(status) {
    return status === "confirming";
  }

  vm.conditionForDispErrForm = function() {
    return vm.formError["is_display_error_step1"] == true ? true : false
  }

  vm.fileChanged = function(event) {
    if (!event.target.files[0]) {return;}
    var previewElm = event.target.getAttribute("target-preview");
    var argument = event.target.getAttribute("argument");
    var editedArgument = event.target.getAttribute("edited-argument");
    var field = event.target.getAttribute("model-name");
    var fileSizeInMb = event.target.files[0].size / 1024 / 1024;

    if (!_.includes(ALLOWED_IMG_TYPES, event.target.files[0].type)) {
      vm.$scope.$apply(function() {
        $("#edited_staff_" + field + "_file_field").val("");
        if (_.includes(STEP_1_RESIDENCE_FILE_FIELDS, field)) {
          var src = angular.element("#" + field + "_old").data("value");
          $("." + field + "_preview").attr("src", src);
          vm.edited_staff[field] = _.isEmpty(vm.edited_staff) ? "" : vm.edited_staff[field];
        } else {
          vm.edited_staff.staff_account_transfer_attributes.passbook_pic = "";
          $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
          eval("vm." + argument)[$(previewElm).attr("data-name")] = _.isEmpty(eval("vm." + editedArgument)) ? "" :
            eval("vm." + editedArgument)[$(previewElm).attr("data-name")];
          vm.edited_staff.staff_account_transfer_attributes.passbook_pic_over_size = false;
        }
      });
      return;
    }

    vm.$scope.$apply(function() {
      if (!_.isEqual(event.target.files.length, 0)) {
        eval("vm." + argument)[field] = event.target.files[0].name;
        staffEntryFrmCommonService.previewImg(event, previewElm);
        if (fileSizeInMb > FILE_SIZE_IN_MB) {
          if (_.includes(STEP_1_RESIDENCE_FILE_FIELDS, field)) {
            vm.edited_staff[field] = "";
            vm.entryForm["edited_staff[" + field + "]"].$error = {"file-max-size": true}
            vm.entryForm["edited_staff[" + field + "]"].$valid = false;
          } else {
            vm.edited_staff.staff_account_transfer_attributes.passbook_pic = "";
            vm.edited_staff.staff_account_transfer_attributes.passbook_pic_over_size = true;
          }
        } else {
          if (_.includes(STEP_1_RESIDENCE_FILE_FIELDS, field)) {
            vm.entryForm["edited_staff[" + field + "]"].$error = {}
            vm.entryForm["edited_staff[" + field + "]"].$valid = true;
          } else {
            vm.edited_staff.staff_account_transfer_attributes.passbook_pic_over_size = false;
          }
        }
      } else {
        $(previewElm).attr("src", $(previewElm).attr("data-old-image"));
        eval("vm." + argument)[$(previewElm).attr("data-name")] = _.isEmpty(eval("vm." + editedArgument)) ? "" :
          eval("vm." + editedArgument)[$(previewElm).attr("data-name")];
      }
    });
  };

  vm.checkNameAddressChanged = function() {
    var isChanged = false;
    var identificationFields = ["residence_img_front", "residence_img_back"];
    _.forEach(COPY_CURRENT_STAFF_FIELDS, function(field) {
      if (!vm.currentStaff[field] && !vm.edited_staff[field]) return;
      if (!_.isEqual(vm.currentStaff[field], vm.edited_staff[field])) {
        isChanged = true;
        return false;
      }
    })
    if (!isChanged) {
      _.forEach(identificationFields, function(field) {
        if (!!vm.entryForm["edited_staff[" + field + "]"].$error["file-not-selected"]) {
          vm.entryForm["edited_staff[" + field + "]"].$error = {};
          vm.entryForm["edited_staff[" + field + "]"].$valid = true;
        }
      })
      return;
    }
    _.forEach(identificationFields, function(field) {
      if (_.isEmpty(vm.entryForm["edited_staff[" + field + "]"].$error)) setValueAttr(field);
    })
  }

  vm.checkTelNumber = function() {
    if (_.isEmpty(vm.edited_staff.tel) || _.isEmpty(vm.edited_staff.emergency_tel)) { return; }
    vm.entryForm["edited_staff[emergency_tel]"].$touched = true;
    if (vm.edited_staff.tel == vm.edited_staff.emergency_tel) {
      vm.entryForm["edited_staff[emergency_tel]"].$error = {"same-tel-number": true};
      vm.entryForm["edited_staff[emergency_tel]"].$valid = false;
    } else {
      vm.entryForm["edited_staff[emergency_tel]"].$error = {};
      vm.entryForm["edited_staff[emergency_tel]"].$valid = true;
    }
  };

  function isStep1SelectFieldsValid() {
    var condition = !vm.edited_staff.prefecture_id || !vm.edited_staff.nationality || !vm.edited_staff.home_station_id ||
      (!!vm.edited_staff.nationality && vm.edited_staff.staff_check_item_attributes["is_foreign?"] && !vm.edited_staff.residence_status);
    if (condition) return false;
    return true;
  }

  function isCheckItemSelectFieldsValid() {
    var staffCheckItem = vm.edited_staff.staff_check_item_attributes;
    var condition = !!staffCheckItem["uniform_size"] && !!staffCheckItem["pant_size"] && !!staffCheckItem["shoes_size"];
    return condition;
  }

  vm.otherFieldsValid = function() {
    var flag = true;

    setTouchedForSelectFields();

    if (!isStep1SelectFieldsValid() || !isCheckItemSelectFieldsValid() ) {return false;}

    for (var idx in STEP_1_RESIDENCE_FILE_FIELDS) {
      var field = STEP_1_RESIDENCE_FILE_FIELDS[idx];
      vm["edited_staff_" + field + "_touched"] = true;
      if (_.isEqual("certificate_img", field)) {
        if (vm.conditionForDispErrFileField(field, true)) {
          flag = false;
          break;
        }
      } else {
        if (vm.conditionForDispErrFileField(field)) {
          flag = false;
          break;
        }
      }
    };
    return flag;
  };

  function initData() {
    moment.locale(LOCALE);

    DATA_HOLDER_IDS.forEach(function(id) {
      vm[id] = angular.element("#" + id).data("infos");
    });
    if (!_.isEmpty(vm.edited_staff)) {
      initDataForEdit();
    }
  }

  function initDataForEdit() {
    watchingChange();
    _.forEach(PROFILE_STEP_1_DATETIME_FIELDS, function(field) {
      if (moment(vm.edited_staff[field]).isValid()) {
        vm.edited_staff[field] = moment(vm.edited_staff[field]).format(ORDER_DATEPICKER_FORMAT);
      }
    });

    formatFirstMiddleLastName();

    vm.edited_staff.staff_check_item_attributes = {};
    _.forEach(STEP1_STAFF_CHECK_ITEMS, function(attr) {
      vm.edited_staff.staff_check_item_attributes[attr] = _.isEmpty(vm.edited_staff_check_item) ? "" :
        vm.edited_staff_check_item[attr];
    });

    copyCurrentStaff();
  }

  function formatFirstMiddleLastName() {
    var nameArr = vm.edited_staff.name.split(" "), nameKanaArr = vm.edited_staff.name_kana.split(" ");
    vm.edited_staff.last_name = nameArr.splice(0, 1).join("");
    vm.edited_staff.first_name = nameArr.splice(nameArr.length - 1, 1).join("");
    var middle_names = nameArr.filter(function(item){return item != ''});
     vm.edited_staff.last_name_kana = nameKanaArr.splice(0, 1).join("");
    vm.edited_staff.first_name_kana = nameKanaArr.splice(nameKanaArr.length - 1, 1).join("");
    var middle_name_kanas = nameKanaArr.filter(function(item){return item != ''});
    [1, 2, 3].forEach(function(idx) {
      vm.edited_staff["middle_name_" + idx] = middle_names[idx - 1];
      vm.edited_staff["middle_name_kana_" + idx] = middle_name_kanas[idx - 1];
    });
    vm.currentMiddleNameLength = middle_names.length;
    vm.currentMiddleNameKanaLength = middle_name_kanas.length;
     if (!_.isEmpty(middle_names) || !_.isEmpty(middle_name_kanas)) {
      vm.currentMiddleNameLength = vm.currentMiddleNameLength == 0 ? 1 : vm.currentMiddleNameLength;
      vm.currentMiddleNameKanaLength = vm.currentMiddleNameKanaLength == 0 ? 1 : vm.currentMiddleNameKanaLength;
      if (_.isEmpty(middle_name_kanas)) vm.edited_staff["middle_name_kana_1"] = "";
      vm.isEnableMiddleName = true;
    }
  }

  function copyCurrentStaff() {
    _.forEach(COPY_CURRENT_STAFF_FIELDS, function(field) {
      vm.currentStaff[field] = vm.edited_staff[field];
    })
  }

  vm.redirectEditVacation = function() {
    $(".disable-submit-btn").prop("disabled", true);
    location.href = "/vacations/new";
  }

  vm.submitForm = function() {
    $("#staff-profile-form-submit").click();
  }

  vm.validateProfileBasicInfo = function() {
    if (vm.edited_staff.is_emergency_address_differ_current_address == 0) {
      ADDRESS_ATTRS.forEach(function(attr) {
        vm.edited_staff["emergency_" + attr] = angular.copy(vm.edited_staff[attr]);
      });
    }

    vm.checkTelNumber();

    if (isStep1InputFieldsValid() && vm.otherFieldsValid()) {
      vm.submitForm();
    } else {
      setFormTouchedStep1(vm.entryForm);
      vm.formError.is_display_error_step1 = true;
      window.scrollTo(0, 0);
    }
  }

  vm.backUrl = function() {
    $window.location.href = "/" + I18n.locale + "/profiles";
  };

  function isStep1InputFieldsValid() {
    var flag = true;
    for (var idx in STEP_1_REQUIRED_INPUT_FIELDS) {
      if (STEP_1_REQUIRED_INPUT_FIELDS[idx] == "residence_number" && !vm.edited_staff['nationality_other_than_japan?']) {
        continue;
      } else {
        var field = "edited_staff[" + STEP_1_REQUIRED_INPUT_FIELDS[idx] + "]";
        if (vm.entryForm[field] && !vm.entryForm[field].$valid) {
          flag = false;
          break;
        }
      }
    }
    if (flag) {
      flag = vm.entryForm["edited_staff[residence_expiration_date]"].$valid;
    }
    return flag;
  }

  vm.checkIsDate = function(fieldName, nestedField) {
    if (_.isUndefined(nestedField)) {
      if (!Date.parse(vm.edited_staff[fieldName])) {
        vm.edited_staff[fieldName] = "";
      }
    } else {
      if (!Date.parse(vm.edited_staff[nestedField][fieldName])) {
        vm.edited_staff[nestedField][fieldName] = "";
      }
    }
  };

  vm.onClickFileField = function(input) {
    vm.fileInput = input;
    document.body.onfocus = checkFileField;
  }

  function checkFileField() {
    vm.$scope.$apply(function() {
      vm["edited_staff_" + vm.fileInput + "_touched"] = true;
    });
    if(_.isEmpty($("#edited_staff_" + vm.fileInput + "_file_field").val()) && _.isEmpty($("#" + vm.fileInput + "_old").data("value"))) {
      vm.$scope.$apply(function() {
        if (_.includes(STEP_1_RESIDENCE_FILE_FIELDS, vm.fileInput) &&
          _.isEmpty(vm.entryForm["edited_staff[" + vm.fileInput + "]"].$error)) {
          setValueAttr(vm.fileInput);
        }
      });
    }
    document.body.onfocus = null;
  }

  vm.setErrorForImg = function() {
    _.forEach(STEP_1_RESIDENCE_FILE_FIELDS, function(field) {
      setValueAttr(field);
    })
  };

  function setValueAttr(field){
    vm.entryForm["edited_staff[" + field + "]"].$error = {"file-not-selected": true};
    vm.entryForm["edited_staff[" + field + "]"].$valid = false;
  }

  vm.errorClassForFileField = function(inputName, isNeedSpecActivities) {
    return {
      "file-field-error-notice": vm.conditionForDispErrFileField(inputName, isNeedSpecActivities)
    };
  };

  vm.conditionForDispErrFileField = function(inputName, isNeedSpecActivities) {
    if (_.isNull(vm.edited_staff[inputName].url)) vm.edited_staff[inputName] = "";
    var condition = vm["edited_staff_" + inputName + "_touched"] && vm.edited_staff['nationality_other_than_japan?'] &&
      (!vm.edited_staff[inputName] && _.isEmpty($("#edited_staff_" + inputName + "_file_field").val()));
    if (isNeedSpecActivities) {
      condition = condition && (_.includes(vm.specific_activity_residence_statuses, vm.edited_staff.residence_status) ||
        !!vm.entryForm["edited_staff[" + inputName + "]"].$error["file-max-size"]);
    }
    if (!vm.edited_staff['nationality_other_than_japan?']) {
      condition = condition || !!vm.entryForm["edited_staff[" + inputName + "]"].$error["file-not-selected"] ||
        !!vm.entryForm["edited_staff[" + inputName + "]"].$error["file-max-size"];
    }
    return condition;
  };

  vm.searchStaffPostalCode = function(isPostalCodeOfStaff) {
    staffEntryFrmCommonService.searchStaffPostalCode(vm, isPostalCodeOfStaff);
  };

  vm.searchPostalCode = function(isResetPage) {
    staffEntryFrmCommonService.searchPostalCode(vm, isResetPage);
  };

  vm.choosePostalCode = function(postalCodeId) {
    staffEntryFrmCommonService.choosePostalCode(vm, "edited_staff", postalCodeId);
  };

  vm.searchPostalCodeByExactlyCode = function(field) {
    staffEntryFrmCommonService.searchPostalCodeByExactlyCode(vm, "edited_staff", field);
  };

  vm.redirectPage = function(href) {
    $(".disable-submit-btn").prop("disabled", true);
    location.href = href;
  }

  vm.errorClassForInput = function(inputName) {
    return {
      "form-error": !vm.entryForm[inputName].$valid && vm.entryForm[inputName].$touched
    };
  };

  vm.postalCodePattern = (function() {
    return {
      test: function(value) {
        return POSTAL_CODE_REGEX.test(value.replace(/\-/g, ""));
      }
    }
  })();

  vm.handlePatternTel = (function() {
    return {
      test: function(value) {
        var length = value.replace(/-/g, "").length;
        return STAFF_TEL_REGEX.test(value) && _.includes(STAFF_TEL_LENGTH, length);
      }
    };
  })();

  vm.handlePatternResidentNumber = (function() {
    return {
      test: function(value) {
        return RESIDENCE_NUMBER_REGEX.test(value);
      }
    };
  })();

  angular.element(document).ready(function () {
    if (_.isUndefined(vm.edited_staff)) return;
    vm.checkResidenseExpDateRequired();
  });

  vm.isResidenseExpDateRequired = function(){
    return (vm.edited_staff.residence_status != NOT_REQUIRED_RESIDENSE_STATUS_ID);
  };

  vm.checkResidenseExpDateRequired = function() {
    if (vm.isResidenseExpDateRequired()){
      if($residenceExpDateLabel.hasClass('d-none')){
        $residenceExpDateLabel.removeClass('d-none');
      }
    }else{
      if(!$residenceExpDateLabel.hasClass('d-none')){
        $residenceExpDateLabel.addClass('d-none');
      }
    };
  };
  
  function disableEnter(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 13) {
      e.preventDefault();
      $("input").blur();
    }
  };

  if (!_.isNull($("#edit_edited_staff"))) {
    $("#edit_edited_staff").keypress(function(e) {
      disableEnter(e);
    });
  }
}
