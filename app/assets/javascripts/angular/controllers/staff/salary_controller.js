"use strict";

angular.module("staffApp")
  .controller("StaffSalariesController", StaffSalariesController);

StaffSalariesController.$inject = ["$location", "$scope", "staffService"];

function StaffSalariesController($location, $scope, staffService) {
  var vm = this;
  vm.$scope = $scope;
  vm.params = $location.search();
  vm.currentPage = vm.params.page ? vm.params.page : 1;
  vm.hasRecord = false;
  vm.params.desc = vm.params.desc == "false" ? false : true;

  vm.downloadPayslip = function(id) {
    var actionURL = "/" + I18n.locale + "/salaries/"+ id +"/detail";
    window.open(actionURL, '_blank');
  }

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
    }
    vm.init();
  };

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !vm.params.desc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !!vm.params.desc
    }
  };

  vm.init = function() {
    vm.params.page = vm.currentPage;
    staffService.loadDataSalaries(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      $location.search(vm.params).replace();
      vm.hasRecord = vm.total_items != 0;
    });
  }
}
