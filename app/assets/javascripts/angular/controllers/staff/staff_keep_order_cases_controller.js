"use strict";

angular.module("staffApp")
  .controller("StaffKeepOrderCaseController", StaffKeepOrderCaseController);
StaffKeepOrderCaseController.$inject = ["$location", "orderCaseService", "orderCaseListService"];

function StaffKeepOrderCaseController($location, orderCaseService, orderCaseListService) {
  var vm = this;
  vm.params = $location.search();
  vm.apply_params = {};
  vm.apply_errors = {};
  vm.apply_warnings = {};
  vm.apply_params.is_used_original_condition = true;
  vm.isMobile = /Mobile|webOS/i.test(navigator.userAgent);
  vm.apply_params.accepted_out_of_contract = false;
  vm.responseData = {};
  vm.studentApplyMessage = "";
  var APPLY_CONDITION_CONTACT_OP = "apply_condition_contact_op";
  var HAKEN_ORDER_CASE_SEGMENT = "haken";
  var CONTRACT_ORDER_CASE_SEGMENT = "contract";
  var REGULAR_ORDER_CASE_SEGMENT = "regular_order";

  vm.init = function() {
    vm.params.page = 1;
    vm.params.per_page = 20;
    vm.hasRecord = true;

    vm.orderKeys = [
      {key: "created_datetime", type: "desc", label: I18n.t("staff.staff_keep_order_cases.index.filter_fields.created_datetime")},
      {key: "case_start", type: "asc", label: I18n.t("staff.staff_keep_order_cases.index.filter_fields.case_started_at")},
      {key: "keeping_job", type: "desc", label: I18n.t("staff.staff_keep_order_cases.index.filter_fields.keep_created_at")}
    ];
    if (_.isUndefined(vm.params.order_key)) {
      vm.params.order_key = vm.orderKeys[0].key;
      vm.params.order_type = vm.orderKeys[0].type;
    }
    vm.order_key_name = _.find(vm.orderKeys, function(element) {
      return element.key == vm.params.order_key;
    }).label;
    vm.refresh();
  }

  vm.keepOrderCase = function() {
    angular.element("#delete-keeping-job-modal").modal("hide");
    orderCaseService.createStaffKeepOrderCase({order_case_id: vm.keepJobId}, I18n.locale).then(function(res) {
      vm.refresh();
    });
  };

  vm.getFeeFlag = function(order_case) {
    if (order_case.segment_id === REGULAR_ORDER_CASE_SEGMENT) {
      return feeFlagRegular(vm.order_case_wages[order_case.id]);
    }

    if (order_case.segment_id === HAKEN_ORDER_CASE_SEGMENT || order_case.segment_id === CONTRACT_ORDER_CASE_SEGMENT) {
      return feeFlagHaken(vm.order_case_wages[order_case.id]);
    }
  };

  vm.moveToDetail = function(orderCase) {
    location.href = "/" + I18n.locale + "/order_cases/" + orderCase.id;
  };

  vm.refresh = function() {
    orderCaseService.getKeepOrderCases({search: vm.params}).then(function(res) {
      vm.order_cases = res.data.order_cases;
      vm["total_items"] = res.data.total_items;
      vm.totalKeeping = I18n.t("staff.staff_keep_order_cases.index.number_keeping", {number: vm.total_items});
      angular.extend(vm, res.data);
      vm.hasRecord = Boolean(vm.order_cases.length);
      $location.search(vm.params).replace();
    });
  };

  vm.isKeeped = function(orderCaseId) {
    return _.includes(vm.keep_order_case_ids, orderCaseId);
  };

  vm.disableOrderCase = function(orderCase) {
    return orderCase.timeout_apply_before_2_hour || _.includes(vm.rejected_order_case_ids, orderCase.id);
  }

  vm.sort = function(field, type) {
    vm.params.order_key = field;
    vm.params.order_type = type;

    vm.order_key_name = _.find(vm.orderKeys, function(element) {
      return element.key == vm.params.order_key;
    }).label;
    vm.refresh();
  };

  vm.openDeleteJobModal = function(orderCaseID) {
    angular.element("#delete-keeping-job-modal").modal("show");
    vm.keepJobId = orderCaseID;
  };

  vm.dismissContactModal = function() {
    $("#modal-contact-op-center-contract-renewal").modal("hide");
    vm.apply_params.accepted_out_of_contract = false;
    vm.refresh();
  }

  vm.applyAllKeepingJobs = function() {
    $("#apply-all-keeping-jobs").modal("hide");
    $("#modal-apply-error").modal("hide");
    vm.apply_oc = vm.all_order_cases[vm.apply_oc_index];
    if (vm.apply_oc_index < vm.all_order_cases.length) {
      vm.apply_params.requested_transportation_fee = "";
      vm.apply_errors = "";
      vm.apply_warnings = "";
      checkApplyConditionWarning(vm.apply_oc.id);
      $("#work-modal").modal("show");
    } else {
      $("#work-modal").modal("hide");
      $("#apply-jobs-done").modal("show");
      $(".disable-submit-btn").prop("disabled", false);
      vm.refresh();
    }
  }

  var checkApplyConditionWarning = function(orderCaseId) {
    orderCaseService.checkApplyCondition({order_case_id: orderCaseId}).then(function(res) {
      if (res.data.status) vm.apply_errors = "";
    });
  }

  vm.acceptJoinInsurance = function() {
    $("#modal-insurance-error").modal("hide");
    vm.apply_params.accepted_apply = true;
    vm.applyOrderCase();
  }

  vm.cancelJoinInsurance = function() {
    $("#modal-insurance-error").modal("hide");
    $("#modal-insurance-notification").modal("show");
    vm.apply_params.accepted_apply = false;
  }

  vm.closeModalJoinInsuranceError = function() {
    $("#modal-insurance-notification").modal("hide");
    vm.apply_oc_index += 1;
    vm.apply_params.requested_transportation_fee = "";
    vm.apply_errors = "";
    $("#spinner").addClass("ng-hide");
    vm.applyAllKeepingJobs();
  }

  vm.refusesToWorkOT = function() {
    vm.apply_params.accepted_ot = false;
    vm.apply_params.accepted_out_of_contract = false;
    $("#modal-warning-work-over-time").modal("hide");
    $("#modal-contact-op-center").modal("show");
  }

  vm.acceptToWorkOT = function() {
    vm.apply_params.accepted_ot = true;
    $("#modal-warning-work-over-time").modal("hide");
    if (!_.isEmpty(vm.warningOT.warningInWeekMessage)) {
      $("#modal-warning-work-over-time-in-week").modal("show");
    } else {
      vm.applyOrderCase();
    }
  }

  vm.acceptToWorkOtInWeek = function() {
    $("#modal-warning-work-over-time-in-week").modal("hide");
    vm.apply_params.accepted_ot_in_week = true;
    vm.applyOrderCase();
  }

  vm.refusesToWorkOtInWeek = function() {
    vm.apply_params.accepted_ot_in_week = false;
    vm.apply_params.accepted_out_of_contract = false;
    $("#modal-warning-work-over-time-in-week").modal("hide");
    $("#modal-contact-op-center").modal("show");
  }

  vm.acceptToApplyOutOfContract = function() {
    $("#modal-warning-end-of-contract").modal("hide");
    vm.apply_params.accepted_out_of_contract = true;
    if (vm.responseData.warning_over_time.status) {
      vm.applyOrderCase();
    } else {
      vm.displayWarnings();
    }
  }

  vm.refuseToApplyOutOfContract = function() {
    vm.apply_params.accepted_out_of_contract = false;
    $("#modal-warning-end-of-contract").modal("hide");
    $("#modal-contact-op-center-contract-renewal").modal("show");
  }


  vm.applyNextOrderCase = function() {
    $("#spinner").addClass("ng-hide");
    $("#modal-student-error").modal("hide");
    vm.apply_oc_index += 1;
    vm.applyAllKeepingJobs();
  }

  vm.applyOrderCase = function() {
    $("#spinner").removeClass("ng-hide");
    vm.apply_params.location_id = vm.all_order_cases[vm.apply_oc_index].location_id;
    vm.apply_params.order_case_id = vm.all_order_cases[vm.apply_oc_index].id;
    vm.apply_params.order_branch_id = vm.all_order_cases[vm.apply_oc_index].order_branch_id;
    vm.apply_params.order_id = vm.all_order_cases[vm.apply_oc_index].order_id;
    vm.responseData = {};

    orderCaseService.applyOrderCase({apply_params: vm.apply_params}, I18n.locale).then(function(res) {
      if (res.data.status) {
        vm.apply_oc_index += 1;
        vm.apply_params.requested_transportation_fee = "";
        vm.apply_params.accepted_apply = false;
        vm.apply_params.accepted_ot = false;
        vm.apply_params.accepted_ot_in_week = false;
        vm.apply_params.accepted_out_of_contract = false;
        vm.apply_errors = "";
        $("#spinner").addClass("ng-hide");
        vm.applyAllKeepingJobs();
      } else {
        vm.studentApplyMessage = "";
        if (!_.isEmpty(res.data.apply_condition_errors)) {
          vm.applyConditionMessage = res.data.apply_condition_errors;
          vm.apply_oc_index += 1;
          vm.apply_params.requested_transportation_fee = "";
          $("#work-modal").modal("hide");
          $("#spinner").addClass("ng-hide");
          $("#modal-apply-error").modal("show");
        } else if(!_.isEmpty(res.data.apply_warnings) || (res.data.warning_over_time != null &&
            !res.data.warning_over_time.status)) {
          $("#work-modal").modal("hide");
          vm.responseData = angular.copy(res.data);
          vm.displayWarnings();
        } else if (!_.isEmpty(res.data.insurance_error)) {
          vm.insuranceErrorMessage = res.data.insurance_error;
          vm.insuranceNotificationMessage = res.data.insurance_notification;
          $("#work-modal").modal("hide");
          $("#spinner").addClass("ng-hide");
          $("#modal-insurance-error").modal("show");
        } else {
          $("#spinner").addClass("ng-hide");
          vm.apply_errors = res.data.errors;
          if (!_.isEmpty(vm.apply_errors.is_used_original_condition)) {
            vm.studentApplyMessage = vm.apply_errors.is_used_original_condition[0];
            $("#work-modal").modal("hide");
            $("#modal-student-error").modal("show");
          }
        }
      }
    });
  }

  vm.displayWarnings = function() {
    if (!_.isEmpty(vm.responseData.apply_warnings.contract_warning) && !vm.apply_params.accepted_out_of_contract) {
      vm.apply_warnings = vm.responseData.apply_warnings.contract_warning[0];
      warningEndOfContract();
    }
    if (!vm.responseData.warning_over_time.status &&
      (_.isEmpty(vm.responseData.apply_warnings.contract_warning) || vm.apply_params.accepted_out_of_contract)) {
      var data = vm.responseData.warning_over_time;
      vm.apply_params.accepted_ot = _.isEmpty(data.warning_message);
      vm.apply_params.accepted_ot_in_week = _.isEmpty(data.warning_in_week_message);
      vm.warningOT = {
        warningMessage: data.warning_message,
        warningInWeekMessage: data.warning_in_week_message,
        mobileDevice: data.mobile_device, note: data.note,
        opCenterTel: data.op_center_tel 
      };
      warningOverTime(data);
    }
  }

  function warningEndOfContract() {
    $("#spinner").addClass("ng-hide");
    $("#modal-warning-end-of-contract").modal("show");
  }

  function warningOverTime(response) {
    $("#modal-warning-end-of-contract").modal("hide");
    $("#work-modal").modal("hide");
    $("#spinner").addClass("ng-hide");
    if (!_.isEmpty(response.warning_message)) {
      $("#modal-warning-work-over-time").modal("show");
    } else {
      $("#modal-warning-work-over-time-in-week").modal("show");
    }
  }

  vm.refreshAfterCancleModalError = function() {
    $("#modal-apply-error").modal("hide");
    vm.refresh();
  }

  vm.refreshAfterCancleModalApply = function() {
    $("#work-modal").modal("hide");
    vm.refresh();
  }

  vm.confirmErrorApply = function() {
    $("#error-all-keeping-jobs").modal("hide");
  }

  vm.bgImageForLocation = function(locationType, thumbnailPath) {
    return orderCaseService.bgImageForLocation(locationType, thumbnailPath);
  };

  vm.bgForNotLawson = function(locationType, locationImage, thumbnailPath) {
    return orderCaseService.bgForNotLawson(locationType, locationImage, thumbnailPath);
  };

  vm.displayApplyDoneNote = function() {
    return !_.isEmpty(vm.apply_warnings);
  };

  vm.isContactDisplayed = function() {
    if (_.isUndefined(vm.applyConditionMessage)) return false;
    var errorType = Object.keys(vm.applyConditionMessage)[0]
    return errorType == APPLY_CONDITION_CONTACT_OP;
  }

  vm.getErrorString = function() {
    if (_.isUndefined(vm.applyConditionMessage)) return "";
    return Object.values(vm.applyConditionMessage)[0][0];
  }

  function feeFlagRegular(order_case_wage) {
    var data_fee = [];

    if (!_.isUndefined(order_case_wage.payment_urgent_price) && order_case_wage.payment_urgent_price > 0) {
      data_fee.push(orderCaseListService.feeFlagByKey(order_case_wage.payment_urgent_price));
    }

    return data_fee
  }

  function feeFlagHaken(order_case_wage) {
    var data_fee = [];

    var addition_fee = 0;
    if (!_.isUndefined(order_case_wage.other_addition_fee)) {
      addition_fee = order_case_wage.other_addition_fee;
    }
    if (!_.isUndefined(order_case_wage.total_unit_price_addition) && order_case_wage.total_unit_price_addition > 0) {
      data_fee.push(orderCaseListService.feeFlagByKey(order_case_wage.total_unit_price_addition));
    }
    if (order_case_wage.total_order_case_addition > 0 || addition_fee > 0) {
      var amount = order_case_wage.total_order_case_addition + addition_fee;
      data_fee.push(orderCaseListService.feeFlagByKey(amount, "offer_fee_by_order_rate"));
    }

    return data_fee;
  }
}
