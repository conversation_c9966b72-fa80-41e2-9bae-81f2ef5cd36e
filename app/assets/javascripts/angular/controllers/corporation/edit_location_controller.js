"use strict";

angular.module("corporationApp").controller("EditLocationController", EditLocationController);
EditLocationController.$inject = ["$location", "$sce", "checkValidDateFunction", "locationService", "toaster", "$scope"];

function EditLocationController($location, $sce, checkValidDateFunction, locationService, toaster, $scope) {
  var vm = this;
  vm.$scope = $scope;
  vm.params = {};
  vm.steps = {step1: true, step2: false, step3: false, step4: false};
  vm.params.location_pics = $("#location-pics-data").data("pics");
  var isParking = angular.element("#is-store-parking-area-usable").data("is-checked")
  if (isParking === true || isParking === false) {vm.params.is_store_parking_area_usable = isParking;}
  vm.currentStep = $("#step-data").data("step");
  vm.currentViolationDay = $("#violation-data").data("violation");
  vm.currentHakenAcceptanceDay = $("#haken-data").data("haken");
  vm.data_custom = {};
  vm.params.is_check_term_service = false;
  $(".edit-location-info").on("input", ".datetimepicker-input", function() {
    var modelName = $(this).attr("ng-model").split(".")[2];
    vm.data_custom[modelName] = $(this).val();
  });

  $('[data-toggle="tooltip"]').tooltip();
  function addEventForDatePicker() {
    setTimeout(function() {
      $(".js-datepicker").datetimepicker({
        format: FULL_DATE_FORMAT
      });
    }, 100);
  }

  function scrollTop() {
    window.scrollTo(0, 0);
  }

  vm.init = function() {
    addEventForDatePicker();
    if (vm.currentStep != 1) {
      vm.params.violation_day = vm.currentViolationDay;
      vm.params.haken_acceptance_started_at = vm.currentHakenAcceptanceDay;
      vm.steps.step1 = false;
      vm.steps.step2 = false;
      vm.steps.step3 = true;
    }
  };

  vm.clearDataDate = function() {
    vm.data_custom.input_date = "";
    vm.params.violation_day = "";
    vm.params.haken_acceptance_started_at = "";
  };

  vm.checkIsDate = function(field) {
    vm.data_custom[field] = checkValidDateFunction.checkValidDate(vm.data_custom[field]) || vm.data_custom[field];
    vm.formatDataStep1()
  };

  vm.formatDataStep1 = function() {
    if (vm.question1Option1Checked == false) {
      vm.params.haken_acceptance_started_at = moment(vm.data_custom.input_date).format(ORDER_DATEPICKER_FORMAT);
      vm.params.violation_day = moment(vm.data_custom.input_date).add(3, 'years').format(ORDER_DATEPICKER_FORMAT);
    }
    if (vm.question1Option1Checked == true) {
      if (vm.question2Option1Checked == true) {
        vm.params.violation_day = moment(vm.data_custom.input_date).add(3, 'years').format(ORDER_DATEPICKER_FORMAT);
        vm.params.haken_acceptance_started_at = moment(vm.data_custom.input_date).subtract(3, 'years').format(ORDER_DATEPICKER_FORMAT);
      } else {
        vm.params.violation_day = moment(vm.data_custom.input_date).format(ORDER_DATEPICKER_FORMAT);
        vm.params.haken_acceptance_started_at = moment(vm.data_custom.input_date).subtract(3, 'years').format(ORDER_DATEPICKER_FORMAT);
      }
    }
  }

  vm.submitStep1 = function() {
    scrollTop();
    vm.formatDataStep1();
    locationService.validateLocationStep1(vm.params).then(function(res) {
      var data = res.data;
      vm.formErrors = data.errors;
      if(data.status) {
        vm.steps.step1 = false;
        vm.steps.step2 = true;
      }
    })
  };

  vm.backToStep1 = function() {
    scrollTop();
    addEventForDatePicker();
    vm.steps.step1 = true;
    vm.steps.step2 = false;
  };

  vm.submitStep2 = function() {
    scrollTop();
    vm.steps.step2 = false;
    vm.steps.step3 = true;
  };

  vm.backToStep2 = function(redirectUrl) {
    if (vm.currentStep != 1 && !!redirectUrl) {
      window.location = redirectUrl;
      return;
    }
    scrollTop();
    vm.steps.step2 = true;
    vm.steps.step3 = false;
  };

  vm.submitStep3 = function() {
    scrollTop();
    if (vm.currentStep != 1) {
      _.omit(vm.params, "violation_day");
      _.omit(vm.params, "haken_acceptance_started_at");
    }
    $("#loading-screen").removeClass("d-none");
    locationService.updateStoreLocation(vm.params).then(function(res) {
      $("input").removeClass("error-field");
      $("#loading-screen").addClass("d-none");
      var data = res.data;
      vm.formErrors = data.errors;
      if(data.status) {
        vm.steps.step3 = false;
        vm.steps.step4 = true;
      } else {
        vm.errorsOptions = data.errors_options;
        if (!!vm.errorsOptions.columns) {
          vm.errorsOptions.columns.forEach(function(field) {
            $("input#location_pics_" + vm.errorsOptions.index + "_" + field).addClass("error-field");
          });
        }
      }
    })
  };

  vm.disableSaveBtn = function() {
    return !vm.params.is_check_term_service || (vm.params.is_store_parking_area_usable != true &&  vm.params.is_store_parking_area_usable != false)
  }

  vm.formatDay = function(columns) {
    return "西暦  " + moment(columns).format("YYYY") + "  年   "  + moment(columns).format("MM") + "  月  " + moment(columns).format("DD") + "  日";
  };
}
