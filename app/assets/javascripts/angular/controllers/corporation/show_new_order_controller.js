"use strict";

angular.module("corporationApp").controller("ShowNewOrderController", ShowNewOrderController);
ShowNewOrderController.$inject = ["orderService", "$sce", "newOrderService"];

function ShowNewOrderController(orderService, $sce, newOrderService) {
  var REST_TIMES = [1, 2, 3];
  var VIOLATION_DAYS = 60;
  var vm = this;
  vm.params = {};
  vm.location = [];
  vm.orderData = {};
  vm.selectedLocation = {isValidLocation: true, haken_destination: [], claim: [], mandator: [], order: []}
  vm.selectedType = "individual_order";
  vm.totalCollectiveOrder = vm.totalIndividualOrder = 0;
  vm.estimateData = [];
  vm.dayNames = I18n.t("date.abbr_day_names");
  vm.types = {"batch": "collective_order", "template": "template_order", "individual": "individual_order"}
  vm.canSubmitStep1 = true;
  vm.canSubmitStep3 = true;
  vm.isCheckTermService = angular.element("#is-check-term-service").data("is-checked");
  vm.agreeTerm = vm.isCheckTermService;

  var picsData = ["claim", "haken_destination", "mandator", "order",
    "is_valid_order_info", "violation_day", "days_to_violation_day", "order_pic_tel",
    "prefecture_id", "is_approval_required", "closed_day_error_message", "transaction_error_message",
    "location_not_survey_error_message", "location_survey_path"];
  var paramsStep1 = ["definition_id", "corporation_id", "corporation_group_id", "organization_id",
    "location_id", "haken_destination_pic_id", "haken_destination_pic_position", "haken_destination_pic_tel",
    "mandator_id", "mandator_position", "mandator_tel", "claim_pic_id", "claim_pic_position",
    "claim_pic_tel", "order_pic_id", "order_pic_email", "order_pic_tel", "note"];

  vm.initOrder = function(paramsData) {
    var orderParams = paramsData.order;
    var locationPics = paramsData.location_pics;
    _.forEach(picsData, function(picData) {
      vm.selectedLocation[picData] = locationPics[picData];
    });

    _.forEach(paramsStep1, function(attr) {
      vm.params[attr] = orderParams[attr];
    });

    vm.params.location_id = orderParams.location_id.toString();
    vm.locations = paramsData.locations;
    var orderType = vm.types[orderParams.type_id];
    vm.selectedType = orderType;
    vm.params.organization_full_name = orderParams.organization_full_name;
    loadLocationData();
    vm.checkLocationIsValid();
    vm.checkViolationDay();
    vm.checkSubmitStep1Condition();
    vm.selectedLocation.violation_day = formatDate(orderParams.violation_day, ORDER_DATEPICKER_FORMAT);
    vm.orderData.overall_started_at = formatDate(orderParams.overall_started_at, ORDER_DATEPICKER_FORMAT);
    vm.orderID = orderParams.id;

    orderService.orderBranchesDetail({order_id: orderParams.id}).then(function(res) {
      vm.params[vm.selectedType] = res.data;
      var orderBranches = res.data;
      _.forEach(Object.keys(orderBranches), function(key) {
        orderBranches[key] = vm.initOrderBranchRestTime(orderBranches[key]);
      });
      vm.params[vm.selectedType] = orderBranches;
      initOrderBranchDetail();
    });
  };

  function convertToDay(dayNumbers) {
    var numberArr = dayNumbers.split(",");
    return _.map(numberArr, function(number) {
      return vm.dayNames[number];
    }).join("");
  }

  function convertToDateFormat(params) {
    var duration = params.week_count;
    var startDate = params.started_at;
    return [startDate.toString(), "～", duration, I18n.t("corporation.order.step3.detail_multi_modal.week")].join("");
  }

  function convertTemplateToDateFormat(params, firstCase) {
    var startDate = params.started_at;
    var endDate = params.ended_at;
    if (startDate === endDate) {return firstCase.date};
    return [startDate.toString(), "～", endDate.toString()].join("");
  };

  vm.isDisplayPriceColumn = function(orderData, field) {
    return _.sumBy(orderData, field) !== 0;
  }

  vm.isDisplaySpeacialCharge = function(orderData, field) {
    return _.sumBy(orderData, field) !== 0;
  }

  vm.displayEstimateByStaff = function(price, staffCount) {
    return I18n.t("corporation.order.step3.detail_modal.price_per_staff", {price: price, staff_count: staffCount});
  }

  function initOrderBranchDetail() {
    vm.estimateData = _.map(vm.params[vm.selectedType], function(order){
      var extendData = {};

      if (vm.selectedType === "collective_order" || vm.selectedType === "template_order") {
        var weekCount = "";
        if (!_.isUndefined(order.started_at)) {
          weekCount = convertToDateFormat({week_count: order.week_count, started_at: order.started_at});
        }
        extendData = {
          total_estimation: _.sumBy(order.estimate_data, "summary"),
          selected_days: convertToDay(order.selected_days ? order.selected_days : ""),
          week_count: weekCount,
          total_employees: _.sumBy(order.estimate_data, "staff_count")
        }

        if(vm.selectedType === "template_order"){
          extendData["selected_days"] = convertTemplateToDateFormat({started_at: order.started_at, ended_at: order.ended_at}, order.estimate_data[0])
        }
      }
      return angular.extend({data: order.estimate_data}, extendData);
    });

    if (vm.selectedType === "collective_order" || vm.selectedType === "template_order") {
      vm.totalCollectiveOrder = _.sumBy(vm.estimateData, "total_estimation");
    } else {
      vm.totalIndividualOrder = _.sumBy(vm.estimateData, "data[summary]");
    }
  }

  vm.isTrue = function(value) {
    return (value == true) || (value == "true");
  }

  vm.initOrderBranchRestTime = function(order_branch) {
    var rest_times = [];
    _.forEach(REST_TIMES, function(rest_time) {
      var startTime = order_branch["rest" + rest_time + "_started_at"];
      var endTime = order_branch["rest" + rest_time + "_ended_at"];
      if (_.isEmpty(order_branch["rest" + rest_time + "_started_at"])) return;
      order_branch["rest" + rest_time + "_started_at"] = formatDate(startTime, TIME_PICKER_FORMAT);
      order_branch["rest" + rest_time + "_ended_at"] = formatDate(endTime, TIME_PICKER_FORMAT);
      rest_times.push(rest_time);
    });
    order_branch["rest_times"] = rest_times;
    return order_branch;
  }

  function loadLocationData() {
    vm.location = _.find(vm.locations, function(lo) {
      return lo.id === parseInt(vm.params.location_id);
    }) || [];
  };

  vm.isStoreParking = function() {
    var isStoreParking = vm.location.is_store_parking_area_usable;
    if (!_.isNull(isStoreParking)) {
      return I18n.t("corporation.order.store_parking." + isStoreParking);
    }
  };

  function formatDate(date, formatType) {
    if (_.isEmpty(date)) return;
    return moment.parseZone(date).format(formatType);
  };

  vm.getViolationDay = function() {
    var violationDay = vm.selectedLocation.violation_day;
    if (violationDay) {
      return violationDay.split("(")[0];
    };
  };

  vm.getLocationPic = function(typeData) {
    var type = typeData + "_pic";
    if (typeData === "mandator") {
      type = typeData;
    };
    var picName = vm.getObjectName(typeData, type + "_id");
    var picPosition = type === "order_pic" ? vm.params[type + "_email"] : vm.params[type + "_position"];
    var isDeleted = isDeletedOrderPic(typeData, type + "_id") ? I18n.t("corporation.order.step3.deleted_order_pic") : "";
    return [picName, picPosition, vm.params[type + "_tel"], isDeleted].join(" ");
  };

  vm.getObjectName = function(typeData, typeId) {
    var object = _.find(vm.selectedLocation[typeData], function(o) {
      return o.id === vm.params[typeId];
    });
    if (object) {
      return object.name;
    }
  };

  var isDeletedOrderPic = function(typeData, picId) {
    if (typeData !== "order") { return; }
    var object = _.find(vm.selectedLocation[typeData], function(o) {
      return o.id === vm.params[picId];
    });
    if (object) {
      return !!object.deleted_at;
    }
    return true;
  };

  vm.checkLocationIsValid = function() {
    vm.selectedLocation.isValidLocation = true;
    var requiresInfo = [vm.selectedLocation.claim, vm.selectedLocation.haken_destination,
      vm.selectedLocation.mandator];
    if (!_.isEmpty(vm.params.location_id.toString())) {
      _.forEach(requiresInfo, function(locationPic) {
        if (_.isUndefined(locationPic) || !vm.selectedLocation.is_valid_order_info) {
          vm.params.order_pic_tel = "";
          vm.selectedLocation.isValidLocation = false;
        }
      });
    }
  };

  vm.checkViolationDay = function() {
    if (!vm.selectedLocation) {return;}
    var days = vm.selectedLocation.days_to_violation_day;
    var message = "";

    if (!_.isInteger(days)) {
      message = I18n.t("corporation.order.step1.violation_day.blank");
    } else if (days <= 0) {
      message = I18n.t("corporation.order.step1.violation_day.error_message");
    } else if (days <= 30) {
      message = I18n.t("corporation.order.step1.violation_day.notice_message_day_30", {days: days});
    } else if (days >= 31 && days <= VIOLATION_DAYS) {
      message = I18n.t("corporation.order.step1.violation_day.notice_message_day_31", {days: days});
    };
    vm.selectedLocation.violation_day_error_message = $sce.trustAsHtml(message);
  };

  vm.checkSubmitStep1Condition = function() {
    var days = vm.selectedLocation.days_to_violation_day;
    var isValidClosedDay = _.isEmpty(vm.selectedLocation.closed_day_error_message);
    var isLocationNotSurvey = _.isEmpty(vm.selectedLocation.location_not_survey_error_message);
    vm.isValidLocation = vm.selectedLocation.isValidLocation && days > 0 && isValidClosedDay && isLocationNotSurvey;
    vm.canSubmitStep1 = vm.isValidLocation && _.isEmpty(vm.selectedLocation.transaction_error_message);
  };

  vm.showMessageError = function() {
    vm.canSubmitStep3 = vm.canSubmitStep1 && isValidStartDate();
    var error = [];
    if (!vm.isValidLocation) {
      error.push(I18n.t("corporation.order.messages.invalid_location"));
    };
    if (!isValidStartDate()) {
      error.push(I18n.t("corporation.order.messages.invalid_start_date"))
    };
    error.push(vm.selectedLocation.transaction_error_message);
    return $sce.trustAsHtml(error.join("<br>"));
  };

  function isValidStartDate() {
    return moment().isSameOrBefore(vm.orderData.overall_started_at, "day");
  };

  vm.updateOrderStatus = function(orderAction) {
    vm.showSpinner();
    newOrderService.updateOrder(vm.orderID, {order_action: orderAction}).then(function(res) {
      if (res.data.status) {
        window.location.href = res.data.redirect_path;
      } else {
        $("#spinner").addClass("ng-hide");
        vm.createErrors = res.data.errors;
        if (!_.isEmpty(vm.createErrors)) {
          window.scrollTo(0, 0);
        }
      }
    });
  }

  vm.showSpinner = function() {
    $("#spinner").removeClass("ng-hide");
  };
}
