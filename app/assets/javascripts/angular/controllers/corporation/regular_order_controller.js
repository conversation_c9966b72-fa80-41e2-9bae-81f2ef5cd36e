"use strict";

angular.module("corporationApp").controller("RegularOrderController", RegularOrderController);
RegularOrderController.$inject = ["$location", "$sce", "checkValidDateFunction", "regularOrderService", "$scope"];

function RegularOrderController($location, $sce, checkValidDateFunction, regularOrderService, $scope) {
  var REST_TIMES = [1, 2, 3];
  var vm = this;

  vm.$scope = $scope;
  var picsData = ["claim", "haken_destination", "mandator", "order",
    "is_valid_order_info", "violation_day", "days_to_violation_day", "order_pic_tel",
    "prefecture_id", "is_approval_required", "closed_day_error_message", "transaction_error_message",
    "location_not_survey_error_message", "location_survey_path"];
  var paramsStep1 = ["definition_id", "corporation_id", "corporation_group_id", "organization_id",
    "location_id", "haken_destination_pic_id", "haken_destination_pic_position", "haken_destination_pic_tel",
    "mandator_id", "mandator_position", "mandator_tel", "claim_pic_id", "claim_pic_position",
    "claim_pic_tel", "order_pic_id", "order_pic_email", "order_pic_tel", "note"];
  var ORDER_LOCATION_DATA = ["postal_code", "tel", "fax", "is_store_parking_area_usable"];
  vm.originLocationData = {};
  vm.first_time_show_edit = false;
  vm.dayNames = I18n.t("date.abbr_day_names");
  vm.params = {};
  vm.selectedLocation = {};
  vm.params.is_used_existing_pic = true;
  vm.params.over_limit_portion = false;
  vm.selectedType = "recurring_order";
  vm.totalCollectiveOrder = vm.totalNonRecurringOrder = 0;
  vm.steps = {
    step1: true,
    step2: false,
    step3: false
  };
  vm.isEditOrder = false;
  vm.organizationOptions = angular.element(".organization-options").data("organization-options");
  vm.corporationId = angular.element(".order-branch-area").data("corporation-id");
  vm.location = [];
  vm.selectedLocation = {isValidLocation: true, haken_destination: [], claim: [], mandator: [], order: []}
  _.forEach(paramsStep1, function(attr) {
      vm.params[attr] = "";
  });
  vm.orderData = {};

  vm.params.recurring_order = [
    {
      order_number: 1,
      break_time: 0,
      is_special_offer: false,
      rest_times: [1]
    }
  ];

  vm.orderDataList = {};

  vm.params.non_recurring_order = [
    {
      order_number: 1,
      break_time: 0,
      week_count: 1,
      is_special_offer: false,
      rest_times: [1]
    }
  ];

  vm.breakTimeOptions = [0, 15, 30, 45, 60, 75, 90, 99];
  vm.durationOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
  vm.specialOfferFeeOptions = SPECIAL_OFFER_FEE;
  vm.estimateData = [];
  vm.totalEstimation = {recurring_order: [], non_recurring_order: []};
  vm.canSubmitStep1 = true;
  vm.buttonsDisabled = false;
  vm.canSubmitStep3 = true;
  vm.isValidLocation = true;
  var currentUserID = angular.element(".create-order__more-info").data("current-user-id");

  vm.initLocationOptions =  function (locations) {
    vm.locations = locations;
  }

  function loadLocationData() {
    vm.location = _.find(vm.locations, function(lo) {
      return lo.id === parseInt(vm.params.location_id);
    }) || [];
  };

  vm.isDisplayPriceColumn = function(orderData, field) {
    return _.sumBy(orderData, field) !== 0;
  }

  vm.isDisplaySpeacialCharge = function(orderData, field) {
    return _.sumBy(orderData, field) !== 0;
  }

  vm.getViolationDay = function() {
    var violationDay = vm.selectedLocation.violation_day;
    if (violationDay) {
      return violationDay.split("(")[0];
    };
  };

  // Edit Order
  vm.initEditOrder = function(paramsData) {
    vm.first_time_show_edit = true;
    vm.isEditOrder = true;
    var locationPics = paramsData.location_pics;
    _.forEach(picsData, function(picData) {
      vm.selectedLocation[picData] = locationPics[picData];
    });
    vm.locations = paramsData.locations;
    var orderParams = paramsData.order;
    var orderType = orderParams.type_id === "recurring" ? "recurring_order" : "non_recurring_order";
    vm.estimateData = vm.totalEstimation[orderType];
    vm.selectedType = orderType;

    angular.element("." + orderType).trigger("click");
    _.forEach(paramsStep1, function(attr) {
      vm.params[attr] = orderParams[attr];
    });
    vm.params.is_used_existing_pic = orderParams.is_used_existing_pic;
    vm.params.organization_id = String(vm.params.organization_id);
    vm.params.organization_full_name = orderParams.organization_full_name;
    vm.params.location_id = String(vm.params.location_id);
    vm.params.id = orderParams.id;

    loadLocationData();
    initLocationData(orderParams);
    vm.checkSubmitStep1Condition();
    vm.selectedLocation.violation_day = formatDate(orderParams.violation_day, ORDER_DATEPICKER_FORMAT);
    vm.orderData.overall_started_at = formatDate(orderParams.overall_started_at, ORDER_DATEPICKER_FORMAT);
    vm.orderData.overall_ended_at = formatDate(orderParams.overall_ended_at, ORDER_DATEPICKER_FORMAT);

    regularOrderService.orderBranchesDetail({order_id: orderParams.id}).then(function(res) {
      vm.params[vm.selectedType] = res.data;
      var orderBranches = res.data;
      _.forEach(Object.keys(orderBranches), function(key) {
        orderBranches[key] = vm.initOrderBranchRestTime(orderBranches[key]);
      });
      vm.params[vm.selectedType] = orderBranches;
      initOrderBranchDetail();
    });
  };

  function initOrderBranchDetail() {
    vm.estimateData = _.map(vm.params[vm.selectedType], function(order){
      var extendData = {};

      if (vm.selectedType === "recurring_order") {
        var weekCount = "";
        if (!_.isUndefined(order.started_at)) {
          weekCount = convertToDateFormat({week_count: order.week_count, started_at: order.started_at});
        }
        extendData = {
          total_estimation: _.sumBy(order.estimate_data, "summary"),
          selected_days: convertToDay(order.selected_days ? order.selected_days : ""),
          started_at: order.started_at,
          ended_at: order.ended_at,
          working_start_time: order.working_start_time,
          working_end_time: order.working_end_time,
          week_count: weekCount,
          is_except_holiday: order.is_except_holiday,
          is_urgent: order.is_urgent,
          total_employees: _.sumBy(order.estimate_data, "staff_count")
        }
      }
      return angular.extend({data: order.estimate_data}, extendData);
    });
    if (vm.selectedType === "recurring_order") {
      vm.totalRecurringOrder = _.sumBy(vm.estimateData, "total_estimation");
    } else {
      vm.totalNonRecurringOrder = calculateTotalNonRecurringOrder(vm.estimateData, true);
    }
  }

  function calculateTotalNonRecurringOrder(estimate_data, isEditOrder) {
    var sum = 0;
    estimate_data.forEach(function(estimate){
      var tmp = estimate;
      if(isEditOrder) {
        tmp = estimate["data"];
      }
      sum += _.sumBy(tmp, "summary");
    });
    return sum;
  }

  vm.isStoreParking = function() {
    var isStoreParking = vm.location.is_store_parking_area_usable;
    if (!_.isNull(isStoreParking)) {
      return I18n.t("corporation.order.store_parking." + isStoreParking);
    }
  };

  vm.getObjectName = function(typeData, typeId) {
    var object = _.find(vm.selectedLocation[typeData], function(o) {
      return o.id === vm.params[typeId];
    });
    if (object) {
      return object.name;
    }
  };

  vm.showSpinner = function() {
    $("#spinner").removeClass("ng-hide");
  };

  function convertToDay(dayNumbers) {
    var numberArr = dayNumbers.split(",");
    return _.map(numberArr, function(number) {
      return vm.dayNames[number];
    }).join("");
  }

  function convertToDateFormat(params) {
    var duration = params.week_count;
    var startDate = params.started_at;
    return [startDate.toString(), "～", duration, I18n.t("corporation.order.step3.detail_multi_modal.week")].join("");
  }

  vm.getLocationPic = function(typeData) {
    var type = typeData + "_pic";
    if (typeData === "mandator") {
      type = typeData;
    };
    var picName = vm.getObjectName(typeData, type + "_id");
    var picPosition = type === "order_pic" ? vm.params[type + "_email"] : vm.params[type + "_position"];
    var isDeleted = isDeletedOrderPic(typeData, type + "_id") ? I18n.t("corporation.order.step3.deleted_order_pic") : "";
    return [picName, picPosition, vm.params[type + "_tel"], isDeleted].join(" ");
  };

  function formatDate(date, formatType) {
    if (_.isEmpty(date)) return;
    return moment.parseZone(date).format(formatType);
  };

  var isDeletedOrderPic = function(typeData, picId) {
    if (typeData !== "order") { return; }
    var object = _.find(vm.selectedLocation[typeData], function(o) {
      return o.id === vm.params[picId];
    });
    if (object) {
      return !!object.deleted_at;
    }
    return true;
  };

  vm.checkSubmitStep1Condition = function() {
    var days = vm.selectedLocation.days_to_violation_day;
    var isValidClosedDay = _.isEmpty(vm.selectedLocation.closed_day_error_message);
    var isLocationNotSurvey = _.isEmpty(vm.selectedLocation.location_not_survey_error_message);
    vm.isValidLocation = vm.selectedLocation.isValidLocation && days > 0 && isValidClosedDay && isLocationNotSurvey;
    vm.canSubmitStep1 = vm.isValidLocation && _.isEmpty(vm.selectedLocation.transaction_error_message);
  };

  function initLocationData(orderParams) {
    angular.extend(vm.originLocationData, vm.location);
    _.forEach(ORDER_LOCATION_DATA, function(attr) {
      vm.location[attr] = orderParams["location_" + attr];
    });
    vm.location.full_address = orderParams.full_address;
  };

  vm.initOrderBranchRestTime = function(order_branch) {
    var rest_times = [];
    _.forEach(REST_TIMES, function(rest_time) {
      var startTime = order_branch["rest" + rest_time + "_started_at"];
      var endTime = order_branch["rest" + rest_time + "_ended_at"];
      if (_.isEmpty(order_branch["rest" + rest_time + "_started_at"])) return;
      order_branch["rest" + rest_time + "_started_at"] = formatDate(startTime, TIME_PICKER_FORMAT);
      order_branch["rest" + rest_time + "_ended_at"] = formatDate(endTime, TIME_PICKER_FORMAT);
      rest_times.push(rest_time);
    });
    order_branch["rest_times"] = rest_times;
    return order_branch;
  }

  vm.displayEstimateByStaff = function(price, staffCount) {
    return I18n.t("corporation.order.step3.detail_modal.price_per_staff", {price: price, staff_count: staffCount});
  }
}
