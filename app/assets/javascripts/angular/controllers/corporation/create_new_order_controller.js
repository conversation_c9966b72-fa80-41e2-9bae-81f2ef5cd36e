"use strict";

angular.module("corporationApp").controller("CreateNewOrderController", CreateNewOrderController);
CreateNewOrderController.$inject = ["$sce", "newOrderService", "orderService",
  "$scope", "orderTemplateService", "toaster"];

function CreateNewOrderController($sce, newOrderService, orderService,
  $scope, orderTemplateService, toaster) {
  moment.locale('ja');
  var vm = this;
  var LOCALE = I18n.locale;
  var MIN_STAFF = 1;
  var MAX_STAFF = 100;
  var NUMBER_BASE = 10;
  var MAX_REST_TIMES = 3;
  var VIOLATION_DAYS = 60;
  var PIC_DATA = ["claim", "haken_destination", "mandator", "order",
    "is_valid_order_info", "violation_day", "days_to_violation_day", "order_pic_tel",
    "prefecture_id", "is_approval_required", "closed_day_error_message", "transaction_error_message",
    "location_not_survey_error_message", "location_survey_path"];
  var STEP_1_PARAMS = ["definition_id", "corporation_id", "corporation_group_id", "organization_id",
    "location_id", "haken_destination_pic_id", "haken_destination_pic_position", "haken_destination_pic_tel",
    "mandator_id", "mandator_position", "mandator_tel", "claim_pic_id", "claim_pic_position",
    "claim_pic_tel", "order_pic_id", "order_pic_email", "order_pic_tel", "note"];
  var TEMPLATE_COPY_FIELDS = ["name", "location_id", "last_used_at", "is_time_changable", "staff_count", "rest_times"];
  var TEMPLATE_PIC_FIELDS = ["is_used_existing_pic", "haken_destination_pic_id", "haken_destination_pic_position",
    "haken_destination_pic_tel", "mandator_id", "mandator_position", "mandator_tel", "claim_pic_id", "claim_pic_position",
    "claim_pic_tel", "order_pic_id", "order_pic_email", "order_pic_tel"];
  var TEMPLATE_WORKING_TIME_FIELDS = ["working_start_time", "working_end_time", "required_start_time", "required_end_time"];
  var TEMPLATE_COMMON_FIELDS = ["working_start_time", "working_end_time", "required_start_time", "required_end_time",
    "rest1_start_time", "rest1_end_time", "rest2_start_time", "rest2_end_time", "rest3_start_time", "rest3_end_time", "staff_count"];
  var NESTED_ERRORS = ["started_at", "working_start_time", "working_end_time", "break_time", "rest1_started_at",
    "rest1_ended_at", "rest2_started_at", "rest2_ended_at", "rest3_started_at", "rest3_ended_at", "staff_count",
    "required_start_time", "required_end_time", "is_except_holiday"];
  var ORDER_BRANCH_DATA = ["started_at", "ended_at", "working_start_time", "working_end_time", "staff_count", "is_range",
    "is_except_holiday", "is_time_changable", "is_urgent", "is_special_offer", "special_offer_fee", "special_offer_note"];
  var BREAK_TIME_ATTRS = ["rest1_started_at", "rest1_ended_at", "rest2_started_at", "rest2_ended_at", "rest3_started_at",
    "rest3_ended_at"];
  var REST_TIMES = ["1", "2", "3"];

  var DEFAULT_EMAIL_SUFFIX = "@lawsonstaff.co.jp";
  var TEMPLATE_TYPE = "template_order";
  var MIDNIGHT = "00:00";

  vm.$scope = $scope;
  vm.params = {
    order_branches_attributes: [],
    is_used_existing_pic: true
  };
  vm.scrollStartDates = [];
  vm.totalDates = 0;
  vm.totalStaffs = 0;
  vm.canSubmitStep1 = true;
  vm.restTime = {rest_start_at: "", rest_end_at: ""}
  vm.maxRestTimes = MAX_REST_TIMES;
  vm.specialOfferFeeOptions = SPECIAL_OFFER_FEE;
  vm.steps = {
    step1: true,
    step2: false,
    step3: false
  };
  vm.orderData = {};
  vm.estimateData = [];
  vm.toasterTimeout = 6200;
  vm.isEditOrder = false;
  vm.selectedLocation = {};
  vm.originalOrderTemplateID = "";
  vm.deletedOrderBranchIDs = [];
  vm.canSubmitStep3 = true;
  vm.hasSavedTemplate = false;
  vm.isMobile = false;
  vm.isIEAccessible = false;
  vm.exceptHoliday = false;
  vm.isVioldationDateUnchanged = true;
  vm.isLocationSurveyUnchanged = true;
  vm.isNotOneManOperation = true;
  vm.confirmMidnight = false

  var DATE_WITH_PREFIX_FORMAT = "YYYY年MM月DD日";
  var DAY_AND_MONTH_FORMAT = "MM月DD日（dd）";
  var DAY_AND_MONTH = "MM月DD日";
  var DAY_AND_MONTH_MOBILE = "MM/DD";
  var ORDER_BRANCH_FIELDS = ["staff_count", "working_start_time", "working_end_time",
    "is_time_changable", "required_start_time", "required_end_time", "is_except_holiday",
    "is_special_offer", "special_offer_fee", "special_offer_note", "rest_times"]

  resetCurrentTemplate();
  resetErrors();

  function resetCurrentTemplate() {
    vm.currentTemplate = {
      staff_count: 1,
      working_start_time: "",
      working_end_time: "",
      is_time_changable: false,
      required_start_time: "",
      required_end_time: "",
      is_except_holiday: false,
      is_special_offer: false,
      special_offer_fee: "",
      special_offer_note: "",
      rest_times: [angular.copy(vm.restTime)]
    };
  };

  function resetCheckboxes() {
    vm.confirmMidnight = false;
  }

  function resetErrors() {
    vm.errorMessages = {};
    vm.nestedErrorMessages = [];
    vm.overLimitPortion = false;
    vm.firstNestedErrorTrackingIndex = 0;
  };

  function removeErrorElm() {
    $(".js-add-tr").remove();
    $(".order-time-block").removeClass("tr-error");
    $(".item-working-hours .datetimepicker").removeClass("form-error");
    $(".item-break-time .datetimepicker").removeClass("form-error");
    $(".item-add-more").removeClass("form-error");
  }

  vm.init = function (isNeedConfirmOrder, isMobile, isIEAccessible) {
    vm.isMobile = isMobile;
    vm.isIEAccessible = isIEAccessible;
    _.forEach(STEP_1_PARAMS, function(attr) {
      vm.params[attr] = "";
    });
    vm.initDateRangePickerOwnerOrder();
    vm.checkValidCreateOrderType();
    vm.initStep3Value(isNeedConfirmOrder);
    vm.corporationId = angular.element(".order-branch-area").data("corporation-id");
    vm.isCheckTermService = angular.element("#is-check-term-service").data("is-checked");
    setTimeChangableSwitch();
  };

  vm.checkValidCreateOrderType = function() {
    var validCreateOrderType = angular.element("#valid-create-order-type").data("is-valid");
    if (validCreateOrderType == "valid") {return;}
    if(validCreateOrderType == "invalid_step_1") {
      $("#modal-store-invalid-step1").modal("show");
    } else {
      $("#modal-store-cant-create-order").modal("show");
    }
  };

  vm.initStep3Value = function(isNeedConfirmOrder) {
    vm.isNeedConfirmOrder = isNeedConfirmOrder;
    var strKey = isNeedConfirmOrder ? "submit_to_superior" : "agree_and_send_create_order";
    vm.saveOrderStr = I18n.t("corporation.order.button")[strKey];
  };

  vm.initLocationOptions =  function (locations) {
    vm.locations = locations;
    if (!vm.isEditOrder && vm.locations.length == 1) {
      vm.params.location_id = vm.locations[0].id;
    }
  };

  vm.initEditOrder = function(paramsData) {
    vm.firstTimeLoadLocation = true;
    vm.firstTimeLoadTemplate = true;
    vm.isEditOrder = true;
    vm.enableStep(2);
    var locationPics = paramsData.location_pics;
    _.forEach(PIC_DATA, function(picData) {
      vm.selectedLocation[picData] = locationPics[picData];
    });
    vm.locations = paramsData.locations;
    var orderParams = paramsData.order;
    var orderType = "template_order";
    vm.selectedType = orderType;

    _.forEach(STEP_1_PARAMS, function(attr) {
      vm.params[attr] = orderParams[attr];
    });
    vm.params.is_used_existing_pic = orderParams.is_used_existing_pic;
    vm.params.organization_id = String(vm.params.organization_id);
    vm.params.organization_full_name = orderParams.organization_full_name;
    vm.params.location_id = String(vm.params.location_id);
    vm.originalOrderTemplateID = orderParams.order_template_id;
    vm.params.id = orderParams.id;

    vm.checkLocationIsValid();
    vm.checkViolationDay();
    vm.checkSubmitStep1Condition();
    vm.selectedLocation.violation_day = formatDate(orderParams.violation_day, ORDER_DATEPICKER_FORMAT);
    vm.orderData.overall_started_at = formatDate(orderParams.overall_started_at, ORDER_DATEPICKER_FORMAT);
    vm.orderData.overall_ended_at = formatDate(orderParams.overall_ended_at, ORDER_DATEPICKER_FORMAT);

    orderService.orderBranchesDetail({order_id: orderParams.id}).then(function(res) {
      _.forEachRight(res.data, function(orderBranch) {
        vm.initOrderBranchData(orderBranch);
      });
    });
  };

  vm.initOrderBranchData = function(rawOrderBranch) {
    var orderBranch = {};
    orderBranch["id"] = rawOrderBranch.id;
    _.forEach(ORDER_BRANCH_DATA, function(attr) {
      orderBranch[attr] = rawOrderBranch[attr];
    });
    orderBranch["required_start_time"] = formatDate(rawOrderBranch["required_start_time"], TIME_PICKER_FORMAT);
    orderBranch["required_end_time"] = formatDate(rawOrderBranch["required_end_time"], TIME_PICKER_FORMAT);
    if (orderBranch["started_at"] != orderBranch["ended_at"]) {
      orderBranch["is_range"] = true;
    }
    var restTimes = [];
    _.forEach(REST_TIMES, function(index) {
      var restTime = {};
      var startTime = rawOrderBranch["rest" + index + "_started_at"];
      var endTime = rawOrderBranch["rest" + index + "_ended_at"];
      if (_.isEmpty(rawOrderBranch["rest" + index + "_started_at"])) return;
      restTime["rest_start_at"] = formatDate(startTime, TIME_PICKER_FORMAT);
      restTime["rest_end_at"] = formatDate(endTime, TIME_PICKER_FORMAT);
      restTimes.push(restTime);
    });
    if (_.isEmpty(restTimes)) {restTimes.push(angular.copy(vm.restTime));}
    orderBranch["rest_times"] = restTimes;
    vm.assignRestField(orderBranch);
    vm.initCalendarForEdit(orderBranch);
  };

  _.forEach(ORDER_BRANCH_FIELDS, function(field) {
    vm.$scope.$watch("vm.currentTemplate." + field, function(newVal, oldVal){
      vm.changeOrderBranches(field);
    }, true);
  });

  vm.changeOrderBranches = function(field) {
    _.forEach(vm.params.order_branches_attributes, function(orderBranch) {
      orderBranch[field] = angular.copy(vm.currentTemplate[field]);
    });
    switch(field) {
      case "is_special_offer":
        if(vm.currentTemplate[field]) {
          vm.currentTemplate.special_offer_fee = vm.specialOfferFeeOptions[0];
        } else {
          vm.currentTemplate.special_offer_fee = "";
          vm.currentTemplate.special_offer_note = "";
        }
        break;
      case "is_time_changable":
        if(!vm.currentTemplate[field]) {
          vm.currentTemplate.required_start_time = "";
          vm.currentTemplate.required_end_time = "";
        } else {
          vm.currentTemplate.required_start_time = vm.currentTemplate.working_start_time;
          vm.currentTemplate.required_end_time = vm.currentTemplate.working_end_time;
        }
        break;
      case "staff_count":
        vm.countDateStaff();
        break;
    }
  };

  vm.addRestTime = function() {
    if (vm.currentTemplate.rest_times.length === MAX_REST_TIMES) return;1
    vm.currentTemplate.rest_times.push(angular.copy(vm.restTime));
    vm.initDateTimePicker();
  };

  vm.removeRestTime = function(index) {
    vm.currentTemplate.rest_times.splice(index, 1);
  };

  vm.countRestTime = function(rest_time) {
    var startTime = rest_time.rest_start_at;
    var endTime = rest_time.rest_end_at;
    if (_.isEmpty(startTime) || _.isEmpty(endTime)) return;
    var start = moment(startTime, TIME_PICKER_FORMAT);
    var end = moment(endTime, TIME_PICKER_FORMAT);
    if (end.isBefore(start)) end.add(1, "day");
    return I18n.t("corporation.order.step2.total_minutes", {minutes: end.diff(start, "minutes")});
  }

  vm.switchDatePicker = function() {
    if($(".switch-multi").hasClass("active")) {
      $(".switch-multi").removeClass("active");
      $(".switch-range").addClass("active");
      $("#scroll-calendar").show();
      $("#multi-scroll-calendar").hide();
    } else if($(".switch-range").hasClass("active")) {
      $(".switch-multi").addClass("active");
      $(".switch-range").removeClass("active");
      $("#scroll-calendar").hide();
      $("#multi-scroll-calendar").show();
    }
  };

  vm.initDateRangePickerOwnerOrder = function() {
    angular.element(document).ready(function() {
      var dateRangeOptions = {
        numberOfMonths: (vm.isMobile || vm.isIEAccessible ? 1 : 13),
        language: "ja",
        gotoCurrent: true,
        minDate: 0,
        maxDate: "+12M",

        beforeShowDay: function (date) {
          var datefrom = $.datepicker.parseDate(
            $(this).datepicker("option", "dateFormat"),
            $("#datefrom").val()
          );
          var dateto = $.datepicker.parseDate(
            $(this).datepicker("option", "dateFormat"),
            $("#dateto").val()
          );
          var dateClass =
            datefrom &&
            (date.getTime() == datefrom.getTime() || (dateto && date >= datefrom && date <= dateto))
              ? "dp-highlight dp-in-range"
              : "";
          if (datefrom && datefrom.getTime() == date.getTime()) {
            dateClass += " dp-start-date";
          } else if (dateto && dateto.getTime() == date.getTime()) {
            dateClass += " dp-end-date";
          }
          return [true, dateClass];
        },

        onSelect: function (dateText, inst) {
          var datefrom = $.datepicker.parseDate(
            $(this).datepicker("option", "dateFormat"),
            $("#datefrom").val()
          );
          var dateto = $.datepicker.parseDate(
            $(this).datepicker("option", "dateFormat"),
            $("#dateto").val()
          );
          var selectedDate = $.datepicker.parseDate(
            $(this).datepicker("option", "dateFormat"),
            dateText
          );

          if (!datefrom || dateto) {
            $("#datefrom").val(dateText);
            $("#dateto").val("");
            $(".dr-showtime").html("<span>" + moment(selectedDate).format(DATE_WITH_PREFIX_FORMAT) + "</span>");
            $(this).datepicker();
            vm.$scope.$apply(function() {
              vm.removeRangeOrderToList();
            });
          } else if (selectedDate < datefrom) {
            $("#dateto").val($("#datefrom").val());
            $("#datefrom").val(dateText);
            $(".dr-showtime").html("<span>" + moment(selectedDate).format(DATE_WITH_PREFIX_FORMAT) + " - " + moment(datefrom).format(DATE_WITH_PREFIX_FORMAT) + "</span>");
            $(this).datepicker();
            vm.$scope.$apply(function() {
              vm.addRangeOrderToList();
            });
          } else {
            $("#dateto").val(dateText);
            $(".dr-showtime").html("<span>" + moment(datefrom).format(DATE_WITH_PREFIX_FORMAT) + " - " + moment(selectedDate).format(DATE_WITH_PREFIX_FORMAT) + "</span>");
            $(this).datepicker();
            vm.$scope.$apply(function() {
              vm.addRangeOrderToList();
            });
          }
        },
      };
      angular.element("#scroll-calendar").datepicker(dateRangeOptions);

      var dateScrollOptions = {
        numberOfMonths: (vm.isMobile || vm.isIEAccessible ? 1 : 13),
        language: "ja",
        gotoCurrent: true,
        minDate: 0,
        maxDate: "+12M",
        altField: '#multiChoose',
        onSelect: function(dateText, inst) {
          vm.$scope.$apply(function() {
            if (_.includes(vm.scrollStartDates, dateText)) {
              vm.removeOrderFromListCreate(dateText);
            } else {
              vm.appendOrderToListCreate(dateText);
            }
            vm.scrollStartDates = $("#multiChoose").val().split(", ");
          })
        }
      }
      $("#multi-scroll-calendar").multiDatesPicker(dateScrollOptions);
    });
  };

  vm.removeOrderFromListCreate = function(startDate, orderBranch) {
    _.remove(vm.params.order_branches_attributes, function(orderBranch) {
      if (orderBranch.started_at === startDate && !!orderBranch.id) {
        vm.deletedOrderBranchIDs.push(orderBranch.id)
      }
      return orderBranch.started_at === startDate;
    });

    if (orderBranch) {
      if (orderBranch.is_range) {
        vm.resetDateRange(orderBranch);
      } else {
        vm.resetMultiDatesPicker(orderBranch);
      }
    }
    vm.arrangeOrderNo();
  };

  vm.removeRangeOrderToList = function() {
    _.remove(vm.params.order_branches_attributes, function(orderBranch) {return orderBranch.is_range});
  };

  vm.appendOrderToListCreate = function(startDate) {
    var orderBranch = angular.copy(vm.currentTemplate);
    orderBranch.started_at = startDate;
    vm.assignRestField(orderBranch);
  };

  vm.addRangeOrderToList = function() {
    var startDate = $("#datefrom").val();
    var endDate = $("#dateto").val();
    var orderBranch = angular.copy(vm.currentTemplate);
    orderBranch.started_at = startDate;
    orderBranch.ended_at = endDate;
    orderBranch.is_range = true;
    vm.assignRestField(orderBranch);
  };

  vm.listTrackingIndex = function() {
    return _.map(vm.params.order_branches_attributes, "tracking_index");
  };

  vm.assignRestField = function(orderBranch) {
    var trackingIndex = moment(orderBranch.started_at).unix();
    var days = 1;
    while(_.includes(vm.listTrackingIndex(), trackingIndex)) {
      trackingIndex = moment(orderBranch.started_at).add(days, "day").unix();
      days++;
    }
    orderBranch.tracking_index = trackingIndex;
    orderBranch.show_rest = false;
    vm.params.order_branches_attributes.unshift(orderBranch);
    vm.arrangeOrderNo();
  };

  vm.showRest = function(orderBranch) {
    orderBranch.show_rest = !orderBranch.show_rest;
  };

  function appendNestedErrorMessages() {
    removeErrorElm();
    if (_.isEmpty(vm.nestedErrorMessages)) {return;}
    _.each(vm.nestedErrorMessages, function(branchErrors, index) {
      var orderBranch = vm.params.order_branches_attributes[index];
      var displayMessages = [];
      var elmErrors = "";
      var $trWrapElm = $("#tr-order-branch-" + orderBranch["tracking_index"]);
      _.forEach(branchErrors, function(errorArray, key) {
        _.forEach(errorArray, function(message) {
          if (!_.includes(displayMessages, message) && _.includes(NESTED_ERRORS, key)) {
            elmErrors += "<p class='row-error " + key + "'>" + message + "</p>";
            displayMessages.push(message);
            highlightErrorInputField(key, orderBranch["tracking_index"]);
          }
        });
      });
      if (!_.isEmpty(elmErrors)) {
        $("<tr class='order-time-block js-wrap-error js-add-tr tr-error'><td colspan='7'>" + elmErrors + "</td></tr>").insertAfter($trWrapElm);
        $($trWrapElm).addClass("tr-error");
        if (vm.firstNestedErrorTrackingIndex == 0) {vm.firstNestedErrorTrackingIndex = orderBranch["tracking_index"];}
      }
    });
  };

  function highlightErrorInputField(key, trackingIndex) {
    switch(key) {
      case "working_start_time":
        $("#order-work-time-wrap-" + trackingIndex).addClass("form-error");
        break;
      case "working_end_time":
        $("#order-work-end-time-wrap-" + trackingIndex).addClass("form-error");
        break;
      case "rest1_started_at":
      case "rest2_started_at":
      case "rest3_started_at":
        var restIndex = _.toInteger(key.charAt(4)) - 1;
        $("#order-rest-time-start-wrap-" + trackingIndex + "-" + restIndex).addClass("form-error");
        $("#item-add-more-" + trackingIndex).addClass("form-error");
        break;
      case "rest1_ended_at":
      case "rest2_ended_at":
      case "rest3_ended_at":
        var restIndex = _.toInteger(key.charAt(4)) - 1;
        $("#order-rest-time-end-wrap-" + trackingIndex + "-" + restIndex).addClass("form-error");
        $("#item-add-more-" + trackingIndex).addClass("form-error");
        break;
    }
  };

  vm.arrangeOrderNo = function() {
    var orderNo = 1;
    _.forEach(vm.params.order_branches_attributes, function(orderBranch) {
      orderBranch["order_no"] = orderNo;
      orderNo++;
    });
    vm.countDateStaff();
    vm.initDateTimePicker();
    if(vm.params.order_branches_attributes.length > 0) {
      vm.scrollToCreateFrom();
    }
    if(vm.params.order_branches_attributes.length > 10) {
      $(".order-table").addClass("table-scroll");
    } else {
      $(".order-table").removeClass("table-scroll");
    }
  };

  vm.initDateTimePicker = function() {
    angular.element(document).ready(function() {
      $(".js-order-timepicker").datetimepicker({
        locale: I18n.locale,
        format: TIME_PICKER_FORMAT,
        useCurrent: "roundedMin",
        roundedMinTo: "zero",
        ignoreReadonly: true
      });
      $(".js-order-break-timepicker").datetimepicker({
        locale: I18n.locale,
        format: TIME_PICKER_FORMAT,
        useCurrent: "roundedMin",
        roundedMinTo: "zero",
        ignoreReadonly: true
      });
    });
  };

  vm.countDateStaff = function() {
    var rangeOrderBranch = _.find(vm.params.order_branches_attributes, function(orderBranch) {return orderBranch.is_range});
    var multiOrderBranches = _.filter(vm.params.order_branches_attributes, function(orderBranch) {return !orderBranch.is_range});
    vm.totalDates = multiOrderBranches.length;
    vm.totalStaffs = _.sum(_.map(multiOrderBranches, function(orderBranch) {
      return _.isNaN(orderBranch.staff_count) ? 0 : orderBranch.staff_count;
    }));

    if (rangeOrderBranch) {
      var startDate = moment(rangeOrderBranch.started_at);
      var endDate = moment(rangeOrderBranch.ended_at);
      var countRangeOrderBranch = 1;
      while(startDate.isBefore(endDate, "day")) {
        startDate.add(1, "days");
        countRangeOrderBranch += 1;
      }
      vm.totalDates += countRangeOrderBranch;
      vm.totalStaffs += (countRangeOrderBranch * (_.isNaN(rangeOrderBranch.staff_count) ? 0 : rangeOrderBranch.staff_count));
    }
  };

  vm.scrollToCreateFrom = function() {
    var calendar = $(".order-calendar");
    var calendarOffsetTop = calendar.offset().top;
    var windowScroll = $(window).scrollTop();
    var addition = vm.isMobile || vm.isIEAccessible ? 0 : 50
    if(windowScroll < calendarOffsetTop) {
      $('html,body').animate({scrollTop: calendarOffsetTop + addition}, 500);
      return;
    }
  };

  vm.clearDatePicker = function() {
    var isDateRange = $(".switch-calendar .switch-range").hasClass("active");
    if (isDateRange) {
      vm.resetDateRange();
      vm.removeRangeOrderToList();
    } else {
      angular.element("#multi-scroll-calendar").multiDatesPicker("resetDates");
      _.remove(vm.params.order_branches_attributes, function(orderBranch) {
        if (!orderBranch.is_range && !!orderBranch.id) {
          vm.deletedOrderBranchIDs.push(orderBranch.id)
        }
        return !orderBranch.is_range;
      });
      vm.scrollStartDates = [];
    }
    vm.countDateStaff();
  };

  vm.resetDateRange = function() {
    $("#datefrom").val("");
    $("#dateto").val("");
    $("#scroll-calendar").datepicker("refresh");
  };

  vm.resetMultiDatesPicker = function(orderBranch) {
    var rangeDates = $("#multiChoose").val().split(", ");
    _.remove(rangeDates, function(date) {return date === orderBranch.started_at});
    $("#multiChoose").val(rangeDates.join(", "));
    vm.scrollStartDates = rangeDates;
    angular.element("#multi-scroll-calendar").multiDatesPicker("removeDates", new Date(orderBranch.started_at));
  };

  vm.initCalendarForEdit = function(orderBranch) {
    if (orderBranch.is_range) {
      $("#datefrom").val(orderBranch.started_at);
      $("#dateto").val(orderBranch.ended_at);
      $("#scroll-calendar").datepicker("refresh");
    } else {
      var multiChoose = $("#multiChoose").val();
      var rangeDates = _.isEmpty(multiChoose) ? [] : multiChoose.split(", ");
      rangeDates.push(orderBranch.started_at);
      $("#multiChoose").val(rangeDates.join(", "));
      vm.scrollStartDates = rangeDates;
      angular.element("#multi-scroll-calendar").multiDatesPicker("addDates", orderBranch.started_at);
    }
  };

  vm.formatStartDate = function(orderBranch) {
    if (orderBranch.is_range) {
      return [moment(orderBranch.started_at).format(DAY_AND_MONTH), moment(orderBranch.ended_at).format(DAY_AND_MONTH)].join("~");
    } else {
      return moment(orderBranch.started_at).format(DAY_AND_MONTH_FORMAT);
    }
  };

  vm.formatStartDateMobile = function(orderBranch) {
    if (orderBranch.is_range) {
      return [moment(orderBranch.started_at).format(DAY_AND_MONTH_MOBILE), moment(orderBranch.ended_at).format(DAY_AND_MONTH_MOBILE)].join("<br>~<br>");
    } else {
      return moment(orderBranch.started_at).format(DAY_AND_MONTH_MOBILE);
    }
  };

  vm.inputNumberOfEmployees = function(orderBranch) {
    var numberOfEmployees = parseInt(orderBranch["staff_count"], NUMBER_BASE);
    if (!_.isNaN(numberOfEmployees) && _.isNumber(numberOfEmployees)) {
      if (numberOfEmployees >= MAX_STAFF) {
        orderBranch["staff_count"] = MAX_STAFF;
      } else if (numberOfEmployees <= MIN_STAFF) {
        orderBranch["staff_count"] = MIN_STAFF;
      } else {
        orderBranch["staff_count"] = numberOfEmployees;
      }
    } else {
      orderBranch["staff_count"] = MIN_STAFF;
    }

    vm.countDateStaff();
  };

  vm.changeNumberOfEmployees = function(add, orderBranch) {
    var numberOfEmployees = parseInt(orderBranch["staff_count"], NUMBER_BASE);
    if (add) {
      orderBranch["staff_count"] = numberOfEmployees >= MAX_STAFF ? MAX_STAFF : numberOfEmployees + 1;
    } else {
      orderBranch["staff_count"] = numberOfEmployees <= MIN_STAFF ? MIN_STAFF : numberOfEmployees - 1;
    }

    vm.countDateStaff();
  };

  vm.isDisableStaffCountBtn = function(add, staffCount) {
    if (add) {
      return staffCount >= MAX_STAFF;
    } else {
      return staffCount <= MIN_STAFF;
    }
  };

  vm.addRestTimeInput = function(restTimeModel, maxRestTimes) {
    if (restTimeModel.length == maxRestTimes) return;
    restTimeModel.push({
      rest_start_at: "",
      rest_end_at: ""
    })
    vm.initDateTimePicker();
  };

  vm.removeRestTimeList = function(restTimeModel, index) {
    if(index < 0 || restTimeModel.length < index + 1) return;
    restTimeModel.splice(index, 1);
  };

  vm.initLocationPicData = function() {
    var types = ["haken_destination", "claim", "order"];
    _.forEach(types, function(type) {
      vm.params[type + "_pic_id"] = getSelectedLocationProperty(type, "id");
      vm.params[type + "_pic_position"] = getSelectedLocationProperty(type, "position");
      vm.params[type + "_pic_email"] = getSelectedLocationProperty(type, "email");
      vm.params[type + "_pic_tel"] = getSelectedLocationProperty(type, "tel");
    });
    vm.initMandator();
  };

  vm.initMandator = function() {
    vm.params.mandator_id = getSelectedLocationProperty("mandator", "id");
    vm.params.mandator_position = getSelectedLocationProperty("mandator", "position");
    vm.params.mandator_tel = getSelectedLocationProperty("mandator", "tel");
    vm.params.order_pic_tel = _.isEmpty(vm.selectedLocation) ? "" : vm.selectedLocation.order_pic_tel;
  };

  function getSelectedLocationProperty(type, key) {
    var currentUserID = angular.element(".create-order__more-info").data("current-user-id");
    if (!_.isEmpty(vm.selectedLocation) && vm.selectedLocation.isValidLocation && vm.selectedLocation[type] &&
      vm.selectedLocation[type][0]) {
      if (type === "order") {
        var selectedOrder = _.find(vm.selectedLocation.order, function(order) {return order.id === currentUserID;});
        var property = selectedOrder[key];
        if (vm.isStoreComputer && _.isEmpty(property) && key === "email") {
          property = vm.locationCode + DEFAULT_EMAIL_SUFFIX;
        }
        return property;
      }
      return vm.selectedLocation[type][0][key];
    }
    return "";
  };

  vm.getOrderTemplatesFromLocation = function(firstTime) {
    if (_.isEmpty(vm.params.location_id)) {
      vm.orderTemplates = [];
      vm.params.order_template_id = "";
      vm.currentTemplate.location_id = "";
    }
    newOrderService.getOrderTemplatesFromLocation({location_id: vm.params.location_id}).then(function(res) {
      if (!!res.data) {
        vm.orderTemplates = res.data.order_templates;
        var templateExist = !!_.find(vm.orderTemplates, function(template) {return template.id == vm.originalOrderTemplateID});
        vm.currentTemplate.location_id = angular.copy(vm.params.location_id);
        if (firstTime && vm.isEditOrder && templateExist) {
          vm.params.order_template_id = vm.originalOrderTemplateID;
        } else if (vm.orderTemplates.length > 0) {
          vm.params.order_template_id = _.toInteger(vm.orderTemplates[0].id);
        } else {
          vm.params.order_template_id = ""
        }
      }
    });
  };

  vm.$scope.$watch("vm.params.is_used_existing_pic", function() {
    if (vm.noTemplate() && !vm.params.is_used_existing_pic && vm.isStoreComputer) {
      vm.params.order_pic_email = "";
    }
  });

  vm.$scope.$watch("vm.params.location_id", function() {
    removeError("location_id");
    if (_.isEmpty(vm.params.location_id.toString())) {
      vm.selectedLocation = {isValidLocation: true};
      vm.initLocationPicData();
      vm.canSubmitStep1 = true;
      return;
    }

    if (!vm.firstTimeLoadLocation) {
      $("#spinner").removeClass("ng-hide");
      orderService.loadPicOptions({location_id: vm.params.location_id}).then(function(res) {
        var results = res.data;
        _.forEach(PIC_DATA, function(pic_data) {
          vm.selectedLocation[pic_data] = results[pic_data];
        });
        vm.params.organization_id = results.organization_id;
        vm.params.organization_full_name = results.organization_full_name;
        vm.locationCode = results.location_code;
        vm.isStoreComputer = results.is_store_computer;
        vm.locationSurveySubmittedTime = results.location_survey_submitted_at;
        vm.checkLocationIsValid();
        vm.checkViolationDay();
        vm.checkSubmitStep1Condition();
        if (vm.selectedLocation.isValidLocation) {
          vm.initLocationPicData();
        }
        vm.getOrderTemplatesFromLocation();
        $("#spinner").addClass("ng-hide");
      }, function(error) {
      });
    } else {
      vm.getOrderTemplatesFromLocation(vm.firstTimeLoadLocation);
    }
    vm.firstTimeLoadLocation = false;
  });

  vm.checkLocationIsValid = function() {
    vm.selectedLocation.isValidLocation = true;
    var requiresInfo = [vm.selectedLocation.claim, vm.selectedLocation.haken_destination,
      vm.selectedLocation.mandator];
    if (!_.isEmpty(vm.params.location_id.toString())) {
      _.forEach(requiresInfo, function(locationPic) {
        if (_.isUndefined(locationPic) || !vm.selectedLocation.is_valid_order_info) {
          vm.params.order_pic_tel = "";
          vm.selectedLocation.isValidLocation = false;
        }
      });
    }
  };

  vm.checkViolationDay = function() {
    if (!vm.selectedLocation) {return;}
    var days = vm.selectedLocation.days_to_violation_day;
    var message = "";

    if (!_.isInteger(days)) {
      message = I18n.t("corporation.order.step1.violation_day.blank");
    } else if (days <= 0) {
      message = I18n.t("corporation.order.step1.violation_day.error_message");
    } else if (days <= 30) {
      message = I18n.t("corporation.order.step1.violation_day.notice_message_day_30", {days: days});
    } else if (days >= 31 && days <= VIOLATION_DAYS) {
      message = I18n.t("corporation.order.step1.violation_day.notice_message_day_31", {days: days});
    };
    vm.selectedLocation.violation_day_error_message = $sce.trustAsHtml(message);
  };

  vm.checkSubmitStep1Condition = function() {
    var days = vm.selectedLocation.days_to_violation_day;
    var isValidClosedDay = _.isEmpty(vm.selectedLocation.closed_day_error_message);
    var isLocationNotSurvey = _.isEmpty(vm.selectedLocation.location_not_survey_error_message);
    vm.isValidLocation = vm.selectedLocation.isValidLocation && days > 0 && isValidClosedDay && isLocationNotSurvey;
    vm.canSubmitStep1 = vm.isValidLocation && _.isEmpty(vm.selectedLocation.transaction_error_message);
  };


  vm.$scope.$watch("vm.params.order_template_id", function() {
    var selectedTemplate = _.find(vm.orderTemplates, function(template) {return template.id === vm.params.order_template_id;});
    if (!_.isEmpty(selectedTemplate)) {
      _.forEach(TEMPLATE_COPY_FIELDS, function(fieldName) {
        vm.currentTemplate[fieldName] = selectedTemplate[fieldName];
      });
      if (!vm.firstTimeLoadTemplate) {
        _.forEach(TEMPLATE_PIC_FIELDS, function(fieldName) {
          vm.params[fieldName] = selectedTemplate[fieldName];
        });
      }
      _.forEach(TEMPLATE_WORKING_TIME_FIELDS, function(fieldName) {
        vm.currentTemplate[fieldName] = moment.parseZone(selectedTemplate[fieldName]).format(TIME_PICKER_FORMAT);
      });
      if (vm.currentTemplate.rest_times.length == 0) {
        vm.currentTemplate.rest_times = [vm.restTime];
      }
    }
    setTimeChangableSwitch();
    if (vm.params.order_template_id) {vm.firstTimeLoadTemplate = false;}
    vm.initDateTimePicker();
  });

  vm.disableSubmitStep2 = function() {
    return !vm.canSubmitStep1 || _.isEmpty(vm.params.order_branches_attributes);
  };

  vm.disableSubmitStep3 = function() {
    return !vm.canSubmitStep3 || vm.buttonsDisabled || !vm.agreeTerm ||
      (vm.showCheckboxFee() && !vm.agreeFee) || !vm.isNotOneManOperation||
      !vm.isVioldationDateUnchanged || !vm.isLocationSurveyUnchanged ||
      (vm.showConfirmMidnightCheckbox() && !vm.confirmMidnight);
  };

  vm.submitCreateOrderStep2 = function() {
    $("#spinner").removeClass("ng-hide");
    $(".disable-submit-btn").prop("disabled", true);
    resetCheckboxes();
    resetErrors();
    removeErrorElm();
    var submitParams = _.omit(vm.params, "id");
    submitParams.order_branches_attributes = _.map(submitParams.order_branches_attributes, function(branch) {
      _.forEach(REST_TIMES, function(restTime) {
        var restIdx = parseInt(restTime) - 1;
        branch["rest" + restTime + "_editable"] = !!branch.rest_times[restIdx];
      });
      return _.omit(branch, "id");
    });
    newOrderService.createOrderStep2({order: submitParams}).then(function(res) {
      vm.overLimitPortion = res.data.over_limit_portion;
      if (res.data.status) {
        vm.orderData = res.data.order;
        vm.holidays = res.data.holidays;
        vm.holiday_dates = _.map(vm.holidays, "format_date");
        vm.orderData.overall_started_at = formatDate(vm.orderData.overall_started_at, ORDER_DATEPICKER_FORMAT);
        vm.orderData.overall_ended_at = formatDate(vm.orderData.overall_ended_at, ORDER_DATEPICKER_FORMAT);
        vm.totalCollectiveOrder = 0;
        vm.estimateTotalOrderPrice();
        vm.enableStep(3);
      } else {
        vm.nestedErrorMessages = convertJSON(res.data.nested_errors);
        vm.errorMessages = convertJSON(res.data.errors);
        appendNestedErrorMessages();
        scrollToError();
      }
      $(".disable-submit-btn").prop("disabled", false);
      $("#spinner").addClass("ng-hide");
    });
  };

  vm.submitCreateOrder = function(status) {
    $("#spinner").removeClass("ng-hide");
    $(".disable-submit-btn").prop("disabled", true);
    vm.buttonsDisabled = true;
    vm.params["status_id"] = status;

    vm.params["is_violation_day_unchanged"] = vm.isVioldationDateUnchanged;
    vm.params["is_location_survey_unchanged"] = vm.isLocationSurveyUnchanged;
    vm.params["is_not_one_man_operation"] = vm.isNotOneManOperation;

    if (vm.isEditOrder) {
      var submitParams = getEditedSubmitParams(status);
      newOrderService.updateOrder(vm.params.id, submitParams).then(function(res) {
        if (res.data.status) {
          window.location.href = res.data.redirect_path;
        } else {
          $("#spinner").addClass("ng-hide");
          $(".disable-submit-btn").prop("disabled", false);
          vm.buttonsDisabled = false;
          vm.createErrors = res.data.errors;
          if (!_.isEmpty(vm.createErrors)) {
            window.scrollTo(0, 0);
          }
        }
      });
    } else {
      newOrderService.createOrder({order: vm.params}).then(function(res) {
        // TODO: handle error - $.lawsonAjax(res.data);
        if (res.data.status) {
          window.location.href = res.data.redirect_path;
        } else {
          vm.createErrors = convertJSON(res.data.all_errors);
          $(".disable-submit-btn").prop("disabled", false);
          $("#spinner").addClass("ng-hide");
          vm.buttonsDisabled = false;
          if (!_.isEmpty(vm.createErrors)) {
            window.scrollTo(0, 0);
          }
        }
      });
    }
  };

  vm.isInvalid = function(field) {
    return !_.isEmpty(vm.errorMessages[field]);
  };

  function scrollToError() {
    if (_.isEmpty(vm.errorMessages)) {return;}
    var area = "";
    var errorKeys = Object.keys(vm.errorMessages);
    if (!!vm.errorMessages.location_id || !vm.selectedLocation.isValidLocation) {
      area = "#location_id"
    } else if (_.intersection(errorKeys, TEMPLATE_COMMON_FIELDS).length > 0) {
      area = "#order-template";
    } else if (vm.firstNestedErrorTrackingIndex > 0) {
      area = "#tr-order-branch-" + vm.firstNestedErrorTrackingIndex;
    } else if (_.intersection(errorKeys, TEMPLATE_PIC_FIELDS).length > 0) {
      area = "#location-pic-area";
    }

    if (_.intersection(errorKeys, TEMPLATE_PIC_FIELDS).length > 0) {
      vm.params.is_used_existing_pic = false;
      $("#location-pic-area-collapse").removeClass("collapsed");
      $("#location-pic-area").addClass("show");
    }

    if (_.isEmpty(area)) {return;}
    $("html,body").animate({
      scrollTop: $(area).offset().top - 100
    }, 1000);
  };

  function removeError(area) {
    switch(area) {
      case "location_id":
        vm.errorMessages.location_id = "";
        break;
    }
  };

  function formatDate(date, formatType) {
    if (_.isEmpty(date)) return;
    return moment.parseZone(date).format(formatType);
  };

  function convertJSON(data) {
    if (typeof(data) != "string") {return data;}
    return JSON.parse(data);
  };

  vm.backToStep2 = function() {
    vm.enableStep(2);
  };

  vm.enableStep = function(step, dontScroll) {
    angular.forEach([1, 2, 3], function(index) {
      if (step === index) {
        vm.steps["step" + index] = true;
      } else {
        vm.steps["step" + index] = false;
      }
    });
    if (!dontScroll) {window.scrollTo(0, 0);}
  };

  vm.estimateTotalOrderPrice = function() {
    vm.estimateData = [];
    _.each(vm.params.order_branches_attributes, function(orderBranch, index) {
      vm.exceptHoliday = orderBranch["is_except_holiday"];
      if (!!orderBranch.is_range || !_.includes(vm.holiday_dates, orderBranch.started_at)) {
        vm.estimateOrderPrice(orderBranch, index);
      }
    });
  };

  vm.estimateOrderPrice = function(branch, index) {
    if(invalidEstimateParams(branch)) {
      return;
    }
    var submitParams = vm.estimateOrderParams(branch);
    orderService.estimateOrderPrice(submitParams).then(function(res) {
      var response_data = res.data;
      vm.params.order_branches_attributes[index]["estimate_data"] = response_data;
      var estimateData = {data: branch.estimate_data};
      var summaryOrderBranch = _.sumBy(response_data, "summary");
      vm.params.order_branches_attributes[index]["total_estimation"] = summaryOrderBranch;
      estimateData["total_estimation"] = summaryOrderBranch;
      estimateData["selected_days"] = convertToDateFormat(submitParams, estimateData["data"][0]);
      estimateData["total_employees"] = _.sumBy(response_data, "staff_count");
      vm.estimateData.push(estimateData);
      vm.totalCollectiveOrder = _.sumBy(vm.estimateData, "total_estimation");
    }, function(error) {
    });
  };

  vm.estimateOrderParams = function(branch) {
    var orderBranchData = {};
    var data = {
      type: TEMPLATE_TYPE,
      prefecture_id: vm.selectedLocation.prefecture_id,
      corporation_id: vm.corporationId,
      location_id: vm.params.location_id
    };
    _.forEach(_.concat(ORDER_BRANCH_DATA, BREAK_TIME_ATTRS), function(key) {
      if (_.includes(BREAK_TIME_ATTRS, key)) {
        var restIndex = _.toInteger(key.charAt(4)) - 1;
        var branchFieldName = key.charAt(6) == "s" ? "rest_start_at" : "rest_end_at";
        if (!_.isEmpty(branch["rest_times"][restIndex])) {
          data[key] = branch["rest_times"][restIndex][branchFieldName];
        }
      } else {
        data[key] = branch[key];
      }
    });
    if (branch["started_at"] && branch["working_start_time"] && branch["working_end_time"] &&
      branch["staff_count"]) {
      orderBranchData = _.extend({}, data);
    }
    return orderBranchData;
  };

  vm.showCheckboxFee = function() {
    var checkValue = false;
    _.forEach(vm.estimateData, function(estimateData) {
      if (!estimateData) {return;}
      var orderDatas = estimateData.data;
      _.forEach(orderDatas, function(orderData) {
        if (moment.parseZone(new Date(orderData.date)).format(ORDER_DATEPICKER_FORMAT) <=
          moment().add(1, "days").format(ORDER_DATEPICKER_FORMAT)) return checkValue = true;
      });
    });
    return checkValue;
  };

  function convertToDateFormat(params, firstCase) {
    var startDate = params.started_at;
    var endDate = params.ended_at;
    if (!endDate || startDate == endDate) {return firstCase.date};
    return [startDate.toString(), "～", endDate.toString()].join("");
  };

  function invalidEstimateParams(order) {
    return false;
    // var dateRegex = /^([0-9]{2})\:([0-9]{2})$/;

    // return _.isEmpty(order.started_at) || _.isEmpty(order.working_start_time) || _.isEmpty(order.working_end_time)
    // || (!_.isEmpty(order.rest1_started_at) && (!moment(order.rest1_started_at, TIME_PICKER_FORMAT).isValid()
    // || !dateRegex.test(order.rest1_started_at)))
    // || (!_.isEmpty(order.rest2_started_at) && (!moment(order.rest2_started_at, TIME_PICKER_FORMAT).isValid()
    // || !dateRegex.test(order.rest2_started_at)))
    // || (!_.isEmpty(order.rest3_started_at) && (!moment(order.rest3_started_at, TIME_PICKER_FORMAT).isValid()
    // || !dateRegex.test(order.rest3_started_at)))
    // || (!_.isEmpty(order.rest1_ended_at) && (!moment(order.rest1_ended_at, TIME_PICKER_FORMAT).isValid()
    // || !dateRegex.test(order.rest1_ended_at)))
    // || (!_.isEmpty(order.rest2_ended_at) && (!moment(order.rest2_ended_at, TIME_PICKER_FORMAT).isValid()
    // || !dateRegex.test(order.rest2_ended_at)))
    // || (!_.isEmpty(order.rest3_ended_at) && (!moment(order.rest3_ended_at, TIME_PICKER_FORMAT).isValid()
    // || !dateRegex.test(order.rest3_ended_at)))
    // || (!_.isEmpty(order.started_at) && (!moment(order.started_at).isValid()
    // || !CHECK_DATE_REGEX.test(order.started_at)))
    // || (!_.isEmpty(order.working_start_time) && (!moment(order.working_start_time, TIME_PICKER_FORMAT).isValid()
    // || !dateRegex.test(order.working_start_time)))
    // || (!_.isEmpty(order.working_end_time) && (!moment(order.working_end_time, TIME_PICKER_FORMAT).isValid()
    // || !dateRegex.test(order.working_end_time)))
  };

  vm.getViolationDay = function() {
    if (!vm.selectedLocation) {return;}
    var violationDay = vm.selectedLocation.violation_day;
    if (violationDay) {
      return violationDay.split("(")[0];
    };
  };

  vm.isTrue = function(value) {
    return (value == true) || (value == "true");
  };

  // TODO: check status
  vm.setOrderStatus = function() {
    var status = "waiting_approved";
    if (!vm.isNeedConfirmOrder) {
      status = vm.selectedLocation.is_approval_required ? "op_checking" : "confirmed";
    }
    return status;
  };

  function setTimeChangableSwitch() {
    if (vm.currentTemplate.is_time_changable) {
      $(".switch-time-changable").addClass("active");
      $(".switch-not-time-changable").removeClass("active");
    } else {
      $(".switch-time-changable").removeClass("active");
      $(".switch-not-time-changable").addClass("active");
    }
  };

  vm.switchTimeChangable = function() {
    if($(".switch-time-changable").hasClass("active")) {
      $(".switch-time-changable").removeClass("active");
      $(".switch-not-time-changable").addClass("active");
      vm.currentTemplate.is_time_changable = false;
    } else if($(".switch-not-time-changable").hasClass("active")) {
      $(".switch-time-changable").addClass("active");
      $(".switch-not-time-changable").removeClass("active");
      vm.currentTemplate.is_time_changable = true;
    }
  };

  vm.openOrderTemplateModal = function() {
    $("#modal-order-template").modal("show");
  };

  vm.openRequestSaveOrderTemplate = function() {
    if (vm.noTemplate() && !vm.hasSavedTemplate) {
      $("#modal-request-save-order-template").modal("show");
    } else {
      vm.submitCreateOrder(vm.setOrderStatus());
    }
  };

  vm.openNewOrderTemplateModal = function(isCreateOrder) {
    vm.isCreateOrder = !!isCreateOrder;
    $("#modal-order-template").modal("hide");
    $("#modal-request-save-order-template").modal("hide");
    setTimeout(function(){
      $("#modal-new-order-template").modal("show");
    }, 400);
  };

  vm.noTemplate = function() {
    return vm.params.order_template_id == "" || typeof vm.params.order_template_id === "undefined";
  };

  vm.editTemplate = function(isNew) {
    resetErrors();
    var templateParams = formatOrderTemplateParams(isNew);
    if (isNew) {
      if (_.isEmpty(templateParams.name)) {
        vm.errorMessages["template_name"] = I18n.t("corporation.order.messages.blank_template_name");
        return;
      }
      orderTemplateService.createOrderTemplate({order_template: templateParams}, LOCALE).then(function(res) {
        handleOrderTemplateResponse(res);
      });
    } else {
      orderTemplateService.updateOrderTemplate(vm.params.order_template_id, {order_template: templateParams}, LOCALE).then(function(res) {
        handleOrderTemplateResponse(res);
      });
    }

    if (vm.isCreateOrder) {
      vm.submitCreateOrder(vm.setOrderStatus());
    }
  };

  function formatOrderTemplateParams(isNew) {
    var templateParams = {};
    _.forEach(_.concat(TEMPLATE_COPY_FIELDS, TEMPLATE_WORKING_TIME_FIELDS), function(fieldName) {
      templateParams[fieldName] = vm.currentTemplate[fieldName];
    });
    _.forEach(TEMPLATE_PIC_FIELDS, function(fieldName) {
      templateParams[fieldName] = vm.params[fieldName];
    });
    _.each(templateParams.rest_times, function(restTime, index) {
      var restNumber = index + 1;
      templateParams["rest" + restNumber + "_start_time"] = restTime.rest_start_at;
      templateParams["rest" + restNumber + "_end_time"] = restTime.rest_end_at;
      templateParams["rest" + restNumber + "_editable"] = true;
    });
    if (isNew) {templateParams.name = !!vm.templateName ? vm.templateName.trim() : "";}
    return templateParams;
  };

  function handleOrderTemplateResponse(res) {
    $("#modal-order-template").modal("hide");
    $("#modal-new-order-template").modal("hide");
    if (res.data.status) {
      vm.hasSavedTemplate = true;
      toaster.pop("success", "", res.data.success_message);
    } else {
      if (vm.steps.step3) {vm.enableStep(2, true);}
      vm.errorMessages = convertJSON(res.data.errors);
      scrollToError();
      $(".disable-submit-btn").prop("disabled", false);
      $("#spinner").addClass("ng-hide");
    }
  };

  vm.isDisplayPriceColumn = function(orderData, field) {
    return _.sumBy(orderData, field) !== 0;
  };

  vm.isDisplaySpeacialCharge = function(orderData, field) {
    return _.sumBy(orderData, field) !== 0;
  };

  vm.displayEstimateByStaff = function(price, staffCount) {
    return I18n.t("corporation.order.step3.detail_modal.price_per_staff", {price: price, staff_count: staffCount});
  };
  
  vm.showMessageError = function() {
    vm.canSubmitStep3 = vm.canSubmitStep1 && isValidStartDate();
    var error = [];
    if (!vm.isValidLocation) {
      error.push(I18n.t("corporation.order.messages.invalid_location"));
    };
    if (!isValidStartDate()) {
      error.push(I18n.t("corporation.order.messages.invalid_start_date"))
    };
    error.push(vm.selectedLocation.transaction_error_message);
    return $sce.trustAsHtml(error.join("<br>"));
  };

  function isValidStartDate() {
    return moment().isSameOrBefore(vm.orderData.overall_started_at, "day");
  };

  function getEditedSubmitParams(orderStatus) {
    var submitParams = {order: angular.copy(vm.params)};
    _.forEach(submitParams.order.order_branches_attributes, function(branch) {
      _.forEach(REST_TIMES, function(restTime) {
        var restIdx = parseInt(restTime) - 1;
        branch["rest" + restTime + "_editable"] = !!branch.rest_times[restIdx];
      });
    });
    _.forEach(vm.deletedOrderBranchIDs, function(id) {
      submitParams.order.order_branches_attributes.push({id: id, _destroy: true});
    });
    if (orderStatus === "draft") {
      submitParams["order_action"] = "save_draft";
    } else if (orderStatus === "waiting_approved") {
      submitParams["order_action"] = "waiting_approved";
    } else {
      submitParams["order_action"] = "send_order";
    }
    return submitParams;
  };

  vm.showLocationErrorMessage = function() {
    return I18n.t("corporation.order.step1.location_notice");
  };

  vm.triggerUpdateModel = function(elem, field, parent, restIdx) {
    if (_.includes([0, 1, 2], restIdx)) {
      if ($("#" + elem).val() == parent.rest_times[restIdx][field]) {return;}
      parent.rest_times[restIdx][field] = $("#" + elem).val();
    } else {
      if ($("#" + elem).val() == parent[field]) {return;}
      parent[field] = $("#" + elem).val();
    }
  };

  vm.showConfirmMidnightCheckbox = function() {
    if (_.isEmpty(vm.params.order_branches_attributes)) {
      return false;
    }

    return _.some(vm.params.order_branches_attributes, function(branch) {
      return branch["working_start_time"] === MIDNIGHT;
    });
  };

  function disableEnter(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 13) {
      e.preventDefault();
      $("input").blur();
    }
  };

  function disableBackspace(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 8) {
      e.preventDefault();
    }
  };

  if (!_.isNull($("#form-create-order"))) {
    $("#form-create-order").keydown(function(e) {
      disableEnter(e);
    });
  }

  $("html, body").keydown(function(e) {
    var $target = $(e.target || e.srcElement);
    if(!$target.is('input,[contenteditable="true"],textarea')) {
      disableBackspace(e);
    }
  });
}
