"use strict";

angular.module("corporationApp")
  .controller("OwnerInfosController", OwnerInfosController);

OwnerInfosController.$inject = ["ownerInfosService", "toaster"];

function OwnerInfosController(ownerInfosService, toaster) {
  var vm = this;
  var LOCALE = I18n.locale;
  vm.perPageSettings = {
    corporation_groups: [],
    locations: []
  };
  vm.params = {
    corporation_groups: {},
    locations: {}
  };
  vm.toasterTimeout = 6200;

  vm.init = function(tabType) {
    vm.perPageSettings[tabType] = angular.element(".per-page-settings__" + tabType).data("settings");
    vm.params[tabType].per_page = vm.perPageSettings[tabType][0];
    vm.params[tabType].page = 1;
    vm.params[tabType].sort_key = null;

    vm.refresh(tabType);
  };

  vm.refresh = function(tabType) {
    var functionName = tabType === "corporation_groups" ? "getCorporationGroups" : "getLocations";
    if (vm.params[tabType].keyword) {
      vm.params[tabType].page = 1;
    }

    ownerInfosService[functionName](vm.params[tabType], LOCALE).then(function(res) {
      if (res.data) {
        $("#" + tabType + " tbody tr").remove();
        vm[tabType] = res.data[tabType];
        vm["total_items_" + tabType] = res.data.metadata.total_items;
        if (functionName == "getCorporationGroups") {
          vm.corporation_group_tags = res.data.corporation_group_tags;
        };
      } else {
        vm[tabType] = [];
        vm["total_items_" + tabType] = 0;
      }
    });
  };

  vm.sort = function(tabType, column) {
    if (_.isEqual(vm.params[tabType].sort_key, column)) {
      vm.params[tabType].desc = !vm.params[tabType].desc;
    } else {
      vm.params[tabType].desc = true;
      vm.params[tabType].sort_key = column;
    }
    vm.refresh(tabType);
  };

  vm.classForSortColumn = function(tabType, column) {
    return {
      "fa-sort": !_.isEqual(vm.params[tabType].sort_key, column),
      "fa-sort-up": _.isEqual(vm.params[tabType].sort_key, column) && !vm.params[tabType].desc,
      "fa-sort-down": _.isEqual(vm.params[tabType].sort_key, column) && !!vm.params[tabType].desc
    }
  };

  vm.getGroupName = function(corporationGroup) {
    var groupName = I18n.t("corporation.owner_infos.corporation_groups.no_setting");
    if (corporationGroup.corporation_group_tag_id) {
      groupName = corporationGroup.group_name;
    };
    return groupName;
  };

  vm.initEditCorporationGroup = function(corporationGroup) {
    vm.group_name = "";
    vm.groupTagId = corporationGroup.corporation_group_tag_id;
    vm.corporationGroup = corporationGroup;
  };

  vm.updateCorporationGroup = function(groupTagId, corporationGroup) {
    var isNewGroupTag = groupTagId == "newRegister";
    var params = {corporation_group_tag_id: groupTagId, is_new_group_tag: isNewGroupTag, group_name: vm.group_name};
    ownerInfosService.updateCorporationGroup(corporationGroup.id, params).then(function(res) {
      var data = res.data;
      if (data.status) {
        if (isNewGroupTag) {
          vm.corporation_group_tags = data.corporation_group_tags;
        };
        corporationGroup.corporation_group_tag_id = data.corporation_group.corporation_group_tag_id;
        corporationGroup.group_name = data.corporation_group.group_name;
        $("#modal-edit").modal("hide");
        toaster.pop("success", "", I18n.t("corporation.owner_infos.corporation_groups.messages.update_success"));
      } else {
        toaster.pop("error", "", I18n.t("corporation.owner_infos.corporation_groups.messages.update_failed"));
      };
    });
  };

  vm.deleteCorporationGroupTag = function(corporationGroupTagId) {
    ownerInfosService.deleteCorporationGroupTag(corporationGroupTagId).then(function(res) {
      var data = res.data;
      if (data.status) {
        toaster.pop("success", "", data.message);
        $("#modal-delete").modal("hide");
        _.remove(vm.corporation_group_tags, function(groupTag) {
          return groupTag.id == corporationGroupTagId;
        });
        if (vm.groupTagId == corporationGroupTagId) {
          vm.groupTagId = null;
        };
        _.forEach(vm.corporation_groups, function(corporationGroup) {
          if (corporationGroup.corporation_group_tag_id == corporationGroupTagId) {
            corporationGroup.corporation_group_tag_id = null;
            corporationGroup.group_name = "";
          };
        });
      } else {
        toaster.pop("error", "", data.message);
      };
    });
  };
};
