'use strict';

angular.module('corporationApp')
  .controller('OrderListController', OrderListController);
OrderListController.$inject = ["$location", "$timeout", "$window", "orderListService", "toaster"];

function OrderListController($location, $timeout, $window, orderListService, toaster) {
  var vm = this;
  var CHECK_ALL_TXT = angular.element("#check-all-location-txt").text().trim();
  var HAVE_PLACEHOLDER_INP_FIELD = ["definition_id", "overall_start_date", "overall_end_date",
    "from_violation_date", "from_order_date"];
  var DATE_FIELD = ["overall_start_date", "overall_end_date", "from_violation_date",
    "to_violation_date", "from_order_date", "to_order_date"];
  var DRAFT_STATUS = "draft";
  var LOCALE = I18n.locale;
  var COMMA = I18n.t("common.comma");
  var REGULAR_ORDER = "regular_order";
  vm.toasterTimeout = 6200;
  moment.locale(LOCALE);

  vm.refresh = function(resetPage, isSearchBtn, isUseCurrentCondition, isSave) {
    formatParamsBeforeSend(resetPage);
    if (_.isEqual(vm.currentParams, vm.params) && isSearchBtn) {return;}
    var params = isUseCurrentCondition ? formatPerPageParam(vm.currentParams) : vm.params;
    orderListService.getOrders({search: params}, LOCALE).then(function(res) {
      angular.extend(vm, res.data);
      vm.orders = formatDataOrders(vm.orders);
      $location.search(params).replace();
      if (isSave) {
        orderListService.createSearchCondition({search: params}, LOCALE);
      }
      vm.currentParams = angular.copy(params);
    }, function(error) {
      vm.orders = [];
    });
  };

  $(".order-list").on("input", ".datetimepicker-input", function() {
    var modelName = $(this).attr("ng-model").split(".")[2];
    vm.params[modelName] = $(this).val();
  });

  init();

  vm.toggleAllStatusChkboxes = function() {
    var chkBoxState = !!vm.chkAllStatusState;
    vm.statuses.forEach(function(status) {
      status.selected = chkBoxState;
    });
  };

  vm.moveToDetailsPage = function(prefixStoreComputer, orderId) {
    if(_.isEmpty(prefixStoreComputer)) {
      $window.location.href = "/" + LOCALE + "/new_orders/" + orderId;
    } else {
      $window.location.href = prefixStoreComputer +"/" + LOCALE + "/new_orders/" + orderId;
    };
  };

  vm.toggleAllLocationChkboxes = function() {
    vm.chkAllLocationState = !vm.chkAllLocationState;
    var chkBoxState = !!vm.chkAllLocationState;
    vm.locations.forEach(function(location) {
      location.selected = chkBoxState;
    });
    checkChangeLocationInput();
  };

  vm.toggleLocationChkbox = function(location) {
    location.selected = !location.selected;
    checkChangeLocationInput();
    checkStateForChkboxAll(location);
  };

  vm.checkStateForChkboxStatusAll = function(status) {
    if(vm.chkAllStatusState && !status.selected)
      vm.chkAllStatusState = !vm.chkAllStatusState;
  }

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".order-result").offset().top
    }, 1500);
  };

  vm.getClassForStatus = function(statusId) {
    return {
      "color--yellow": _.isEqual(statusId, "waiting_approved"),
      "color--blue": _.isEqual(statusId, "op_checking"),
      "color--green": _.isEqual(statusId, "confirmed")
    }
  };

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
      vm.currentParams.desc = !vm.currentParams.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
      vm.currentParams.desc = true;
      vm.currentParams.order_key = column;
    }
    vm.refresh(false, false, true, true);
    vm.scrollToResult();
  };

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !vm.params.desc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !!vm.params.desc
    }
  };

  vm.isAbleToDelete = function(order) {
    return order.status_id == DRAFT_STATUS;
  };

  vm.openDestroyStaffModal = function(id) {
    vm.beDeletedOrderId = id;
    angular.element(".delete-order-modal").modal("show");
  };

  vm.deleteOrder = function() {
    orderListService.deleteOrder(vm.beDeletedOrderId, LOCALE).then(function(res) {
      $timeout(function() {
        vm.refresh(false, false, true);
      }, 800);
      angular.element(".delete-order-modal").modal("hide");
      toaster.pop(res.data.status, "", res.data.message);
      vm.beDeletedOrderId = null;
    });
  };

  vm.checkIsDate = function(fieldName) {
    if (!Date.parse(vm.params[fieldName])) {
      vm.params[fieldName] = "";
    }
  };

  function formatParamsBeforeSend(resetPage) {
    if (resetPage) {
      vm.params.page = 1;
    }
    vm.params.location_id = vm.locations.filter(function(location) {
      return !!location.selected;
    }).map(function(location) {
      return location.id
    }).join(",");

    vm.params.status_before_cast = vm.statuses.filter(function(status) {
      return !!status.selected;
    }).map(function(status) {
      return status.id;
    }).join(",");
  }

  function formatDataOrders(orders) {
    return orders.map(function(order) {
      var status = vm.statuses.filter(function(status) {
        return _.isEqual(status.key, order.status_id);
      });
      order.status = status[0].name;
      return order;
    });
  }

  function formatDate(dateStr) {
    return moment(dateStr).format(DATE_TIME_FORMAT_WITH_DAY_NAME);
  }

  function checkChangeLocationInput() {
    var selectedLocations = vm.locations.filter(function(location) {
      return !!location.selected;
    });
    if (vm.chkAllLocationState && selectedLocations.length == vm.locations.length) {
      vm.locationInp = CHECK_ALL_TXT;
    } else {
      vm.locationInp = selectedLocations.map(function(location) {
        return location.name;
      }).join(COMMA);
    }
    $("#location-input").val(vm.locationInp).trigger("input");
  }

  function init() {
    vm.statuses = angular.element(".search-info__status").data("statuses");
    vm.locations = angular.element(".location-list").data("locations");
    vm.perPageSettings = angular.element(".per-page-settings").data("settings");

    vm.params = $location.search();
    vm.params.per_page = vm.perPageSettings[0];
    vm.params.desc = vm.params.desc == "false" ? false : true;

    if (vm.params.is_ignore_condition) {
      initLocationInpStates(false);
      initStatusInpStates(true);
      delete vm.params.is_ignore_condition;
      vm.refresh(true);
    } else {
      orderListService.getLastSearchConditions(LOCALE).then(function(res) {
        if (res.data.status) {
          vm.params = JSON.parse(res.data.last_conditions);
        } else {
          if (!vm.params.overall_start_date) {
            vm.params.overall_start_date = moment().format(ORDER_DATEPICKER_FORMAT);
          }
          if (!vm.params.overall_end_date) {
            vm.params.overall_end_date = moment().add(1, "months").format(ORDER_DATEPICKER_FORMAT);
          }
        }

        DATE_FIELD.forEach(function(fieldName) {
          if (!Date.parse(vm.params[fieldName])) {
            vm.params[fieldName] = "";
          }
        });

        HAVE_PLACEHOLDER_INP_FIELD.forEach(function(fieldName) {
          var inpVal = vm.params[fieldName];
          if (!_.isNull(inpVal)) {
            $("." + fieldName).val(inpVal).trigger("input");
          }
        });

        initLocationInpStates(res.data.status);
        initStatusInpStates(res.data.status);
        vm.currentParams = angular.copy(vm.params);
        setTimeout(function(){vm.refresh(true);}, 200);
      });
    }
  }

  function initLocationInpStates(paramStatus) {
    if (paramStatus) {
      var locationParams = !_.isNil(vm.params.location_id) ? vm.params.location_id.split(",") : [];
      var numSelectedLocation = 0;
      vm.locations.forEach(function(location) {
        location.selected = _.includes(locationParams, location.id.toString());
        if (location.selected) {numSelectedLocation += 1;}
      });
      if (_.isEqual(numSelectedLocation, vm.locations.length)) {vm.chkAllLocationState = true;}
    } else {
      vm.locations.forEach(function(location) {
        location.selected = true;
      });
      vm.chkAllLocationState = true;
    }
    checkChangeLocationInput();
  }

  function initStatusInpStates(paramStatus) {
    if (paramStatus) {
      var statusParams = !_.isNil(vm.params.status_before_cast) ? vm.params.status_before_cast.split(",") : [];
      var numSelectedStatus = 0;
      vm.statuses.forEach(function(status) {
        status.selected = _.includes(statusParams, status.id.toString());
        if (status.selected) {numSelectedStatus += 1;}
      });
      if (_.isEqual(numSelectedStatus, vm.statuses.length)) {vm.chkAllStatusState = true;}
    } else {
      vm.statuses.forEach(function(status) {
        status.selected = true;
      });
      vm.chkAllStatusState = true;
    }
  }

  function checkStateForChkboxAll(location) {
    if(vm.chkAllLocationState && !location.selected)
      vm.chkAllLocationState = !vm.chkAllLocationState;
  }

  vm.setDefaultIdSearch = function(searchId, model){
    if(_.isUndefined(searchId) || searchId < 0){
      vm.params[model] = 0;
    };
  }

  vm.changePerPage = function() {
    vm.refresh(false, false, true, true);
    vm.scrollToResult();
  }

  vm.changePage = function() {
    vm.currentParams.page = vm.params.page;
    vm.refresh(false, false, true);
    vm.scrollToResult();
  }

  function formatPerPageParam(currentParams) {
    currentParams.per_page = vm.params.per_page;
    return currentParams;
  }

  vm.isShowAllLocationChkboxes = function() {
    return !_.isEmpty(vm.locations);
  }
}
