"use strict";

angular.module("corporationApp")
  .controller("OrderCaseListController", OrderCaseListController);

OrderCaseListController.$inject = ["orderCaseListService", "checkValidDateFunction", "$location", "toaster"];

function OrderCaseListController(orderCaseListService, checkValidDateFunction, $location, toaster) {
  var vm = this;
  var CHECK_ALL_TXT = angular.element("#check-all-location-txt").text().trim();
  var LOCALE = I18n.locale;
  var COMMA = I18n.t("common.comma");
  var orderCaseStatuses = I18n.t("corporation.order_case.order_case_list_page.status_by_portion");
  vm.perPageSettings = [];
  vm.params = $location.search();
  vm.toasterTimeout = 6200;
  vm.params.search_by_start_time = false;

  $(".project-list").on("input", ".datetimepicker-input", function() {
    var modelName = $(this).attr("ng-model").split(".")[2];
    vm.params[modelName] = $(this).val();
  });

  vm.init = function() {
    vm.statuses = angular.element(".search-info__status").data("statuses");
    vm.working_statuses = angular.element(".arrangement-statuses").data("arrangement-statuses");
    vm.locations = angular.element(".location-list").data("locations");
    vm.perPageSettings = angular.element(".per-page-settings").data("settings");
    vm.params.per_page = vm.perPageSettings[0];
    vm.params.page = 1;
    vm.chkAllWorkingHour = vm.chkAllOrderType = vm.checkDisabledTime = false;
    vm.cancel_target_id = null;
    var lastConditionEl = angular.element(".search-order-case");
    var lastCondition = lastConditionEl.data("last-condition");
    var existLastCondition = false;
    vm.params.asc = vm.params.asc == "false" ? false : true;
    vm.workingHours = [
      {name: "morning", value: "06:00-08:59", label: I18n.t("corporation.order_case.order_case_list_page.early_morning")},
      {name: "lunch", value: "09:00-16:59", label: I18n.t("corporation.order_case.order_case_list_page.noon")},
      {name: "evening", value: "17:00-21:59", label: I18n.t("corporation.order_case.order_case_list_page.evening")},
      {name: "night", value: "22:00-23:59,00:00-05:59", label: I18n.t("corporation.order_case.order_case_list_page.late_night")}
    ];
    if (vm.params.is_ignore_condition) {
      initStatus(vm.working_statuses, "working_status_before_cast", false);
      delete vm.params.is_ignore_condition;
    } else if (!_.isEmpty(lastCondition)) {
      vm.params = lastCondition;
      existLastCondition = true;
    }

    initWorkingTimeCondition(existLastCondition);
    initLocationInpStates(existLastCondition);
    initStatusStates(existLastCondition);
    initOrderType(existLastCondition);
    checkInputWorkingTime();
    vm.currentParams = vm.params;
    vm.notAvalableCancelIds = [];
    vm.order_cases = [];

    vm.refresh();
  };

  vm.isDisableCheckBox = function(orderCaseId) {
    return vm.notAvalableCancelIds.includes(orderCaseId);
  };

  vm.disableCheckBoxClass = function(orderCaseId) {
    if (vm.isDisableCheckBox(orderCaseId)) return "d-none";
  };

  vm.hiddenCheckAllClass = function() {
    if (vm.notAvalableCancelIds.length == vm.order_cases.length) return "d-none";
  };

  vm.checkAllOrderCase = function() {
    _.forEach(vm.order_cases, function(orderCase) {
      if (!vm.isDisableCheckBox(orderCase.id)) {
        orderCase.select = vm.selectAll;
      }
    });
  };

  vm.checkSelected = function(orderCaseId) {
    var orderCase = _.filter(vm.order_cases, function(order_case) {
      return order_case.id == orderCaseId;
    });
    if (!vm.isDisableCheckBox(orderCase.id)) {
      orderCase.select = true;
    }
    var notSelectedCounter = vm.order_cases.length - vm.getCheckedIds().length;
    if (notSelectedCounter - vm.notAvalableCancelIds.length == 0) {
      vm.selectAll = true;
    } else {
      vm.selectAll = false;
    }
  };

  vm.hasSelectedItem = function() {
    return vm.getCheckedIds().length > 0;
  };

  vm.getCheckedIds = function() {
    var selectedItems = _.filter(vm.order_cases, function(orderCase) {
      if (!vm.isDisableCheckBox(orderCase.id)) {
        return orderCase.select;
      }
    });
    return _.map(selectedItems, "id");
  };

  vm.cancelOrderCases = function() {
    if (!vm.hasSelectedItem()) {
      $("#modal-confirm-cancel").modal("hide");
      return false;
    }
    var params = { order_case_ids: vm.getCheckedIds() };
    orderCaseListService.cancelOrderCases(params, LOCALE).then(function(res) {
      $("#modal-confirm-cancel").modal("hide");
      if (res.status) {
        toaster.pop("success", "", res.message);
      } else {
        toaster.pop("error", "", res.message);
      }
      location.reload(true);
    });
  };

  vm.cancelOrderCasesModal = function(orderCaseId) {
    $("#modal-confirm-cancel").modal("show");
  };

  function initStatusStates(paramStatus) {
    if (paramStatus) {
      initStatus(vm.statuses, "status_before_cast", "chkAllStatusState");
      initStatus(vm.working_statuses, "working_status_before_cast", "chkAllArrangementStatusState");
    };
  }

  function initStatus(statuses, paramsName, checkAll) {
    var statusIDs = vm.params[paramsName] === "" ? [] : vm.params[paramsName].split(",");
    statusIDs.forEach(function(status) {
      _.find(statuses, function(o) {return o.key === _.toInteger(status);}).selected = true;
    });
    vm[checkAll] = _.filter(statuses, function(status) {return status.selected;}).length === statuses.length;
  };

  function initOrderType(paramStatus) {
    if (paramStatus) {
      var orderTypes = _.isNil(vm.params.type_id) ? [] : vm.params.type_id.split(",");
      if (_.includes(orderTypes, "individual")) {vm.individual = true;}
      if (_.includes(orderTypes, "batch")) {vm.batch = true;}
      if (vm.individual && vm.batch) {
        vm.chkAllOrderType = true;
      }
    }
  }

  function initLocationInpStates(paramStatus) {
    if (paramStatus) {
      var locationParams = vm.params.location_id === "" ? [] : vm.params.location_id.split(",");
      var numSelectedLocation = 0;
      vm.locations.forEach(function(location) {
        location.selected = _.includes(locationParams, location.id.toString());
        if (location.selected) {numSelectedLocation += 1;}
      });
      if (_.isEqual(numSelectedLocation, vm.locations.length)) {vm.chkAllLocationState = true;}
    }
    checkChangeLocationInput();
  }

  vm.refresh = function(isUseUserCondition, isSave) {
    vm.selectAll = false;
    formatParamsBeforeSend();
    var params = isUseUserCondition ? formatPerPageParam(vm.currentParams) : vm.params;
    orderCaseListService.getOrderCases({search: params}, LOCALE).then(function(res) {
      if (res.data) {
        $("tbody tr").remove();
        vm.notAvalableCancelIds = res.data.not_avalable_cancel_ids;
        vm.order_cases = res.data.order_cases;
        vm["total_items"] = res.data.metadata.total_items;
      } else {
        vm.order_cases = [];
        vm["total_items"] = 0;
      }
      if (isSave) {
        orderCaseListService.createSearchCondition({search: params}, LOCALE).then(function(res) {
        });
      }
      $location.search(params).replace();
      vm.currentParams = angular.copy(params);
    });
  };

  function formatPerPageParam(currentParams) {
    currentParams.per_page = vm.params.per_page;
    return currentParams;
  }

  function initWorkingTimeCondition(paramStatus) {
    if (paramStatus) {
      var byWorkingTime = vm.params.search_by_start_time;
      var workingTimes = vm.params.working_time === "-" ? [] : vm.params.working_time.split(",");
      if (byWorkingTime) {
        workingTimes.forEach(function(time) {
          _.find(vm.workingHours, function(o) {return _.includes(o.value, time);}).selected = true;
        });
        vm.chkAllWorkingHour = getWorkingHourSelected().length === vm.workingHours.length;
      }
    }
  }

  function formatParamsBeforeSend() {
    formatWorkingTimeParams();
    formatLocationParams();
    formatStatusParams(vm.statuses, "status_before_cast");
    formatStatusParams(vm.working_statuses, "working_status_before_cast");
    formatTypeIdParams();
  }

  function formatTypeIdParams() {
    if (_.isUndefined(vm.individual) && _.isUndefined(vm.batch)) return;
    var individual = vm.individual ? "individual" : "";
    var batch = vm.batch ? "batch" : "";
    vm.params.type_id = [individual, batch].join(",");
    if (vm.params.type_id === ",") {vm.params.type_id = ""};
  }

  function formatStatusParams(statuses, paramsKey) {
    var statusSelected = _.filter(statuses, function(status) { return !!status.selected;});
    vm.params[paramsKey] =  _.map(statusSelected, function(status) {
      return status.key;
    }).join(",");
  }

  function formatWorkingTimeParams() {
    vm.params.working_time = [vm.params.start_time, vm.params.end_time].join("-");
    if (vm.checkDisabledTime) {
      vm.params.working_time = _.map(getWorkingHourSelected(), function(working) {
        return working.value;
      }).join(",");
    }
  }

  function formatLocationParams() {
    vm.params.location_id = _.map(getLocationSelected(), function(location) {
      return location.id;
    }).join(",");
  }

  function getLocationSelected() {
    return _.filter(vm.locations, function(location) { return !!location.selected;});
  }

  vm.toggleAllLocationChkboxes = function() {
    vm.chkAllLocationState = !vm.chkAllLocationState;
    var chkBoxState = !!vm.chkAllLocationState;
    vm.locations.forEach(function(location) {
      location.selected = chkBoxState;
    });
    checkChangeLocationInput();
  };

  function checkChangeLocationInput() {
    var selectedLocations = getLocationSelected();
    if (vm.chkAllLocationState && selectedLocations.length === vm.locations.length) {
      vm.locationInp = CHECK_ALL_TXT;
    } else {
      vm.locationInp = selectedLocations.map(function(location) {
        return location.name;
      }).join(COMMA);
    }
    $("#location-input").val(vm.locationInp).trigger("input");
  };

  vm.toggleLocationChkbox = function(location) {
    location.selected = !location.selected;
    checkChangeLocationInput();
    checkStateForChkboxAll(location);
  };

  function checkStateForChkboxAll(location) {
    if (vm.chkAllLocationState && !location.selected)
      vm.chkAllLocationState = !vm.chkAllLocationState;
  };

  vm.toggleAllStatusChkboxes = function(statuses, checkAll) {
    var chkBoxState = !!checkAll;
    statuses.forEach(function(status) {
      status.selected = chkBoxState;
    });
  };

  vm.checkStateForChkboxStatusAll = function(statuses, status, checkAllName) {
    if (vm[checkAllName] && !status.selected) {
      vm[checkAllName] = !vm[checkAllName];
    };
    var selectedStatus =  _.filter(statuses, function(status) { return status.selected;});
    vm[checkAllName] = selectedStatus.length === statuses.length;
  };

  vm.checkStateForChkboxArrangeStatusAll = function(status) {
    if(vm.chkAllStatusState && !status.selected)
      vm.chkAllStatusState = !vm.chkAllStatusState;
    var selectedStatus =  _.filter(vm.working_statuses, function(status) { return status.selected;});
    vm.chkAllStatusState = selectedStatus.length === vm.working_statuses.length;
  };

  vm.toggleAllWorkingHour = function() {
    var chkBoxWoking = !!vm.chkAllWorkingHour;
    vm.workingHours.forEach(function(workingHour) {
      workingHour.selected = chkBoxWoking;
    });
    checkInputWorkingTime();
  }

  vm.checkWokingCheckBoxAll = function(workingHour) {
    if (vm.chkAllWorkingHour && !workingHour.selected) {
      vm.chkAllWorkingHour = !vm.chkAllWorkingHour;
    }
    vm.chkAllWorkingHour = getWorkingHourSelected().length === vm.workingHours.length;
    checkInputWorkingTime();
  }

  function checkInputWorkingTime() {
    vm.params.search_by_start_time = vm.checkDisabledTime = getWorkingHourSelected().length > 0;
  }

  function getWorkingHourSelected() {
    return _.filter(vm.workingHours, function(working) { return !!working.selected;});
  }

  vm.toggleAllOrderType = function() {
    var chkBoxOrderType = !!vm.chkAllOrderType;
    vm.individual = vm.batch = chkBoxOrderType;
  }

  vm.changeOrderType = function(orderType) {
    if (vm.chkAllOrderType && !vm[orderType]) {
      vm.chkAllOrderType = !vm.chkAllOrderType;
    }
    if (vm.individual && vm.batch) {
      vm.chkAllOrderType = true;
    }
  }

  vm.changePage = function() {
    vm.currentParams.page = vm.params.page;
    vm.refresh();
  };

  vm.cancelOrderCase = function(order_case_id) {
    if (_.isNull(order_case_id)) {
      return;
    };
    orderCaseListService.cancelOrderCase({id: order_case_id}, LOCALE).then(function(res) {
      vm.cancel_target_id = null;
      var responseData = res.data;
      if (responseData.valid_order_case) {
        if (responseData.status) {
          var orderCase = _.find(vm.order_cases, function(orderCase) {
            return orderCase.id == responseData.order_case.id;
          });
          vm.notAvalableCancelIds.push(order_case_id);
          vm.checkSelected();
          orderCase.valid_cancel = responseData.order_case.valid_cancel;
          orderCase.status_id_i18n = responseData.order_case.status_id_i18n;
          toaster.pop("success", "", responseData.message);
        } else {
          toaster.pop("error", "", responseData.message);
        }
        $("#modal-confirm").modal("hide");
      } else {
        location.reload(true);
        toaster.pop("error", "", responseData.message);
      }
    });
  };

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.asc = vm.currentParams.asc = !vm.params.asc;
    } else {
      vm.params.asc = vm.currentParams.asc = true;
      vm.params.order_key = vm.currentParams.order_key = column;
    }
    vm.refresh(true, true);
    vm.scrollToResult();
  };

  vm.changePerPage = function() {
    vm.refresh(true, true);
    vm.scrollToResult();
  };

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !!vm.params.asc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !vm.params.asc
    }
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".order-case-search-result").offset().top
    }, 1000);
  };

  vm.redirectToDetail = function(orderCaseId) {
    var hostNamePrefix = $("#hostNamePrefix").attr("href");
    if (hostNamePrefix) {
      location.href = hostNamePrefix + "/" + I18n.locale + "/order_cases/" + orderCaseId;
    } else {
      location.href = "/" + I18n.locale + "/order_cases/" + orderCaseId;
    };
  };

  vm.cancelOrderCaseModal = function(orderCaseId) {
    vm.cancel_target_id = orderCaseId;
    $("#modal-confirm").modal("show");
  };

  vm.getClassStatus = function(status) {
    var classStatus = "project-status--inprogress";
    if (_.includes([orderCaseStatuses.cancel, orderCaseStatuses.finish_recruiting], status)) {
      classStatus = "color--gray project-status--finished"
    } else if (status == orderCaseStatuses.arranged) {
      classStatus = "color--green project-status--confirmed"
    };
    return classStatus;
  };

  vm.isShowAllLocationChkboxes = function() {
    return !_.isEmpty(vm.locations);
  };

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  }

  vm.checkValidTime = function(field) {
    if (!_.isEmpty(vm.params[field]) && !moment(vm.params[field], TIME_PICKER_FORMAT).isValid()) {
      var currentTime = moment.parseZone(Date(Date.now())).format(TIME_PICKER_FORMAT);
      vm.params[field] = currentTime;
    }
  }
}
