'use strict';

angular.module('corporationApp')
  .controller('RegisterUserController', RegisterUserController);

RegisterUserController.$inject = ['userService', '$window'];

function RegisterUserController(userService, $window) {
  var vm = this;
  var CHECK_ALL_TXT = angular.element("#check-all-location-txt").text().trim();
  var ALL_CHKBOXES = 0;
  var TOTAL_CHECKED = 0;
  var USER_GROUPS_TYPE = "user_groups";
  vm.chkboxAllState = false;
  vm.isSendEmail = false;
  vm.locationInp = [];
  vm.data = {
    user_roles: [],
    corporation_groups: [],
    user_corporation_groups: [],
    user_locations: [],
    usr_corp_grp_exists_order: [],
    usr_location_exists_order: [],
    user: {},
    account: {}
  }
  vm.userParams = {
    user: {
      role_id: null,
      user_locations_attributes: {},
      user_corporation_groups_attributes: {},
      user_groups_attributes: {}
    },
    account: {
      name: '', name_kana: '', email: ''
    }
  };
  vm.errorMessages = {};
  vm.currentSelected = {
    corporation_groups: [],
    locations: []
  };
  vm.action_type = "";

  vm.initData = function(user_roles, corporation_groups, user_corporation_groups,
    user_locations, usr_corp_grp_exists_order, usr_location_exists_order, user, account) {
    _.forIn(vm.data, function(_, key) {
      var value = eval(key);
      if(value) vm.data[key] = value;
    });

    if(!_.isNull(vm.data.user.id)) updateUserParams();
    if(!_.isEmpty(vm.data.user_corporation_groups)) updateChkboxForCorGrp(vm.data.user_corporation_groups);
    if(!_.isEmpty(vm.data.user_locations)) updateChkboxForLocation(vm.data.user_locations);
    countTotalElements();
    showLocationInput();

    if (!vm.data.user.id) vm.toggleForChkboxAll(true);
  }

  vm.openDeleteModal = function(modal_type) {
    angular.element(".confirm-delete-user").modal("show");
  }

  vm.createUser = function() {
    $(".disable-submit-btn").prop("disabled", true);
    prepareParams('new_user');
    userService.createUser(vm.userParams).then(function(res) {
      handleResponse(res);
    });
  }

  vm.updateUser = function(user_id) {
    $(".disable-submit-btn").prop("disabled", true);
    prepareParams('update_user');
    userService.updateUser(user_id, vm.userParams).then(function(res) {
      handleResponse(res);
    });
  }

  vm.deleteUser = function(user_id) {
    userService.deleteUser(user_id).then(function(res) {
      handleResponse(res);
    });
  }

  vm.cancelEdit = function(prefixStoreComputer) {
    if (_.isEmpty(prefixStoreComputer)) {
      $window.location.href = '/' + I18n.locale + '/users';
    } else {
      $window.location.href = prefixStoreComputer + '/' + I18n.locale + '/users';
    };
  }

  vm.toggleForChkboxAll = function(isCheckRole) {
    if (vm.isOwnerRole() && isCheckRole) return;
    vm.chkboxAllState = !vm.chkboxAllState;
    var currentState = vm.chkboxAllState;

    _.forEach(vm.data.corporation_groups, function(corporation_group) {
      vm.toggleForCorporationGroupChkbox(corporation_group, currentState);
    });
  }

  vm.toggleForCorporationGroupChkbox = function(corporation_group, parentChkboxState) {
    if(!!corporation_group.exists == false) corporation_group.selected = parentChkboxState || !corporation_group.selected;

    _.forEach(corporation_group.locations, function(location) {
      if(!!location.exists == false && !!corporation_group.exists == false)
        vm.toggleForLocationChkbox(location, corporation_group.selected);
      else if(!!location.exists == false && !!corporation_group.exists)
        vm.toggleForLocationChkbox(location, parentChkboxState);
    });
    checkChangedStateForView(corporation_group, 'corporation_groups');
    showLocationInput();
  };

  vm.toggleForLocationChkbox = function(location, parentChkboxState) {
    location.selected = _.isUndefined(parentChkboxState) ? !location.selected : parentChkboxState;
    if(_.isUndefined(parentChkboxState)) checkStateForCorGrpChkbox(location);
    checkChangedStateForView(location, 'locations');
    showLocationInput();
  }

  function checkStateForCorGrpChkbox(location) {
    var parentCorGrp = getCorGrpChkboxByLocation(location);

    if(!!parentCorGrp.exists == true) return false;
    else {
      if(!location.selected && !!parentCorGrp.selected) {
        parentCorGrp.selected = !parentCorGrp.selected;
      }
    }
    checkChangedStateForView(parentCorGrp, 'corporation_groups');
  }

  function getCorGrpChkboxByLocation(location) {
    return _.find(vm.data.corporation_groups, function(corporation_group) {
      return _.includes(corporation_group.locations, location);
    });
  }

  vm.isInvalid = function(element) {
    if(vm.errorMessages[element] != undefined)
     return true;
  }

  vm.isSelfEdit = function(user, current_user) {
    return _.isEqual(user, current_user);
  }

  vm.isHasRoleId = function() {
    return vm.userParams.user.role_id == null ? vm.data.user_roles[0].id : vm.userParams.user.role_id;
  }

  vm.isDisabled = function(element, elementType) {
    if (vm.isOwnerRole()) return;
    if(!element.exists) {
      if(elementType == 'corpGrp')
        vm.toggleForCorporationGroupChkbox(element);
      else
        vm.toggleForLocationChkbox(element);
    }
  }

  function updateUserParams() {
    _.assign(vm.userParams.user, vm.data.user);
    _.assign(vm.userParams.account, vm.data.account);
  }

  function updateChkboxForCorGrp(userCorporationGroups) {
    _.forEach(userCorporationGroups, function(userCorporationGroup) {
      var groupId = userCorporationGroup.corporation_group_id;
      var type = "user_corporation_groups";
      if (userCorporationGroup.corporation_group_tag_id) {
        groupId = userCorporationGroup.corporation_group_tag_id;
        type = "user_groups";
      }
      var selectedCorGrp = getCorGrById(groupId, type);
      if(selectedCorGrp) {
        var isDisabled = _.find(vm.data.usr_corp_grp_exists_order, function(orderCorporationGroupId) {
          return orderCorporationGroupId == selectedCorGrp.id && type == "user_corporation_groups";
        });
        if(isDisabled) selectedCorGrp.exists = true;

        selectedCorGrp.selected = true;

        if(!_.includes(vm.currentSelected.corporation_groups, selectedCorGrp))
          vm.currentSelected.corporation_groups.push(selectedCorGrp);
      }
    });
  }

  function updateChkboxForLocation(user_locations) {
    _.forEach(user_locations, function(user_location) {
      _.forEach(vm.data.corporation_groups, function(corporation_group) {
        var selectedLocation = _.find(corporation_group.locations, function(location) {
          return user_location.location_id == location.id;
        });

        if(selectedLocation) {
          var isDisabled = _.find(vm.data.usr_location_exists_order, function(order) {
            return order.location_id == selectedLocation.id;
          });
          if(isDisabled) selectedLocation.exists = true;

          selectedLocation.selected = true;

          if(!_.includes(vm.currentSelected.locations, selectedLocation))
            vm.currentSelected.locations.push(selectedLocation);
        }
      });
    });
  }

  function getCorGrById(id, type) {
    return _.find(vm.data.corporation_groups, function(corporation_group) {
      return corporation_group.id == id && corporation_group.type == type;
    });
  }

  function checkChangedStateForView(element, elementType) {
    var targetElement = vm.currentSelected[elementType];

    if(element.selected && !_.includes(targetElement, element)) {
      targetElement.push(element);
    } else if(!element.selected) {
      _.remove(targetElement, function(seletedElement) { return seletedElement == element });
    }
    vm.currentSelected[elementType] = targetElement;
  }

  function showLocationInput() {
    var selectedLocations = [];
    var totalCheckedChkboxes = vm.currentSelected.corporation_groups.length + vm.currentSelected.locations.length;
    if(totalCheckedChkboxes == ALL_CHKBOXES) {
      vm.chkboxAllState = true;
      selectedLocations = CHECK_ALL_TXT;
    } else {
      vm.chkboxAllState = false;
      var locationNames = _.map(vm.currentSelected.locations, function(location) { return location.name});
      selectedLocations = _.join(locationNames, ', ');
    }

    $('#location-input').val(selectedLocations).trigger('input');
  }

  function countTotalElements() {
    ALL_CHKBOXES = vm.data.corporation_groups.length;
    _.forEach(vm.data.corporation_groups, function(corporation_group) {
      ALL_CHKBOXES += corporation_group.locations.length;
    });
  }

  function handleResponse(res) {
    if(!_.isEmpty(res.data.errors)) {
      _.forEach(res.data.errors, function(error, key) {
        vm.errorMessages[key] = error[0].trim();
      });
      if (_.isEqual(res.config.method, "DELETE")) {
        toastr["error"](_.values(res.data.errors)[0]);
      };
      $(".disable-submit-btn").prop("disabled", false);
    } else {
      $window.location.href = res.data.redirect_path;
    }
  }

  function prepareParams(action_type) {
    vm.errorMessages = {};
    vm.action_type = action_type;
    vm.userParams.user.user_locations_attributes = {};
    vm.userParams.user.corporation_group_ids = [];
    getDataForUserParams();
  }

  function getDataForUserParams() {
    if(!!vm.chkboxAllState) {
      getAllChkboxes();
    } else {
      getCheckedChkboxes();
    }
  }

  function getAllChkboxes() {
    _.forEach(vm.data.corporation_groups, function(corporation_group) {
      getCorGrpChkboxData(corporation_group);
      _.forEach(corporation_group.locations, function(location) {
        getLocationChkboxData(corporation_group, location);
      });
    });
  }

  function getCheckedChkboxes() {
    getSelectedCorGrpChkboxes();
    getSelectedLocationChkboxes();
    if(!_.isEmpty(vm.data.user_corporation_groups)) getUnCheckedUserCorpGrp();
    if(!_.isEmpty(vm.data.user_locations)) getUnCheckedLocation();
  }

  function getSelectedCorGrpChkboxes() {
    _.forEach(vm.currentSelected.corporation_groups, function(corporation_group) {
      getCorGrpChkboxData(corporation_group);
    });
  }

  function getSelectedLocationChkboxes() {
    _.forEach(vm.data.corporation_groups, function(corporation_group) {
      var selectedLocations = getselectedLocationsBy(corporation_group);
      if(selectedLocations.length != 0) {
        _.forEach(selectedLocations, function(selectedLocation) {
          getLocationChkboxData(corporation_group, selectedLocation);
        });
      }
    });
  }

  function getUnCheckedLocation() {
    _.forEach(vm.data.corporation_groups, function(corporation_group) {
      _.forEach(corporation_group.locations, function(location) {
        var isInSelectedLocationsArr = _.find(vm.data.user_locations, function(user_location) {
          return user_location.location_id == location.id;
        });
        if(!location.selected && isInSelectedLocationsArr) {
          getLocationChkboxData(corporation_group, location, true);
        }
      });
    });
  }

  function getUnCheckedUserCorpGrp() {
    _.forEach(vm.data.corporation_groups, function(corporationGroup) {
      var isInSelectedCorGrpArr = _.find(vm.data.user_corporation_groups, function(userCorpGrp) {
        return checkGroupTagOrCorporationGroupSelected(userCorpGrp, corporationGroup);
      });
      if(!corporationGroup.selected && isInSelectedCorGrpArr) {
        getCorGrpChkboxData(corporationGroup, true);
      }
    })
  }

  function getCorGrpChkboxData(corporationGroup, _destroy) {
    var corpGrpChkboxData = {
      corporation_group_id: corporationGroup.id,
      _destroy: _destroy || false
    };
    if (corporationGroup.type == USER_GROUPS_TYPE) {
      corpGrpChkboxData.corporation_group_tag_id = corpGrpChkboxData.corporation_group_id;
      delete corpGrpChkboxData.corporation_group_id;
    }
    if(vm.action_type == "update_user") {
      var userCorporationGroup = getUserCorGrpByCorGrp(corporationGroup);
      if(userCorporationGroup != undefined) corpGrpChkboxData["id"] = userCorporationGroup.id;
    }
    if (corporationGroup.type == USER_GROUPS_TYPE) {
      vm.userParams.user.user_groups_attributes[TOTAL_CHECKED] = corpGrpChkboxData;
    } else {
      vm.userParams.user.user_corporation_groups_attributes[TOTAL_CHECKED] = corpGrpChkboxData;
    }
    TOTAL_CHECKED += 1;
  }

  function getLocationChkboxData(corporation_group, location, _destroy) {
    var locationChkboxData = {
      corporation_group_id: corporation_group.id,
      location_id: location.id,
      _destroy: _destroy || false
    };
    if (corporation_group.type == USER_GROUPS_TYPE) {
      locationChkboxData.corporation_group_id = location.corporation_group_id;
    }

    if(vm.action_type == "update_user") {
      var user_location = getUserLocationByLocation(location);
      if(user_location != undefined) locationChkboxData["id"] = user_location.id;
    }
    vm.userParams.user.user_locations_attributes[TOTAL_CHECKED] = locationChkboxData;
    TOTAL_CHECKED += 1;
  }

  function getUserLocationByLocation(location) {
    return _.find(vm.data.user_locations, function(user_location) { return user_location.location_id == location.id });
  }

  function getUserCorGrpByCorGrp(corporationGroup) {
    return _.find(vm.data.user_corporation_groups, function(usrCorGrp) {
      return checkGroupTagOrCorporationGroupSelected(usrCorGrp, corporationGroup);
    });
  }

  function getselectedLocationsBy(corporation_group) {
    return _.filter(corporation_group.locations, function(location) {
      return !!location.selected;
    });
  }

  vm.isOwnerRole = function() {
    return vm.userParams.user.role_id === "owner";
  }

  function checkGroupTagOrCorporationGroupSelected(userCorporationGroup, corporationGroup) {
    if (userCorporationGroup.corporation_group_tag_id) {
      return userCorporationGroup.corporation_group_tag_id == corporationGroup.id && corporationGroup.type == "user_groups";
    }
    return userCorporationGroup.corporation_group_id == corporationGroup.id && corporationGroup.type == "user_corporation_groups";
  }

  vm.changeRoleUser = function() {
    if (vm.isOwnerRole()) {
      vm.chkboxAllState = false;
      vm.toggleForChkboxAll();
    }
  }

  vm.locationName = function(location) {
    if (_.isUndefined(location.corporation_group_full_name)) return location.name;
    return location.name + "(" + location.corporation_group_full_name + ")";
  }
}
