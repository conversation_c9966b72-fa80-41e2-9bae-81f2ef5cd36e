"use strict";

angular.module("corporationApp")
  .controller("WorkAchievementListController", WorkAchievementListController);

WorkAchievementListController.$inject = ["workAchievementService", "$location", "toaster"];

function WorkAchievementListController(workAchievementService, $location, toaster) {
  var vm = this;
  var EVALUATION_TYPES = ["evaluation1", "evaluation2", "evaluation3"];
  var REST_TIMES = [1, 2, 3];
  var MAX_REST_TIMES = 3;
  var MIN_REST_TIMES = 1;
  var TYPES_PREFIX = ["staff_", ""];
  vm.workAchievement = {};
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  vm.params = $location.search();
  vm.toasterTimeout = 6200;
  vm.hasRecord = true;

  vm.init = function() {
    vm.params.page = 1;
    vm.params.per_page = vm.perPageSettings[0];
    vm.params.only_confirming = false;

    vm.refresh();
  };

  vm.refresh = function() {
    workAchievementService.getWorkAchievements(vm.params).then(function(res) {
      angular.extend(vm, res.data);
      vm.staffEvaluations = res.data.staff_evaluations;
      if (vm.work_achievements.length > 0 ) {
        vm.hasRecord = true;
      }
      $location.search(vm.params).replace();
    });
  };

  vm.displayTotalItems = function() {
    if (_.isUndefined(vm.total_items)) return;
    return I18n.t("corporation.work_achievements.index.total_items", {count: vm.total_items});
  };

  vm.isDiffWorkingTime = function(workAchievement) {
    var arrangementTimes = workAchievement.arrangement_working_time.split("~");
    var workAchievementTimes = workAchievement.staff_input_working_time.split("~");
    return (arrangementTimes[0] != workAchievementTimes[0]) || (arrangementTimes[1] != workAchievementTimes[1]);
  };

  vm.openInputWorkingTimeModal = function(workAchievement) {
    $(".disable-submit-btn").prop("disabled", false);
    vm.initWorkAchievement(workAchievement);
    vm.workAchievement.has_break = true;
    vm.currentAchievementId = workAchievement.id;
    $(".has-error").removeClass("has-error");
    $(".help-block").remove();
    $("#modal-time-status").modal("show");
  };

  vm.initWorkAchievement = function(workAchievement) {
    var defaultAttrs = {id: workAchievement.id, staff_name: workAchievement.staff_account_name}
    vm.workAchievement = _.merge(vm.setWorkAchievementTime(workAchievement), defaultAttrs);
  };

  vm.setWorkAchievementTime = function(workAchievement) {
    var workingTime = {};
    var restTimes = [];
    var prefix = workAchievement.working_time_status_id === "not_inputted" ? TYPES_PREFIX[1] : TYPES_PREFIX[0];
    workingTime["owner_working_started_at"] = formatTime(workAchievement[prefix + "working_started_at"]);
    workingTime["owner_working_ended_at"] = formatTime(workAchievement[prefix + "working_ended_at"]);
    _.forEach(REST_TIMES, function(rest_time) {
      var startTime = workAchievement[prefix + "rest" + rest_time + "_started_at"];
      var endTime = workAchievement[prefix + "rest" + rest_time + "_ended_at"];
      if (_.isEmpty(startTime)) return;
      workingTime["owner_rest" + rest_time + "_started_at"] = formatTime(startTime);
      workingTime["owner_rest" + rest_time + "_ended_at"] = formatTime(endTime);
      restTimes.push(rest_time);
    });
    workingTime["rest_times"] = restTimes;
    workingTime["owner_comment"] = workAchievement[prefix + "comment"];
    return workingTime;
  };

  vm.addRestTime = function(type, order) {
    var restTimeCount = vm.workAchievement.rest_times.length;
    if (restTimeCount === MAX_REST_TIMES) return;
    vm.workAchievement.rest_times.push(restTimeCount + 1);
  }

  vm.removeRestTime = function(restTime) {
    if (vm.workAchievement.rest_times.length === MIN_REST_TIMES) return;
    if (restTime === 2) {
      vm.workAchievement.owner_rest2_started_at = vm.workAchievement.owner_rest3_started_at;
      vm.workAchievement.owner_rest2_ended_at = vm.workAchievement.owner_rest3_ended_at;
    }
    vm.workAchievement.owner_rest3_started_at = vm.workAchievement.owner_rest3_ended_at = "";
    vm.workAchievement.rest_times.pop();
  }

  vm.countRestTime = function(rest_time) {
    var startTime = vm.workAchievement["owner_rest" + rest_time + "_started_at"];
    var endTime = vm.workAchievement["owner_rest" + rest_time + "_ended_at"];
    if (_.isEmpty(startTime) || _.isEmpty(endTime)) return;
    var start = moment(startTime, TIME_PICKER_FORMAT);
    var end = moment(endTime, TIME_PICKER_FORMAT);
    if (end.isBefore(start)) end.add(1, "day");
    return I18n.t("corporation.order.step2.total_minutes", {minutes: end.diff(start, "minutes")});
  }

  vm.submitWorkAchievement = function(checkBreakTime) {
    $(".disable-submit-btn").prop("disabled", true);
    var workAchievementParams = $("form#owner-input-working-time").serializeJSON();
    if (!workAchievementParams.work_achievements.has_break) {
      _.forEach(REST_TIMES, function(rest_time) {
        workAchievementParams.work_achievements["owner_rest" + rest_time + "_started_at"] = "";
        workAchievementParams.work_achievements["owner_rest" + rest_time + "_ended_at"] = "";
      });
    }
    _.merge(workAchievementParams, {check_break_time: checkBreakTime});
    workAchievementService.updateWorkAchievement(workAchievementParams, vm.workAchievement.id).then(function(res) {
      if (res.data.status) {
        if (res.data.break_time_warning) {
          $("#modal-confirm-break-time-warning").modal("show");
          $(".disable-submit-btn").prop("disabled", false);
        } else {
          $("#modal-time-status").modal("hide");
          if (_.isUndefined(vm.currentEvaluation)) {
            vm.initStaffEvaluation();
            $("#modal-review").modal("show");
            $(".disable-submit-btn").prop("disabled", false);
          } else {
            displaySuccessMessage();
            vm.refresh();
          }
        }
      } else {
        $.lawsonAjax(res.data);
        if (res.data.locked_message) {
          toaster.pop("error", "", res.data.locked_message);
        }
        $(".disable-submit-btn").prop("disabled", false);
      }
    });
  }

  function calMaxEvaluation(evaluationType) {
    return _.maxBy(vm[evaluationType], function(star) {if (star.selected) return star.value;});
  }

  vm.approveWorkAchievement = function(workAchievement) {
    $(".disable-submit-btn").prop("disabled", true);
    workAchievementService.updateWorkAchievement({approve: true}, workAchievement.id).then(function(res) {
      vm.initStaffEvaluation();
      vm.setCurrentEvaluation(workAchievement.arrangement_id);
      if (_.isEmpty(vm.currentEvaluation)) {
        vm.initWorkAchievement(workAchievement);
        vm.currentAchievementId = workAchievement.id;
        $("#modal-review").modal("show");
        $(".disable-submit-btn").prop("disabled", false);
      } else {
        $("#modal-time-status").modal("hide");
        displaySuccessMessage();
        vm.refresh();
      }
    });
  }

  vm.initStaffEvaluation = function() {
    vm.evaluation1 = [{selected: true, value: 1}, {selected: false, value: 2}, {selected: false, value: 3},
      {selected: false, value: 4}, {selected: false, value: 5}];
    vm.evaluation2 = angular.copy(vm.evaluation1);
    vm.evaluation3 = angular.copy(vm.evaluation1);
    vm.evaluationParams = {};
  }

  vm.setCurrentEvaluation = function(arrangementId) {
    vm.currentEvaluation = _.find(vm.staffEvaluations, function(evaluation) {return evaluation.arrangement_id === arrangementId});
    if (vm.currentEvaluation) {
      _.forEach(EVALUATION_TYPES, function(type) {
        _.forEach(vm[type], function(currentStar) {
          currentStar.selected = currentStar.value <= vm.currentEvaluation[type];
        });
        vm.evaluationParams[type + "_comment"] = vm.currentEvaluation[type + "_comment"];
      });
    }
  }

  vm.changeActivedStar = function(evaluationType, star) {
    if (vm.currentEvaluation) return;
    _.forEach(vm[evaluationType], function(currentStar) {
      currentStar.selected = currentStar.value <= star.value;
    });
  }

  vm.staffReviewEvaluation = function() {
    $(".disable-submit-btn").prop("disabled", true);
    _.forEach(EVALUATION_TYPES, function(type) {
      vm.evaluationParams[type] = calMaxEvaluation(type).value;
    });
    var submitParams = {work_achievement_id: vm.currentAchievementId, staff_evaluation: vm.evaluationParams};
    workAchievementService.reviewStaffEvaluation(submitParams).then(function(res) {
      $.lawsonAjax(res.data);
      if (res.data.status) {
        vm.closeReviewModal();
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  }

  vm.closeReviewModal = function() {
    $("#modal-review").modal("hide");
    displaySuccessMessage();
    vm.refresh();
  }

  vm.openConfirmBreakTimeWarning = function(workAchievement) {
    vm.currentWorkAchievement = workAchievement;
    $("#modal-confirm-approve-break-time-warning").modal("show");
  }

  function formatTime(time) {
    if (_.isEmpty(time)) return;
    return moment.parseZone(time).format(TIME_PICKER_FORMAT);
  }

  function displaySuccessMessage() {
    return toaster.pop("success", "", I18n.t("work_achievement.input_working_time.input_success"));
  }
}
