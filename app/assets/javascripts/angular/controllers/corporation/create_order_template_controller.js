"use strict"

angular.module("corporationApp")
  .controller("CreateOrderTemplateController", CreateOrderTemplateController);

CreateOrderTemplateController.$inject = ["orderService", "orderTemplateService", "$location", "$scope"];

function CreateOrderTemplateController(orderService, orderTemplateService, $location, $scope) {
  var vm = this;
  var LOCALE = I18n.locale;
  var MIN_STAFF = 1;
  var MAX_STAFF = 100;
  var NUMBER_BASE = 10;
  var MAX_REST_TIMES = 3;
  var MIN_REST_TIMES = 1;
  var REST_TIMES = [1, 2, 3];
  var DEFAULT_EMAIL_SUFFIX = "@lawsonstaff.co.jp";
  var ALL_FIELDS = ["id", "name", "location_id", "last_used_at", "is_used_existing_pic", "haken_destination_pic_id",
    "haken_destination_pic_position", "haken_destination_pic_tel", "mandator_id", "mandator_position", "mandator_tel",
    "claim_pic_id", "claim_pic_position", "claim_pic_tel", "order_pic_id", "order_pic_email", "order_pic_tel",
    "working_start_time", "working_end_time", "is_time_changable", "required_start_time", "required_end_time",
    "rest1_start_time", "rest1_end_time", "rest2_start_time", "rest2_end_time", "rest3_start_time", "rest3_end_time",
    "staff_count"];
  var TIME_FIELDS = ["working_start_time", "working_end_time", "required_start_time", "required_end_time"];
  var TEMPLATE_COMMON_FIELDS = ["working_start_time", "working_end_time", "required_start_time", "required_end_time",
    "rest1_start_time", "rest1_end_time", "rest2_start_time", "rest2_end_time", "rest3_start_time", "rest3_end_time", "staff_count"];
  var TEMPLATE_PIC_FIELDS = ["is_used_existing_pic", "haken_destination_pic_id", "haken_destination_pic_position",
    "haken_destination_pic_tel", "mandator_id", "mandator_position", "mandator_tel", "claim_pic_id", "claim_pic_position",
    "claim_pic_tel", "order_pic_id", "order_pic_email", "order_pic_tel"];
  var PIC_DATA = ["claim", "haken_destination", "mandator", "order",
    "is_valid_order_info", "violation_day", "days_to_violation_day", "order_pic_tel",
    "prefecture_id", "is_approval_required", "closed_day_error_message", "transaction_error_message",
    "location_not_survey_error_message", "location_survey_path"];
  var currentUserID = angular.element(".create-order__more-info").data("current-user-id");
  vm.$scope = $scope;
  vm.errorMessages = {};
  vm.params = {
    is_time_changable: false,
    is_used_existing_pic: true
  }
  vm.selectedLocation = {};
  vm.buttonsDisabled = false;
  vm.isValidLocation = true;

  vm.$scope.$watch("vm.params.is_used_existing_pic", function(){
    if (!vm.params.is_used_existing_pic && vm.isStoreComputer) {
      vm.params.order_pic_email = "";
    }
  });

  vm.$scope.$watch("vm.params.location_id", function() {
    vm.refreshLocationPicData();
  });

  vm.$scope.$watch("vm.params.is_time_changable", function() {
    if (!vm.params.is_time_changable) {
      vm.params.required_start_time = vm.params.required_end_time = "";
    }
  });

  vm.init = function(locations, orderTemplate) {
    vm.locations = locations;
    if (!!orderTemplate) {
      vm.firstTimeShowEdit = true;
      _.forEach(ALL_FIELDS, function(fieldName) {
        if (_.includes(TIME_FIELDS, fieldName) && !_.isEmpty(orderTemplate[fieldName])) {
          vm.params[fieldName] = moment.parseZone(orderTemplate[fieldName]).format(TIME_PICKER_FORMAT);
        } else {
          vm.params[fieldName] = orderTemplate[fieldName];
        }
      });
    }
    vm.initRestTime();
  };

  vm.refreshLocationPicData = function() {
    removeErrorFromArea(".location-pic-input-area");
    if (!vm.params.location_id || _.isEmpty(vm.params.location_id.toString())) {
      vm.selectedLocation = {isValidLocation: true};
      vm.initLocationPicData();
      return vm.selectedLocation;
    }
    loadLocationData();
    if (!_.isEmpty(vm.originLocationData) && vm.location.id === vm.originLocationData.id) {
      vm.location = vm.originLocationData;
      vm.originLocationData = {};
    };

    if (!!vm.params.location_id) {
      $("#spinner").removeClass("ng-hide");
      var firstTime = vm.firstTimeShowEdit;
      orderService.loadPicOptions({location_id: vm.params.location_id}).then(function(res) {
        var results = res.data;
        _.forEach(PIC_DATA, function(pic_data) {
          vm.selectedLocation[pic_data] = results[pic_data];
        });
        vm.params.organization_id = results.organization_id;
        vm.params.organization_full_name = results.organization_full_name;
        vm.locationCode = results.location_code;
        vm.isStoreComputer = results.is_store_computer;
        vm.checkLocationIsValid();
        if (vm.selectedLocation.isValidLocation) {
          vm.initLocationPicData(firstTime);
        }
        $("#spinner").addClass("ng-hide");
      }, function(error) {
        $("#spinner").addClass("ng-hide");
      });
    }
    vm.firstTimeShowEdit = false;
  };

  vm.removeErrorFromField = function(field) {
    if (!!vm.params[field]) {
      vm.errorMessages[field] = "";
    }
  };

  vm.isInvalid = function(field) {
    if(vm.errorMessages[field] == undefined || vm.errorMessages[field] == "") {return false;}
    return true;
  };

  vm.changeStaffCount = function(add) {
    var staffCount = vm.params.staff_count;
    if (add) {
      vm.params.staff_count = staffCount >= MAX_STAFF ? MAX_STAFF : staffCount + 1;
    } else {
      vm.params.staff_count = staffCount <= MIN_STAFF ? MIN_STAFF : staffCount - 1;
    }
  };

  vm.isDisableStaffCountBtn = function(add, staffCount) {
    if (add) {
      return staffCount >= MAX_STAFF;
    } else {
      return staffCount <= MIN_STAFF;
    }
  };

  vm.inputStaffCount = function() {
    var staffCount = parseInt(vm.params.staff_count, NUMBER_BASE);
    if (!_.isNaN(staffCount) && _.isNumber(staffCount)) {
      if (staffCount >= MAX_STAFF) {
        vm.params.staff_count = MAX_STAFF;
      } else if (staffCount <= MIN_STAFF) {
        vm.params.staff_count = MIN_STAFF;
      } else {
        vm.params.staff_count = staffCount;
      }
    } else {
      vm.params.staff_count = MIN_STAFF;
    }
  };

  vm.initRestTime = function() {
    var rest_times = [];
    _.forEach(REST_TIMES, function(rest_time) {
      var startTime = vm.params["rest" + rest_time + "_start_time"];
      var endTime = vm.params["rest" + rest_time + "_end_time"];
      if (_.isEmpty(vm.params["rest" + rest_time + "_start_time"])) {
        if (!_.isEmpty(vm.params["rest" + (rest_time + 1) + "_start_time"])) {
          rest_times.push(rest_time);
        }
        return;
      }
      vm.params["rest" + rest_time + "_start_time"] = formatDate(startTime, TIME_PICKER_FORMAT);
      vm.params["rest" + rest_time + "_end_time"] = formatDate(endTime, TIME_PICKER_FORMAT);
      rest_times.push(rest_time);
    });
    vm.rest_times = _.isEmpty(rest_times) ? [1] : rest_times;
  };

  vm.addRestTime = function() {
    var restTimes = vm.rest_times.slice();
    var restTimeCount = restTimes.length;
    if (restTimeCount === MAX_REST_TIMES) return;
    restTimes.push(restTimeCount + 1);
    vm.rest_times = restTimes;
  };

  vm.removeRestTime = function(restTime) {
    var restTimes = vm.rest_times.slice();
    var restTimeCount = restTimes.length;
    if (restTimeCount === MIN_REST_TIMES) return;
    if (restTime === 2) {
      vm.params.rest2_start_time = vm.params.rest3_start_time;
      vm.params.rest2_end_time = vm.params.rest3_end_time
    }
    vm.params.rest3_start_time = vm.params.rest3_end_time = "";
    restTimes.pop();
    vm.rest_times = restTimes;
  };

  vm.countRestTime = function(restTime) {
    var startTime = vm.params["rest" + restTime + "_start_time"];
    var endTime = vm.params["rest" + restTime + "_end_time"];
    if (_.isEmpty(startTime) || _.isEmpty(endTime)) return;
    var start = moment(startTime, TIME_PICKER_FORMAT);
    var end = moment(endTime, TIME_PICKER_FORMAT);
    if (end.isBefore(start)) end.add(1, "day");
    return I18n.t("corporation.order.step2.total_minutes", {minutes: end.diff(start, "minutes")});
  };

  vm.initLocationPicData = function(firstTime, order) {
    if (firstTime) {return;}
    var types = ["haken_destination", "claim", "order"];
    var isDeleted = false;
    if (!_.isEmpty(order)) {
      isDeleted = !_.find(vm.selectedLocation.order, function(o) {
        return o.id == order["order_pic_id"];
      });
    }
    _.forEach(types, function(type) {
      if (type === "order" && isDeleted) {
        vm.params[type + "_pic_id"] = "";
        vm.params[type + "_pic_email"] = "";
        vm.params[type + "_pic_position"] = order[type + "_pic_position"];
        vm.params[type + "_pic_tel"] = order[type + "_pic_tel"];
      } else if (_.isEmpty(order)) {
        vm.params[type + "_pic_id"] = getSelectedLocationProperty(type, "id");
        vm.params[type + "_pic_position"] = getSelectedLocationProperty(type, "position");
        vm.params[type + "_pic_email"] = getSelectedLocationProperty(type, "email");
        vm.params[type + "_pic_tel"] = getSelectedLocationProperty(type, "tel");
      } else {
        vm.params[type + "_pic_id"] = order[type + "_pic_id"];
        vm.params[type + "_pic_position"] = order[type + "_pic_position"];
        vm.params[type + "_pic_email"] = order[type + "_pic_email"];
        vm.params[type + "_pic_tel"] = order[type + "_pic_tel"];
      }
    });
    vm.initMandator(order);
  };

  vm.initMandator = function(order) {
    if (_.isEmpty(order)) {
      vm.params.mandator_id = getSelectedLocationProperty("mandator", "id");
      vm.params.mandator_position = getSelectedLocationProperty("mandator", "position");
      vm.params.mandator_tel = getSelectedLocationProperty("mandator", "tel");
      vm.params.order_pic_tel = _.isEmpty(vm.selectedLocation) ? "" : vm.selectedLocation.order_pic_tel;
    } else {
      vm.params.mandator_id = order["mandator_id"];
      vm.params.mandator_position = order["mandator_position"];
      vm.params.mandator_tel = order["mandator_tel"];
      vm.params.order_pic_tel = _.isEmpty(vm.selectedLocation) ? "" : vm.selectedLocation.order_pic_tel;
    }
  };

  vm.changePosition = function(type) {
    var picID = vm.params[type + "_pic_id"];
    var locationPic = {};
    if (_.isNaN(picID)) {
      locationPic = {
        position: "",
        email: "",
        tel: ""
      }
    } else {
      var currentPics = vm.selectedLocation[type];
      locationPic = _.find(currentPics, function(pic) {return pic.id === parseInt(picID)});
    }
    changePicValue(type, locationPic, false);
  };

  vm.changePositionMandator = function() {
    var picID = vm.params.mandator_id;
    var locationPic = {};
    if (_.isNaN(picID)) {
      locationPic = {
        position: "",
        tel: ""
      }
    } else {
      locationPic = _.find(vm.selectedLocation.mandator, function(pic) {return pic.id === parseInt(picID)});
    }
    changePicValue("mandator", locationPic, true);
  };

  vm.checkLocationIsValid = function() {
    vm.selectedLocation.isValidLocation = true;
    var requiresInfo = [vm.selectedLocation.claim, vm.selectedLocation.haken_destination,
      vm.selectedLocation.mandator];
    if (!_.isEmpty(vm.params.location_id.toString())) {
      _.forEach(requiresInfo, function(locationPic) {
        if (_.isUndefined(locationPic) || !vm.selectedLocation.is_valid_order_info) {
          vm.params.order_pic_tel = "";
          vm.selectedLocation.isValidLocation = false;
        }
      });
    }
  };

  vm.createOrderTemplate = function() {
    vm.errorMessages = {};
    $("#spinner").removeClass("ng-hide");
    $(".disable-submit-btn").prop("disabled", true);
    vm.buttonsDisabled = true;
    setEditableParams();
    orderTemplateService.createOrderTemplate({order_template: vm.params}, LOCALE).then(function(res) {
      if (res.data.status) {
        window.location.href = res.data.redirect_path;
      } else {
        $(".disable-submit-btn").prop("disabled", false);
        $("#spinner").addClass("ng-hide");
        applyResponseErrors(res.data.errors);
        scrollToError();
      }
    });
  };

  vm.updateOrderTemplate = function() {
    vm.errorMessages = {};
    $("#spinner").removeClass("ng-hide");
    $(".disable-submit-btn").prop("disabled", true);
    vm.buttonsDisabled = true;
    setEditableParams();
    orderTemplateService.updateOrderTemplate(vm.params.id, {order_template: vm.params}, LOCALE).then(function(res) {
      if (res.data.status || !!res.data.redirect_path) {
        window.location.href = res.data.redirect_path;
      } else {
        $(".disable-submit-btn").prop("disabled", false);
        $("#spinner").addClass("ng-hide");
        applyResponseErrors(res.data.errors);
        scrollToError();
      }
    });
  };

  function removeErrorFromArea(wrapArea) {
    var $wrapPicArea = $(wrapArea);
    $wrapPicArea.find(".error-notice").remove();
    $wrapPicArea.find(".form-group").removeClass("form-error");
  };

  function loadLocationData() {
    vm.location = _.find(vm.locations, function(lo) {
      return lo.id === parseInt(vm.params.location_id);
    }) || [];
  };

  function getSelectedLocationProperty(type, key) {
    if (!_.isEmpty(vm.selectedLocation) && vm.selectedLocation.isValidLocation && vm.selectedLocation[type] &&
      vm.selectedLocation[type][0]) {
      if (type === "order") {
        var selectedOrder = _.find(vm.selectedLocation.order, function(order) {return order.id === currentUserID;});
        var property = selectedOrder[key];
        if (vm.isStoreComputer && _.isEmpty(property) && key === "email") {
          property = vm.locationCode + DEFAULT_EMAIL_SUFFIX;
        }
        return property;
      }
      return vm.selectedLocation[type][0][key];
    }
    return "";
  };

  function formatDate(date, formatType) {
    if (_.isEmpty(date)) return;
    return moment.parseZone(date).format(formatType);
  };

  function changePicValue(type, locationPic, isMadator) {
    removeErrorFromArea("." + type);
    if (_.isUndefined(locationPic)) {
      return;
    }
    var validLocation = vm.selectedLocation.isValidLocation;
    if (isMadator) {
      vm.params[type + "_position"] = validLocation ? locationPic.position : "";
      vm.params[type + "_tel"] = validLocation ? locationPic.tel : "";
    } else {
      vm.params[type + "_pic_position"] = validLocation ? locationPic.position : "";
      vm.params[type + "_pic_email"] = validLocation ? locationPic.email : "";
      if (type === "order") {
        vm.params.user_locations_pic_tel = validLocation ? locationPic.tel : "";
      } else {
        vm.params[type + "_pic_tel"] = validLocation ? locationPic.tel : "";
      }
    }
  };

  function applyResponseErrors(errors) {
    if (_.isEmpty(errors)) {return;}
    _.forEach(errors, function(error, key) {
      vm.errorMessages[key] = error[0].trim();
    });
  };

  vm.triggerUpdateModel = function(elem, field) {
    if ($("#" + elem).val() == vm.params[field]) {return;}
    vm.params[field] = $("#" + elem).val();
  };

  function scrollToError() {
    if (_.isEmpty(vm.errorMessages)) {return;}
    var area = "";
    var errorKeys = Object.keys(vm.errorMessages);
    if (!!vm.errorMessages.location_id || !vm.selectedLocation.isValidLocation) {
      area = "#location_id"
    } else if (_.intersection(errorKeys, TEMPLATE_COMMON_FIELDS).length > 0) {
      area = "#order-template";
    } else if (_.intersection(errorKeys, TEMPLATE_PIC_FIELDS).length > 0) {
      area = "#location-pic-area";
    }

    if (_.intersection(errorKeys, TEMPLATE_PIC_FIELDS).length > 0) {
      vm.params.is_used_existing_pic = false;
    }

    if (_.isEmpty(area)) {return;}
    $("html,body").animate({
      scrollTop: $(area).offset().top - 100
    }, 1000);
  };

  function setEditableParams() {
    _.forEach(REST_TIMES, function(restTime) {
      vm.params["rest" + restTime + "_editable"] = _.includes(vm.rest_times, restTime);
    });
  };

  function disableEnter(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 13) {
      e.preventDefault();
      $("input").blur();
    }
  };

  function disableBackspace(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 8) {
      e.preventDefault();
    }
  };

  if (!_.isNull($("#order-template-form"))) {
    $("#order-template-form").keydown(function(e) {
      disableEnter(e);
    });
  }

  $("html, body").keydown(function(e) {
    var $target = $(e.target || e.srcElement);
    if(!$target.is('input,[contenteditable="true"],textarea')) {
      disableBackspace(e);
    }
  });
};