"use strict";

angular.module("corporationApp")
  .controller("UsageAchievementsController", UsageAchievementsController);

UsageAchievementsController.$inject = ["usageAchievementsService", "homeService", "$location", "toaster"];

function UsageAchievementsController(usageAchievementsService, homeService, $location, toaster) {
  var vm = this;
  var LOCALE = I18n.locale;
  var SEARCH_CONDITION_TYPE = "user_arrange_billing_search_conditions";
  var MAX_SELECTED_LOCATIONS = 14;
  vm.params = $location.search();
  vm.perPageSettings = angular.element(".per-page-settings").data("settings");
  var hostNamePrefix = $("#hostNamePrefix").attr("href");

  init();

  $(".usage-confirm").on("input", ".datetimepicker-input", function() {
    var modelName = $(this).attr("ng-model").split(".")[2];
    vm.params[modelName] = $(this).val();
  });

  vm.search = function(isSave, isReset) {
    if (isReset) {
      vm.params.page = 1;
    };
    vm.params.per_page = _.toInteger(vm.params.per_page) || vm.perPageSettings[0];
    vm.checkValidDate("start_date");
    vm.checkValidDate("end_date");
    usageAchievementsService.searchOrderCase({search: vm.params}).then(function(res) {
      if (res.data) {
        angular.extend(vm, res.data);
        _.forEach(vm.arrange_billings, function(arrangeBilling) {
          arrangeBilling.select = false;
        });
        vm.isSelected = false;
        vm.selectAll = false;
        vm.is_check_all_records = false;
      } else {
        vm.order_cases = [];
        vm.total_items = 0;
      };
    });
    $location.search(vm.params).replace();
    if (isSave) {
      var searchConditionParams = _.omit(vm.params, "page", "limit");
      var params = {search: searchConditionParams, search_type: "arrange_billing",
        location_id: vm.params.location_id, search_condition: SEARCH_CONDITION_TYPE};
      usageAchievementsService.createSearchCondition(params).then(function(res) {
      });
    };
  };

  vm.searchByLocation = function(isAll, location, inList) {
    if (isAll) {
      vm.location_name = I18n.t("corporation.home.index.all_sites");
      vm.params.location_id = null;
    } else {
      vm.location_name = location.name;
      vm.params.location_id = _.toString(location.id);
      var selectedLocation = _.find(vm.last_selected_locations, function(selectedLocation) {
        return selectedLocation.id == location.id;
      });
    };
    if (!isAll && inList && !selectedLocation) {
      if (vm.last_selected_locations.length === MAX_SELECTED_LOCATIONS) {
        vm.last_selected_locations.shift();
      };
      vm.last_selected_locations.push(location);
    };
    vm.search(true, true);
  };

  vm.changePerPage = function() {
    vm.search(true);
    vm.scrollToResult();
  };

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $(".arrange-billing-list").offset().top
    }, 1000);
  };

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.asc = !vm.params.asc;
    } else {
      vm.params.asc = true;
      vm.params.order_key = column;
    }
    vm.search(true, false);
  };

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !!vm.params.asc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !vm.params.asc
    }
  };

  vm.checkAll = function() {
    _.forEach(vm.arrange_billings, function(arrangeBilling) {
      arrangeBilling.select = vm.selectAll;
    });
    vm.checkSelected(true);
  };

  vm.checkSelected = function(isChecked) {
    var selectedItems = _.find(vm.arrange_billings, {select: true});
    vm.isSelected = vm.is_check_all_records || selectedItems ? true : false;
    if (vm.selectAll && !isChecked) {
      vm.selectAll = false;
    };
  };

  vm.goToBatchDetail = function() {
    var params = "";
    if (vm.is_check_all_records) {
      var searchAllParams = _.omit(vm.params, "page", "per_page");
      searchAllParams.is_check_all_records = vm.is_check_all_records;
      params = $.param(searchAllParams);
    } else {
      var selectedItems = _.filter(vm.arrange_billings, function(arrangeBilling) {
        return arrangeBilling.select;
      });
      params = "ids=" + _.map(selectedItems, "id");
    };

    if (hostNamePrefix) {
      window.open(hostNamePrefix + "/" + LOCALE + "/usage_achievements/view_detail/?" + params, "_blank");
    } else {
      window.open("/" + LOCALE + "/usage_achievements/view_detail/?" + params, "_blank");
    };
  };

  vm.goToDetail = function(arrangeBillingId) {
    if (hostNamePrefix) {
      window.open(hostNamePrefix + "/" + LOCALE + "/usage_achievements/view_detail/?ids=" + arrangeBillingId, "_blank");
    } else {
      window.open("/" + LOCALE + "/usage_achievements/view_detail/?ids=" + arrangeBillingId, "_blank");
    };
  };

  vm.checkValidDate = function(field) {
    var dateValue = vm.params[field];
    if (field == "end_date" && _.isEmpty(dateValue)) return;
    if (dateValue && !moment(dateValue, FULL_DATE_FORMAT, true).isValid() ||
      dateValue < moment.parseZone(new Date("2019/06/01")).format(ORDER_DATEPICKER_FORMAT ||
      !CHECK_DATE_REGEX.test(dateValue))) {
      vm.params[field] = moment.parseZone(new Date("2019/06/01")).format(ORDER_DATEPICKER_FORMAT);
    }
  }

  function init() {
    vm.is_check_all_records = false;
    usageAchievementsService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
      };
      vm.search(false, true);
      homeService.getHomeLocations({location_id: vm.params.location_id, search_type: "arrange_billing"}, LOCALE).then(function(res) {
        if (res.data) {
          angular.extend(vm, res.data);
          if (_.isNull(vm.params.location_id) || _.isUndefined(vm.params.location_id)) {
            vm.location_name = I18n.t("corporation.home.index.all_sites");
          };
        } else {
          vm.regions = [];
        };
      });
    });
  };
};
