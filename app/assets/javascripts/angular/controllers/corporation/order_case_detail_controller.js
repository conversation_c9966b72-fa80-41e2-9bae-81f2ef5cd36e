"use strict";

angular.module("corporationApp")
  .controller("OrderCaseDetailController", OrderCaseDetailController);
OrderCaseDetailController.$inject = ["$location", "orderCaseListService", "toaster", "$window"];

function OrderCaseDetailController($location, orderCaseListService, toaster, $window) {
  var vm = this;
  var EVALUATION_TYPES = ["evaluation1", "evaluation2", "evaluation3"];
  var REST_TIMES = [1, 2, 3];
  var MAX_REST_TIMES = 3;
  var MIN_REST_TIMES = 1;
  var TYPES = ["staff", "owner"];
  var STAFF_CONFIRMING = "staff_confirming";
  vm.workAchievement = {};
  vm.toasterTimeout = 6200;

  vm.initOrderCase = function(orderCase) {
    vm.staffNumber = "1";
    vm.orderCase = orderCase;
    vm.orderCaseId = orderCase.id;
  }

  vm.initPriceOrderPortion = function(orderCasePrice) {
    vm.orderCasePrice = orderCasePrice;
    vm.summaryPrice = vm.addedPortionSummaryPrice = vm.orderCasePrice.summary || 0;
  }

  vm.initRequestSpecial = function() {
    vm.specialOfferFeeOptions = SPECIAL_OFFER_FEE;
    vm.resetSpecialOffer();
  }

  vm.calculateSummaryPrice = function() {
    orderCaseListService.calculateSummaryPrice({id: vm.orderCaseId, total_portion: vm.staffNumber}).then(function(res) {
      vm.addedPortionSummaryPrice = vm.summaryPrice + res.data.new_summary;
    });
  }

  vm.initStaffEvaluation = function(staffEvaluations) {
    vm.staffEvaluations = staffEvaluations;
    vm.evaluation1 = [{selected: true, value: 1}, {selected: false, value: 2}, {selected: false, value: 3},
      {selected: false, value: 4}, {selected: false, value: 5}];
    vm.evaluation2 = angular.copy(vm.evaluation1);
    vm.evaluation3 = angular.copy(vm.evaluation1);
    vm.evaluationParams = {};
  }

  vm.setCurrentEvaluation = function(arrangementId) {
    vm.currentEvaluation = _.find(vm.staffEvaluations, function(evaluation) {return evaluation.arrangement_id === arrangementId});
    if (vm.currentEvaluation) {
      _.forEach(EVALUATION_TYPES, function(type) {
        _.forEach(vm[type], function(currentStar) {
          currentStar.selected = currentStar.value <= vm.currentEvaluation[type];
        });
        vm.evaluationParams[type + "_comment"] = vm.currentEvaluation[type + "_comment"];
      });
    }
  }

  vm.changeNumberStaff = function() {
    $("#spinner").removeClass("ng-hide");
    orderCaseListService.changeOrderPortionNumber({id: vm.orderCaseId, total_portion: vm.staffNumber}).then(function(res) {
      $window.location.href = location.href;
    });
  }

  vm.showConfirmRemovePortion = function(portionId) {
    vm.removePortionId = portionId;
    var totalPortion = vm.orderCase.total_portion - 1;
    orderCaseListService.calculateSummaryPrice({id: vm.orderCaseId, total_portion: totalPortion}).then(function(res) {
      vm.removedSummaryPrice = res.data.new_summary;
    });
  }

  vm.deleteOrderPortion = function() {
    orderCaseListService.deleteOrderPortion(vm.removePortionId, {id: vm.orderCaseId}).then(function(res) {
      location.reload();
    });
  }

  vm.resetStaffNumber = function() {
    vm.staffNumber = "1";
    vm.addedPortionSummaryPrice = vm.summaryPrice;
  }

  vm.updateSpecialOfferFee = function() {
    var params = angular.element(".form-owner-update-special-fee").serializeJSON();
    orderCaseListService.updateSpecialOfferFee(vm.orderCaseId, params).then(function(res) {
      location.reload();
    });
  }

  vm.resetSpecialOffer = function() {
    vm.specialFeeParams = {
      special_offer_fee: vm.orderCase.special_offer_fee || SPECIAL_OFFER_FEE[0].toString(),
      special_offer_note: vm.orderCase.special_offer_note
    }
  }

  vm.changeActivedStar = function(evaluationType, star) {
    if (vm.currentEvaluation) return;
    _.forEach(vm[evaluationType], function(currentStar) {
      currentStar.selected = currentStar.value <= star.value;
    });
  }

  vm.staffReviewEvaluation = function() {
    $(".disable-submit-btn").prop("disabled", true);
    _.forEach(EVALUATION_TYPES, function(type) {
      vm.evaluationParams[type] = calMaxEvaluation(type).value;
    });
    var submitParams = {work_achievement_id: vm.currentAchievementId, staff_evaluation: vm.evaluationParams};
    orderCaseListService.reviewStaffEvaluation(submitParams).then(function(res) {
      $.lawsonAjax(res.data);
      if (res.data.status) {
        location.reload();
      }
      $(".disable-submit-btn").prop("disabled", false);
    });
  }

  vm.initWorkingTimes = function(orderBranch) {
    vm.orderBranch = orderBranch;
    vm.orderBranchUpdate = {};
    vm.orderBranchUpdate.is_time_changable = orderBranch.is_time_changable;
    vm.switchChangeable();
  }

  vm.switchChangeable = function() {
    if (!!vm.orderBranchUpdate.is_time_changable) {
      if (vm.orderBranch.required_start_time && vm.orderBranch.required_end_time) {
        vm.orderBranchUpdate.required_start_time = formatDate(vm.orderBranch.required_start_time, TIME_PICKER_FORMAT);
        vm.orderBranchUpdate.required_end_time = formatDate(vm.orderBranch.required_end_time, TIME_PICKER_FORMAT);
      }
      if (!vm.orderBranchUpdate.required_start_time && !vm.orderBranchUpdate.required_end_time) {
        vm.orderBranchUpdate.required_start_time = formatDate(vm.orderBranch.working_start_time, TIME_PICKER_FORMAT);
        vm.orderBranchUpdate.required_end_time = formatDate(vm.orderBranch.working_end_time, TIME_PICKER_FORMAT);
      }
    } else {
      vm.orderBranchUpdate.required_start_time = "";
      vm.orderBranchUpdate.required_end_time = "";
    }
  }

  vm.setOrderBranchWorkingTime = function() {
    var workingTime = {};
    var restTimes = [];
    workingTime["owner_working_started_at"] = formatTime(vm.orderBranch.working_start_time);
    workingTime["owner_working_ended_at"] = formatTime(vm.orderBranch.working_end_time);
    _.forEach(REST_TIMES, function(rest_time) {
      var startTime = vm.orderBranch["rest" + rest_time + "_started_at"];
      var endTime = vm.orderBranch["rest" + rest_time + "_ended_at"];
      if (_.isEmpty(startTime)) return;
      workingTime["owner_rest" + rest_time + "_started_at"] = formatTime(startTime);
      workingTime["owner_rest" + rest_time + "_ended_at"] = formatTime(endTime);
      restTimes.push(rest_time);
    });
    workingTime["rest_times"] = restTimes;
    return workingTime;
  }

  vm.initWorkAchievement = function(workAchievement) {
    if (workAchievement.working_time_status_id === "not_inputted") {
      vm.workAchievement = _.merge(vm.setOrderBranchWorkingTime(), {id: workAchievement.id, staff_name: workAchievement.staff_account_name});
    } else {
      vm.workAchievement = _.merge(vm.setWorkAchievementTime(workAchievement), {staff_name: workAchievement.staff_account_name});
    }
  }

  vm.setWorkAchievementTime = function(workAchievement) {
    var workingTime = {};
    var restTimes = [];
    var type = workAchievement.working_time_status_id === STAFF_CONFIRMING ? TYPES[1] : TYPES[0];
    workingTime["owner_working_started_at"] = formatTime(workAchievement[type + "_working_started_at"]);
    workingTime["owner_working_ended_at"] = formatTime(workAchievement[type + "_working_ended_at"]);
    _.forEach(REST_TIMES, function(rest_time) {
      var startTime = workAchievement[type + "_rest" + rest_time + "_started_at"];
      var endTime = workAchievement[type + "_rest" + rest_time + "_ended_at"];
      if (_.isEmpty(startTime)) return;
      workingTime["owner_rest" + rest_time + "_started_at"] = formatTime(startTime);
      workingTime["owner_rest" + rest_time + "_ended_at"] = formatTime(endTime);
      restTimes.push(rest_time);
    });
    workingTime["rest_times"] = restTimes;
    workingTime["id"] = workAchievement.id;
    workingTime["owner_comment"] = workAchievement[type + "_comment"];
    return workingTime;
  }

  vm.openInputWorkingTimeModal = function(workAchievement) {
    vm.initWorkAchievement(workAchievement);
    vm.workAchievement.has_break = true;
    vm.currentAchievementId = workAchievement.id;
    $(".has-error").removeClass("has-error");
    $(".help-block").remove();
    $("#modal-time-status").modal("show");
  }

  vm.addRestTime = function(type, order) {
    var restTimeCount = vm.workAchievement.rest_times.length;
    if (restTimeCount === MAX_REST_TIMES) return;
    vm.workAchievement.rest_times.push(restTimeCount + 1);
  }

  vm.removeRestTime = function(restTime) {
    if (vm.workAchievement.rest_times.length === MIN_REST_TIMES) return;
    if (restTime === 2) {
      vm.workAchievement.owner_rest2_started_at = vm.workAchievement.owner_rest3_started_at;
      vm.workAchievement.owner_rest2_ended_at = vm.workAchievement.owner_rest3_ended_at;
    }
    vm.workAchievement.owner_rest3_started_at = vm.workAchievement.owner_rest3_ended_at = "";
    vm.workAchievement.rest_times.pop();
  }

  vm.countRestTime = function(rest_time) {
    var startTime = vm.workAchievement["owner_rest" + rest_time + "_started_at"];
    var endTime = vm.workAchievement["owner_rest" + rest_time + "_ended_at"];
    if (_.isEmpty(startTime) || _.isEmpty(endTime)) return;
    var start = moment(startTime, TIME_PICKER_FORMAT);
    var end = moment(endTime, TIME_PICKER_FORMAT);
    if (end.isBefore(start)) end.add(1, "day");
    return I18n.t("corporation.order.step2.total_minutes", {minutes: end.diff(start, "minutes")});
  }

  vm.submitWorkAchievement = function(checkBreakTime) {
    $(".disable-submit-btn").prop("disabled", true);
    var workAchievementParams = $("form#owner-input-working-time").serializeJSON();
    if (!workAchievementParams.work_achievements.has_break) {
      _.forEach(REST_TIMES, function(rest_time) {
        workAchievementParams.work_achievements["owner_rest" + rest_time + "_started_at"] = "";
        workAchievementParams.work_achievements["owner_rest" + rest_time + "_ended_at"] = "";
      });
    }
    _.merge(workAchievementParams, {check_break_time: checkBreakTime});
    orderCaseListService.updateWorkAchievement(workAchievementParams, vm.workAchievement.id).then(function(res) {
      if (res.data.status) {
        if (res.data.break_time_warning) {
          $("#modal-confirm-break-time-warning").modal("show");
          $(".disable-submit-btn").prop("disabled", false);
        } else {
          if (_.isUndefined(vm.currentEvaluation)) {
            $("#modal-time-status").modal("hide");
            $("#modal-review").modal("show");
            $(".disable-submit-btn").prop("disabled", false);
          } else {
            location.reload();
          }
        }
      } else {
        $.lawsonAjax(res.data);
        if (res.data.locked_message) {
          toaster.pop("error", "", res.data.locked_message);
        }
        $(".disable-submit-btn").prop("disabled", false);
      }
    });
  }

  function calMaxEvaluation(evaluationType) {
    return _.maxBy(vm[evaluationType], function(star) {if (star.selected) return star.value;});
  }

  vm.approveWorkAchievement = function(workAchievement) {
    $(".disable-submit-btn").prop("disabled", true);
    orderCaseListService.updateWorkAchievement({approve: true}, workAchievement.id).then(function(res) {
      vm.setCurrentEvaluation(workAchievement.arrangement_id);
      if (_.isEmpty(vm.currentEvaluation)) {
        vm.initWorkAchievement(workAchievement);
        vm.currentAchievementId = workAchievement.id;
        $("#modal-review").modal("show");
        $(".disable-submit-btn").prop("disabled", false);
      } else {
        location.reload();
      }
    });
  }

  vm.reloadPage = function() {
    location.reload();
  }

  function formatTime(time) {
    if (_.isEmpty(time)) return;
    return moment.parseZone(time).format(TIME_PICKER_FORMAT);
  }

  function formatDate(date, formatType) {
    if (_.isEmpty(date)) return;
    return moment.parseZone(date).format(formatType);
  };

  vm.cancelOrderCase = function(order_case_id) {
    $("#modal-confirm").modal("hide");
    orderCaseListService.cancelOrderCaseDetail({id: order_case_id}).then(function(res) {
      location.replace(res.data.redirect_url)
    });
  };

  vm.openConfirmBreakTimeWarning = function(workAchievement) {
    vm.currentWorkAchievement = workAchievement;
    $("#modal-confirm-approve-break-time-warning").modal("show");
  }

  vm.showPopupUpdateChangeable = function(orderPortionId) {
    vm.selectorderPortionId = orderPortionId;
    vm.switchChangeable();
    $("#modal-update-changeable").modal("show");
  }

  vm.updateChangeable = function() {
    if (vm.orderBranchUpdate.is_time_changable == vm.orderBranch.is_time_changable &&
      vm.orderBranchUpdate.required_start_time == vm.orderBranch.required_start_time &&
      vm.orderBranchUpdate.required_end_time == vm.orderBranch.required_end_time) {
      vm.selectorderPortionId = null;
      $("#modal-update-changeable").modal("hide");
      return;
    }
    orderCaseListService.updateChangeable(vm.selectorderPortionId, {order_branch: vm.orderBranchUpdate}).then(function(res) {
      var message = res.data.messages[0]
      if (res.data.status) {
        if (res.data.order_case){
          vm.to_order_case = res.data.order_case;
          $("#modal-move-to-order-case").modal("show");
        }
      } else {
        toaster.pop("error", "", message);
      }
      $("#modal-update-changeable").modal("hide");
    });
  }

  vm.goToOrderCase =  function() {
    location.href = vm.to_order_case.redirect_url;
  }

  vm.closeOderCaseModal = function() {
    $("#modal-move-to-order-case").modal("hide");
    location.reload();
  }
}
