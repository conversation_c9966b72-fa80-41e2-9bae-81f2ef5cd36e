"use strict"

angular.module("corporationApp").controller("OrderTemplateController", OrderTemplateController);

OrderTemplateController.$inject = ["orderTemplateService", "$location", "$window"];

function OrderTemplateController(orderTemplateService, $location, $window) {
  var vm = this;
  var LOCALE = I18n.locale;
  var hostNamePrefix = $("#hostNamePrefix").attr("href");
  vm.params = $location.search();
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;

  vm.init = function(locations) {
    vm.locations = locations;
    vm.search(true);
  };

  vm.search = function(isReset) {
    if (isReset) {
      vm.params.page = 1;
      vm.params.per_page = vm.perPageSettings[0];
      vm.params.location_id = "";
      vm.params.name_keyword = "";
    }
    vm.params.per_page = _.toInteger(vm.params.per_page) || vm.perPageSettings[0];
    orderTemplateService.getOrderTemplates(vm.params, LOCALE).then(function(res) {
      if (res.data) {
        angular.extend(vm, res.data);
      } else {
        vm.order_templates = [];
        vm.total_items = 0;
      };
    });
    $location.search(vm.params).replace();
  };

  vm.changePerPage = function() {
    vm.search();
    scrollToResult();
  };

  vm.openDeleteModal = function(templateID) {
    vm.selectedID = templateID;
    $(".confirm-delete-order-template").modal("show");
  };

  vm.goToEditTemplate = function(templateID) {
    if (hostNamePrefix) {
      $window.location.href = hostNamePrefix + "/" + LOCALE + "/order_templates/" + templateID + "/edit";
    } else {
      $window.location.href = "/" + LOCALE + "/order_templates/" + templateID + "/edit";
    }
  };

  vm.copyTemplate = function(templateID) {
    if (hostNamePrefix) {
      $window.location.href = hostNamePrefix + "/" + LOCALE + "/order_templates/new?copy_id=" + templateID;
    } else {
      $window.location.href = "/" + LOCALE + "/order_templates/new?copy_id=" + templateID;
    }
  }

  vm.deleteOrderTemplate = function() {
    orderTemplateService.deleteOrderTemplate(vm.selectedID, LOCALE).then(function(res) {
      if (res.data.status) {
        toastr["success"](_.values(res.data.message));
        vm.search();
      } else {
        toastr["error"](_.values(res.data.message));
      }
      $(".confirm-delete-order-template").modal("hide");
    });
  };

  function scrollToResult() {
    $("html, body").animate({
      scrollTop: $(".work-history__result").offset().top
    }, 1000);
  };
}