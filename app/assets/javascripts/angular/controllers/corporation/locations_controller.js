"use strict";

angular.module("corporationApp").controller("LocationsController", LocationsController);
LocationsController.$inject = ["$location", "locationService", "toaster"];

function LocationsController($location, locationService, toaster) {
  var vm = this;
  vm.location = angular.element(".location-detail-info").data("location");
  vm.locationPics = angular.element(".location-detail-info").data("pics");
  vm.hasError = false;
  vm.toasterTimeout = 6200;
  vm.originalShortName = vm.location.short_name;
  var originalLocationPics = {};
  vm.originalStoreParkingAreaUsable = vm.location.is_store_parking_area_usable;
  vm.parkingUsable = [
    {name: I18n.t("corporation.location.yes_option"), value: "1"},
    {name: I18n.t("corporation.location.no_option"), value: "0"}
  ];
  var PIC_TYPES = ["haken_destination", "claim", "mandator"];
  var PIC_TYPE_IDS = [{type: "haken_destination", type_id: 1}, {type: "claim", type_id: 2}, {type: "mandator", type_id: 3}];
  initParkingArea();
  initLocationPic();

  function initParkingArea() {
    if (vm.location.is_store_parking_area_usable) {
      vm.reuseCarPark = I18n.t("corporation.location.yes_option");
      vm.location.is_store_parking_area_usable = "1";
    } else {
      vm.reuseCarPark = I18n.t("corporation.location.no_option");
      vm.location.is_store_parking_area_usable = "0";
    }
  }

  vm.editLocation = function(type) {
    var params = {
      field: type,
      value: vm.location[type]
    }
    locationService.editLocation(vm.location.id, params).then(function(res) {
      var data = res.data;
      if (data.status) {
        window.location.href = location.href;
      } else {
        var shortName = I18n.t("activerecord.attributes.location.short_name_owner");
        vm.hasError = true;
        var errorType = data.errors.short_name[0].error;
        var maxLength = data.errors.short_name[0].count;
        vm.errorMessage = I18n.t("activerecord.errors.messages." + errorType, {attribute: shortName, count: maxLength});
      }
    });
  }

  function initLocationPic() {
    _.forEach(PIC_TYPES, function(type) {
      vm[type] = vm.locationPics[type];
      if (_.isNil(vm[type])) {
        vm[type + "_pic"] = {id: "", position: "", tel: ""};
      } else {
        vm[type + "_pic"] = {
          id: vm[type][0].id,
          originalName: vm[type][0].name,
          position: vm[type][0].position,
          originalPosition: vm[type][0].position,
          tel: vm[type][0].tel,
          originalTel: vm[type][0].tel
        }
      }
      originalLocationPics[type + "_pic"] = angular.copy(vm[type + "_pic"]);
    });
  }

  vm.changeNameOption = function(pic_id, type) {
    var newPic = _.find(vm[type], function(pic) {return pic.id == pic_id});
    vm[type + "_pic"].id = newPic.id;
    vm[type + "_pic"].position = newPic.position;
    vm[type + "_pic"].tel = newPic.tel;
  }

  vm.saveLocationPic = function() {
    var $form = angular.element(".form-edit-pics");
    var locationParams = $form.serializeJSON();
    locationService.updateLocationPic(locationParams).then(function(res) {
      $.lawsonAjaxLocationPic(res.data);
      if (res.data.status) {
        window.location.href = location.href;
      }
    });
  }

  function changeLocationPicToEdit(picData, picOptionData) {
    var hakenPic = _.find(picData, function(pic) {return pic.pic_type_id === 1;});
    vm.haken_destination_pic.id = hakenPic.id;
    vm.haken_destination = picOptionData.haken_destination;
    var claimPic = _.find(picData, function(pic) {return pic.pic_type_id === 2;});
    vm.claim_pic.id = claimPic.id;
    vm.claim = picOptionData.claim;
    var mandatorPic = _.find(picData, function(pic) {return pic.pic_type_id === 3;});
    vm.mandator_pic.id = mandatorPic.id;
    vm.mandator = picOptionData.mandator;
    var validPicData = {haken_destination: hakenPic, claim: claimPic, mandator: mandatorPic};
    fillLocationPicData(validPicData);
  }

  function fillLocationPicData(validPicData) {
    _.forEach(PIC_TYPES, function(type) {
      originalLocationPics[type + "_pic"] = {
        id: validPicData[type].id,
        name: validPicData[type].name,
        position: validPicData[type].position,
        tel: validPicData[type].tel
      }
    })
  }

  vm.resetShortName = function() {
    vm.location.short_name = vm.originalShortName;
    vm.hasError = false;
  }

  vm.resetReuseCarParkHaken = function() {
    $(".edit-parking-usable-modal").modal("hide");
    vm.location.is_store_parking_area_usable = vm.originalStoreParkingAreaUsable ? "1" : "0";
  }

  vm.resetLocationPic = function() {
    _.forEach(PIC_TYPES, function(type) {
      vm[type + "_pic"].id = originalLocationPics[type + "_pic"].id;
      vm[type + "_pic"].name = originalLocationPics[type + "_pic"].name;
      vm[type + "_pic"].position = originalLocationPics[type + "_pic"].position;
      vm[type + "_pic"].tel = originalLocationPics[type + "_pic"].tel;
    });
    $(".has-error").removeClass("has-error");
    $(".help-block").remove();
  }
}
