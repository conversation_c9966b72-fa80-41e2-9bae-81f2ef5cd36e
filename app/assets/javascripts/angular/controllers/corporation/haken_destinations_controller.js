"use strict";

angular.module("corporationApp")
  .controller("HakenDestinationsController", HakenDestinationsController);

HakenDestinationsController.$inject = ["hakenDestinationsService", "checkValidDateFunction", "$location"];

function HakenDestinationsController(hakenDestinationsService, checkValidDateFunction, $location) {
  var vm = this;
  var CHECK_ALL_TXT = angular.element("#check-all-location-txt").text().trim();
  var LOCALE = I18n.locale;
  var SEARCH_CONDITION_TYPE = "user_haken_destination_search_conditions";
  vm.params = $location.search();
  vm.perPageSettings = ANGULAR_ITEMS_PER_PAGE;
  var COMMA = I18n.t("common.comma");

  $(".work-history").on("input", ".datetimepicker-input", function() {
    var modelName = $(this).attr("ng-model").split(".")[2];
    vm.params[modelName] = $(this).val();
  });

  vm.search = function(isSave, isReset) {
    formatParamsBeforeSend(isReset);
    vm.params.per_page = _.toInteger(vm.params.per_page) || vm.perPageSettings[0];
    hakenDestinationsService.searchArrangement({search: vm.params}).then(function(res) {
      if (res.data) {
        angular.extend(vm, res.data);
        _.forEach(vm.arrangements, function(arrangement) {
          arrangement.select = false;
        });
        vm.isSelected = false;
        vm.selectAll = false;
      } else {
        vm.arrangements = [];
        vm.total_items = 0;
      };
    });

    $location.search(vm.params).replace();
    if (isSave) {
      hakenDestinationsService.createSearchCondition({search: vm.params, search_condition: SEARCH_CONDITION_TYPE});
      scrollToResult();
    };
  };

  function formatParamsBeforeSend(resetPage) {
    if (resetPage) {
      vm.params.page = 1;
    }
    vm.params.location_id = vm.locations.filter(function(location) {
      return !!location.selected;
    }).map(function(location) {
      return location.id
    }).join(",");
  }

  vm.changePerPage = function() {
    vm.search(true);
    scrollToResult();
  };

  function scrollToResult() {
    $("html, body").animate({
      scrollTop: $(".work-history__result").offset().top
    }, 1000);
  };

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.desc = !vm.params.desc;
    } else {
      vm.params.desc = true;
      vm.params.order_key = column;
    }
    vm.search();
  };

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !!vm.params.desc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !vm.params.desc
    }
  };

  vm.checkAll = function() {
    _.forEach(vm.arrangements, function(arrangement) {
      arrangement.select = vm.selectAll;
    });
    vm.checkSelected(true);
  };

  vm.checkSelected = function(isChecked) {
    var selectedItems = _.find(vm.arrangements, {select: true});
    vm.isSelected = selectedItems ? true : false;
    if (vm.selectAll && !isChecked) {
      vm.selectAll = false;
    };
  };

  function checkChangeLocationInput() {
    var selectedLocations = vm.locations.filter(function(location) {
      return !!location.selected;
    });
    if (vm.chkAllLocationState && selectedLocations.length == vm.locations.length) {
      vm.locationInp = CHECK_ALL_TXT;
    } else {
      vm.locationInp = selectedLocations.map(function(location) {
        return location.name;
      }).join(COMMA);
    }
    $("#location-input").val(vm.locationInp).trigger("input");
  }

  function initLocationInpStates(paramStatus) {
    if (paramStatus) {
      var locationParams = !_.isNil(vm.params.location_id) ? vm.params.location_id.split(",") : [];
      var numSelectedLocation = 0;
      vm.locations.forEach(function(location) {
        location.selected = _.includes(locationParams, location.id.toString());
        if (location.selected) {numSelectedLocation += 1;}
      });
      if (_.isEqual(numSelectedLocation, vm.locations.length)) {vm.chkAllLocationState = true;}
    } else {
      vm.locations.forEach(function(location) {
        location.selected = true;
      });
      vm.chkAllLocationState = true;
    }
    checkChangeLocationInput();
  }

  vm.toggleAllLocationChkboxes = function() {
    vm.chkAllLocationState = !vm.chkAllLocationState;
    var chkBoxState = !!vm.chkAllLocationState;
    vm.locations.forEach(function(location) {
      location.selected = chkBoxState;
    });
    checkChangeLocationInput();
  };

  vm.toggleLocationChkbox = function(location) {
    location.selected = !location.selected;
    checkChangeLocationInput();
    checkStateForChkboxAll(location);
  };

  function checkStateForChkboxAll(location) {
    if(vm.chkAllLocationState && !location.selected)
      vm.chkAllLocationState = !vm.chkAllLocationState;
  }

  vm.init = function() {
    vm.locations = angular.element(".location-list").data("locations");
    vm.staffs = angular.element("#staff-list").data("staffs");
    hakenDestinationsService.getLastSearchConditions({search_condition: SEARCH_CONDITION_TYPE}).then(function(res) {
      if (res.data.status) {
        vm.params = JSON.parse(res.data.last_conditions);
      } else {
        vm.params.per_page = vm.perPageSettings[0];
        vm.total_items = 0;
        vm.params.desc = true;
        vm.params.order_key = "working_date";
      }
      initLocationInpStates(res.data.status);
      vm.search(false, true);
    });
  };

  vm.checkValidDate = function(field) {
    vm.params[field] = checkValidDateFunction.checkValidDate(vm.params[field]) || vm.params[field];
  }
};
