"use strict";

angular.module("corporationApp")
  .controller("FormSurveyLocationController", FormSurveyLocationController);

FormSurveyLocationController.$inject = ["$scope", "locationService", "formLocationSurveyConstants", "$window"]

function FormSurveyLocationController($scope, locationService, formLocationSurveyConstants, $window){
  var vm = this;
  vm.params = {};
  vm.buttonsDisabled = true;
  var DEFAULT_ANSWER_FOR_TEXT_BOX = formLocationSurveyConstants.DEFAULT_ANSWER_FOR_TEXT_BOX;
  var POS_SELECT_BOX_SYSTEM_HAS = formLocationSurveyConstants.POS_SELECT_BOX_SYSTEM_HAS;
  var TOTAL_QUESTIONS_OF_SELECT_BOX = formLocationSurveyConstants.TOTAL_QUESTIONS_OF_SELECT_BOX;
  var SYSTEM_HAS = formLocationSurveyConstants.SYSTEM_HAS;
  var NO_SYSTEM = formLocationSurveyConstants.NO_SYSTEM;
  var PLACEHOLDER_TEXTBOX = formLocationSurveyConstants.PLACEHOLDER_TEXTBOX;
  var DEFAULT_ANSWERS_VALIDATE = formLocationSurveyConstants.DEFAULT_ANSWERS_VALIDATE;
  var DEFAULT_ANSWERS_FOR_SELECT_BOX = getDeafultValueForSelectBox();
  var DEFAULT_REQUIRED_FOR_TEXT_BOX = getInitValueRequiredTextBox();
  var WATCH_GROUP = DEFAULT_ANSWERS_FOR_SELECT_BOX.map(function(val){
    return "vm.params.group_select_box_".concat(val.order);
  })

  vm.params.isRecordInvalid = false;

  vm.init = function(){
    setDefautAnswerForTextBox();
    setButtonDisabledSurveyForm();
    setSalaryByPrefecture();
    setDeafultValueForSelectBox();
    setDefaultRequiredForTextBox();
    setDefautAnswerVaildateForTextBox();
  }

  function setDefautAnswerForTextBox(){
    DEFAULT_ANSWER_FOR_TEXT_BOX.forEach(function(answer){
      var element = angular.element(document.querySelector(".form-survey-answer-".concat(answer.order)));
      element.val(answer.value);
    });
  }

  function setSalaryByPrefecture(){
    locationService.loadSalaryByPrefecture($scope.location_id).then(function(res){
      var element = angular.element(document.querySelector(".form-survey-answer-14"));
      element.val(res.data.salary_range);
    })
  }

  function setDeafultValueForSelectBox(){
    DEFAULT_ANSWERS_FOR_SELECT_BOX.forEach(function(answer){
      vm.params["group_select_box_".concat(answer.order)] = answer.value;
      var param = vm.params["group_select_box_".concat(answer.order)];
      var element = $(".form-survey-location__table .group_select_box_".concat(answer.order));
      if(param == NO_SYSTEM){
        element.prop("disabled", true);
      }
    })
  }

  function setDefaultRequiredForTextBox(){
    DEFAULT_REQUIRED_FOR_TEXT_BOX.forEach(function(textBox){
      vm.params["required_answer_".concat(textBox.order)] = textBox.required;
    })
  }

  function setDefautAnswerVaildateForTextBox(){
    DEFAULT_ANSWERS_VALIDATE.forEach(function(answer){
      vm.params["answer_".concat(answer.order)] = answer.value;
    })
  }

  function getDeafultValueForSelectBox(){
    var temp = [];
    for(var i = 1; i <= TOTAL_QUESTIONS_OF_SELECT_BOX; i++){
      var object;
      if(POS_SELECT_BOX_SYSTEM_HAS.indexOf(i) > -1){
        object = { value: SYSTEM_HAS, order: i }
      }
      else {
        object = { value: NO_SYSTEM, order: i }
      }
      temp.push(object);
    }
    return temp;
  }

  function getInitValueRequiredTextBox(){
    var startPos = 24;
    var temp = [];
    for(var i = 1; i <= TOTAL_QUESTIONS_OF_SELECT_BOX; i++){
      var object1 = { required: false, order: startPos, group: i }
      var object2 = { required: false, order: startPos + 1, group: i }
      var object3 = { required: false, order: startPos + 2, group: i }
      temp.push(object1, object2, object3);
      startPos = startPos + 4;
    }
    return temp;
  }

  function setButtonDisabledSurveyForm(){
    $(".btn-submit-survey-form").prop("disabled", true);
  }

  vm.acceptTermService = function(){
    vm.buttonsDisabled = !vm.buttonsDisabled
    if(vm.buttonsDisabled){
      $(".btn-submit-survey-form").prop("disabled", true);
      return
    }
    $(".btn-submit-survey-form").prop("disabled", false);
  }

  $scope.$watchGroup(WATCH_GROUP, function(newValue, oldValue){
    for(var i = 0; i <= newValue.length; i++){
      if(newValue[i] !== oldValue[i]){
        var pos = i + 1;
        var elements = $(".form-survey-location__table .group_select_box_".concat(pos));
        if(vm.params["group_select_box_".concat(pos)] == NO_SYSTEM){
          elements.prop("disabled", true);
          setValidateWhenSystemHas(pos, false);
          clearPlaceHolderForTextBoxsOfSelectBox(elements);
        } else {
          elements.prop("disabled", false);
          setValidateWhenSystemHas(pos, true);
          setPlaceHolderForTextBoxsOfSelectBox(elements);
        }
      }
    }
  })
  
  function setPlaceHolderForTextBoxsOfSelectBox(elements){
    PLACEHOLDER_TEXTBOX.forEach(function(text, index){
      elements[index].placeholder = text;
    })
  }

  function clearPlaceHolderForTextBoxsOfSelectBox(elements){
    PLACEHOLDER_TEXTBOX.forEach(function(_, index){
      elements[index].placeholder = "";
    })
  }

  function setValidateWhenSystemHas(pos, value){
    DEFAULT_REQUIRED_FOR_TEXT_BOX.forEach(function(textBox){
      if(pos == textBox.group){
        if(!value){
          vm.params["answer_".concat(textBox.order)] = "";
        }
        vm.params["required_answer_".concat(textBox.order)] = value;
      }
    })
  }

  var getFormData = function(form) {
    var $inputs = $("input[type='file']:not([disabled])", form);
    $inputs.each(function(_, input) {
      if (input.files.length > 0) return;
      $(input).prop("disabled", true);
    });
    var formData = new FormData(form);
    $inputs.prop("disabled", false);
    return formData;
  };

  $(".location-survey-form").on("submit", function(e){
    e.preventDefault();
    var actionURL = $(this).attr("action");
    $.ajax({
      url: actionURL,
      method: "POST",
      dataType: "json",
      data: getFormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        if(response.status){
          $window.location.href = response.redirect_path
        } else {
          $(".btn-submit-survey-form").prop("disabled", false);
          $(".btn-submit-survey-form").removeClass("disabled");
          $(".location-survey-notice-error").removeClass("hide-error");
          window.scroll(0,0);
        }
      },
      error: function(){
        location.reload();
      }
    });
  })
}