"use strict";

angular.module("corporationApp")
  .controller("HomesController", HomesController);

HomesController.$inject = ["orderCaseListService", "homeService", "locationService", "$location", "toaster", "$window", "$rootScope"];

function HomesController(orderCaseListService, homeService, locationService, $location, toaster, $window, $rootScope) {
  var vm = this;
  var LOCALE = I18n.locale;
  var MAX_SELECTED_LOCATIONS = 14;
  var statuses = I18n.t("corporation.order_case.order_case_list_page.status_by_portion");
  var ARRANGED_STATUS_CLASS = "arranged";
  var NOT_ARRANGED_STATUS_CLASS = "inprogress";
  var CONFIRMED_STATUS_CLASS = "confirmed";
  var SCROLL_WHEN_ARRANGEMENT_COUNTER = 3;
  var POINT_CLASS = [{status: "arranged", class: "in-progress"}, {status: "recruiting", class: "done"}]
  var hostNamePrefix = $("#hostNamePrefix").attr("href");
  vm.modalHomePage = true;
  vm.validCreateOrderType = angular.element("#valid-create-order-type").data("is-valid");
  vm.isCheckTermService = angular.element("#is-check-term-service").data("is-checked");
  vm.isStoreCom = angular.element("#is-store-computer").data("is-storecom");
  vm.location_name = I18n.t("corporation.home.index.all_sites");
  vm.params = $location.search();
  vm.toasterTimeout = 6200;
  vm.currentPage = 1;
  vm.perPage = 3;
  vm.ownerNotifications = [];
  vm.hasOwnerNotification = false;
  vm.totalCalendarOrderCases = 0;
  vm.totalOwnerNotification = 0;
  vm.adminLinkUrl = {messages: "", "attachment_url": "", title: ""};
  vm.downloadUrl = "#";
  var $notificationInfoModal = $("#modal-notification-info");
  var $notificationAttachmentShow = $("#show-notification-attachment");
  var $body = $("body");
  var $notificationPopup = $("#notification-popup");

  getTotalOwnerNotification();

  vm.init = function() {
    vm.cancel_target_id = null;
    delete vm.params.search_by_choose_date;
    vm.perPageSettings = angular.element(".per-page-settings").data("settings");
    vm.params.per_page = vm.perPageSettings[0];
    vm.params.page = 1;
    vm.params.start_date = moment().format(ORDER_DATEPICKER_FORMAT);
    vm.params.asc = vm.params.asc == "false" ? false : true;
    vm.loadMoreNotification();

    homeService.getHomeLocations({location_id: vm.params.location_id}, LOCALE).then(function(res) {
      if (res.data) {
        angular.extend(vm, res.data);
        if (_.isUndefined(vm.params.location_id)) {
          vm.location_name = I18n.t("corporation.home.index.all_sites");
        };
      } else {
        vm.regions = [];
      };
    });

    vm.getCalendarOrderCases(vm.params.start_date, true);
    updateCalendar();
    backTosearchOrderCases();
    var prefectureAllowed = $('#prefecture_allowed').val() === 'false';
    if (vm.validCreateOrderType == "invalid_step_1" && !prefectureAllowed) {
      $("#modal-store-invalid-step1").modal("show");
    } else if (!vm.isStoreCom && !vm.isCheckTermService) {
      $("#modal-is-check-term-service").modal("show");
    } else {
      vm.loadLocationsNotSurvey();
    }
  };

  vm.agreeTermService = function() {
    homeService.agreeTermService().then(function(res) {
      if (res.data.status) {
        $("#modal-is-check-term-service").modal("hide");
        var prefectureAllowed = $('#prefecture_allowed').val() === 'false';
        if (vm.validCreateOrderType == "invalid_step_1" && !prefectureAllowed) {
          $("#modal-store-invalid-step1").modal("show");
        } else {
          vm.loadLocationsNotSurvey();
        }
      } else {
        location.reload(true);
      }
    });
  };

  vm.closeStoreInvalidStep1 = function() {
    $("#modal-store-invalid-step1").modal("hide");
    vm.loadLocationsNotSurvey();
  };

  vm.showModalSurveyLocation = function() {
    $("#work-modal-survey-location").modal("show");
  }

  vm.loadLocationsNotSurvey = function() {
    if ($('#prefecture_allowed').val() === 'false') { return; }
    locationService.loadLocationNotSurvey().then(function(res) {
      if(res.data.status) {
        vm.showModalSurveyLocation();
      }
      vm.locations_not_survey = res.data.locations_not_survey;
    })
  }

  vm.refresh = function(isSave) {
    $body.removeClass("modal-open");
    homeService.getHomeOrderCases({search: vm.params}, LOCALE).then(function(res) {
      if (res.data) {
        angular.extend(vm, res.data);
      } else {
        vm.order_cases = [];
        vm.total_items = 0;
        vm.total_arranged = 0;
      };
    });
    $location.search(vm.params).replace();
    if (isSave) {
      var params = {search: {}, search_type: "home_order_cases", location_id: vm.params.location_id};
      orderCaseListService.createSearchCondition(params, LOCALE).then(function(res) {
      });
    };
  };

  vm.classForSortColumn = function(column) {
    return {
      "fa-sort": !_.isEqual(vm.params.order_key, column),
      "fa-sort-up": _.isEqual(vm.params.order_key, column) && !!vm.params.asc,
      "fa-sort-down": _.isEqual(vm.params.order_key, column) && !vm.params.asc
    };
  };

  vm.sort = function(column) {
    if (_.isEqual(vm.params.order_key, column)) {
      vm.params.asc = !vm.params.asc;
    } else {
      vm.params.asc = true;
      vm.params.order_key = column;
    }
    vm.refresh();
  };

  vm.getClassStatus = function(status) {
    var classStatus = "project-status--inprogress";
    if (_.includes([statuses.cancel, statuses.finish_recruiting], status)) {
      classStatus = "color--gray project-status--finished"
    } else if (status == statuses.arranged) {
      classStatus = "color--green project-status--confirmed"
    };
    return classStatus;
  };

  vm.getTotalConfirmed = function() {
   var order_case_confirmed = _.filter(vm.order_cases, function(orderCase) {
      return orderCase.status_id_i18n === statuses.arranged;
    });
   return order_case_confirmed.length;
  };

  vm.searchByLocation = function(isAll, location, inList) {
    vm.params.page = 1;
    if (isAll) {
      vm.location_name = I18n.t("corporation.home.index.all_sites");
      vm.params.location_id = null;
    } else {
      vm.location_name = location.name;
      vm.params.location_id = _.toString(location.id);
      var selectedLocation = _.find(vm.last_selected_locations, function(selectedLocation) {
        return selectedLocation.id == location.id;
      });
    };
    if (isAll || !inList || selectedLocation) {
      vm.refresh();
      var startDate = $("#dateinline1").datetimepicker("viewDate").format(ORDER_DATEPICKER_FORMAT)
      vm.getCalendarOrderCases(startDate, true);
    } else {
      if (vm.last_selected_locations.length === MAX_SELECTED_LOCATIONS) {
        vm.last_selected_locations.shift();
      };
      vm.last_selected_locations.push(location);
      vm.refresh(true);
      var startDate = $("#dateinline1").datetimepicker("viewDate").format(ORDER_DATEPICKER_FORMAT)
      vm.getCalendarOrderCases(startDate, true);
    };
  };

  vm.cancelOrderCaseModal = function(orderCaseId) {
    vm.cancel_target_id = orderCaseId;
    $("#modal-confirm").modal("show");
  };

  vm.linkToDetail = function(orderCaseId) {
     if (hostNamePrefix) {
      $window.location.href = hostNamePrefix + "/" + LOCALE + "/order_cases/" + orderCaseId;
     } else {
      $window.location.href = "/" + LOCALE + "/order_cases/" + orderCaseId;
     }
  };

  vm.cancelOrderCase = function(order_case_id) {
    if (_.isNull(order_case_id)) {
      return;
    };
    orderCaseListService.cancelOrderCase({id: order_case_id}, LOCALE).then(function(res) {
      vm.cancel_target_id = null;
      var responseData = res.data;
      if (responseData.valid_order_case) {
        if (responseData.status) {
          var orderCase = _.find(vm.order_cases, function(orderCase) {
            return orderCase.id == responseData.order_case.id;
          });
          orderCase.valid_cancel = responseData.order_case.valid_cancel;
          orderCase.status_id_i18n = responseData.order_case.status_id_i18n;
          toaster.pop("success", "", responseData.message);
        } else {
          toaster.pop("error", "", responseData.message);
        }
        $("#modal-confirm").modal("hide");
      } else {
        location.reload(true);
        toaster.pop("error", "", responseData.message);
      }
    });
  };

  vm.backToList = function(controllerPathName, queryString) {
    if (hostNamePrefix) {
      location.href = hostNamePrefix + "/" + I18n.locale + "/" + controllerPathName + "?" + queryString;
    } else {
      location.href = "/" + I18n.locale + "/" + controllerPathName + "?" + queryString;
    };
  }

  vm.updateNotificationChecked = function(id) {
    homeService.updateNotificationChecked({id: id}).then(function(res) {
      $("#notification-item-" + id).remove();
      _.remove(vm.ownerNotifications, function(e){return e.id == id})
      homeService.getOwnerNotifications(vm.currentPage - 1, vm.perPage).then(function(res) {
        vm.ownerNotifications = _.uniqBy(vm.ownerNotifications.concat(res.data.owner_notifications), "id");
        vm.totalOwnerNotification = res.data.total_owner_notification;
        vm.hasOwnerNotification = vm.ownerNotifications.length < vm.totalOwnerNotification;
        angular.element("#total-notification-header").html(vm.totalOwnerNotification);
        $rootScope.$emit("total_notice", vm.totalOwnerNotification);
      })
    })
  }

  vm.loadMoreNotification = function() {
    homeService.getOwnerNotifications(vm.currentPage, vm.perPage).then(function(res) {
      vm.ownerNotifications = _.uniqBy(vm.ownerNotifications.concat(res.data.owner_notifications), "id");
      vm.totalOwnerNotification = res.data.total_owner_notification;
      vm.hasOwnerNotification = vm.ownerNotifications.length < vm.totalOwnerNotification;
      if (res.data.is_show_popup && vm.currentPage == 1) {
        $notificationPopup.show();
        $body.addClass("modal-open");
      }
      vm.currentPage += 1;
    })
  }

  vm.goToNotificationHome = function() {
    if (location.pathname.endsWith("/" + I18n.locale + "/homes")) {
      vm.scrollToNotfications();
    } else {
      var url = "/" + I18n.locale + "/homes#notification-list";
      if (hostNamePrefix) {
        url = hostNamePrefix + url;
      };
      location.href = url;
    };
  }

  vm.scrollToNotfications = function() {
    $("html, body").animate({
      scrollTop: $(".notification__list").offset().top
    }, 1000);
  };

  vm.showAdminNotification = function(linkContent, notificationId) {
    vm.adminLinkUrl = linkContent;
    vm.downloadUrl = hostNamePrefix + "/" +  I18n.locale + "/homes/download_notification_attachment?notification_id=" + notificationId;
    $notificationInfoModal.show();
    $body.addClass("modal-open");
  };

  vm.closeAdminNotification = function() {
    $notificationInfoModal.hide();
    $body.removeClass("modal-open");
    vm.adminLinkUrl = {messages: "", "attachment_url": "", title: ""};
    vm.downloadUrl = "#";
  };

  vm.closeNotificationPopup = function() {
    $notificationPopup.hide();
    $body.removeClass("modal-open");
    homeService.turnOffNotificationPopup();
  };

  function getTotalOwnerNotification() {
    $rootScope.$on("total_notice", function(event, total) {
      vm.totalOwnerNotification = total;
    });
  }

  function updateCalendar() {
    $("#dateinline1").on("change.datetimepicker", function(e) {
      moment.locale(I18n.locale);
      vm.dateChoosed = e.date.format(DATE_TIME_FORMAT_WITH_DAY_NAME);
      vm.getCalendarOrderCases(e.date.format(ORDER_DATEPICKER_FORMAT));
    })

    $(".filter-calendar__toggle-btn").on("click", function(e) {
      var startDate = moment().format(ORDER_DATEPICKER_FORMAT);
      moment.locale(I18n.locale);
      vm.dateChoosed = moment().format(DATE_TIME_FORMAT_WITH_DAY_NAME);
      vm.getCalendarOrderCases(startDate, true);
    })

    $(".js-datepickerinline-owner").on("update_month.datetimepicker", function(e) {
      vm.addPointToDay();
    });
  }

  function backTosearchOrderCases() {
    $(".filter-calendar__toggle-btn").on("click", function() {
      if ($(".filter-calendar__toggle-btn").hasClass("switch-view")) {
        delete vm.params.search_by_choose_date;
        vm.params.start_date = moment().format(ORDER_DATEPICKER_FORMAT);
        vm.refresh();
      }
    })
  }

  vm.addClassToArrangementCounter = function(arrangementsCount) {
    return arrangementsCount > SCROLL_WHEN_ARRANGEMENT_COUNTER ? "many-arrangements" : "hide-scroll";
  };

  vm.addClassToArrangementStatus = function(arrangementStatus) {
    return "index-staff--" + vm.arrangementStatus(arrangementStatus);
  };

  vm.addClassToArrangementStaffName = function(arrangementStatus) {
    return "staff--" + vm.arrangementStatus(arrangementStatus);
  };

  vm.addClassToArrangementButton = function(arrangementStatus) {
    return "filter-plan__status--" + vm.arrangementStatus(arrangementStatus);
  };

  vm.arrangementStatus = function(arrangementStatus) {
    return arrangementStatus === ARRANGED_STATUS_CLASS ? CONFIRMED_STATUS_CLASS : NOT_ARRANGED_STATUS_CLASS;
  };

  vm.addPointToDay = function() {
    vm.params.from_start_date = moment.parseZone($(".datepicker-days").find("tbody").find("td").first().data("day")).format(FULL_DATE_FORMAT);
    vm.params.to_start_date = moment.parseZone($(".datepicker-days").find("tbody").find("td").last().data("day")).format(FULL_DATE_FORMAT);
    vm.params.search_by_choose_date = true;
    homeService.getOrderCaseStatus({search: vm.params}).then(function(res) {
      vm.dateHighlight = res.data.date_highlight;
      vm.duplicateDate = _.filter(vm.dateHighlight, function(val, i, iteratee){
        return _.includes(_.map(iteratee, function(e){return e.date}), val.date, i + 1);
      });
      _.map(vm.dateHighlight, function(e) {
        if (_.includes(vm.duplicateDate, e) || e.partly_arranged) {
          return e.html_element = "<div class='planpiker'><span class='plan-status in-progress'>\
            </span> <span class='plan-status done'></span></div>"
        } else {
          return e.html_element = "<div class='planpiker'><span class='plan-status " +
            _.filter(POINT_CLASS, function(p){return p.status == e.status})[0].class + "'></span></div>";
        }
      });
      $(".js-datepickerinline-owner").datetimepicker("dateExtraElement", vm.dateHighlight);
    })
  }

  vm.getCalendarOrderCases = function(startDate, isAddPointToDay) {
    if (!$(".filter-calendar__toggle-btn").hasClass("switch-view")) {
      delete vm.params.start_date;
      vm.params.from_start_date = startDate;
      vm.params.to_start_date = startDate;
      vm.params.search_by_choose_date = true;
      homeService.getOrderCasesByChooseDate({search: vm.params}).then(function(res) {
        vm.calendarOrderCases = res.data.order_cases;
        vm.totalCalendarOrderCases = res.data.total_count;
        if (isAddPointToDay) {
          vm.addPointToDay();
        }
      });
    }
  }

  vm.changePerPage = function() {
    vm.refresh(true);
    vm.scrollToResult();
  };

  vm.formatMessages = function(messages) {
    var urlRegex =/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
    return messages.replace(urlRegex, function(url) {
      return '<a href="' + url + '" target="_blank">' + url + '</a>';
    });
  }

  vm.scrollToResult = function() {
    $("html, body").animate({
      scrollTop: $("#home-order-cases").offset().top
    }, 1000);
  };
}
