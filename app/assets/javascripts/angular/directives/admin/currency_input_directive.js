angular.module("adminApp").directive("currencyDirective", ["$filter", "$browser",
  function($filter, $browser) {
  return {
    require: "ngModel",
    link: function($scope, $element, $attrs, ngModelCtrl) {
      var listener = function() {
        var value = $element.val();
        if ($element.attr("negative") === "true") {
          value = formatValueNegative(value);
          if (_.isEqual(value, "-")) return;
        }
        else {
          value = value.replace(/[^0-9]/g, "");
        }
        $element.val($filter("number")(parseInt(value), false));
      };

      var formatValueNegative = function(value) {
        var isNegative = _.isEqual(value[0], "-");
        value = value.replace(/[^0-9]/g, "");
        if (isNegative) value = "-" + value;
        return value;
      }

      ngModelCtrl.$parsers.push(function(viewValue) {
        if ($element.attr("negative") === "true") {
          viewValue = formatValueNegative(viewValue);
          if (_.isEqual(viewValue, "-")) return 0;
        }
        else {
          viewValue = viewValue.replace(/[^0-9]/g, "");
        }
        return viewValue;
      });

      ngModelCtrl.$render = function() {
        $element.val($filter("number")(ngModelCtrl.$viewValue, false));
      };

      $element.bind("change", listener);
      $element.bind("keydown", function(event) {
        var key = event.keyCode;
        if (key == 91 || (15 < key && key < 19) || (37 <= key && key <= 40))
          return;
        $browser.defer(listener);
      });

      $element.bind("paste cut", function() {
        $browser.defer(listener)
      });
    }
  };
}]);
