angular.module("adminApp")
  .directive("select2OnNgRepeat", function() {
    return {
      restrict: "A",
      link: function () {
        $(".select2-custom").select2({width: "100%"});
      }
    }
  });
angular.module("adminApp")
  .directive('select2Search', function() {
    return {
      restrict: 'A',
      require: 'ngModel',
      scope: {
        ngModel: '='
      },
      link: function(scope, element, attr, ngModelCtrl) {
        element.on('change', function() {
          var value = $(this).val();
          scope.$apply(function(){
            ngModelCtrl.$modelValue = value;
            scope.ngModel = value;
          });
        });
      }
    }
  });
angular.module("adminApp")
  .directive("select2SearchLimit", function() {
    return {
      restrict: "A",
      require: "ngModel",
      scope: {
        ngModel: "=",
        corporationId: "<"
      },
      link: function (scope, element, attr, ngModelCtrl) {
        element.on("change", function() {
          var value = element.val();
          scope.$digest(function() {
            ngModelCtrl.$modelValue = value;
            scope.ngModel = value;
          });
          ngModelCtrl.$setViewValue(value);
        });

        setTimeout(function() {
          setSelect2();
        }, 200);

        function setSelect2() {
          element.select2({
            width: "100%",
            ajax: {
              url: attr.url,
              dataType: "json",
              delay: 1000,
              data: function(params) {
                return {
                  search_content: params.term,
                  selected: element.val(),
                  class_name: attr.className,
                  foreigener: attr.foreigener,
                  training_staff: attr.trainingStaff,
                  corporation_id: scope.corporationId,
                  with_staff_number: attr.withStaffNumber,
                  billing_type: attr.billingType
                }
              },
              processResults: function(data) {
                return {results: data.results};
              }
            },
            templateResult: formatResult,
            templateSelection: formatSelection
          });
        }

        function formatResult(obj) {
          return obj.account_name || obj.name || obj.full_name || obj.account_name_with_staff_number || obj.name_and_code;
        }

        function formatSelection(obj) {
          return obj.text || obj.account_name || obj.name || obj.full_name || obj.account_name_with_staff_number || obj.name_and_code;
        }
      }
    }
  });

angular.module("adminApp")
  .directive("select2CreatePayrollModal", function() {
    return {
      restrict: "A",
      require: "ngModel",
      scope: {
        ngModel: "=",
        corporationId: "<"
      },
      link: function (scope, element, attr, ngModelCtrl) {
        element.on("change", function() {
          var value = element.val();
          scope.$digest(function() {
            ngModelCtrl.$modelValue = value;
            scope.ngModel = value;
          });
          ngModelCtrl.$setViewValue(value);
        });

        setTimeout(function() {
          setSelect2();
        }, 200);

        function setSelect2() {
          element.select2({
            dropdownParent: $("#create-payroll-modal"),
            width: "100%",
            ajax: {
              url: attr.url,
              dataType: "json",
              delay: 1000,
              data: function(params) {
                return {
                  search_content: params.term,
                  selected: element.val(),
                  class_name: attr.className,
                  with_staff_number: attr.withStaffNumber
                }
              },
              processResults: function(data) {
                return {results: data.results};
              }
            },
            templateResult: formatResult,
            templateSelection: formatSelection
          });
        }

        function formatResult(obj) {
          return obj.account_name || obj.name || obj.full_name || obj.account_name_with_staff_number || obj.name_and_code;
        }

        function formatSelection(obj) {
          return obj.text || obj.account_name || obj.name || obj.full_name || obj.account_name_with_staff_number || obj.name_and_code;
        }
      }
    }
  });

angular.module("adminApp")
  .directive("select2BillingUnitPrice", function() {
    return {
      restrict: "A",
      require: "ngModel",
      scope: {
        ngModel: "=",
        corporationId: "<"
      },
      link: function (scope, element, attr, ngModelCtrl) {
        element.on("change", function() {
          var value = element.val();
          scope.$digest(function() {
            ngModelCtrl.$modelValue = value;
            scope.ngModel = value;
          });
          ngModelCtrl.$setViewValue(value);
        });

        setTimeout(function() {
          $(element).closest(".row-data").find(".select-prefecture").each(function(index, element) {
            setSelect2(element);
          });
        }, 200);

        function setSelect2(element) {
          if ($(element).closest(".row-data").find(".select-prefecture").length > 1) {
            var optionText = I18n.t("admin.billing_unit_price.select.all_other");
          } else {
            var optionText = I18n.t("admin.billing_unit_price.select.all");
          }
          $(element).select2({
            width: "100%",
            ajax: {
              url: attr.url,
              dataType: "json",
              data: function(params) {
                return {
                  search_content: params.term,
                  selected: $(element).val(),
                  class_name: attr.className,
                  is_all: optionText,
                  chain_id: attr.chainId
                }
              },
              processResults: function(data) {
                var options = "";
                $.each(data.results, function(index, result) {
                  options += "<option value='" + result.id + "'>" + result.full_name.stripTags() + "</option>"
                });
                $(element).append(options).trigger("change");
                return {results: data.results};
              }
            },
            templateResult: formatResult,
            templateSelection: formatSelection
          });
        }

        function formatResult(obj) {
          return obj.account_name || obj.name || obj.full_name;
        }

        function formatSelection(obj) {
          return obj.text || obj.account_name || obj.name || obj.full_name;
        }
      }
    }
  });
