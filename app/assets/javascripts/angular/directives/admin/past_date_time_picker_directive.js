angular.module("adminApp")
  .directive("pastDatetimepicker", function() {
    return {
      restrict: "A",
      link: function () {
        angular.element(document).ready(function() {
          $(".datepicker-input").datetimepicker({
            locale: "ja",
            format: ORDER_DATEPICKER_FORMAT,
            dayViewHeaderFormat: DATE_PICKER_FORMAT,
            maxDate: moment().endOf("day"),
            keepInvalid: true
          });

          $(".timepicker-input").datetimepicker({
            locale: "ja",
            format: TIME_PICKER_FORMAT,
            useCurrent: "roundedMin",
            roundedMinTo: 10
          });

          $(".zero-timepicker").datetimepicker({
            locale: "ja",
            format: TIME_PICKER_FORMAT,
            useCurrent: "roundedMin",
            roundedMinTo: "zero"
          });

          $(".box").boxWidget();
        });
      }
    }
  });
