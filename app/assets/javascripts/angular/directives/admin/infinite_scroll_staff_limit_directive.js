angular.module('adminApp')
  .directive('infiniteScrollStaffLimit', infiniteScrollStaffLimit);
function infiniteScrollStaffLimit() {
  return {
    scope: true,
    link: function (scope, element, attrs) {
      var e = element[0];

      $(".result-area").bind('scroll', function() {
        if ((e.scrollTop + e.offsetHeight) >= e.scrollHeight) {
          scope.$apply(attrs.infiniteScrollStaffLimit);
        }
      })
    }
  };
};
