angular.module("adminApp")
  .directive("onEnter", function() {
    return function($scope, element, attrs) {
      var enterKeyCode = 13;

      element.bind("keypress", function(evt) {
        if (evt.which === enterKeyCode && !evt.shiftKey) {
          $scope.$apply(attrs.onEnter);
        }
      });
    }
  })
  .directive("onEsc", function() {
    return function($scope, element, attrs) {
      var escKeyCode = 27;

      element.bind("keydown", function(evt) {
        if (evt.which === escKeyCode) {
          $scope.$apply(attrs.onEsc);
        }
      });
    }
  })
  .directive("arrangementInlineEdit", ["$timeout", "toaster", function($timeout, toaster) {
    var inputTextTemplate = '<input type="text" class="form-control"\
      ng-model="model" ng-show="isEditMode" on-enter="save()" on-esc="cancelEditMode()" />\
    ';
    var selectTemplate = '\
      <div class="row" ng-show="isEditMode">\
        <div class="col-md-10">\
          <select class="form-control" ng-model="model" ng-change="save()">\
            <option ng-repeat="element in selectOptions"\
              ng-value="element.key"\
              ng-selected="element.key == model">{{element.i18n_label}}</option>\
          </select>\
        </div>\
        <div class="col-md-2 no-padding">\
          <a href="javascript:;" ng-click="cancelEditMode()" ng-show="isEditMode"><i class="fa fa-times"></i></a>\
        </div>\
      </div>\
    ';
    var textareaTemplate = '\
      <textarea class="form-control fix-resize-horizontal" ng-model="model"\
        ng-show="isEditMode" on-enter="save()" on-esc="cancelEditMode()" />\
    ';
    var timePickerTemplate = '\
      <input type="text" class="form-control ng-inline-datetimpicker timepicker-input"\
        ng-model="model" on-enter="save()" on-esc="cancelEditMode()" ng-show="isEditMode"\
        data-toggle="datetimepicker" ng-blur="datetimePickerBlurEvent($event)"\
        ng-click="initTime(\'model\')"/>\
    ';
    var datePickerTemplate = '\
      <input type="text" class="form-control ng-inline-datetimpicker datepicker-input"\
        ng-model="model" on-enter="save()" on-esc="cancelEditMode()" ng-show="isEditMode"\
        data-toggle="datetimepicker" ng-blur="datetimePickerBlurEvent($event)" />\
    ';
    var dualTimePickerTemplate = '\
      <div class="row" ng-show="isEditMode">\
        <div class="col-md-5">\
          <input type="text" class="form-control ng-inline-datetimpicker timepicker-input"\
            ng-model="dualStartTime" data-toggle="datetimepicker" ng-blur="datetimePickerBlurEvent($event)"\
            ng-click="initTime(\'dualStartTime\')"/>\
        </div>\
        <div class="col-md-5">\
          <input type="text" class="form-control ng-inline-datetimpicker timepicker-input"\
            ng-model="dualEndTime" data-toggle="datetimepicker" ng-blur="datetimePickerBlurEvent($event)"\
            ng-click="initTime(\'dualEndTime\')"/>\
        </div>\
        <div class="col-md-2 no-padding">\
          <a href="javascript:;" ng-click="save()" ng-show="isEditMode"><i class="fa fa-check"></i></a>\
          <a href="javascript:;" ng-click="cancelEditMode()" ng-show="isEditMode"><i class="fa fa-times"></i></a>\
        </div>\
        <div class="clearfix"></div>\
      </div>\
    ';
    var inputNumberTemplate = '\
      <input type="number" class="form-control" min="{{ numberMin }}" max="{{ numberMax }}" step="{{ numberStep }}"\
        ng-model="model" ng-show="isEditMode" on-enter="save()" on-esc="cancelEditMode()" />\
    ';

    var getDirectiveTemplete = function(editType) {
      var template = "";

      switch(editType) {
        case "text":
          template = inputTextTemplate;
          break;
        case "select":
          template = selectTemplate;
          break;
        case "textarea":
          template = textareaTemplate;
          break;
        case "timepicker":
          template = timePickerTemplate;
          break;
        case "datepicker":
          template = datePickerTemplate;
          break;
        case "dualtimepicker":
          template = dualTimePickerTemplate;
          break;
        case "number":
          template = inputNumberTemplate;
          break;
      }

      return template;
    }

    return {
      restrict: "A",
      scope: {
        model: "=arrangementInlineEdit",
        selectOptions: "=",
        onSaveHandler: "&onSave",
        onCancelHandler: "&onCancel",
        dateTimeFormat: "@",
        dualStartTime: "=?",
        dualEndTime: "=?",
        startTimeOrigin: "=?",
        endTimeOrigin: "=?",
        dualTimeSeparator: "@",
        numberMin: "@",
        numberMax: "@",
        numberStep: "@",
        saveIntegerData: "@",
        hideCondition: "=?",
        canUpdate: "=?"
      },
      transclude: true,
      link: function($scope, element, attrs) {
        var previousValue;
        $scope.numberMin = $scope.numberMin || attrs.min || 0;
        $scope.numberMax = $scope.numberMax || MAX_INTEGER;
        $scope.numberStep = $scope.numberStep || 1;
        moment.locale(I18n.locale);

        $scope.switchToEditMode = function() {
          $scope.isEditMode = true;
          previousValue = $scope.model;

          $timeout(function() {
            var inputElement = element.find("input, select, textarea");

            if (inputElement.length) {
              inputElement[0].focus();
            }
          }, 0, false);
        };

        $scope.cancelEditMode = function() {
          $scope.isEditMode = false;
          $scope.model = previousValue;
          if (attrs.editType === "dualtimepicker") {
            var tempValue = previousValue;
            if (!tempValue) {
              $scope.dualStartTime = $scope.dualEndTime = previousValue;
            } else {
              if (tempValue.includes(I18n.t("staff.order_cases.next_day"))) {
                tempValue = tempValue.replace(I18n.t("staff.order_cases.next_day"), "");
              }
              var previousTime = tempValue.split("~");
              $scope.dualStartTime = previousTime[0];
              $scope.dualEndTime = previousTime[1];
            }
          }
          $scope.onCancelHandler({value: $scope.model});
        };

        $scope.save = function() {
          $scope.isEditMode = false;
          var nextDay = "";
          var scopeModel = $scope.model;
          if ($scope.checkInvalidTime("timepicker", $scope.model) || $scope.checkInvalidTime("dualtimepicker", $scope.dualEndTime) ||
              $scope.checkInvalidTime("dualtimepicker", $scope.dualStartTime)) {
            $scope.setPreviousValue($scope);
            toaster.pop("error", "", I18n.t("admin.arrangements.index.action_status.update_fail"));
            return true;
          }
          if (attrs.editType === "dualtimepicker") {
            var newStartTime = $scope.formatDateTime($scope.startTimeOrigin, $scope.dualStartTime);
            var newEndTime = $scope.formatDateTime($scope.startTimeOrigin, $scope.dualEndTime);

            if (moment(newEndTime).isBefore(newStartTime)) {
              nextDay = I18n.t("staff.order_cases.next_day");
            };
            $scope.model = $scope.dualStartTime + $scope.dualTimeSeparator + nextDay + $scope.dualEndTime;
            scopeModel = $scope.formatDateTime($scope.startTimeOrigin, $scope.model);
            if (!$scope.dualStartTime && !$scope.dualEndTime) {
              $scope.model = "";
              scopeModel = $scope.dualTimeSeparator;
              $scope.dualStartTime = $scope.dualEndTime = "";
            }
            $scope.startTimeOrigin = $scope.formatDateTime($scope.startTimeOrigin, $scope.dualStartTime);
            $scope.endTimeOrigin = $scope.formatDateTime($scope.endTimeOrigin, $scope.dualEndTime);
          }

          if (attrs.editType === "timepicker" && $scope.saveIntegerData && !$scope.model) {
            $scope.model = moment().startOf("day").format(TIME_PICKER_FORMAT);
          }

          $scope.onSaveHandler({value: scopeModel});
          $scope.$parent.$watch("vm.currentErrorArrangementId", function(previousValue, arrangementId, oldStatus) {
            if (arrangementId != 0 && this.$parent.arrangement.id == arrangementId) {
              $scope.setPreviousValue(this);
            }
          }.bind($scope, previousValue));
          $scope.$parent.vm.currentErrorArrangementId = 0;
        };

        $scope.datetimePickerBlurEvent = function(evt) {
          $(evt.target).datetimepicker("hide");
        };

        $scope.initTime = function(time) {
          if (!$scope[time]) {
            $scope[time] = moment().format(TIME_PICKER_FORMAT);
          }
        };

        $scope.formatDateTime = function(dateTimeOrigin, newTime) {
          return moment.parseZone(dateTimeOrigin).format(ORDER_DATEPICKER_FORMAT) + " " + newTime;
        };

        $scope.setPreviousValue = function(scope) {
          if (attrs.editType === "dualtimepicker") {
            var tempValue = previousValue;
            if (!tempValue) {
              scope.dualStartTime = scope.dualEndTime = "";
            } else {
              if (tempValue.includes(I18n.t("staff.order_cases.next_day"))) {
                tempValue = tempValue.replace(I18n.t("staff.order_cases.next_day"), "");
              }
              var previousTime = tempValue.split("~");
              scope.dualStartTime = previousTime[0];
              scope.dualEndTime = previousTime[1];
            }
            scope.startTimeOrigin = $scope.formatDateTime(scope.startTimeOrigin, scope.dualStartTime);
            scope.endTimeOrigin = $scope.formatDateTime(scope.endTimeOrigin, scope.dualEndTime);
          }
          this.model = previousValue;
        };

        $scope.checkInvalidTime = function(editType, model) {
          return attrs.editType == editType && !moment(model, TIME_PICKER_FORMAT, true).isValid() && model;
        };

        if (attrs.editType === "timepicker" || attrs.editType === "datepicker" || attrs.editType === "dualtimepicker") {
          var dateFormatStr = $scope.dateTimeFormat || ORDER_DATEPICKER_FORMAT;
          var timeFormatStr = $scope.dateTimeFormat || TIME_PICKER_FORMAT;
          if (attrs.editType === "dualtimepicker" && !moment($scope.startTimeOrigin, ORDER_DATEPICKER_FORMAT, true).isValid()) {
            $scope.dualStartTime = moment.parseZone($scope.startTimeOrigin).format(TIME_PICKER_FORMAT);
            $scope.dualEndTime = moment.parseZone($scope.endTimeOrigin).format(TIME_PICKER_FORMAT);
          }

          angular.element(document).ready(function() {
            element.find(".ng-inline-datetimpicker").each(function() {
              var randomDateTimePickerID = new Date().getTime() + "_" + Math.random().toString(36).substr(2);
              $(this).attr({
                "id": randomDateTimePickerID,
                "data-target": "#" + randomDateTimePickerID
              });
            });

            element.find(".ng-inline-datetimpicker.timepicker-input").datetimepicker({
              locale: "ja",
              format: TIME_PICKER_FORMAT,
              widgetParent: ".table-responsive"
            });

            element.find(".ng-inline-datetimpicker").on("show.datetimepicker", function(e) {
              var currentElementOffset = $(e.target).offset();
              $(".bootstrap-datetimepicker-widget").css({
                top: currentElementOffset.top - 130,
                left: currentElementOffset.left - 245
              });
            });

            element.find(".ng-inline-datetimpicker.datepicker-input").datetimepicker({
              locale: "ja",
              format: dateFormatStr,
              dayViewHeaderFormat: DATE_PICKER_FORMAT,
              keepInvalid: true,
              widgetParent: ".table-responsive"
            });
          })
        }
      },
      template: function(element, attrs) {
        var editType = attrs.editType || "text";

        if (attrs.canUpdate == "false") return '<ng-transclude ng-hide="isEditMode"></ng-transclude>';

        var templateHTML = '<ng-transclude ng-hide="isEditMode"></ng-transclude>\
          ' + getDirectiveTemplete(editType) + '\
          <a href="javascript:;" ng-click="switchToEditMode()" ng-hide="isEditMode || hideCondition" class="add-margin">\
            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>\
          </a>\
        ';

        return templateHTML;
      }
    }
  }]);
