angular.module("adminApp")
  .directive("datetimepicker", function() {
    return {
      restrict: "A",
      link: function () {
        angular.element(document).ready(function() {
          $(".datepicker-input").datetimepicker({
            locale: "ja",
            format: ORDER_DATEPICKER_FORMAT,
            dayViewHeaderFormat: DATE_PICKER_FORMAT,
            minDate: moment().startOf("day"),
            keepInvalid: true
          });

          $(".datepicker-input-multi-dates").datetimepicker({
            locale: "ja",
            format: ORDER_DATEPICKER_FORMAT,
            dayViewHeaderFormat: DATE_PICKER_FORMAT,
            minDate: moment().startOf("day"),
            keepInvalid: true,
            allowMultidate: true,
            multidateSeparator: ','
          })

          $(".timepicker-input").datetimepicker({
            locale: "ja",
            format: TIME_PICKER_FORMAT,
            useCurrent: "roundedMin",
            roundedMinTo: 10
          });

          $(".zero-timepicker").datetimepicker({
            locale: "ja",
            format: TIME_PICKER_FORMAT,
            useCurrent: "roundedMin",
            roundedMinTo: "zero"
          });

          $(".box").boxWidget();
        });
      }
    }
  });
