angular.module("adminApp")
  .directive("addSpinner", function() {
    return {
      restrict: "A",
      link: function (scope, element, attrs) {
        angular.element(attrs.otherSpinnerSelector).addClass("ng-hide");
        angular.element(attrs.spinnerSelector).removeClass("ng-hide");
      }
    }
  });
angular.module("adminApp")
  .directive("removeSpinnerOnEnd", function() {
    return {
      restrict: "A",
      link: function(scope, element, attrs) {
        if (scope.$last && scope.$parent.$last) {
          angular.element(attrs.spinnerSelector).addClass("ng-hide");
        }
      }
    }
  });
