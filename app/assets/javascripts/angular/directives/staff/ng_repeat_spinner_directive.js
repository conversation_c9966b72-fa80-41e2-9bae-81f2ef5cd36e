angular.module("staffApp")
  .directive("addSpinner", function() {
    return {
      restrict: "A",
      link: function (scope) {
        angular.element("#spinner").removeClass("ng-hide");
      }
    }
  });
angular.module("staffApp")
  .directive("removeSpinnerOnEnd", function() {
    return {
      restrict: "A",
      link: function(scope) {
        if (scope.$last && scope.$parent.$last) {
          angular.element("#spinner").addClass("ng-hide");
        }
      }
    }
  });
