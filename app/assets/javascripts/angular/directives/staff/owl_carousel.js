angular.module("staffApp")
  .directive("owlCarousel", function() {
    return {
      restrict: "E",
      transclude: false,
      link: function (scope) {
        scope.initCarousel = function(element) {
          var defaultOptions = {
            loop: false,
            margin: 30,
            slideBy: 2,
            dots: false,
            nav: true,
            navText: ["<i class='fa fa-angle-left'></i>", "<i class='fa fa-angle-right'></i>"],
            responsive:{
              0: {
                items: 2,
                margin: 10,
                dots: true,
                nav: false
              },
              768: {
                items: 3,
                dots: true,
                nav: false
              },
              992: {
                items: 3
              },
              1200: {
                items: 4
              }
            }
          }
          $(element).owlCarousel(defaultOptions).trigger("refresh.owl.carousel");
        };
      }
    };
})
.directive("owlCarouselItem", [function() {
  return {
    restrict: "A",
    transclude: false,
    link: function(scope, element) {
      if(scope.$last) {
        scope.initCarousel(element.parent());
      }
    }
  };
}]);
