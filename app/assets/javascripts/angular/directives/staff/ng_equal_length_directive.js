angular.module("staffApp")
  .directive("ngEqualLength", function() {
    return {
      restrict: "A",
      require: "ngModel",
      link: function (scope, element, attributes, control) {
        control.$validators.equallength = function (modelValue) {
          if(modelValue.length == attributes.ngEqualLength){
            return true;
          }
          return false;
        };
      }
    };
  });
