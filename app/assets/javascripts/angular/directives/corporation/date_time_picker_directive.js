angular.module('corporationApp')
  .directive('datetimepicker', ['$timeout', function($timeout){
    return {
      restrict: 'A',
      link: function ($scope, $element) {
        angular.element(document).ready(function() {
          $('.js-datepicker').datetimepicker({
            locale: I18n.locale,
            format: ORDER_DATEPICKER_FORMAT,
            dayViewHeaderFormat: DATE_PICKER_FORMAT,
            minDate: moment().startOf('day'),
            keepInvalid: true,
            ignoreReadonly: true
          });

          $('.js-timepicker').datetimepicker({
            locale: I18n.locale,
            format: TIME_PICKER_FORMAT,
            useCurrent: "roundedMin",
            roundedMinTo: 10,
            ignoreReadonly: true
          });

          $(".zero-timepicker").datetimepicker({
            locale: I18n.locale,
            format: TIME_PICKER_FORMAT,
            useCurrent: "roundedMin",
            roundedMinTo: "zero",
            ignoreReadonly: true
          })

          $element.closest('.datetimepicker')
            .find('.datetimepicker-input')
            .focusin(function () {
              $(this).parent().parent().find('.ic-datetime').addClass('active');
            }).focusout(function () {
              $(this).parent().parent().find('.ic-datetime').removeClass('active');
            });
        });
      }
    }
  }
]);
