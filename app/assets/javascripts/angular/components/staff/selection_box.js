"use strict";

angular.module("staffApp")
  .component("selectionBox", {
    templateUrl: "selection-box-template.html",
    controller: "SelectionBoxCtrl",
    bindings: {
      label: "@",
      requiredTxt: "@",
      requiredMsg: "@",
      hiddenFieldName: "@",
      items: "<",
      isDisabled: "<",
      clearIfId: "<",
      enableFilterBox: "<",
      enableBlankOption: "<",
      blankOption: "@",
      isRailway: "<",
      defaultSelected: "<",
      selectedItemId: "=",
      isBlur: "=?",
      isSearchCode: "<",
      labelOntop: "<"
    }
  })
  .controller("SelectionBoxCtrl", ["$scope", function ($scope) {
    var vm = this;
    vm.$scope = $scope;

    vm.init = function() {
      if (!!vm.defaultSelected && !vm.selectedItemId && vm.items.length > 0) {
        vm.selectedItemId = vm.items[0].id;
        vm.selectedItemTxt = vm.items[0].name ? vm.items[0].name : vm.items[0].full_name;
      }
    };

    vm.classForSelectbox = function() {
      return {
        "form-error": (vm.requiredTxt || vm.requiredMsg) && vm.isBlur && !vm.selectedItemId
      }
    };

    vm.$scope.$watchGroup(["$ctrl.selectedItemId", "$ctrl.items"], function(newVal, oldVal) {
      if (_.isNil(newVal[0])) {return;}
      if (vm.items.length < 1) {
        vm.selectedItemTxt = "";
        return;
      }
      if (_.isEqual(newVal[0], "") && !!vm.defaultSelected && vm.items.length > 0) {
        vm.selectedItemId = vm.items[0].id;
        vm.selectedItemTxt = vm.items[0].name ? vm.items[0].name : vm.items[0].full_name;
        return;
      }
      if (_.isEqual(newVal[0], "") || vm.clearIfId && vm.clearIfId == newVal[0]) {
        vm.selectedItemTxt = "";
        return;
      }
      var selectedItem = vm.items.filter(function(item) {
        return item.id == newVal[0];
      })[0];
      if(_.isUndefined(selectedItem)) return;
      vm.selectedItemTxt = selectedItem.name ? selectedItem.name : selectedItem.full_name;
    });

    vm.onClickSelectBox = function() {
      if (_.isEqual(vm.isBlur, true)) return;
      if (_.isUndefined(vm.checkOnClickBody)){
        vm.checkOnClickBody = false;
      }
      else {
        vm.isBlur = true;
      }
      document.body.onclick = setBlur;
    }

    function setBlur() {
      if (_.isEqual(vm.checkOnClickBody, false)) {
        vm.checkOnClickBody = true;
      } else {
        vm.$scope.$apply(function() {
          vm.isBlur = true;
        });
        document.body.onclick = null;
      }
    }
  }]);
