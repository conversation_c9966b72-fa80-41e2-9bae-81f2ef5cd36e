"use strict";

angular.module("staffApp")
  .component("errorMessages", {
    templateUrl: ["$element", "$attrs", function($element, $attrs) {
      if ($attrs.templateForStaffWorkExp) {
        return "error-messages-staff-work-exp-template.html";
      } else {
        return "error-messages-template.html";
      }
    }],
    controller: "ErrorMessageCtrl",
    bindings: {
      showCondition: "<",
      messages: "<",
      pattern: "<",
      chkboxRequiredMsg: "<",
      attribute: "@",
      withAttribute: "@",
      maxlength: "@",
      equalLength: "@",
      maxSize: "<",
      patternMessage: "@",
      customPositionRequired: "<",
      templateForStaffWorkExp: "<"
    }
  })
  .controller("ErrorMessageCtrl", ["$scope", function ($scope) {
    var vm = this;
    vm.i18n = I18n;
  }]);
