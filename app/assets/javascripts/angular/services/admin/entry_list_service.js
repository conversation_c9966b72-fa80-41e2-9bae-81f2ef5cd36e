"use strict";

angular.module("adminApp")
  .factory("entryListService", ["common", "searchConditionFunction", entryListService]);

function entryListService(common, searchConditionFunction) {
  var service = {
    getStaff: getStaff,
    deleteStaff: deleteStaff,
    changeStatus: changeStatus
  }

  angular.merge(service, searchConditionFunction);

  return service;

  function getStaff(params) {
    return common.ajaxCall("GET", "/entries", params);
  }

  function deleteStaff(params) {
    return common.ajaxCall("DELETE", "/delete_staff_list", params);
  }

  function changeStatus(params) {
    return common.ajaxCall("POST", "/update_status_list", params);
  }
}
