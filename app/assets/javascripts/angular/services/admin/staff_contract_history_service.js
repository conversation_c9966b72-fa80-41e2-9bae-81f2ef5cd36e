"use strict";

angular.module("adminApp")
  .factory("staffContractHistoryService", ["common", staffContractHistoryService]);

function staffContractHistoryService(common) {
  var service = {
    validateContracts: validateContracts,
    saveContracts: saveContracts,
    getStaffContractHistories: getStaffContractHistories
  }

  return service;

  function validateContracts(params) {
    return common.ajaxCall("POST", "/staff_contract_histories/validate_contracts", params);
  }

  function saveContracts(params) {
    return common.ajaxCall("POST", "/staff_contract_histories/save_contracts", params);
  }

  function getStaffContractHistories(params) {
    return common.ajaxCall("GET", "/staff_contract_histories", params);
  }
}
