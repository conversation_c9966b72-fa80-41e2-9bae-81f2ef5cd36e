"use strict";

angular.module("adminApp")
  .factory("corporationGroupService", ["common", corporationGroupService]);

function corporationGroupService(common) {
  var service = {
    getCorporationGroup: getCorporationGroup,
    searchCorporationGroup: searchCorporationGroup,
    getListCorporationGroupTag: getListCorporationGroupTag,
    deleteCorporationGroupTag: deleteCorporationGroupTag
  }

  return service

  function getCorporationGroup(params) {
    return common.ajaxCall("GET", "/corporation_groups", params);
  };

  function searchCorporationGroup(params) {
    return common.ajaxCall("GET", "/corporation_group_tags/search_corporation_groups", params);
  };

  function getListCorporationGroupTag(params) {
    return common.ajaxCall("GET", "/corporation_group_tags", params);
  };

  function deleteCorporationGroupTag(id) {
    return common.ajaxCall("DELETE", "/corporation_group_tags/" + id + "?no_flash=true");
  };
}
