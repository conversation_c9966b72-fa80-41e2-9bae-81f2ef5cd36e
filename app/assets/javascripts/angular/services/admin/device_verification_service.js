"use strict";

angular.module("adminApp")
  .factory("deviceVerificationService", ["common", "searchConditionFunction", deviceVerificationService]);

function deviceVerificationService(common, searchConditionFunction) {
  var service = {
    getDeviceList: getDeviceList,
    updateStatus: updateStatus
  }

  angular.merge(service, searchConditionFunction);

  return service;

  function getDeviceList(params) {
    return common.ajaxCall("GET", "/device_verifications", params);
  }

  function updateStatus(ID, params) {
    return common.ajaxCall("PUT", "/device_verifications/" + ID, params);
  }
}
