'use strict';

angular.module('adminApp').factory('AdminNotificationOwnerService', ['common', AdminNotificationOwnerService]);

function AdminNotificationOwnerService(common) {
  var service;
  service = {
    searchNotification: searchNotification,
    deleteNotification: deleteNotification
  };

  angular.merge(service);

  return service;

  function searchNotification(params) {
    return common.ajaxCall('GET', '/admin_notification_owners', params);
  }

  function deleteNotification(id) {
    return common.ajaxCall('DELETE', '/admin_notification_owners/' + id);
  }
}
