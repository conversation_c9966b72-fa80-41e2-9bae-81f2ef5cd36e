"use strict";

angular.module("adminApp")
  .factory("deviceLoginLogService", ["common", "searchConditionFunction", deviceLoginLogService]);

function deviceLoginLogService(common, searchConditionFunction) {
  var service = {
    getDeviceLoginLogList: getDeviceLoginLogList
  }

  angular.merge(service, searchConditionFunction);

  return service;

  function getDeviceLoginLogList(Id, params) {
    return common.ajaxCall("GET", "/device_verifications/" + Id + "/device_login_logs", params);
  }
}
