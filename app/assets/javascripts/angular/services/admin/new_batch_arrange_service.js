"use strict";

angular.module("adminApp").factory("newBatchArrangeService", ["common", newBatchArrangeService]);

function newBatchArrangeService(common) {
  var service = {
    getBatchArranges: getBatchArranges,
    getBaseInformations: getBaseInformations,
    searchStaffs: searchStaffs,
    createSearchCondition: createSearchCondition,
    cancelTemporaryArrange: cancelTemporaryArrange,
    saveTemporaryArrange: saveTemporaryArrange,
    saveArrange: saveArrange,
    sendBatchArrangeMailStatus: sendBatchArrangeMailStatus,
    getTemporaryArrangeWorkerData: getTemporaryArrangeWorkerData,
    alertMessageArranged: alertMessageArranged,
    getArrangeWorkerData: getArrangeWorkerData,
    deleteStaffApplyOrderCase: deleteStaffApplyOrderCase,
    updateBasicField: updateBasicField,
    batchUpdatePortionStatus: batchUpdatePortionStatus,
    batchAdminConfirmWorkAchievements: batchAdminConfirmWorkAchievements,
    updateWorkAchievementWorkingTimeStatusId: updateWorkAchievementWorkingTimeStatusId,
    showNotes: showNotes,
    saveTemporaryArrangeBySearch: saveTemporaryArrangeBySearch,
    sendOfferMailToStaffs: sendOfferMailToStaffs,
    sendBatchOfferMailToStaffs: sendBatchOfferMailToStaffs,
    sendOfferMailStatus: sendOfferMailStatus,
    updateOrderCaseOffer: updateOrderCaseOffer,
    selectStaffApplyOrderCase: selectStaffApplyOrderCase,
    getRegularOrderTime: getRegularOrderTime,
    getRegularOrderPrice: getRegularOrderPrice,
    checkInputOrderPrice: checkInputOrderPrice,
    getStaffProfile: getStaffProfile,
    getOrderCaseNotes: getOrderCaseNotes,
    saveOrderCaseNotes: saveOrderCaseNotes,
    getStaffNotes: getStaffNotes,
    checkMultipleDepartments: checkMultipleDepartments
  }

  return service

  function getBatchArranges(params) {
    return common.ajaxCall('GET', '/new_batch_arranges/', params);
  };

  function getBaseInformations(params) {
    return common.ajaxCall('GET', '/new_batch_arranges/base_informations', params);
  };

  function searchStaffs(params) {
  return common.ajaxCall('GET', '/new_batch_arranges/search_staffs', params);
  };

  function createSearchCondition(params) {
    return common.ajaxCall('POST', '/new_batch_arranges/save_search_conditions', params);
  };

  function saveTemporaryArrange(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/save_temporary_arrange", params);
  };

  function cancelTemporaryArrange(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/cancel_temporary_arrange", params);
  };

  function saveArrange(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/save_arrange", params);
  };

  function sendBatchArrangeMailStatus(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/send_batch_arrange_mail_status", params);
  };

  function getTemporaryArrangeWorkerData(params) {
    return common.ajaxCall("GET", "/new_batch_arranges/get_temporary_arrange_worker_data", params);
  };

  function alertMessageArranged(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/alert_message_arranged", params);
  };

  function getArrangeWorkerData(params) {
    return common.ajaxCall("GET", "/new_batch_arranges/get_arrange_worker_data", params);
  };

  function deleteStaffApplyOrderCase(params) {
    return common.ajaxCall("POST", "/staff_apply_order_cases/batch_destroy", params);
  };

  function updateBasicField(id, params) {
    return common.ajaxCall("PUT", "/arrangements/" + id + "/update_basic_field", params);
  };

  function batchUpdatePortionStatus(params) {
    return common.ajaxCall("PUT", "/order_portions/batch_update_status", params);
  };

  function batchAdminConfirmWorkAchievements(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/admin_confirm_work_achievements", params);
  };

  function updateWorkAchievementWorkingTimeStatusId(params, id) {
    return common.ajaxCall("PUT", "/arrangements/" + id + "/update_working_time_status_id", params);
  };

  function showNotes(params){
    return common.ajaxCall("GET", "/new_batch_arranges/show_notes", params);
  };

  function saveTemporaryArrangeBySearch(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/save_temporary_arrange_by_search", params);
  };

  function sendOfferMailToStaffs(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/send_offer_to_staffs", params);
  };

  function sendOfferMailStatus(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/send_offer_mail_status", params);
  };

  function sendBatchOfferMailToStaffs(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/send_batch_offer_to_staffs", params);
  };

  function updateOrderCaseOffer(params) {
    return common.ajaxCall("POST", "/staff_order_case_offers/update_offer", params);
  };

  function selectStaffApplyOrderCase(params) {
    return common.ajaxCall("GET", "/new_batch_arranges/select_staff_apply_order_case", params);
  };

  function getRegularOrderTime(params) {
    return common.ajaxCall("GET", "/new_batch_arranges/get_regular_order_time", params);
  };

  function getRegularOrderPrice(params) {
    return common.ajaxCall("GET", "/new_batch_arranges/get_regular_order_price", params)
  };

  function checkInputOrderPrice(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/check_input_order_price", params)
  };

  function getStaffProfile(staffID) {
    return common.ajaxCall("GET", "/staffs/" + staffID + "/profile");
  };

  function getOrderCaseNotes(params) {
    return common.ajaxCall("GET", "/new_batch_arranges/get_order_case_notes", params)
  };

  function saveOrderCaseNotes(params) {
    return common.ajaxCall("POST", "/new_batch_arranges/save_order_case_notes", params)
  };

  function getStaffNotes(staffID) {
    return common.ajaxCall("GET", "/staffs/" + staffID + "/notes");
  };

  function checkMultipleDepartments(params) {
    return common.ajaxCall("GET", "/arrangements/check_multiple_departments", params);
  }
}