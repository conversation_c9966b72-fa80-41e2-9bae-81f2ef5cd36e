'use strict';

angular.module('adminApp').factory('interviewService', ['common', interviewService]);

function interviewService(common) {
  var service = {
    getInterviews: getInterviews,
    create: create,
    destroy: destroy,
    update: update,
    show: show,
    updateAdmin: updateAdmin,
    checkDestroy: checkDestroy,
    cancelStaff: cancelStaff
  }

  return service

  function getInterviews(params) {
    return common.ajaxCall('GET', '/interviews', params);
  }

  function create(params) {
    return common.ajaxCall('POST', '/interviews', params);
  }

  function destroy(id, params) {
    return common.ajaxCall('DELETE', '/interviews/' + id, params);
  }

  function checkDestroy(id, params) {
    return common.ajaxCall('POST', '/interviews/' + id + "/check_destroy", params);
  }

  function update(id, params) {
    return common.ajaxCall('PUT', '/interviews/' + id, params);
  }

  function show(id) {
    return common.ajaxCall('GET', '/interviews/' + id);
  }

  function updateAdmin(id, interviews_room) {
    return common.ajaxCall('GET', '/interviews/' + id + "/interviews_rooms/" + interviews_room.id + "/update_admin");
  }

  function cancelStaff(params) {
    return common.ajaxCall('POST', '/interviews/cancel_staff', params);
  }
}
