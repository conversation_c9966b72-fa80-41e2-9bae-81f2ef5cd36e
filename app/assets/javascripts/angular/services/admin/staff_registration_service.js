"use strict";

angular.module("adminApp")
  .factory("staffRegistrationService", ["common", staffRegistrationService]);

function staffRegistrationService(common) {
  var service = {
    getBankBraches: getBankBraches,
    loadDataStaffVersion: loadDataStaffVersion,
    loadDataStaffLevel: loadDataStaffLevel,
    loadDataStaffRank: loadDataStaffRank,
    approveRequestChangeProfile: approveRequestChangeProfile,
    rejectRequestChangeProfile: rejectRequestChangeProfile,
    viewStaffInfos: viewStaffInfos
  }
  return service;

  function getBankBraches(params) {
    return common.ajaxCall("GET", "/bank_branches", params);
  }

  function loadDataStaffVersion(params) {
    return common.ajaxCall("GET", "/staffs/load_staff_version_history", params);
  }

  function loadDataStaffLevel(params) {
    return common.ajaxCall("GET", "/staff_levels", params);
  }

  function loadDataStaffRank(params) {
    return common.ajaxCall("GET", "/staff_ranks", params);
  }

  function approveRequestChangeProfile(params) {
    return common.ajaxCall("POST", "/staffs/approve_request_change_profile", params);
  }

  function rejectRequestChangeProfile(params) {
    return common.ajaxCall("POST", "/staffs/reject_request_change_profile", params);
  }

  function viewStaffInfos(params) {
    return common.ajaxCall("POST", "/staffs/view_staff_infos", params);
  }
}
