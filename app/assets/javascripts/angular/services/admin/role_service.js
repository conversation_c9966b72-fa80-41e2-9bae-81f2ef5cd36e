"use strict";

angular.module("adminApp")
  .factory("rolesService", ["common", rolesService]);

function rolesService(common) {
  var service = {
    updateRoles: updateRoles,
    loadRolesList: loadRolesList
  }

  return service;

  function updateRoles(params) {
    return common.ajaxCall("POST", "/roles/update_roles", params);
  }

  function loadRolesList() {
    return common.ajaxCall("GET", "/roles");
  }
}
