'use strict';

angular.module('adminApp').factory('adminService', ['common', "searchConditionFunction", adminService]);

function adminService(common, searchConditionFunction) {
  var service;
  service = {
    searchAdmin: seachAdmin
  };

  angular.merge(service, searchConditionFunction);

  return service;

  function seachAdmin(params) {
    return common.ajaxCall('GET', '/admins', params);
  }
}
