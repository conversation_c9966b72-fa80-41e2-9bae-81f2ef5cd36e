"use strict";

angular.module("adminApp").factory("usageAchievementsService", ["common", "searchConditionFunction",
  usageAchievementsService]);

function usageAchievementsService(common, searchConditionFunction) {
  var service = {
    getArrangeBillings: getArrangeBillings,
    relateCorporationData: relateCorporationData
  };

  angular.merge(service, searchConditionFunction);

  return service;

  function getArrangeBillings(params) {
    return common.ajaxCall("GET", "/usage_achievements", params);
  };

  function relateCorporationData(params) {
    return common.ajaxCall("GET", "/usage_achievements/relate_corporation_data", params);
  }
}
