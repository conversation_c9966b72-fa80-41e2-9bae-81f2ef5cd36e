'use strict';

angular.module('adminApp').factory('trainingScheduleService', ['common', 'searchConditionFunction', trainingScheduleService]);

function trainingScheduleService(common, searchConditionFunction) {
  var service = {
    getTrainingSchedules: getTrainingSchedules,
    list: list,
    create: create,
    show: show,
    destroy: destroy,
    bulkDestroy: bulkDestroy,
    updateStatus: updateStatus,
    createApplicant: createApplicant,
    restore: restore,
    getTrainingScheduleHistory: getTrainingScheduleHistory,
    getStaffAnswerSurvey: getStaffAnswerSurvey,
    updateReasonCancel: updateReasonCancel,
    getStaffAppliedHistory: getStaffAppliedHistory,
  }

  angular.merge(service, searchConditionFunction);

  return service

  function getTrainingSchedules(params) {
    return common.ajaxCall('GET', '/training_schedules', params);
  }

  function list(params) {
    return common.ajaxCall('GET', '/training_schedules/list', params);
  }

  function create(params) {
    return common.ajaxCall('POST', '/training_schedules', params);
  }

  function show(id) {
    return common.ajaxCall('GET', '/training_schedules/' + id);
  }

  function destroy(id) {
    return common.ajaxCall('DELETE', '/training_schedules/' + id, {});
  }

  function bulkDestroy(params) {
    return common.ajaxCall('DELETE', '/training_schedules/bulk_destroy', params);
  }

  function updateStatus(id, params) {
    return common.ajaxCall('POST', '/training_schedule_applicants/' + id + '/update_status', params);
  }

  function createApplicant(params) {
    return common.ajaxCall('POST', '/training_schedule_applicants', params);
  }

  function getTrainingScheduleHistory(id) {
    return common.ajaxCall('GET', '/training_schedules/' + id + '/histories');
  }

  function getStaffAnswerSurvey(id) {
    return common.ajaxCall('GET', '/training_schedule_applicants/' + id + '/staff_training_survey_answers')
  }

  function updateReasonCancel(id, params) {
    return common.ajaxCall('POST', '/training_schedule_applicants/' + id + '/staff_training_survey_answers', params);
  }

  function restore(params) {
    return common.ajaxCall('POST', '/training_schedules/restore', params);
  }

  function getStaffAppliedHistory(staff_id) { 
    return common.ajaxCall('GET', '/training_schedule_applicants/staff_histories/' + staff_id);
  }
}
