"use strict";

angular.module("adminApp")
  .factory("staffDepartmentsService", ["common", staffDepartmentsService]);

function staffDepartmentsService(common) {
  var service = {
    getStaffDepartments: getStaffDepartments,
    getDepartments: getDepartments,
    createStaffDepartment: createStaffDepartment,
    updateStaffDepartment: updateStaffDepartment,
    deleteStaffDepartment: deleteStaffDepartment,
    fetchRecentDepartment: fetchRecentDepartment
  }

  return service;

  function getDepartments() {
    return common.ajaxCall("GET", "/departments")
  };

  function getStaffDepartments(staffId) {
    return common.ajaxCall("GET", "/staffs/" + staffId + "/staff_departments");
  };

  function createStaffDepartment(staffId, params) {
    return common.ajaxCall("POST", "/staffs/" + staffId + "/staff_departments", params);
  }

  function updateStaffDepartment(staffId, staffDepartmentId, params) {
    return common.ajaxCall("PUT", "/staffs/" + staffId + "/staff_departments/" + staffDepartmentId, params);
  };

  function deleteStaffDepartment(staffId, staffDepartmentId) {
    return common.ajaxCall("DELETE", "/staffs/" + staffId + "/staff_departments/" + staffDepartmentId);
  };

  function fetchRecentDepartment(staffId) {
    return common.ajaxCall("GET", "/staffs/" + staffId + "/staff_departments/recent_department");
  }
}
