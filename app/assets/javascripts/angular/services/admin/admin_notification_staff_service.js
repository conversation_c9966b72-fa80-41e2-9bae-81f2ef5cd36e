"use strict";

angular.module("adminApp").factory("AdminNotificationStaffService", ["common", AdminNotificationStaffService]);

function AdminNotificationStaffService(common) {
  var service;
  service = {
    searchNotification: searchNotification,
    deleteNotification: deleteNotification
  };

  angular.merge(service);

  return service;

  function searchNotification(params) {
    return common.ajaxCall("GET", "/admin_notification_staffs", params);
  }

  function deleteNotification(id) {
    return common.ajaxCall("DELETE", "/admin_notification_staffs/" + id);
  }
}
