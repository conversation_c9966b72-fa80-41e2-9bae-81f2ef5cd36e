"use strict";

angular.module("adminApp")
  .factory("individualNumberService", ["common", individualNumberService]);

function individualNumberService(common) {
  var service = {
    getIndividualNumbers: getIndividualNumbers,
    approveIndividualNumber: approveIndividualNumber,
    returnIndividualNumber: returnIndividualNumber,
  }

  return service;

  function getIndividualNumbers(params) {
    return common.ajaxCall("GET", "/individual_numbers", params);
  }

  function approveIndividualNumber(params) {
    return common.ajaxCall("PUT", "/individual_numbers/approve", params);
  }

  function returnIndividualNumber(params) {
    return common.ajaxCall("PUT", "/individual_numbers/return", params);
  }
}
  