"use strict";

angular.module("adminApp")
  .factory("foreignEmploymentStatusesService", ["common", "searchConditionFunction", foreignEmploymentStatusesService]);

function foreignEmploymentStatusesService(common, searchConditionFunction) {
  var service = {
    getStaffs: getStaffs
  }

  angular.merge(service, searchConditionFunction);

  return service;

  function getStaffs(params) {
    return common.ajaxCall("GET", "/foreign_employment_statuses", params);
  }
}
