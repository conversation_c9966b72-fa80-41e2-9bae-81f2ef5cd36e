'use strict';

angular.module('adminApp')
  .factory('periodRateDefaultService', ['common', periodRateDefaultService]);

function periodRateDefaultService(common) {
  var service = {
    getDefaultPeriodRates: getDefaultPeriodRates,
    editDefaultPeriodRate: editDefaultPeriodRate,
    updateDefaultRate: updateDefaultRate,
    createPeriodRate: createPeriodRate,
    deletePeriodRate: deletePeriodRate,
    getDestroyedWarning: getDestroyedWarning
  }

  return service;

  function getDefaultPeriodRates(params) {
    return common.ajaxCall('GET', '/period_rates/set_default', params);
  }

  function editDefaultPeriodRate(periodRateId) {
    return common.ajaxCall('GET', '/period_rates/' + periodRateId  + '/edit');
  }

  function updateDefaultRate(periodRateId, params) {
    return common.ajaxCall('PUT', '/period_rates/' + periodRateId, params);
  }

  function createPeriodRate(params) {
    return common.ajaxCall('POST', '/period_rates', params);
  };

  function deletePeriodRate(periodRateId) {
    return common.ajaxCall('DELETE', '/period_rates/' + periodRateId);
  }

  function getDestroyedWarning(periodRateId) {
    return common.ajaxCall('GET', '/period_rates/' + periodRateId + '/destroyed_warning');
  }
}