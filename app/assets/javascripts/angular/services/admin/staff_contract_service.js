"use strict";

angular.module("adminApp")
  .factory("staffContractService", ["common", "searchConditionFunction", staffContractService]);

function staffContractService(common, searchConditionFunction) {
  var service = {
    getStaffContracts: getStaffContracts,
    bulkUpdateContractStatus: bulkUpdateContractStatus
  }
  angular.merge(service, searchConditionFunction)

  return service;

  function getStaffContracts(params) {
    return common.ajaxCall("GET", "/staff_contracts", params);
  }

  function bulkUpdateContractStatus(params) {
    return common.ajaxCall("PUT", "/staff_contracts/bulk_update_status", params);
  }
}
