'use strict';

angular.module('adminApp').factory('paymentRequestService', ['common', paymentRequestService]);

function paymentRequestService(common) {
  var service;
  service = {
    loadDataPaymentRequest: loadDataPaymentRequest,
    updatePaymentRequests: updatePaymentRequests,
    getDefaultTransferDate: getDefaultTransferDate,
    listValidUpdateStatus: listValidUpdateStatus,
    checkTransferDate: checkTransferDate,
    checkValidToDownload: checkValidToDownload,
    reschedule: reschedule
  };

  return service;

  function loadDataPaymentRequest(params) {
    return common.ajaxCall('GET', '/payment_requests', params);
  }

  function updatePaymentRequests(params) {
    return common.ajaxCall('PUT', '/payment_requests/bulk_update', params);
  }

  function getDefaultTransferDate() {
    return common.ajaxCall('GET', '/payment_requests/load_default_transfer_date');
  }

  function listValidUpdateStatus(params) {
    return common.ajaxCall('POST', '/payment_requests/list_valid_update_status', params);
  }

  function checkTransferDate(params) {
    return common.ajaxCall('GET', '/payment_requests/check_transfer_date', params);
  }

  function checkValidToDownload(params) {
    return common.ajaxCall('GET', '/payment_requests/check_valid_to_download', params);
  }

  function reschedule(params) {
    return common.ajaxCall('POST', '/payment_requests/reschedule', params);
  }
}
