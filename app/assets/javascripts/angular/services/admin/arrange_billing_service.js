"use strict";

angular.module("adminApp").factory("arrangeBillingService", ["common", arrangeBillingService]);

function arrangeBillingService(common) {
  var service;
  service = {
    searchArrangeBilling: searchArrangeBilling,
    cancelBilling: cancelBilling,
    getMonthBillings: getMonthBillings,
    searchArrangeBillingStaff: searchArrangeBillingStaff,
    confirmBillings: confirmBillings
  };

  return service;

  function searchArrangeBilling(params) {
    return common.ajaxCall("GET", "/arrange_billings", params);
  }

  function cancelBilling(params) {
    return common.ajaxCall("POST", "/arrange_billings/cancel_billing", params);
  }

  function getMonthBillings(params) {
    return common.ajaxCall("GET", "/arrange_billings/month_billings", params);
  }

  function searchArrangeBillingStaff(params) {
    return common.ajaxCall("POST", "/arrange_billings/search_download_by_staff", params);
  }

  function confirmBillings(params) {
    return common.ajaxCall("POST", "/arrange_billings/confirm_billings", params);
  }
}
