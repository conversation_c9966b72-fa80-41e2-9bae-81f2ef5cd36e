"use strict";

angular.module("adminApp")
  .factory("staffListService", ["common", "searchConditionFunction", staffListService]);

function staffListService(common, searchConditionFunction) {
  var service = {
    getStaff: getStaff,
    updateContractStatus: updateContractStatus,
    sendVerificationEmail: sendVerificationEmail,
    validateExportWorkCondition: validateExportWorkCondition,
    createStaffContractHistory: createStaffContractHistory,
    checkAdminPassword: checkAdminPassword,
    sendRemindLoginMail: sendRemindLoginMail,
    getStaffOtp: getStaffOtp
  }

  angular.merge(service, searchConditionFunction);

  return service;

  function getStaff(params) {
    return common.ajaxCall("GET", "/staffs", params);
  }

  function getStaffOtp(staffContact) {
    return common.ajaxCall("GET", "/staffs/otp", staffContact);
  }

  function updateContractStatus(params) {
    return common.ajaxCall("PATCH", "/staffs/bulk_update_contract_status", params);
  }

  function sendVerificationEmail(params) {
    return common.ajaxCall("POST", "/staffs/send_verification_email", params);
  }

  function validateExportWorkCondition(params) {
    return common.ajaxCall("GET", "/staff_work_conditions/validate_export_work_condition", params);
  }

  function createStaffContractHistory(params) {
    return common.ajaxCall("POST", "/staffs/create_staff_contract_history", params);
  }

  function checkAdminPassword(params) {
    return common.ajaxCall("POST", "/staffs/check_admin_password", params);
  }

  function sendRemindLoginMail(params) {
    return common.ajaxCall("POST", "/staffs/send_remind_login_mail", params);
  }
}
