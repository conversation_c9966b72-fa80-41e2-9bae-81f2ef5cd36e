"use strict";

angular.module("adminApp").factory("billingUnitPriceService", ["common", "searchConditionFunction", billingUnitPriceService]);

function billingUnitPriceService(common, searchConditionFunction) {
  var service;
  service = {
    searchBillingUnitPrice: searchBillingUnitPrice,
    saveBillingUnitPrice: saveBillingUnitPrice,
    updatePaymentRate: updatePaymentRate
  };

  angular.merge(service, searchConditionFunction);

  return service;

  function searchBillingUnitPrice(params) {
    return common.ajaxCall("GET", "/billing_unit_prices", params);
  };

  function saveBillingUnitPrice(params) {
    return common.ajaxCall("POST", "/billing_unit_prices", params);
  };

  function updatePaymentRate(params) {
    return common.ajaxCall("PUT", "/billing_unit_prices/update_payment_rate", params);
  };
};
