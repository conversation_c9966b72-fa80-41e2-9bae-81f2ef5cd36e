"use strict";

angular.module("adminApp").factory("orderService", ["common", orderService]);

function orderService(common) {
  var service = {
    loadLocation: loadLocation,
    searchOrder: searchOrder,
    loadLocations: loadLocations,
    loadPicOptions: loadPicOptions,
    createOrderStep1: createOrderStep1,
    createOrderStep2: createOrderStep2,
    estimateOrderPrice: estimateOrderPrice,
    createSearchCondition: createSearchCondition,
    deleteOrder: deleteOrder,
    loadOrganizations: loadOrganizations,
    loadCorporationGroupData: loadCorporationGroupData,
    orderBranchesDetail: orderBranchesDetail,
    createOrder: createOrder,
    checkMidNight: checkMidNight,
    loadLocationTemplates: loadLocationTemplates,
    loadLocationJobCategories: loadLocationJobCategories
  }

  return service;

  function loadLocation(params) {
    return common.ajaxCall("GET", "/load_location", params);
  }

  function searchOrder(params) {
    return common.ajaxCall("GET", "/orders", params);
  }

  function loadLocations(params) {
    return common.ajaxCall('GET', '/order_locations', params);
  }

  function loadLocationJobCategories(locationId) {
    return common.ajaxCall('GET', "/locations/" + locationId + "/location_job_categories");
  }

  function loadPicOptions(params) {
    return common.ajaxCall('GET', '/order_location_data', params);
  }

  function createOrderStep1(params) {
    return common.ajaxCall('POST', '/orders/step_1', params);
  }

  function createOrderStep2(params) {
    return common.ajaxCall('POST', '/orders/step_2', params);
  }

  function estimateOrderPrice(params) {
    return common.ajaxCall('GET', '/estimate_order_price', params);
  }

  function createSearchCondition(params) {
    return common.ajaxCall("POST", "/admin_order_search_conditions", params);
  }

  function deleteOrder(id) {
    return common.ajaxCall("DELETE", "/orders/" + id);
  }

  function loadOrganizations(params) {
    return common.ajaxCall('GET', '/order_organizations', params);
  }

  function loadCorporationGroupData(params) {
    return common.ajaxCall('GET', '/order_corporation_group_data', params);
  }

  function createOrder(params) {
    return common.ajaxCall('POST', '/orders', params);
  }

  function orderBranchesDetail(params) {
    return common.ajaxCall('GET', '/order_branches_details', params);
  }

  function checkMidNight(params) {
    return common.ajaxCall('GET', '/order_check_midnight', params);
  }

  function loadLocationTemplates(params) {
    return common.ajaxCall('GET', '/billing_payment_templates/load_location_templates', params);
  }
}
