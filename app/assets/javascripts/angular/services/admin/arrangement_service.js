"use strict";

angular.module("adminApp").factory("arrangementService", ["common", "searchConditionFunction", arrangementService]);

function arrangementService(common, searchConditionFunction) {
  var service;
  service = {
    searchArrangement: searchArrangement,
    getOrderPortionStatus: getOrderPortionStatus,
    getCurrentStaffLimited: getCurrentStaffLimited,
    updateBreakTime: updateBreakTime,
    arrangeStaff: arrangeStaff,
    updateOrderPortionStatus: updateOrderPortionStatus,
    deleteArrangement: deleteArrangement,
    resendConfirmArrangeMail: resendConfirmArrangeMail,
    updateOrderCaseSegementId: updateOrderCaseSegementId,
    seachStaffForStatusLimit: seachStaffForStatusLimit,
    getListOrderPortionCancel: getListOrderPortionCancel,
    getTemporaryArrangeArrangements: getTemporaryArrangeArrangements,
    updateArrangePayment: updateArrangePayment,
    updateWorkingTimeStatusId: updateWorkingTimeStatusId,
    updateArrangementBasicField: updateArrangement<PERSON>asi<PERSON><PERSON>ield,
    updateWorkingAchievementBasicField: updateWorkingAchievementBasicField,
    updateArrangeBilling: updateArrangeBilling,
    sendConfirmArrangeJobMail: sendConfirmArrangeJobMail,
    getArrangeJobMailWorkerStatus: getArrangeJobMailWorkerStatus,
    getJobAvailable: getJobAvailable,
    sendOfferMail: sendOfferMail,
    getSelectedOfferJob: getSelectedOfferJob,
    sendOfferMailStatus: sendOfferMailStatus,
    getArrangeHistory: getArrangeHistory,
    getWorkAchievementHistory: getWorkAchievementHistory,
    getStatisticData: getStatisticData,
    seachStaffForTemporaryArrange: seachStaffForTemporaryArrange,
    seachStaffForCreateArrange: seachStaffForCreateArrange,
    loadArrangementHeaders: loadArrangementHeaders,
    copyPortion: copyPortion,
    createArrangement: createArrangement,
    updateIsStoreParkingAreaUsable: updateIsStoreParkingAreaUsable,
    checkMultipleDepartments: checkMultipleDepartments,
    searchParams: searchParams,
    getArrangementSumFooters: getArrangementSumFooters,
    updateInvoicetarget: updateInvoicetarget,
    getBillingPaymentTemplates: getBillingPaymentTemplates,
    getBillingPaymentTemplate: getBillingPaymentTemplate,
    updateBillingPaymentTemplateId: updateBillingPaymentTemplateId,
    checkValidToExport: checkValidToExport,
    getStaffEvaluation: getStaffEvaluation,
    getLocationEvaluation: getLocationEvaluation,
    checkLimitExport: checkLimitExport,
  };

  angular.merge(service, searchConditionFunction);

  return service;

  var searchParams = {};

  function searchArrangement(params) {
    return common.ajaxCall("GET", "/arrangements", params);
  }

  function getTemporaryArrangeArrangements(params) {
    return common.ajaxCall("GET", "/arrangements/temporary_arranges", params);
  }

  function copyPortion(params) {
    return common.ajaxCall("POST", "/arrangements/copy_portion", params);
  }

  function getOrderPortionStatus(params) {
    return common.ajaxCall("GET", "/order_portions/get_current_status", params)
  }

  function getCurrentStaffLimited(params) {
    return common.ajaxCall("GET", "/order_portions/get_staff_limited", params)
  }

  function updateBreakTime(params, id) {
    return common.ajaxCall("PUT", "/arrangements/" + id + "/update_break_time", params);
  }

  function arrangeStaff(params) {
    return common.ajaxCall("POST", "/arrangements/temporary_arrange", params);
  }

  function updateOrderPortionStatus(params, orderCaseId) {
    return common.ajaxCall("PUT", "/order_portions/" + orderCaseId + "/update_status", params);
  }

  function deleteArrangement(id) {
    return common.ajaxCall("DELETE", "/arrangements/" + id);
  }

  function updateOrderCaseSegementId(params, id) {
    return common.ajaxCall("PUT", "/arrangements/" + id + "/update_order_case_segment_id", params);
  }

  function seachStaffForStatusLimit(params) {
    return common.ajaxCall("GET", "/order_portions/search_staff", params);
  }

  function getListOrderPortionCancel(orderCaseId) {
    return common.ajaxCall("GET", "/order_portions/" + orderCaseId + "/get_list_order_portion")
  }

  function updateArrangePayment(params, arrangementId) {
    return common.ajaxCall("PUT", "/arrange_payments/" + arrangementId, params);
  }

  function updateWorkingTimeStatusId(params, id) {
    return common.ajaxCall("PUT", "/arrangements/" + id + "/update_working_time_status_id", params);
  }

  function updateArrangementBasicField(params, id) {
    return common.ajaxCall("PUT", "/arrangements/" + id + "/update_basic_field", params);
  }

  function updateWorkingAchievementBasicField(params, id) {
    return common.ajaxCall("PUT", "/work_achievements/" + id + "/update_basic_field", params);
  }

  function updateArrangeBilling(params, arrangementId) {
    return common.ajaxCall("PUT", "/arrange_billings/" + arrangementId, params)
  }

  function sendConfirmArrangeJobMail(params) {
    return common.ajaxCall("POST", "/arrangements/send_confirm_arrange_job_mail", params);
  }

  function getArrangeJobMailWorkerStatus(params) {
    return common.ajaxCall("POST", "/arrangements/send_confirm_arrange_job_mail_status", params);
  }

  function getJobAvailable(params) {
    return common.ajaxCall("GET", "/arrangements/job_available", params);
  }

  function checkMultipleDepartments(params) {
    return common.ajaxCall("GET", "/arrangements/check_multiple_departments", params);
  }

  function getSelectedOfferJob(params) {
    return common.ajaxCall("GET", "/arrangements/get_selected_offer_job", params);
  }

  function sendOfferMail(params) {
    return common.ajaxCall("POST", "/arrangements/send_offer_mail", params);
  }

  function sendOfferMailStatus(params) {
    return common.ajaxCall("POST", "/arrangements/send_offer_mail_status", params);
  }

  function resendConfirmArrangeMail(toOwner, arrangementId) {
    var params = {to_owner: toOwner, id: arrangementId};
    return common.ajaxCall("GET", "/arrangements/resend_confirm_arrange_mail", params);
  }

  function getArrangeHistory(id) {
    return common.ajaxCall("GET", "/arrangements/" + id + "/get_arrange_history");
  }

  function getWorkAchievementHistory(id) {
    return common.ajaxCall("GET", "/arrangements/" + id + "/get_work_achievement_history");
  }

  function getStatisticData(params) {
    return common.ajaxCall("GET", "/arrangements/get_statistic_data", params);
  }

  function seachStaffForTemporaryArrange(params) {
    return common.ajaxCall("GET", "/arrangements/search_staff_for_temporary_arrange", params);
  }

  function seachStaffForCreateArrange(params) {
    return common.ajaxCall("GET", "/arrangements/search_staff_for_create_arrange", params);
  }

  function loadArrangementHeaders() {
    return common.ajaxCall("GET", "/arrangements/load_table_headers");
  }

  function getArrangementSumFooters(params) {
    return common.ajaxCall("GET", "/arrangements/get_sum_data_columns", params);
  }

  function createArrangement(params) {
    return common.ajaxCall("POST", "/arrangements", params);
  }

  function updateIsStoreParkingAreaUsable(params, id) {
    return common.ajaxCall("PUT", "/arrangements/" + id + "/update_is_store_parking_area_usable", params);
  }

  function updateInvoicetarget(params, id) {
    return common.ajaxCall("PUT", "/arrangements/" + id + "/update_order_case_invoice_target", params);
  }

  function getBillingPaymentTemplate(id) {
    return common.ajaxCall("GET", "/billing_payment_templates/" + id);
  }

  function getBillingPaymentTemplates(locationId) {
    return common.ajaxCall("GET", "/billing_payment_templates?page=1&per_page=100&location_id=" + locationId);
  }

  function updateBillingPaymentTemplateId(id, params) {
    return common.ajaxCall("PUT", "/arrangements/" + id + "/update_billing_payment_template_id", params);
  }

  function checkValidToExport(params) {
    return common.ajaxCall('POST', '/arrangements/check_valid_to_export', params);
  }

  function getStaffEvaluation(arrangementId) {
    return common.ajaxCall("GET", "/arrangements/" + arrangementId + "/staff_evaluations");
  }

  function getLocationEvaluation(arrangementId) {
    return common.ajaxCall("GET", "/arrangements/" + arrangementId + "/location_evaluations");
  }

  function checkLimitExport(params) {
    return common.ajaxCall('GET', '/arrangements/check_limit_export', params);
  }
}
