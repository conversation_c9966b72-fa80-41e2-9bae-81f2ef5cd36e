"use strict";

angular.module("adminApp")
  .factory("staffDetailsService", ["common", staffDetailsService]);

function staffDetailsService(common) {
  var service = {
    rejectStaff: rejectStaff,
    sendAuthenticateMail: sendAuthenticateMail,
    sendMailEntry: sendMailEntry,
    checkSendMailEntry: checkSendMailEntry,
    getStations: getStations,
    getBankBranches: getBankBranches,
    validateContact: validateContact,
    validateStaffTraining: validateStaffTraining,
    validateStraffStableEmployment: validateStraffStableEmployment,
    validateStaffCareer: validateStaffCareer,
    validateStaffComplaint: validateStaffComplaint,
    mailHistoryDetail: mailHistoryDetail,
    moreMailHistories: moreMailHistories
  }

  return service;

  function rejectStaff(params) {
    return common.ajaxCall("GET", "/entries/reject_staff", params);
  }

  function sendAuthenticateMail(params) {
    return common.ajaxCall("GET", "/entries/send_authenticate_mail", params);
  }

  function sendMailEntry(params) {
    return common.ajaxCall("GET", "/entries/send_mail_entry", params);
  }

  function checkSendMailEntry(params) {
    return common.ajaxCall("GET", "/entries/check_before_send_modification_mail", params);
  }

  function getStations(params) {
    return common.ajaxCall("GET", "/stations", params);
  }

  function getBankBranches(params) {
    return common.ajaxCall("GET", "/bank_branches", params);
  }

  function validateContact(params) {
    return common.ajaxCall("POST", "/staff_contact_tabs/validate_contact", params);
  }

  function validateStaffTraining(params) {
    return common.ajaxCall("POST", "/staff_contact_tabs/validate_staff_training", params);
  }

  function validateStraffStableEmployment(params) {
    return common.ajaxCall("POST", "/staff_contact_tabs/validate_staff_stable_employment", params);
  }

  function validateStaffCareer(params) {
    return common.ajaxCall("POST", "/staff_contact_tabs/validate_staff_career", params);
  }

  function validateStaffComplaint(params) {
    return common.ajaxCall("POST", "/staff_contact_tabs/validate_staff_complaint", params);
  }

  function mailHistoryDetail(staffId, params) {
    return common.ajaxCall("GET", "/staffs/" + staffId + "/mail_history_detail", params);
  }

  function moreMailHistories(staffId, params) {
    return common.ajaxCall("GET", "/staffs/" + staffId + "/more_mail_histories", params);
  }
}
