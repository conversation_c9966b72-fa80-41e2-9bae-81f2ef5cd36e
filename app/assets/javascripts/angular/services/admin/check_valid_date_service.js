"use strict";

angular.module("adminApp")
  .factory("checkValidDateFunction", ["common", checkValidDateFunction]);

function checkValidDateFunction(common) {
  var functions = {
    checkValidDate: checkValidDate,
    checkValidTime: checkValidTime
  }

  return functions;

  function checkValidDate(params) {
    if (!_.isEmpty(params) && (!moment(params, FULL_DATE_FORMAT).isValid() || !CHECK_DATE_REGEX.test(params))) {
      return moment.parseZone(Date(Date.now())).format(FULL_DATE_FORMAT);
    }
  }

  function checkValidTime(params) {
    if (!_.isEmpty(params) && (!moment(params, TIME_PICKER_FORMAT).isValid())) {
      return moment.parseZone(Date(Date.now())).format(TIME_PICKER_FORMAT);
    }
  }
}
