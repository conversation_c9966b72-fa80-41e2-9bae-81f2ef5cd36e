"use strict";

angular.module("adminApp")
  .factory("adminRegistrationCodeService", ["common", adminRegistrationCodeService]);

function adminRegistrationCodeService(common) {
  var service = {
    printRegistrationCode: printRegistrationCode
  }

  return service;

  function printRegistrationCode(source, txtContent, title) {
    var printWindow = window.open("about:blank", "_new");
    printWindow.document.open();
    printWindow.document.write(printedDocument(source, txtContent, title));
    printWindow.document.close();
  }

  function printedDocument(source, txtContent, title) {
    return '\
      <html>\
        <body onload="window.print(); window.close();">\
          <h2>' + title + '</h2>\
          ' + txtContent + '\
          <img src=' + source + ' />\
        </body>\
      </html>';
  }
}
