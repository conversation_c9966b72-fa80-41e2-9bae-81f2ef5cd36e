"use strict";

angular.module("adminApp").factory("AdminEmailToStaffService", ["common", AdminEmailToStaffService]);

function AdminEmailToStaffService(common) {
  var service;
  service = {
    searchEmail: searchEmail,
    reviewEmail: reviewEmail,
    createEmail: createEmail,
    showEmailInfomation: showEmailInfomation,
    pauseSendEmail: pauseSendEmail,
    continueSendEmail: continueSendEmail,
  };

  angular.merge(service);

  return service;

  function searchEmail(params) {
    return common.ajaxCall("GET", "/admin_email_to_staffs", params);
  }

  function reviewEmail(params) {
    return common.ajaxCall("POST", "/admin_email_to_staffs/review", params);
  }

  function createEmail(params) {
    return common.ajaxCall("POST", "/admin_email_to_staffs", params);
  }

  function pauseSendEmail(id) {
    return common.ajaxCall("POST", "/admin_email_to_staffs/" + id + "/pause");
  }

  function continueSendEmail(id) {
    return common.ajaxCall("POST", "/admin_email_to_staffs/" + id + "/continue");
  }

  function showEmailInfomation(id) {
    return common.ajaxCall("GET", "/admin_email_to_staffs/" + id + "/information");
  }
}
