"use strict";

angular.module("adminApp")
  .factory("userDetailsService", ["common", userDetailsService]);

function userDetailsService(common) {
  var service = {
    viewOwnerInfos: viewOwnerInfos,
    sendRemindLoginMail: sendRemindLoginMail
  }

  return service;

  function viewOwnerInfos(params) {
    return common.ajaxCall("POST", "/users/view_owner_infos", params);
  }

  function sendRemindLoginMail(params) {
    return common.ajaxCall("POST", "/users/send_remind_login_mail", params);
  }
}
