"use strict"

angular.module("adminApp")
  .factory("dashboardService", ["common", "searchConditionFunction", dashboardService]);

function dashboardService(common, searchConditionFunction) {
  var service = {
    getDataSumReport: getDataSumReport,
    getDataSumDetail: getDataSumDetail
  }

  angular.merge(service, searchConditionFunction);

  return service;

  function getDataSumReport(params) {
    var url = "/dashboards";
    return common.ajaxCall("GET", url, params);
  }

  function getDataSumDetail(params) {
    var url = "/dashboards/view_detail";
    return common.ajaxCall("GET", url, params);
  }
}
