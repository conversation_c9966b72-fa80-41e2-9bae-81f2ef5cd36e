"use strict";

angular.module("adminApp")
  .factory("registrationListService", ["common", "searchConditionFunction", registrationListService]);

function registrationListService(common, searchConditionFunction) {
  var service = {
    getStaff: getStaff,
    approveTemporaryStaff: approveTemporaryStaff,
    rejectTemporaryStaff: rejectTemporaryStaff,
    sendAuthenticateMail: sendAuthenticateMail,
    sendUpdateRegisteredProfileMail: sendUpdateRegisteredProfileMail,
    deleteStaff: deleteStaff,
    checkAdminPassword: checkAdminPassword,
    viewRegistrationInfos: viewRegistrationInfos,
    updateRegistration: updateRegistration,
    getStations: getStations,
    getBankBranches: getBankBranches
  }

  angular.merge(service, searchConditionFunction);

  return service;

  function getStaff(params) {
    return common.ajaxCall("GET", "/registrations", params);
  }

  function approveTemporaryStaff(staffID) {
    return common.ajaxCall("PUT", "/registrations/" + staffID +"/approve");
  }

  function rejectTemporaryStaff(staffID) {
    return common.ajaxCall("PUT", "/registrations/" + staffID +"/reject");
  }

  function sendAuthenticateMail(params) {
    return common.ajaxCall("GET", "/registrations/send_authenticate_mail", params);
  }

  function sendUpdateRegisteredProfileMail(params) {
    return common.ajaxCall("GET", "/registrations/send_update_registered_profile", params);
  }

  function deleteStaff(params) {
    return common.ajaxCall("DELETE", "/registrations/delete_staff_list", params);
  }

  function checkAdminPassword(params) {
    return common.ajaxCall("POST", "/staffs/check_admin_password", params);
  }

  function viewRegistrationInfos(params) {
    return common.ajaxCall("POST", "/registrations/view_registration_infos", params);
  }

  function updateRegistration(staffID, params) {
    return common.ajaxCall("PUT", "/registrations/"+ staffID + "/update_registration", params);
  }

  function getStations(params) {
    return common.ajaxCall("GET", "/stations", params);
  }


  function getBankBranches(params) {
    return common.ajaxCall("GET", "/bank_branches", params);
  }
}
