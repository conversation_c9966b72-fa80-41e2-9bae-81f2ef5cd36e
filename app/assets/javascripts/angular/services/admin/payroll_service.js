"use strict";

angular.module("adminApp")
  .factory("payrollService", ["common", "searchConditionFunction", payrollService]);

function payrollService(common, searchConditionFunction) {
  var service = {
    updatePayroll: updatePayroll,
    cancelCalculate: cancelCalculate,
    getStafsfWithPayroll: getStafsfWithPayroll,
    batchCancelCalculate: batchCancelCalculate,
    exportStaffInfo: exportStaffInfo,
    exportPayroll: exportPayroll,
    getJobStatus: getJobStatus,
    performCalculate: performCalculate,
    checkIsCalculating: checkIsCalculating,
    conditionExportFBPayroll: conditionExportFBPayroll,
    exportFBPayroll: exportFBPayroll,
    sendNotification: sendNotification,
    checkBeforeSendNotification: checkBeforeSendNotification,
    cancelNotification: cancelNotification,
    checkBeforeCancelNotification: checkBeforeCancelNotification,
    checkIsAbleToPerformForAllPayrolls: checkIsAbleToPerformForAllPayrolls,
    checkIsAbleToCancelPayroll: checkIsAbleToCancelPayroll,
    checkIsCalculatingRankLevel: checkIsCalculatingRankLevel,
    performCalculateRankAndLevel: performCalculateRankAndLevel,
    createPayroll: createPayroll
  }

  angular.merge(service, searchConditionFunction);

  return service;

  function updatePayroll(params, staffId, id) {
    var url = "/staffs/" + staffId + "/payrolls/" + id;
    return common.ajaxCall("PUT", url, params);
  }

  function cancelCalculate(staffId, id) {
    var url = "/staffs/" + staffId + "/payrolls/" + id + "/cancel_calculate";
    return common.ajaxCall("POST", url)
  }

  function getStafsfWithPayroll(params) {
    var url = "/payrolls";
    return common.ajaxCall("GET", url, params);
  }

  function batchCancelCalculate(params) {
    var url = "/payrolls/batch_cancel_calculate"
    return common.ajaxCall("POST", url, params);
  }

  function exportStaffInfo(params) {
    var url = "/payrolls/export_staff_infos";
    return common.ajaxCall("POST", url, params);
  }

  function exportPayroll(params) {
    var url = "/payrolls/export";
    return common.ajaxCall("POST", url, params);
  }

  function getJobStatus(params) {
    var url = "/payrolls/get_job_status";
    return common.ajaxCall("POST", url, params);
  }

  function performCalculate(params) {
    var url = "/payrolls/perform_calculate";
    return common.ajaxCall("POST", url, params);
  }

  function checkIsCalculating(params) {
    var url = "/payrolls/check_is_calculating_or_exist_processing_request";
    return common.ajaxCall("POST", url, params);
  }

  function conditionExportFBPayroll(params) {
    var url = "/payrolls/condition_export_payroll";
    return common.ajaxCall("POST", url, params);
  }

  function exportFBPayroll(params) {
    var url = "/payrolls/export_payroll";
    return common.ajaxCall("POST", url, params);
  }

  function sendNotification(params) {
    var url = "/payrolls/send_notification";
    return common.ajaxCall("POST", url, params);
  }

  function checkBeforeSendNotification(params) {
    var url = "/payrolls/check_before_send_notification";
    return common.ajaxCall("POST", url, params);
  }

  function checkBeforeCancelNotification(params) {
    var url = "/payrolls/check_before_cancel_notification";
    return common.ajaxCall("POST", url, params);
  }

  function cancelNotification(params) {
    var url = "/payrolls/cancel_notification";
    return common.ajaxCall("POST", url, params);
  }

  function checkIsAbleToPerformForAllPayrolls(params) {
    var url = "/payrolls/check_is_able_to_perform_for_all_payrolls";
    return common.ajaxCall("POST", url, params);
  }

  function checkIsAbleToCancelPayroll(params) {
    var url = "/payrolls/check_is_able_to_cancel_payroll";
    return common.ajaxCall("POST", url, params);
  }

  function performCalculateRankAndLevel(params) {
    return common.ajaxCall("POST", "/payrolls/perform_calculate_rank_and_level", params);
  }

  function checkIsCalculatingRankLevel(params) {
    return common.ajaxCall("POST", "/payrolls/check_is_calculating_rank_level", params);
  }

  function createPayroll(params) {
    return common.ajaxCall("POST", "/payrolls", params);
  }
}
