'use strict';

angular.module('adminApp').factory('locationSurveysService', ['common', 'searchConditionFunction', locationSurveysService]);

function locationSurveysService(common, searchConditionFunction) {
  var service = {
    searchLocation: searchLocation
  }

  angular.merge(service, searchConditionFunction);

  return service

  function searchLocation(params) {
    return common.ajaxCall('GET', '/location_surveys/search_locations', params);
  }
}
