"use strict";

angular.module("adminApp").factory("billingPaymentTemplateService", ["common", billingPaymentTemplateService]);

function billingPaymentTemplateService(common) {
  var service = {
    getTemplates: getTemplates,
    deleteTemplate: deleteTemplate
  }

  return service;

  function getTemplates(params) {
    return common.ajaxCall("GET", "/billing_payment_templates", params);
  }

  function deleteTemplate(id) {
    return common.ajaxCall("DELETE", "/billing_payment_templates/" + id);
  }
}