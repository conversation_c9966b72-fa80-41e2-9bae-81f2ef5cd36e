"use strict";

angular.module("adminApp")
  .factory("corporationListService", ["common", "searchConditionFunction", corporationListService]);

function corporationListService(common, searchConditionFunction) {
  var service = {
    getCorporationList: getCorporationList,
    getLocations: getLocations
  }

  angular.merge(service, searchConditionFunction);

  return service;

  function getCorporationList(params) {
    return common.ajaxCall("GET", "/corporations", params);
  }

  function getLocations(corporationId) {
    return common.ajaxCall("GET", "/corporations/" + corporationId + "/load_corporation_groups_and_locations")
  }
}
