'use strict';

angular.module('adminApp').factory('locationService', ['common', 'searchConditionFunction', locationService]);

function locationService(common, searchConditionFunction) {
  var service = {
    getLocation: getLocation,
    searchLocation: searchLocation
  }

  angular.merge(service, searchConditionFunction);

  return service

  function getLocation(params) {
    return common.ajaxCall('GET', '/location_tab', params);
  }

  function searchLocation(params) {
    return common.ajaxCall('GET', '/locations', params);
  }
}
