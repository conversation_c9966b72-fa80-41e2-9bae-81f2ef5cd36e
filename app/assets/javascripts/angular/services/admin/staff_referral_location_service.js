"use strict";

angular.module("adminApp")
  .factory("staffReferralLocationService", ["common", staffReferralLocationService]);

function staffReferralLocationService(common) {
  var service = {
    getList: getList,
    create: create,
    destroy: destroy
  }

  angular.merge(service, searchConditionFunction);

  return service;

  function getList(params) {
    return common.ajaxCall("GET", "/staff_referral_locations", params);
  }

  function create(params) {
    return common.ajaxCall("POST", "/staff_referral_locations/", params)
  }

  function destroy(id) {
    return common.ajaxCall("DELETE", "/staff_referral_locations/" + id)
  }
}
