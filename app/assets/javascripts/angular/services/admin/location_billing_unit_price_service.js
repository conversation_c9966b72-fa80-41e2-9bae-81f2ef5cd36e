"use strict";

angular.module("adminApp").factory("locationBillingUnitPriceService", ["common", locationBillingUnitPriceService]);

function locationBillingUnitPriceService(common) {
  var service;
  service = {
    searchBillingUnitPriceByLocations: searchBillingUnitPriceByLocations,
  };

  return service;

  function searchBillingUnitPriceByLocations(params) {
    return common.ajaxCall("GET", "/location_billing_unit_prices", params);
  };
};
