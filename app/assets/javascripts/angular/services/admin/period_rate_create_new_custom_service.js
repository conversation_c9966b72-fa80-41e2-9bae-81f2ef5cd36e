'use strict';

angular.module('adminApp')
  .factory('periodRateCreateNewCustomService', ['common', periodRateCreateNewCustomService]);

function periodRateCreateNewCustomService(common) {
  var service = {
    getPrefectures: getPrefectures,
    createCustomRatesByDate: createCustomRatesByDate,
    getCustomRatesByDate: getCustomRatesByDate,
    periodRateCreateNewCustomService: periodRateCreateNewCustomService,
    editPeriodRate: editPeriodRate,
    updatePeriodRate: updatePeriodRate,
    deletePeriodRate: deletePeriodRate,
    getDestroyedWarning: getDestroyedWarning
  }

  return service;

  function getPrefectures() {
    return common.ajaxCall('GET', '/prefectures');
  }

  function createCustomRatesByDate(params) {
    return common.ajaxCall('POST', '/period_rates/create_custom_rates_by_date', params);
  }

  function getCustomRatesByDate(peakPeriodId) {
    return common.ajaxCall('GET', '/period_rates/custom_rates_by_date/' + peakPeriodId);
  }

  function periodRateCreateNewCustomService(params) {
    return common.ajaxCall('POST', '/period_rates', params);
  }

  function editPeriodRate(periodRateId) {
    return common.ajaxCall('GET', '/period_rates/' + periodRateId  + '/edit');
  }

  function updatePeriodRate(periodRateId, params) {
    return common.ajaxCall('PUT', '/period_rates/' + periodRateId, params);
  }

  function deletePeriodRate(periodRateId) {
    return common.ajaxCall('DELETE', '/period_rates/' + periodRateId);
  }

  function getDestroyedWarning(periodRateId) {
    return common.ajaxCall('GET', '/period_rates/' + periodRateId + '/destroyed_warning');
  }
}