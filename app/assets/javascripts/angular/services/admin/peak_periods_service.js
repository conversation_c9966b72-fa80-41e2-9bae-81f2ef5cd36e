'use strict';

angular.module('adminApp')
  .factory('peakPeriodsService', ['common', peakPeriodsService]);

function peakPeriodsService(common) {
  var service = {
    getPeakPeriods: getPeakPeriods,
    createPeakPeriod: createPeakPeriod,
    destroyPeakPeriod: destroyPeakPeriod,
    getByTargetDate: getByTargetDate
  }

  return service;

  function getPeakPeriods(params) {
    return common.ajaxCall('GET', '/peak_periods', params);
  }

  function createPeakPeriod(params) {
    return common.ajaxCall('POST', '/peak_periods', params);
  }

  function destroyPeakPeriod(peakPeriodId) {
    return common.ajaxCall('DELETE', '/peak_periods/' + peakPeriodId);
  }

  function getByTargetDate(params) {
    return common.ajaxCall('GET', '/peak_periods/get_by_target_date', params);
  }
}