"use strict";

angular.module("corporationApp")
  .factory("ownerInfosService", ["common", ownerInfosService]);

function ownerInfosService(common) {
  return {
    getCorporationGroups: getCorporationGroups,
    getLocations: getLocations,
    updateCorporationGroup: updateCorporationGroup,
    deleteCorporationGroupTag: deleteCorporationGroupTag
  };

  function getCorporationGroups(params, locale) {
    return common.ajaxCall("GET", "/" + locale + "/owner_infos/corporation_groups", params);
  }

  function getLocations(params, locale) {
    return common.ajaxCall("GET", "/" + locale + "/owner_infos/locations", params);
  }

  function updateCorporationGroup(id, params) {
    return common.ajaxCall("PUT",  "/" + I18n.locale + "/owner_infos/" + id, params);
  }

  function deleteCorporationGroupTag(id) {
    return common.ajaxCall("DELETE", "/" + I18n.locale + "/corporation_group_tags/" + id);
  }
}
