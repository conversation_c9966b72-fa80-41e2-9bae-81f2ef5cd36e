"use strict";

angular.module("corporationApp")
  .factory("formLocationSurveyConstants", ["common", formLocationSurveyConstants]);


function formLocationSurveyConstants(common){
  var _this = this;

  _this.DEFAULT_ANSWER_FOR_TEXT_BOX = [
    { value: "コンビニエンスストアにおける販売業務", order: 1 },
    { value: "レジ、品出し", order: 2 },
    { value: "無", order: 3 },
    { value: "無", order: 4 },
    { value: "無", order: 5 },
    { value: "無", order: 6 },
    { value: "無", order: 7 },
    { value: "無", order: 8 },
    { value: "無", order: 9 },
    { value: "無", order: 10 },
    { value: "有期雇用労働者(週間所定労働20時間)", order: 11 },
    { value: "比較対象労働者：業務の内容が同一であると見込まれる短時間・有期雇用労働者", order: 12},
    { value: "受け入れようとする派遣労働者と業務内容や職務内容が同一の通常の労働者がいない為。", order: 13 },
    { value: "労働に対する基本的な対償として支払われるもの", order: 15 },
    { value: "採用状況を考慮し単価を設定", order: 16 },
    { value: "基本時給に対し、２５％割増", order: 26 },
    { value: "従業員の突発的な遅刻、欠勤時等に人材の確保をする目的", order: 27 },
    { value: "勤務時間帯を考慮", order: 28 },
    { value: "深夜労働手当のみ基本時給に対し、２５％割増", order: 29 },
    { value: "事務所内に設けた休憩室の利用の機会を付与", order: 38 },
    { value: "業務の円滑な遂行に資する目的", order: 39 },
    { value: "全社員対象", order: 40 },
    { value: "事務所内に設けた更衣室の利用の機会を付与", order: 41 },
    { value: "業務の円滑な遂行に資する目的", order: 42 },
    { value: "全社員対象", order: 43 },
    { value: "深夜に勤務できる人材の確保をする目的", order: 30},
    { value: "勤務時間帯を考慮", order: 31}
  ]
  _this.DEFAULT_ANSWERS_VALIDATE = [
    { value: "基本時給に対し、２５％割増", order: 36 },
    { value: "業務の円滑な遂行に資する目的", order: 37 },
    { value: "従業員の突発的な遅刻、欠勤時等に人材の確保をする目的", order: 38 },
    { value: "深夜労働手当のみ基本時給に対し、２５％割増", order: 40 },
    { value: "深夜に勤務できる人材の確保をする目的", order: 41 },
    { value: "勤務時間帯を考慮", order: 42},
    { value: "事務所内に設けた休憩室の利用の機会を付与", order: 52 },
    { value: "業務の円滑な遂行に資する目的", order: 53 },
    { value: "全社員対象", order: 54 },
    { value: "事務所内に設けた更衣室の利用の機会を付与", order: 56 },
    { value: "業務の円滑な遂行に資する目的", order: 57 },
    { value: "全社員対象", order: 58 }
  ]
  _this.SYSTEM_HAS = "true";
  _this.NO_SYSTEM = "false";
  _this.PLACEHOLDER_TEXTBOX = ["待遇の内容", "待遇の性質・目的", "待遇決定に当たって考慮した事項"];
  _this.POS_SELECT_BOX_SYSTEM_HAS = [4,5,8,9];
  _this.TOTAL_QUESTIONS_OF_SELECT_BOX = 18;
  return _this;
}