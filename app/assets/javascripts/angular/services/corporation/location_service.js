"use strict";

angular.module("corporationApp").factory("locationService", ["common", locationService]);

function locationService(common) {
  var service = {
    editLocation: editLocation,
    updateLocationPic: updateLocationPic,
    loadLocationNotSurvey: loadLocationNotSurvey,
    loadSalaryByPrefecture: loadSalaryByPrefecture,
    validateLocationStep1: validateLocationStep1,
    updateStoreLocation: updateStoreLocation
  };

  return service;

  function editLocation(id, params) {
    return common.ajaxCall("PUT", "/" + I18n.locale + "/locations/" + id, params);
  }

  function updateLocationPic(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/locations/" + params.id + "/update_location_pic", params);
  }

  function loadLocationNotSurvey() {
    return common.ajaxCall("GET", "/" + I18n.locale + "/location_surveys/locations_not_survey");
  }

  function loadSalaryByPrefecture(location_id) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/location_surveys/" + location_id + "/load_salary_by_prefecture");
  }

  function validateLocationStep1(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/locations/validate_step_1", params);
  }

  function updateStoreLocation(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/locations/update_store_location", params);
  }
}
