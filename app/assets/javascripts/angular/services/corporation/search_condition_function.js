"use strict";

angular.module("corporationApp")
  .factory("searchConditionFunction", ["common", searchConditionFunction]);

function searchConditionFunction(common) {
  var functions = {
    createSearchCondition: createSearchCondition,
    getLastSearchConditions: getLastSearchConditions
  }

  return functions;

  function createSearchCondition(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/search_conditions", params);
  }

  function getLastSearchConditions(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/search_conditions", params);
  }
}
