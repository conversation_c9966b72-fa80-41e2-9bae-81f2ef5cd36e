"use strict";

angular.module("corporationApp").factory("homeService", ["common", homeService]);

function homeService(common) {
  return {
    updateNotificationChecked: updateNotificationChecked,
    turnOffNotificationPopup: turnOffNotificationPopup,
    getOwnerNotifications: getOwnerNotifications,
    getHomeOrderCases: getHomeOrderCases,
    getHomeLocations: getHomeLocations,
    getOrderCasesByChooseDate: getOrderCasesByChooseDate,
    getOrderCaseStatus: getOrderCaseStatus,
    agreeTermService: agreeTermService
  };

  function updateNotificationChecked(id) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/homes/update_owner_notification_checked/", id);
  }

  function turnOffNotificationPopup() {
    return common.ajaxCall("POST", "/" + I18n.locale + "/homes/turn_off_notification_popup/");
  }

  function getOwnerNotifications(page, per_page) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/homes/get_owner_notifications?per_page=" + per_page + "&page=" + page);
  };

  function getOrderCasesByChooseDate(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/homes/choose_date", params);
  }

  function getHomeOrderCases(params, locale) {
    return common.ajaxCall("GET", "/" + locale + "/homes", params);
  };

  function getHomeLocations(params, locale) {
    return common.ajaxCall("GET", "/" + locale + "/home_locations", params);
  };

  function getOrderCaseStatus(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/homes/highlight_calendar", params);
  };

  function agreeTermService() {
    return common.ajaxCall("POST", "/" + I18n.locale + "/homes/agree_term_service");
  };
}
