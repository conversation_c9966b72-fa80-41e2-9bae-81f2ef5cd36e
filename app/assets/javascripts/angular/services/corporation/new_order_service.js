'use strict';

angular.module('corporationApp').factory('newOrderService', ['common', newOrderService]);

function newOrderService(common) {
  var service = {
    createOrderStep2: createOrderStep2,
    createOrder: createOrder,
    updateOrder: updateOrder,
    getOrderTemplatesFromLocation: getOrderTemplatesFromLocation
  };

  return service;

  function createOrderStep2(params) {
    return common.ajaxCall('POST', '/' + I18n.locale + '/new_orders/step_2', params);
  }

  function createOrder(params) {
    return common.ajaxCall('POST', '/' + I18n.locale + '/new_orders', params);
  }

  function updateOrder(id, params) {
    return common.ajaxCall('PUT', '/' + I18n.locale + '/new_orders/' + id, params);
  }

  function getOrderTemplatesFromLocation(params) {
    return common.ajaxCall('GET', '/' + I18n.locale + '/order_templates/get_order_templates_from_location', params);
  }
}
