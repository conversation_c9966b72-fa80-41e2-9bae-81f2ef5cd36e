'use-strict';

angular.module('corporationApp').factory('userService', ['common', userService]);

function userService(common) {
  var service = {
    createUser: createUser,
    updateUser: updateUser,
    deleteUser: deleteUser
  }

  return service;

  function createUser(params) {
    return common.ajaxCall('POST', '/' + I18n.locale + '/users', params);
  }

  function updateUser(user_id, params) {
    return common.ajaxCall('PUT', '/' + I18n.locale + '/users/' + user_id, params);
  }

  function deleteUser(user_id) {
    return common.ajaxCall('DELETE', '/' + I18n.locale + '/users/' + user_id + "/?flash=true");
  }
}
