"use strict"

angular.module("corporationApp")
  .factory("orderTemplateService", ["common", orderTemplateService]);

function orderTemplateService(common) {
  var service = {
    getOrderTemplates: getOrderTemplates,
    createOrderTemplate: createOrderTemplate,
    updateOrderTemplate: updateOrderTemplate,
    deleteOrderTemplate: deleteOrderTemplate
  };

  return service;

  function getOrderTemplates(params, locale) {
    return common.ajaxCall("GET", "/" + locale + "/order_templates", params);
  };

  function createOrderTemplate(params, locale) {
    return common.ajaxCall("POST", "/" + locale + "/order_templates", params)
  };

  function updateOrderTemplate(id, params, locale) {
    return common.ajaxCall("PUT", "/" + locale + "/order_templates/" + id, params)
  };

  function deleteOrderTemplate(id, locale) {
    return common.ajaxCall("DELETE", "/" + locale + "/order_templates/" + id);
  };
}