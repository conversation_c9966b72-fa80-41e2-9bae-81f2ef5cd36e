"use strict";

angular.module("corporationApp").factory("usageAchievementsService", ["common", "searchConditionFunction",
  usageAchievementsService]);

function usageAchievementsService(common, searchConditionFunction) {
  var service = {
    searchOrderCase: searchOrderCase
  };

  angular.merge(service, searchConditionFunction);

  return service;

  function searchOrderCase(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/usage_achievements", params);
  };
}
