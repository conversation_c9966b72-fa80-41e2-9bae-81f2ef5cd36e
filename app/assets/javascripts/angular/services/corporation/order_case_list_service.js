"use strict";

angular.module("corporationApp")
  .factory("orderCaseListService", ["common", orderCaseListService]);

function orderCaseListService(common) {
  return {
    getOrderCases: getOrderCases,
    cancelOrderCase: cancelOrderCase,
    cancelOrderCases: cancelOrderCases,
    createSearchCondition: createSearchCondition,
    calculateSummaryPrice: calculateSummaryPrice,
    changeOrderPortionNumber: changeOrderPortionNumber,
    deleteOrderPortion: deleteOrderPortion,
    updateSpecialOfferFee: updateSpecialOfferFee,
    reviewStaffEvaluation: reviewStaffEvaluation,
    updateWorkAchievement: updateWorkAchievement,
    cancelOrderCaseDetail: cancelOrderCaseDetail,
    updateChangeable: updateChangeable
  };

  function getOrderCases(params, locale) {
    return common.ajaxCall("GET", "/" + locale + "/order_cases", params);
  };

  function cancelOrderCase(params, locale) {
    return common.ajaxCall("POST", "/" + locale + "/cancel_order_case", params);
  };

  function cancelOrderCases(params, locale) {
    return common.ajaxCall("POST", "/" + locale + "/cancel_order_cases", params);
  };

  function cancelOrderCaseDetail(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/cancel_order_case_detail", params);
  };

  function createSearchCondition(params, locale) {
    return common.ajaxCall("POST", "/" + locale + "/user_order_case_search_conditions", params);
  };

  function calculateSummaryPrice(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/calculate_summary_price", params);
  }

  function changeOrderPortionNumber(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/change_order_portion_number", params);
  }

  function deleteOrderPortion(portionId, params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/delete_order_portion/" + portionId, params);
  }

  function updateSpecialOfferFee(orderCaseId, params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/order_cases/" + orderCaseId + "/update_special_offer_fee/", params);
  }

  function reviewStaffEvaluation(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/staff_evaluations/", params);
  }

  function updateWorkAchievement(params, id) {
    return common.ajaxCall("PUT", "/" + I18n.locale + "/work_achievements/" + id + "/update_working_time", params);
  }

  function updateChangeable(portionId, params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/update_changeable/" + portionId, params);
  }
}
