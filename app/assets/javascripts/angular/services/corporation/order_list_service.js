"use strict";

angular.module("corporationApp")
  .factory("orderListService", ["common", orderListService]);

function orderListService(common) {
  var service = {
    getOrders: getOrders,
    deleteOrder: deleteOrder,
    createSearchCondition: createSearchCondition,
    getLastSearchConditions: getLastSearchConditions
  }

  return service;

  function getOrders(params, locale) {
    return common.ajaxCall("GET", "/" + locale + "/orders", params);
  }

  function deleteOrder(id, locale) {
    return common.ajaxCall("DELETE", "/" + locale + "/orders/" + id);
  }

  function createSearchCondition(params, locale) {
    return common.ajaxCall("POST", "/" + locale + "/user_order_search_conditions", params);
  }

  function getLastSearchConditions(locale) {
    return common.ajaxCall("GET", "/" + locale + "/user_order_last_search_conditions");
  }
}
