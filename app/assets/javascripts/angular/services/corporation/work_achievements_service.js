"use strict";

angular.module("corporationApp")
  .factory("workAchievementService", ["common", workAchievementService]);

function workAchievementService(common) {
  var service = {
    getWorkAchievements: getWorkAchievements,
    updateWorkAchievement: updateWorkAchievement,
    reviewStaffEvaluation: reviewStaffEvaluation
  }

  return service;

  function getWorkAchievements(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/work_achievements", params);
  }

  function updateWorkAchievement(params, id) {
    return common.ajaxCall("PUT", "/" + I18n.locale + "/work_achievements/" + id + "/update_working_time", params);
  }

  function reviewStaffEvaluation(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/staff_evaluations/", params);
  }
}
