'use strict';

angular.module('corporationApp').factory('orderService', ['common', orderService]);

function orderService(common) {
  var service = {
    loadPicOptions: loadPicOptions,
    estimateOrderPrice: estimateOrderPrice,
    orderBranchesDetail: orderBranchesDetail
  };

  return service;

  function loadPicOptions(params) {
    return common.ajaxCall('GET', '/' + I18n.locale + '/order_location_data', params);
  }

  function estimateOrderPrice(params) {
    return common.ajaxCall('GET', '/' + I18n.locale + '/estimate_order_price', params);
  }

  function orderBranchesDetail(params) {
    return common.ajaxCall('GET', '/' + I18n.locale + '/order_branches_details', params);
  }
}
