"use strict";

angular.module("corporationApp").factory("hakenDestinationsService", ["common", "searchConditionFunction",
  hakenDestinationsService]);

function hakenDestinationsService(common, searchConditionFunction) {
  var service = {
    searchArrangement: searchArrangement
  };

  angular.merge(service, searchConditionFunction);

  return service;

  function searchArrangement(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/haken_destinations", params);
  };
}
