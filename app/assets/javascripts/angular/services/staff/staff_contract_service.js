"use strict";

angular.module("staffApp")
  .factory("staffContractService", ["common", staffContractService]);

function staffContractService(common) {
  var service = {
    getData: getData,
    saveStaffContract: saveStaffContract,
    validateStep1: validateStep1,
    getDataEdit: getDataEdit,
    updateRegistrationType: updateRegistrationType
  }

  return service;

  function getData(contract_code) {
    return common.ajaxCall("GET", "/contracts/" + contract_code);
  }

  function saveStaffContract(params, contract_code) {
    return common.ajaxCall("PUT", "/contracts/" + contract_code + "/update_contract", params);
  }

  function validateStep1(params, contract_code) {
    return common.ajaxCall("POST", "/contracts/" + contract_code + "/step_1_valid", params);
  }

  function getDataEdit(contract_code) {
    return common.ajaxCall("GET", "/contracts/" + contract_code + "/edit");
  }

  function updateRegistrationType(params, contract_code) {
    return common.ajaxCall("PUT", "/contracts/" + contract_code + "/update_registration_type", params); 
  }
}
