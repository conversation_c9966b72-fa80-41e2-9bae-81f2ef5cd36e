"use strict";

angular.module("staffApp")
  .factory("workListService", ["common", workListService]);

function workListService(common) {
  var currentTabName;
  var service = {
    loadDataWorkList: loadDataWorkList,
    loadDataWorkHistoryList: loadDataWorkHistoryList,
    loadDataArrangedWorkList: loadDataArrangedWorkList,
    setCurrentTabName: setCurrentTabName,
    getCurrentTabName: getCurrentTabName
  }

  return service;

  function loadDataWorkList(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/work_lists/load_data_work_list", params);
  };

  function loadDataWorkHistoryList(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/work_lists/load_data_work_history_list", params);
  };

  function loadDataArrangedWorkList(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/work_lists/load_data_arranged_work_list", params);
  }

  function setCurrentTabName(name) {
    currentTabName = name;
  }

  function getCurrentTabName() {
    return currentTabName;
  }
};
