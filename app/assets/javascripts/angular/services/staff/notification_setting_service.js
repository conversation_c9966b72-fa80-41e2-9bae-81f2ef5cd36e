"use strict";

angular.module("staffApp")
  .factory("notificationSettingService", ["common", notificationSettingService]);

// ! Deprecated
function notificationSettingService(common) {
  var service = {
    disableNotificationDisplay: disableNotificationDisplay,
    setPermissionRequested: setPermissionRequested
  }

  return service;

  function disableNotificationDisplay() {
    return common.ajaxCall("GET", "/disable_notification_display");
  }

  function setPermissionRequested() {
    return common.ajaxCall("GET", "/set_permission_requested");
  }
}
