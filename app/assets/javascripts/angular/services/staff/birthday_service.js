"use strict";

angular.module("staffApp").factory("birthdayService", birthdayService);


function birthdayService() {
  var service = {
    initBirthday: initBirthday,
    changeYear: changeYear,
    changeMonth: changeMonth,
    changeDay: changeDay,
    rangeDateTime: rangeDateTime,
    formatMonthAndDay: formatMonthAndDay,
    assignBirthday: assignBirthday
  };

  return service;

  function changeYear(scope) {
    if (!_.isNumber(scope.vm.birthday.month)) {
      scope.vm.birthday.month = 1;
    }
    resetDay(scope);
    assignBirthday(scope);
  }

  function changeMonth(scope) {
    resetDay(scope);
    assignBirthday(scope);
  }

  function changeDay(scope) {
    assignBirthday(scope);
  }

  function resetDay(scope) {
    var numberDaysOfMonth = new Date(scope.vm.birthday.year, scope.vm.birthday.month, 0).getDate();

    if (!_.isNumber(scope.vm.birthday.day) || _.isNaN(numberDaysOfMonth) || scope.vm.birthday.day > numberDaysOfMonth) {
      scope.vm.birthday.day = 1;
    }
    if (_.isNaN(numberDaysOfMonth)) return;
    scope.vm.days = rangeDateTime(1, numberDaysOfMonth);
    return numberDaysOfMonth;
  }

  function assignBirthday(scope) {
    if (_.isUndefined(scope.vm.staff)) {
      scope.vm.staff = {};
    }
    if (!_.isNumber(scope.vm.birthday.year) || !_.isNumber(scope.vm.birthday.month) || !_.isNumber(scope.vm.birthday.day)) {
      scope.vm.staff.birthday = "";
    } else {
      scope.vm.staff.birthday = [scope.vm.birthday.year, formatMonthAndDay(scope.vm.birthday.month), formatMonthAndDay(scope.vm.birthday.day)].join("/");
    }
  }

  function formatMonthAndDay(value) {
    return value >= 10 ? value : ("0" + value);
  }

  function rangeDateTime(start, end) {
    return Array(end - start + 1).fill().map(function(_, idx) { return {name: (start + idx), id: start + idx} })
  }

  function initBirthday(scope) {
    var yearNow = moment().year();
    scope.vm.years = rangeDateTime(yearNow - REGISTRATION_LOWER_YEAR, yearNow - REGISTRATION_UPPER_YEAR).reverse();
    scope.vm.months = rangeDateTime(1, 12);
    scope.vm.days = rangeDateTime(1, 31);
  }
}
