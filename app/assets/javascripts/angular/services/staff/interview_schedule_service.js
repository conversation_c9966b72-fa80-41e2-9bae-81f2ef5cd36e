"use strict";

angular.module("staffApp")
  .factory("interviewScheduleService", ["common", interviewScheduleService]);

function interviewScheduleService(common) {
  var service = {
    schedule: schedule,
    cancel: cancel,
    submitEmail: submitEmail
  }

  return service;

  function schedule(params) {
    return common.ajaxCall("POST", "/interview_schedules", params);
  }

  function cancel(params) {
    return common.ajaxCall("POST", "/interview_schedules/cancel", params)
  }

  function submitEmail(params) {
    return common.ajaxCall("POST", "/interview_schedules/submit_email", params)
  }
}
