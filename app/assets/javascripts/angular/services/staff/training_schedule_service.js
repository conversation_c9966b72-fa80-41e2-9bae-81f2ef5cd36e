"use strict";

angular.module("staffApp")
  .factory("trainingScheduleService", ["common", trainingScheduleService]);

function trainingScheduleService(common) {
  var service = {
    trainingJobs: trainingJobs,
    book: book,
    reschedule: reschedule,
    cancel: cancel,
    submitEmail: submitEmail,
    getCancellationInfo: getCancellationInfo,
    checkAbleToAbsent: checkAbleToAbsent
  }

  return service;

  function trainingJobs(params) {
    return common.ajaxCall("GET", "/training_schedules/list_by_training_center", params);
  }

  function book(params) {
    return common.ajaxCall("POST", "/training_schedules", params);
  }

  function reschedule(params) {
    return common.ajaxCall("POST", "/training_schedules/reschedule", params);
  }

  function cancel(params) {
    return common.ajaxCall("POST", "/training_schedules/cancel", params);
  }

  function submitEmail(params) {
    return common.ajaxCall("POST", "/training_schedules/submit_email", params);
  }

  function getCancellationInfo(params) {
    return common.ajaxCall("GET", "/training_schedules/cancellation_info", params);
  }

  function checkAbleToAbsent(params) {
    return common.ajaxCall("GET", "/training_schedules/check_able_to_absent", params);
  }
}
