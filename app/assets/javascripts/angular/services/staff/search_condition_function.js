"use strict";

angular.module("staffApp")
  .factory("searchConditionFunction", ["common", searchConditionFunction]);

function searchConditionFunction(common) {
  var functions = {
    createSearchCondition: createSearchCondition,
    getLastSearchConditions: getLastSearchConditions
  }

  return functions;

  function createSearchCondition(params) {
    return common.ajaxCall("POST", "/search_conditions", params);
  }

  function getLastSearchConditions(params) {
    return common.ajaxCall("GET", "/search_conditions", params);
  }
}
