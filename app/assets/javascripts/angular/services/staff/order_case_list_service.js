"use strict";

angular.module("staffApp")
  .factory("orderCaseListService", ["common", orderCaseListService]);

function orderCaseListService(common) {
  var FORMAT_PRICE = /\B(?=(\d{3})+(?!\d))/g;
  var HAKEN_ORDER_CASE_STATUS = "haken";
  var CONTRACT_ORDER_CASE_STATUS = "contract";
  var REGULAR_ORDER_CASE_STATUS = "regular_order";

  return {
    getOrderCases: getOrderCases,
    createStaffKeepOrderCase: createStaffKeepOrderCase,
    getFeeFlag: getFeeFlag,
    getAdditionFeeHightLight: getAdditionFeeHightLight,
    getAdditionFeeByWages: getAdditionFeeByWages,
    feeFlagByKey: feeFlagByKey
  };

  function getOrderCases(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/order_cases", params);
  };

  function createStaffKeepOrderCase(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/staff_keep_order_cases", params);
  };

  function getFeeFlag(order_case, arrangements, order_case_by_staff_fees) {
    var arrangement = _.find(arrangements, function(arrangement) {
      return arrangement.order_case_id == order_case.id;
    });

    var order_case_by_staff_fee = _.find(order_case_by_staff_fees, function(order_case_by_staff_fee) {
      return order_case_by_staff_fee.order_case_id == order_case.id;
    });

    if (order_case.special_offer_order_rate > 0) {
      return feeFlagByKey(order_case.special_offer_order_rate, "offer_fee_by_order_rate");
    } else if (!_.isEmpty(arrangement) && arrangement.total_unit_price_addition > 0) {
      return feeFlagByKey(arrangement.total_unit_price_addition);
    } else if (order_case.is_urgent && !_.isUndefined(order_case_by_staff_fee.fee) && order_case_by_staff_fee.fee > 0) {
      return feeFlagByKey(order_case_by_staff_fee.fee);
    } else if (order_case.payment_unit_price_addition > 0) {
      return feeFlagByKey(order_case.payment_unit_price_addition);
    }
  };

  function getAdditionFeeHightLight(order_case, order_case_by_staff_fees) {
    var order_fee = 0;
    var data_fee = [];

    var order_case_by_staff_fee = _.find(order_case_by_staff_fees, function(order_case_by_staff_fee) {
      return order_case_by_staff_fee.order_case_id == order_case.id;
    });

    if (order_case.segment_id === REGULAR_ORDER_CASE_STATUS) {
      if (!_.isUndefined(order_case_by_staff_fee.fee) && order_case_by_staff_fee.fee > 0) {
        data_fee.push(feeFlagByKey(order_case_by_staff_fee.fee));
      }
    }

    if (order_case.segment_id === HAKEN_ORDER_CASE_STATUS || order_case.segment_id === CONTRACT_ORDER_CASE_STATUS) {
      var addition_fee = 0;
      if (!_.isUndefined(order_case_by_staff_fee.other_addition_fee)) {
        addition_fee = order_case_by_staff_fee.other_addition_fee;
      }
      if (!_.isUndefined(order_case_by_staff_fee.unit_fee) && order_case_by_staff_fee.unit_fee > 0) {
        data_fee.push(feeFlagByKey(order_case_by_staff_fee.unit_fee));
      }
      if (order_case.special_offer_order_rate > 0 || addition_fee > 0) {
        var amount = order_case.special_offer_order_rate + addition_fee;
        data_fee.push(feeFlagByKey(amount, "offer_fee_by_order_rate"));
      }
    }
    return data_fee;
  };

  function getAdditionFeeByWages(order_case, wages_fees) {
    var data_fee = [];
    var fee = _.find(wages_fees, function(wage) {
      return wage.order_case_id == order_case.id;
    });
    if (!_.isUndefined(fee.unit_fee) && fee.unit_fee > 0) {
      data_fee.push(feeFlagByKey(fee.unit_fee));
    }
    if (!_.isUndefined(fee.order_fee) && fee.order_fee > 0) {
      data_fee.push(feeFlagByKey(fee.order_fee, "offer_fee_by_order_rate"));
    }
    return data_fee;
  };

  function feeFlagByKey(fee, key) {
    if (!_.isUndefined(key)) {
      return I18n.t("staff.order_cases.special_offer_flag.offer_fee_by_order_rate", {count: numberWithCommas(fee)});
    } else {
      return I18n.t("staff.order_cases.special_offer_flag.offer_fee_by_unit_rate", {count: numberWithCommas(fee)});
    }
  }

  function numberWithCommas(price) {
    return price.toString().replace(FORMAT_PRICE, ",");
  };
}
