"use strict";

angular.module("staffApp")
  .factory("staffEntryFrmCommonService", ["common", "staffEntryFrmConsts", "postalCodeService",
    "stationService", staffEntryFrmCommonService]);

function staffEntryFrmCommonService(common, staffEntryFrmConsts, postalCodeService,
  stationService) {
  var ADDRESS_ATTRS = staffEntryFrmConsts.ADDRESS_ATTRS;
  var EMERGENCY_ADDRESS_ATTRS = staffEntryFrmConsts.EMERGENCY_ADDRESS_ATTRS;
  var STEP_1_DIFFER_ADDRESS_EMERGENCY_INPUT = staffEntryFrmConsts.STEP_1_DIFFER_ADDRESS_EMERGENCY_INPUT;
  var STEP_1_DIFFER_ADDRESS_EMERGENCY_SELECT = staffEntryFrmConsts.STEP_1_DIFFER_ADDRESS_EMERGENCY_SELECT;
  var service = {
    previewImg: previewImg,
    initOverViewSteps: initOverViewSteps,
    ableToSearch: ableToSearch,
    prefixForStep1And4: prefixForStep1And4,
    prefixForStep2: prefixForStep2,
    prefixForStep3: prefixForStep3,
    prefixForStaffProfile: prefixForStaffProfile,
    getStepInfo: getStepInfo,
    removeFalseItem: removeFalseItem,
    checkTimeWorked: checkTimeWorked,
    isChosen: isChosen,
    isStep1CheckboxFieldsValid: isStep1CheckboxFieldsValid,
    searchStaffPostalCode: searchStaffPostalCode,
    searchPostalCode: searchPostalCode,
    watchStation: watchStation,
    watchBirthday: watchBirthday,
    choosePostalCode: choosePostalCode,
    searchPostalCodeByExactlyCode: searchPostalCodeByExactlyCode,
    watchDifferCurrentAddr: watchDifferCurrentAddr,
    watchResidencePermission: watchResidencePermission,
    isConvLawsonEndTimeLessOneYear: isConvLawsonEndTimeLessOneYear,
    watchResidenceExpirationDate: watchResidenceExpirationDate
  }

  return service;

  function previewImg(event, previewElm) {
    var reader = new FileReader();

    reader.onload = function(evt) {
      $(previewElm).attr("src", evt.target.result);
    }

    reader.readAsDataURL(event.target.files[0]);
  }

  function initOverViewSteps() {
    return [
      {
        id: 1,
        name: I18n.t("staff.entry.steps_overview.basic_info"),
        itemNumberClassName: "stepbystep__item--5",
        itemClassName: "stepbystep__item--info",
        iconClassName: "stepbystep__icon--info",
        active: true,
        done: false
      },
      {
        id: 2,
        name: I18n.t("staff.entry.steps_overview.employment_history"),
        itemNumberClassName: "stepbystep__item--5",
        itemClassName: "stepbystep__item--jobhistory",
        iconClassName: "stepbystep__icon--jobhistory",
        active: false,
        done: false
      },
      {
        id: 3,
        name: I18n.t("staff.entry.steps_overview.bank_account"),
        itemNumberClassName: "stepbystep__item--5",
        itemClassName: "stepbystep__item--bankacc",
        iconClassName: "stepbystep__icon--bankacc",
        active: false,
        done: false
      },
      {
        id: 4,
        name: I18n.t("staff.entry.steps_overview.other"),
        itemNumberClassName: "stepbystep__item--5",
        itemClassName: "stepbystep__item--last",
        iconClassName: "stepbystep__icon--other",
        active: false,
        done: false
      }
    ];
  }

  function ableToSearch(postalCode) {
    if (!postalCode) {return;}
    var validCode = postalCode.replace(/\-/g, "").trim();
    return window.config.postalCode.test(validCode) && validCode.length === POSTAL_CODE_SEARCH_NUMBER;
  }

  function prefixForStep1And4(element) {
    return "staff[" + element + "]";
  }

  function prefixForStaffProfile(element) {
    return "edited_staff[" + element + "]";
  }

  function prefixForStep2(element) {
    if (_.includes(staffEntryFrmConsts.STEP_2_EXPECTATION_SHORT_TERM_REQUIRED_FIELDS, element)) {
      return "staff[staff_expectation_attributes][" + element + "]";
    }
    return "staff[staff_employment_history_attributes][" + element + "]";
  }

  function prefixForStep3(element) {
    return "staff[staff_account_transfer_attributes][" + element + "]";
  }

  function getStepInfo(step) {
    var stepInfo = {};
    switch (step) {
      case "step1":
        stepInfo = {requiredFields: staffEntryFrmConsts.STEP_1_REQUIRED_INPUT_FIELDS,
          stepFunc: "staffEntryFrmCommonService.prefixForStep1And4"};
        break;
      case "step2":
        stepInfo = {requiredFields: staffEntryFrmConsts.STEP_2_ALL_HISTORY_ATTRS
          .concat(staffEntryFrmConsts.STEP_2_EXPECTATION_SHORT_TERM_REQUIRED_FIELDS),
          stepFunc: "staffEntryFrmCommonService.prefixForStep2"};
        break;
      case "step3":
        stepInfo = {requiredFields: staffEntryFrmConsts.STEP_3_REQUIRED_INPUT_FIELDS,
          stepFunc: "staffEntryFrmCommonService.prefixForStep3"};
        break;
      case "step4":
        stepInfo = {requiredFields: staffEntryFrmConsts.STEP_4_REQUIRED_INPUT_FIELDS,
          stepFunc: "staffEntryFrmCommonService.prefixForStep1And4"};
        break;
      case "profile":
        stepInfo = {requiredFields: staffEntryFrmConsts.STEP_1_REQUIRED_INPUT_FIELDS,
          stepFunc: "staffEntryFrmCommonService.prefixForStaffProfile"};
        break;
    };
    return stepInfo;
  }

  function removeFalseItem(array){
    var result = []
    angular.forEach(array, function(val, key) {
      if(val){result.push(parseInt(key));}
    });
    return result;
  }

  function checkTimeWorked(timeEnd, timeStart){
    if (timeEnd && (timeEnd < timeStart)) {
      return "error-end-date";
    } else {
      return "";
    }
  }

  function isChosen(itemList, item) {
    return _.includes(itemList, item);
  }

  function isStep1CheckboxFieldsValid(career_up_ids) {
    var careerUpIds = removeFalseItem(career_up_ids);
    if (careerUpIds.length == 0) {return false;}
    return true;
  }

  function searchStaffPostalCode(context, isPostalCodeOfStaff) {
    angular.element("#list-postal-code").modal("show");
    context.postalCodeSearch.page = 1;
    context.isPostalCodeOfStaff = isPostalCodeOfStaff;
    context.postalCodeSearch.code = "";
    searchPostalCode(context);
  }

  function searchPostalCode(context, isResetPage) {
    if (isResetPage) context.postalCodeSearch.page = 1;
    context.postalCodeSearch.postal_code = angular.copy(context.postalCodeSearch.code);
    postalCodeService.getPostalCode({
      postal_code: context.postalCodeSearch.postal_code, page: context.postalCodeSearch.page
    }).then(function(res) {
      angular.extend(context.postalCodeSearch, res.data);
      context.postalCodeSearch.hasRecord = Boolean(context.postalCodeSearch.postal_codes.length);
      context.postalCodeSearch.limit = res.data.pagination_limit;
    });
  };

  function setAddress(context, obj, postalCode) {
    if (context.isPostalCodeOfStaff) {
      context[obj].selected_prefecture_id = postalCode.prefecture.name;
    } else {
      context[obj].selected_emergency_prefecture_id = postalCode.prefecture.name;
    }
    ADDRESS_ATTRS.forEach(function(attr) {
      var staffField = context.isPostalCodeOfStaff ? attr : "emergency_" + attr;
      if (attr != "street_number" || (attr === "street_number" && !postalCode.duplicate_code)) {
        context[obj][staffField] = postalCode[attr];
        $("#" + staffField).val(context[obj][staffField]).trigger("input");
      } else {
        context[obj][staffField] = "";
      }
    });
  }

  function choosePostalCode(context, obj, postalCodeId) {
    var postalCode = context.postalCodeSearch.postal_codes.filter(function(item) {
      return item.id == postalCodeId;
    })[0];
    setAddress(context, obj, postalCode);
    angular.element("#list-postal-code").modal("hide");
  }

  function searchPostalCodeByExactlyCode(context, obj, field) {
    var postalCodeVal = context[obj][field];
    if (ableToSearch(postalCodeVal)) {
      postalCodeService.getPostalCode({postal_code: postalCodeVal}).then(function(res) {
        var postalCodes = res.data.postal_codes;
        if (postalCodes.length > 0) {
          context.isPostalCodeOfStaff = _.isEqual(field, "postal_code");
          setAddress(context, obj, postalCodes[0]);
        }
      });
    }
  }

  function watchStation(context, obj) {
    if (context[obj].prefecture_id) {
      stationService.getStation({prefecture_id: context[obj].prefecture_id}).then(function(res) {
        context.homeStations = res.data.stations;
      });
    } else {
      context.homeStations = [];
      reInitStationData(context[obj], "home");
    }

    if (_.isEmpty(context.edited_staff)) {
      reInitStationData(context[obj], "home");
    } else {
      if(context.isInitedHomeStation) reInitStationData(context[obj], "home");
      else context.isInitedHomeStation = true;
    }
  }

  function reInitStationData(obj, type) {
    obj[type + "_station_id"] = "";
  }

  function watchBirthday(context, obj) {
    if(!Date.parse(context[obj].birthday)) {
      context.entryForm[""+obj+"[birthday]"].$valid = false;
      context.entryForm[""+obj+"[birthday]"].$error = {"required": true};
    } else if(isAgeLessThan15(context[obj].birthday)) {
      context.entryForm[""+obj+"[birthday]"].$valid = false;
      context.entryForm[""+obj+"[birthday]"].$error = {"age-must-over15": true};
    } else {
      context.entryForm[""+obj+"[birthday]"].$valid = true;
      context.entryForm[""+obj+"[birthday]"].$error = {};
    }
  }

  function watchResidenceExpirationDate(context, obj) {
    if (_.isEmpty(context[obj].residence_expiration_date) && context[obj].residence_status != NOT_REQUIRED_RESIDENSE_STATUS_ID) {
      context.entryForm[""+obj+"[residence_expiration_date]"].$valid = false;
      context.entryForm[""+obj+"[residence_expiration_date]"].$error = {"required": true};
    } else if (isDateLessThanCurrentDate(context[obj].residence_expiration_date)) {
      context.entryForm[""+obj+"[residence_expiration_date]"].$valid = false;
      context.entryForm[""+obj+"[residence_expiration_date]"].$error = {"residence-expiration-date-must-than-current-date": true};
      context.entryForm.$error["residence-expiration-date-must-than-current-date"] = [context.entryForm[""+obj+"[residence_expiration_date]"]];
    } else {
      context.entryForm[""+obj+"[residence_expiration_date]"].$valid = true;
      context.entryForm[""+obj+"[residence_expiration_date]"].$error = {};
      delete context.entryForm.$error["residence-expiration-date-must-than-current-date"];
    }
  }

  function watchDifferCurrentAddr(context, obj) {
    if (context[obj].is_emergency_address_differ_current_address == 1) {
      EMERGENCY_ADDRESS_ATTRS.forEach(function(attr) {
        context[obj][attr] = _.isEmpty(context.edited_staff) ? "" : context.edited_staff[attr];
      });
      resetTouchedEmergencyField(context, obj);
    } else {
      ADDRESS_ATTRS.forEach(function(attr) {
        context[obj]["emergency_" + attr] = angular.copy(context[obj][attr]);
      });
    }
  }

  function watchResidencePermission(context, obj) {
    context.entryForm[obj + "[residence_permission_validity]"].$touched = false;
  }

  function isConvLawsonEndTimeLessOneYear(timeStart, timeEnd) {
    if (_.isEmpty(timeEnd) && !_.isEmpty(timeStart)) {
      return true;
    } else if (!_.isEmpty(timeEnd)) {
      var now = moment().format("YYYY/MM/DD");
      var add1year = moment(timeEnd).add(1, 'years').format("YYYY/MM/DD");
      if (add1year >= now) return true;
    } else {
      return false;
    }
  }

  function isAgeLessThan15(birthday) {
    var years = moment().diff(birthday, "years", true);
    if (years < 15) return true;
    return false;
  }

  function resetTouchedEmergencyField(context, obj) {
    STEP_1_DIFFER_ADDRESS_EMERGENCY_INPUT.forEach(function(field) {
      context.entryForm[obj + "[" + field + "]"].$touched = false;
    })

    STEP_1_DIFFER_ADDRESS_EMERGENCY_SELECT.forEach(function(field) {
      context["staff_" + field + "_blur"] = false;
    });
  }

  function isDateLessThanCurrentDate(input_date) {
    var input_date = Date.parse(input_date);
    var current_date = Date.parse(moment().format(FULL_DATE_FORMAT));
    return input_date < current_date;
  }
}
