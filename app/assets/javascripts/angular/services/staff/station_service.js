"use strict";

angular.module("staffApp")
  .factory("stationService", ["common", stationService]);

function stationService(common) {
  var service = {
    getStation: getStation,
    getStationByName: getStationByName
  }

  return service;

  function getStation(params) {
    return common.ajaxCall("GET", "/stations", params);
  }

  function getStationByName(params) {
    return common.ajaxCall("GET", "/stations_by_name", params);
  }
}
