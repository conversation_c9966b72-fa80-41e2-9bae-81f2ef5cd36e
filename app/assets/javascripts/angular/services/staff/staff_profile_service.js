"use strict";

angular.module("staffApp")
  .factory("staffProfileService", ["common", staffProfileService]);

function staffProfileService(common) {
  var service = {
    setFormatDate: setFormatDate,
    updateWorkExperiences: updateWorkExperiences,
    getDataTabWorkExperiences: getDataTabWorkExperiences
  }

  return service;

  function setFormatDate() {
    $('.js-only-month').datetimepicker({
      locale: 'ja',
      format: DATE_MONTH_YEAR_FORMAT,
      viewMode: DATE_MONTH_YEAR_VIEW_MODE
    });
  }

  function updateWorkExperiences(params) {
    return common.ajaxCall("POST", "/profiles/update_work_experiences", params);
  }

  function getDataTabWorkExperiences() {
    return common.ajaxCall("GET", "/profiles/work_experience_data");
  }
}
