"use strict";

angular.module("staffApp")
  .factory("topPageService", ["common", topPageService]);

function topPageService(common) {
  var service = {
    getData: getData,
    getNotifications: getNotifications,
    removeNotifications: removeNotifications,
    getNewNotifications: getNewNotifications,
    turnOffNotificationPopup: turnOffNotificationPopup,
    disableNotificationDisplay: disableNotificationDisplay, // ! Deprecated
    getOrderCasesData: getOrderCasesData,
    getLocationsData: getLocationsData
  }

  return service;

  function getData() {
    return common.ajaxCall("GET", "/" + I18n.locale + "/");
  }

  function getOrderCasesData() {
    return common.ajaxCall("GET", "/" + I18n.locale + "/top_pages/order_cases_list");
  }

  function getLocationsData() {
    return common.ajaxCall("GET", "/" + I18n.locale + "/top_pages/locations_list");
  }

  function getNotifications(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/top_pages/notifications", params);
  }

  function getNewNotifications(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/top_pages/new_notifications", params);
  }

  function turnOffNotificationPopup() {
    return common.ajaxCall("POST", "/" + I18n.locale + "/top_pages/turn_off_notification_popup/");
  }

  function removeNotifications(params) {
    return common.ajaxCall("GET", "/my_page", params);
  }

  function disableNotificationDisplay() {
    return common.ajaxCall("GET", "/top_pages/disable_notification_display");
  }
}
