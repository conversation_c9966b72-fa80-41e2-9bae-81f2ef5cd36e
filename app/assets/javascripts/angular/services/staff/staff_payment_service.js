"use strict";

angular.module("staffApp")
  .factory("staffPaymentService", ["common", staffPaymentService]);

function staffPaymentService(common) {
  var service = {
    loadDataArrangements: loadDataArrangements,
    confirmRequest: confirmRequest,
    submitRequest: submitRequest
  }

  return service;

  function loadDataArrangements(params) {
    return common.ajaxCall("GET", "/payments/load_data_arrangements", params);
  }

  function confirmRequest(params) {
    return common.ajaxCall("GET", "/payments/confirm_request", params);
  }

  function submitRequest(params) {
    return common.ajaxCall("POST", "/payments/submit_request", params);
  }
}
