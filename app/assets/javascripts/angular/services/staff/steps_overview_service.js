"use strict";

angular.module("staffApp")
  .factory("stepsOverviewService", [stepsOverviewService]);

function stepsOverviewService() {
  var service = {
    initExpRegistrationSteps: initExpRegistrationSteps,
    initExpProfileSteps: initExpProfileSteps,
    initNonExpSteps: initNonExpSteps
  }

  return service;

  function initExpRegistrationSteps(activeStep) {
    return [
      {
        id: 1,
        name: I18n.t("staff.entry.steps_overview.registration_answer"),
        itemNumberClassName: "stepbystep__item--2",
        itemClassName: "stepbystep__item--info",
        iconClassName: "stepbystep__icon--info",
        active: activeStep == 1,
        done: activeStep > 1
      },
      {
        id: 2,
        name: I18n.t("staff.entry.steps_overview.interview_schedule"),
        itemNumberClassName: "stepbystep__item--2",
        itemClassName: "stepbystep__item--last",
        iconClassName: "stepbystep__icon--info",
        active: activeStep == 2,
        done: activeStep > 2
      },
    ];
  }

  function initExpProfileSteps(activeStep) {
    return [
      {
        id: 1,
        name: I18n.t("staff.entry.steps_overview.basic_info"),
        itemNumberClassName: "stepbystep__item--3",
        itemClassName: "stepbystep__item--info",
        iconClassName: "stepbystep__icon--info",
        active: activeStep == 1,
        done: activeStep > 1
      },
      {
        id: 2,
        name: I18n.t("staff.entry.steps_overview.upload_document"),
        itemNumberClassName: "stepbystep__item--3",
        itemClassName: "stepbystep__item--info",
        iconClassName: "stepbystep__icon--info",
        active: activeStep == 2,
        done: activeStep > 2
      },
      {
        id: 3,
        name: I18n.t("staff.entry.steps_overview.bank_account"),
        itemNumberClassName: "stepbystep__item--3",
        itemClassName: "stepbystep__item--last",
        iconClassName: "stepbystep__icon--info",
        active: activeStep == 3,
        done: activeStep > 3
      },
    ];
  }

  function initNonExpSteps(activeStep) {
    return [
      {
        id: 1,
        name: I18n.t("staff.entry.steps_overview.registration_answer"),
        itemNumberClassName: "stepbystep__item--2",
        itemClassName: "stepbystep__item--info",
        iconClassName: "stepbystep__icon--info",
        active: activeStep == 1,
        done: activeStep > 1
      },
      {
        id: 2,
        name: I18n.t("staff.entry.steps_overview.training_schedule"),
        itemNumberClassName: "stepbystep__item--2",
        itemClassName: "stepbystep__item--last",
        iconClassName: "stepbystep__icon--info",
        active: activeStep == 2,
        done: activeStep > 2
      }
    ];
  }
}
