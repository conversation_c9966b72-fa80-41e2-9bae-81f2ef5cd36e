"use strict";

angular.module("staffApp")
  .factory("staffEducationService", ["common", staffEducationService]);

function staffEducationService(common) {
  var service = {
    updateEducations: updateEducations,
    getDataTabEducations: getDataTabEducations
  }

  return service;

  function updateEducations(params) {
    return common.ajaxCall("POST", "/profiles/update_educations", params);
  }

  function getDataTabEducations() {
    return common.ajaxCall("GET", "/profiles/education_data");
  }
}
