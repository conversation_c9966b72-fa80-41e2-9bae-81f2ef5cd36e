"use strict";

angular.module("staffApp").factory("otpVerificationService", ["common", otpVerificationService]);

function otpVerificationService(common) {
  var service = {
    verifyOtp: verifyOtp,
    resendOtp: resendOtp,
    staffVerifyOtp: staffVerifyOtp,
    staffResendOtp: staffResendOtp,
  }

  return service;

  function verifyOtp(params) {
    return common.ajaxCall("POST", "/otps/verify", params);
  }

  function resendOtp(params) {
    return common.ajaxCall("POST", "/otps/resend", params);
  }

  function staffVerifyOtp(params) {
    return common.ajaxCall("POST", "/staff_otps/verify", params);
  }

  function staffResendOtp(params) {
    return common.ajaxCall("POST", "/staff_otps/resend", params);
  }
}
