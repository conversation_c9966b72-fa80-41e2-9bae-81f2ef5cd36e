"use strict";

angular.module("staffApp")
  .factory("staffEntryFrmConsts", ["common", staffEntryFrmConsts]);

function staffEntryFrmConsts(common) {
  var _this = this;

  _this.ADDRESS_ATTRS = ["postal_code", "prefecture_id", "city", "street_number", "house_number", "building"];
  _this.EMERGENCY_ADDRESS_ATTRS = ["emergency_postal_code", "emergency_prefecture_id",
    "emergency_city", "emergency_street_number", "emergency_house_number", "emergency_building",
    "selected_emergency_prefecture_id"];
  _this.STEP_1_ALL_ATTRS = ["name", "name_kana", "birthday", "gender_id", "postal_code", "prefecture_id", 
    "city", "street_number", "house_number", "building", "home_station_id", "school_station_id",
    "email", "tel", "home_tel", "social_attribute","social_attribute_other", "registration_history",
    "is_emergency_address_differ_current_address", "emergency_postal_code", "emergency_prefecture_id",
    "emergency_city", "emergency_street_number", "emergency_house_number", "emergency_building",
    "emergency_name", "emergency_relationship_id", "emergency_tel",
    "nationality", "residence_status", "residence_validity", "residence_expiration_date",
    "residence_permission", "residence_permission_validity", "residence_img_front", "residence_number",
    "residence_img_back", "certificate_img", "staff_code", "residence_card_name", "other_nationality",
    "entry_uniform_size", "entry_pant_size", "entry_shoes_size"];
  _this.STEP_2_ALL_HISTORY_ATTRS = ["convenience_lawson_shop", "partime_place_name", "partime_start", "id"];
  _this.STEP_2_CONVENIENCE_FIELDS = ["convenience_lawson", "convenience_7eleven", "convenience_family", "convenience_other"];
  _this.STEP_2_ALL_EXPECTATION_ATTRS = ["short_term_note", "is_working_car", "expectation_employment_type",
    "monday_start_time_1", "monday_end_time_1", "is_monday",
    "tuesday_start_time_1", "tuesday_end_time_1", "is_tuesday", "wednesday_start_time_1", "wednesday_end_time_1", "is_wednesday",
    "friday_start_time_1", "friday_end_time_1", "is_friday", "thursday_start_time_1", "thursday_end_time_1", "is_thursday",
    "saturday_start_time_1", "saturday_end_time_1", "is_saturday", "sunday_start_time_1", "sunday_end_time_1", "is_sunday", "id", "work_condition"];
  _this.STEP_2_EXPECTATION_SHORT_TERM_REQUIRED_FIELDS = ["nearest_station_price", "training_start_time", "monday_start_time_1",
    "monday_end_time_1", "tuesday_start_time_1", "tuesday_end_time_1", "wednesday_start_time_1", "wednesday_end_time_1",
    "friday_start_time_1", "friday_end_time_1", "thursday_start_time_1", "thursday_end_time_1", "saturday_start_time_1",
    "saturday_end_time_1", "sunday_start_time_1", "sunday_end_time_1"];
  _this.STEP_2_PART_TIME_FIELDS = ["partime_place_name", "partime_start", "partime_end"];
  _this.STEP_2_PART_TIME_EXPERIENCES = ["is_convenience_lawson", "is_convenience_7eleven", "is_convenience_family",
    "is_convenience_other"]
  _this.STEP_2_EMP_HISTORY_DATETIME_FIELDS = ["partime_start", "partime_end"];
  _this.STEP_2_EXPECTATION_DATETIME_FIELDS = ["first_training_date"];
  _this.STEP_2_REQUIRED_SELECT_FIELDS = ["work_condition"];
  _this.STEP_3_ALL_ATTRS = ["staff_id", "bank_id", "bank_branch_id", "account_name",
    "account_name_kana", "account_type", "account_number", "passbook_pic", "id"];
  _this.STEP_4_ALL_ATTRS = ["is_agree_handling", "is_agree_contract",
    "is_expectation_condition", "payment_request", "accept_my_number_requirement"];
  _this.STEP_3_FILE_FIELDS = ["passbook_pic"];
  _this.STEP_3_REQUIRED_SELECT_FIELDS = ["bank_id", "bank_branch_id"];
  _this.DATA_HOLDER_IDS = ["relationships", "registration_histories", "japan", "nationalities",
    "residence_statuses", "prefectures", "residence_permissions", "banks",
    "specific_activity_residence_statuses", "edited_staff", "edited_staff_account_transfer",
    "edited_staff_employment_history", "edited_staff_expectation", "edited_staff_service_work_types",
    "edited_staff_other_work_types", "edited_staff_office_work_types", "other_nationality",
    "has_residence_permission", "uniform_sizes", "shoes_sizes", "pant_sizes"];
  _this.DATA_SELECT_BOX_ATTR = ["relationships", "registration_histories", "nationalities",
    "residence_statuses", "prefectures", "residence_permissions", "banks",
    "specific_activity_residence_statuses"];
  _this.STEP_1_RESIDENCE_FILE_FIELDS = ["residence_img_front", "residence_img_back", "certificate_img"];
  _this.STEP_1_JP_RESIDENCE_FILE_FIELDS = ["jp_residence_img_front", "jp_residence_img_back"];
  _this.STEP_1_RESIDENCE_SELECT_FIELDS = ["residence_status", "residence_permission"];
  _this.STEP_1_RESIDENCE_DATETIME_FIELDS = ["residence_validity", "residence_expiration_date", "residence_permission_validity"];
  _this.STEP_1_RESIDENCE_INPUT_FIELDS = ["other_nationality", "residence_validity", "residence_expiration_date", "residence_permission_validity", "residence_card_name", "residence_number"];
  _this.STEP_1_REQUIRED_SELECT_FIELDS = ["prefecture_id", "nationality", "residence_status", "residence_permission",
    "emergency_relationship_id", "emergency_prefecture_id", "home_station_id", "uniform_sizes", "shoes_sizes", "pant_sizes"];
  _this.STEP_1_DATETIME_FIELDS = ["birthday", "residence_validity", "residence_permission_validity",
    "residence_expiration_date"];
  _this.STEP_1_REQUIRED_INPUT_FIELDS = ["name", "name_kana", "birthday", "postal_code", "city", "street_number", "house_number",
    "email", "tel", "other_nationality", "residence_validity", "residence_expiration_date", "residence_permission_validity",
    "emergency_name", "emergency_tel", "emergency_postal_code", "emergency_city", "emergency_street_number", "emergency_house_number",
    "home_tel", "residence_number"];
  _this.STEP_3_REQUIRED_INPUT_FIELDS = ["account_first_name", "account_last_name", "account_first_name_kana", "account_last_name_kana",
    "account_number", "account_middle_name_1", "account_middle_name_2", "account_middle_name_3",
    "account_middle_name_kana_1", "account_middle_name_kana_2", "account_middle_name_kana_3"];
  _this.STEP_4_REQUIRED_INPUT_FIELDS = ["is_agree_handling", "is_expectation_condition", "accept_my_number_requirement"];
  _this.ERROR_KEY_FROM_SERVER_MAPPINGS = {
    "blank": "required",
    "wrong_format": "pattern",
    "katakana_name": "pattern",
    "too_long": "maxlength"
  };
  _this.ERROR_FORM_STEP = ["is_display_error_step1", "is_display_error_step2",
    "is_display_error_step3", "is_display_error_step4"];

  _this.ALLOWED_IMG_TYPES = ["image/jpeg", "image/png", "image/jpg"];
  _this.STEP_1_DIFFER_ADDRESS_EMERGENCY_INPUT = ["emergency_postal_code", "emergency_city", "emergency_street_number", "emergency_house_number", "emergency_building"];
  _this.STEP_1_DIFFER_ADDRESS_EMERGENCY_SELECT = ["emergency_prefecture_id"];
  _this.MIDD_NAME_FIELDS = ["middle_name_1", "middle_name_2", "middle_name_3"];
  return _this;
}
