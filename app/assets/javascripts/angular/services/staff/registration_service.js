"use strict";

angular.module("staffApp")
  .factory("registrationService", ["common", registrationService]);

function registrationService(common) {
  var service = {
    validateExistedPostalCode: validateExistedPostalCode,
    validateUniqueTel: validateUniqueTel,
    registerPhone: registerPhone,
    verifyPhone: verifyPhone,
    register: register
  }

  return service;

  function validateExistedPostalCode(params) {
    return common.ajaxCall("GET", "/registrations/check_existed_postal_code", params);
  }

  function validateUniqueTel(params) {
    return common.ajaxCall("GET", "/registrations/check_tel_uniq", params);
  }

  function registerPhone(params) {
    return common.ajaxCall("POST", "/registrations/register_phone", params);
  }

  function verifyPhone(params) {
    return common.ajaxCall("POST", "/registrations/verify_phone", params);
  }

  function register(params) {
    return common.ajaxCall("POST", "/registrations", params);
  }
}
