"use strict";

angular.module("staffApp")
  .factory("staffScheduleService", ["common", staffScheduleService]);

function staffScheduleService(common) {
  var service = {
    list: list,
    update: update
  }

  function list() {
    return common.ajaxCall("GET", "/staff_schedules");
  }

  function update(params) {
    return common.ajaxCall("PUT", "/staff_schedules/update", params);
  }

  return service;
}
