"use strict";

angular.module("staffApp")
  .factory("orderCaseService", ["common", orderCaseService]);

function orderCaseService(common) {
  var LOCATION_TYPE = {
    not_lawson: "not_lawson",
    lawson: "ローソン",
    nature_lawson: "ナチュラルローソン",
    lawson_store_100: "ローソンストア100"
  };
  var BG_CLASS = {
    not_lawson: "bg-not-lawson",
    nature_lawson: "bg-nature-lawson",
    lawson_store_100: "bg-lawson-store"
  }
  var service = {
    getOrderCases: getOrderCases,
    createStaffKeepOrderCase: createStaffKeepOrderCase,
    applyOrderCase: applyOrderCase,
    createStaffLikeLocation: createStaffLikeLocation,
    updateArrangement: updateArrangement,
    createLocationEvaluation: createLocationEvaluation,
    updateWorkAchievement: updateWorkAchievement,
    removeStaffLikeLocation: removeStaffLikeLocation,
    getOfferJobs: getOfferJobs,
    getKeepOrderCases: getKeepOrderCases,
    getHistoryWorkLocations: getHistoryWorkLocations,
    submitLikeLocation: submitLikeLocation,
    checkApplyCondition: checkApplyCondition,
    checkInsuranceOfStaff: checkInsuranceOfStaff,
    checkInsuranceForWorkAchievement: checkInsuranceForWorkAchievement,
    saveEmploymentInsurance: saveEmploymentInsurance,
    bgImageForLocation: bgImageForLocation,
    bgForNotLawson: bgForNotLawson,
    getSimilarOrderCases: getSimilarOrderCases,
    bgForNotLawsonBigSize: bgForNotLawsonBigSize,
    checkInterviewTime: checkInterviewTime
  }

  return service;

  function getOrderCases(params, locale) {
    return common.ajaxCall("GET", "/" + locale + "/order_cases", params);
  };

  function getSimilarOrderCases(id, locale) {
    return common.ajaxCall("GET", "/" + locale + "/similar_order_cases/" + id);
  };

  function createStaffKeepOrderCase(params, locale) {
    return common.ajaxCall("POST", "/" + locale + "/staff_keep_order_cases", params);
  };

  function applyOrderCase(params, locale) {
    return common.ajaxCall("POST", "/" + locale + "/staffs/apply_order_case", params);
  }

  function createStaffLikeLocation(params, locale) {
    return common.ajaxCall("POST", "/" + locale + "/staff_like_locations", params);
  }

  function updateArrangement(params, arrangement_id) {
    return common.ajaxCall("PUT", "/" + I18n.locale + "/arrangements/" + arrangement_id, params);
  };

  function createLocationEvaluation(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/location_evaluations/", params);
  };

  function updateWorkAchievement(id, params) {
    return common.ajaxCall("PUT", "/" + I18n.locale + "/work_achievements/" + id, params);
  };

  function getKeepOrderCases(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/staff_keep_order_cases/", params);
  };

  function removeStaffLikeLocation(id) {
    return common.ajaxCall("DELETE", "/" + I18n.locale + "/staff_like_locations/" + id);
  }

  function getOfferJobs(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/offer_jobs", params);
  };

  function getHistoryWorkLocations(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/history_work_locations", params);
  };

  function submitLikeLocation(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/staff_like_locations", params);
  };

  function checkInsuranceOfStaff(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/check_insurance_of_staff", params);
  };

  function checkApplyCondition(params) {
    return common.ajaxCall("PUT", "/" + I18n.locale + "/check_apply_condition", params);
  };

  function checkInsuranceForWorkAchievement(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/order_cases/check_insurance_for_work_achievement", params);
  };

  function saveEmploymentInsurance(params) {
    return common.ajaxCall("PUT", "/" + I18n.locale + "/order_cases/save_employment_insurance", params);
  };

  function checkInterviewTime(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/order_cases/check_interview_time", params);
  }

  function bgImageForLocation(locationType, categoryThumbnail) {
    if ((!!categoryThumbnail && categoryThumbnail != '') || locationType === LOCATION_TYPE.not_lawson){
      return BG_CLASS.not_lawson;
    }
    if (locationType === LOCATION_TYPE.lawson_store_100) {return BG_CLASS.lawson_store_100;}
    if (locationType === LOCATION_TYPE.nature_lawson) {return BG_CLASS.nature_lawson;}
  }

  function bgForNotLawson(locationType, locationImage, categoryThumbnail) {
    if (!!categoryThumbnail && categoryThumbnail != '') {
      return {'background-image': 'linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), url(' + categoryThumbnail +')'};
    }
    if (locationType == LOCATION_TYPE.not_lawson && !!locationImage && locationImage != '') {
      return {'background-image': 'linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), url(' + locationImage +')'};
    }
  }

  function bgForNotLawsonBigSize(locationType, locationImage, categoryThumbnail) {
    if (!!categoryThumbnail && categoryThumbnail != '') {
      return {'background-image': 'linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), url(' + categoryThumbnail +')'};
    }
    if (locationType == LOCATION_TYPE.not_lawson && !!locationImage && locationImage != '') {
      return {
        'background-image': 'linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), url(' + locationImage +')',
        'background-size': 'contain'
      };
    }
  }
};
