"use strict";

angular.module("staffApp")
  .factory("registeredProfileService", ["common", registeredProfileService]);

function registeredProfileService(common) {
  var service = {
    validateStep1: validateStep1,
    validateStep2: validateStep2,
    validateStep3: validateStep3,
    validateStep4: validateStep4
  }

  return service;

  function validateStep1(params) {
    return common.ajaxCall("POST", "/registered_profiles/step_1", params);
  }

  function validateStep2(params) {
    return common.ajaxCall("POST", "/registered_profiles/step_2", params);
  }

  function validateStep3(params) {
    return common.ajaxCall("POST", "/registered_profiles/step_3", params);
  }

  function validateStep4(params) {
    return common.ajaxCall("POST", "/registered_profiles/step_4", params);
  }
}
