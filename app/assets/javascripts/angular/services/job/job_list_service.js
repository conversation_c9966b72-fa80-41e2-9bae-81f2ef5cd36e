"use strict";

angular.module("jobApp")
  .factory("jobListService", ["common", jobListService]);

function jobListService(common) {
  var service = {
    getJobs: getJobs,
    getLocationJobs: getLocationJobs,
    getLocationJobStatuses: getLocationJobStatuses,
    createStaffLikeLocation: createStaffLikeLocation,
    removeStaffLikeLocation: removeStaffLikeLocation,
    createSearchCondition: createSearchCondition,
    getNextDateHasJob: getNextDateHasJob,
    getRemainedJobs: getRemainedJobs
  };

  return service;

  function getJobs(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/jobs", params);
  }

  function getNextDateHasJob(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/jobs/next_date_has_job", params);
  }

  function getRemainedJobs(params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/jobs/remained_jobs", params);
  }

  function getLocationJobs(locationId, params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/locations/" + locationId + "/jobs", params);
  }

  function getLocationJobStatuses(locationId, params) {
    return common.ajaxCall("GET", "/" + I18n.locale + "/locations/" + locationId + "/jobs_status", params);
  }

  function createStaffLikeLocation(params) {
    return common.ajaxCall("POST", "/" + I18n.locale + "/staff_like_locations", params);
  }

  function removeStaffLikeLocation(id) {
    return common.ajaxCall("DELETE", "/" + I18n.locale + "/staff_like_locations/" + id);
  }

  function createSearchCondition(params) {
    return common.ajaxCall("POST", "/search_conditions", params);
  }
}
