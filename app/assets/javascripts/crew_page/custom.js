// var FORMAT_PRICE = /\B(?=(\d{3})+(?!\d))/g;
// var webUrl = "http://crew.workz.local:3000/";
// $.ajax({
//   dataType: "json",
//   url: webUrl,
//   success: function(jobs) {
//     if (jobs.length == 0 ){
//       hideJobList();
//       return;
//     }
//     showJobList();
//     var template = $('#job-template').html();
//     $.each(jobs, function (index, job) {
//       var result = compileJobTemplate(template, job);
//       $('#jobs').append(result);
//     });
//     createSlide();
//   },
//   error: function (error) {
//     hideJobList();
//   }
// });

// function hideJobList() {
//   $('.jobs-content').addClass("d-none");
// }

// function showJobList() {
//   $('.jobs-content').removeClass("d-none");
// }

// function compileJobTemplate(template, job){
//   var ex = /{(.+)}/;
//   for (var key in job) {
//     template = template.replace(RegExp('{' + key + '}', 'g'), renderJobTag(key, job));
//   }
//   template = template.replace(RegExp('{job_salary}', 'g'), renderSalaryTag(job));
//   template = template.replace(/{\w+}/g, '')
//   return template;
// }
  
// function renderJobTag(key, job) {
//   if (key == 'segment_regular') {
//     return !!job[key] ? "<span class='st-work__status st-similar__status st-similar__status--regular'><span>連日</span></span>" : "";
//   }
//   if (key == 'is_time_changable') {
//     return !!job[key] ? "<span class='st-work__status st-similar__status st-similar__status--adjustable'><span>調整可</span></span>" : "";
//   }
//   if (key == 'break_time') {
//     return (job[key] > 0) ? "<span>" +  job[key] + "分</span>" : "<span>休憩なし</span>";
//   }
//   return job[key];
// }

// function renderSalaryTag(job) {
//   if (!job["order_fee"] && !job["unit_fee"]) { return '<span></span>';}
//   var text = '';
//   if (job["order_fee"] > 0) {
//     text += "<span class='st-work__tag' >+" + numberWithCommas(job["order_fee"]) + "円 増額中！</span>"
//   }
//   if (job["unit_fee"] > 0) {
//     text += "<span class='st-work__tag' >+" + numberWithCommas(job["unit_fee"]) + "円 時給割増！</span>"
//   }
//   return text;
// }

// function numberWithCommas(price) {
//   return price.toString().replace(FORMAT_PRICE, ",");
// };

// function createSlide() {
  $("#jobs").owlCarousel({
    loop: true,
    margin: 30,
    slideBy: 2,
    dots: false,
    nav: true,
    navText: ["<i class='fas fa-angle-left'></i>", "<i class='fas fa-angle-right'></i>"],
    responsive:{
      0: {items: 1},
      290: {items: 2, margin: 15},
      680: {items: 2},
      768: {items: 3},
      992: {items: 3},
      1200: {items: 4}
    }
  }).trigger("refresh.owl.carousel");
// }
