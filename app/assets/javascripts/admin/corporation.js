$(document).ready(function() {
  /**
   * This block has bug with Select2 multiple
   * Wait for fix in future
   */
  // $(document).on('focus', '.select2', function (e) {
  //   if (e.originalEvent) {
  //     $(this).siblings('select').select2('open');
  //   }
  // });

  var $picDepartmentSelect = $('#pic-department-select');
  $picDepartmentSelect.select2({width: '100%'});

  $('#pic-select').select2({width: '100%'}).on('change', function() {
    $picDepartmentSelect.empty().trigger('change');
    var adminId = $(this).val();

    if (adminId) {
      loadAdminDepartment(adminId).done(function(response) {
        var selectedData = $picDepartmentSelect.data('selected');
        var selectedDepartment = _.find(response.data, {department_id: selectedData});
        $.each(response.data, function(index, adminDepartment) {
          var option = new Option(adminDepartment.department_name, adminDepartment.department_id, true, true);
          $picDepartmentSelect.append(option);
        });
        if (!selectedDepartment && response.data[0]) {
          var selectedData = response.data[0].department_id
        }
        $picDepartmentSelect.val(selectedData).trigger('change');
      });
    }
  }).trigger('change');

  var $datePickerItems = $('#transaction-start, #transaction-end, .corporation-date-picker, .violation-day');
  $datePickerItems.datepicker({
    autoclose: true,
    todayBtn: true,
    todayHighlight: true,
    format: DEFAULT_DATE_TIME_FORMAT
  });

  $('#form-corporation').on('submit', function(e) {
    e.preventDefault();
    var actionURL = $(this).attr('action');
    var extraErrorElements = {};
    $picDepartmentSelect.remove('#params-department');
    if ($picDepartmentSelect.val() == undefined) {
      $picDepartmentSelect.append($('<input>', {value: "", name: "corporation[pic_department_id]",
        type: "hidden", id: "params-department"}));
    }
    $.ajax({
      url: actionURL,
      method: 'POST',
      dataType: 'json',
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $.lawsonAjax(response);
      }
    })
  });

  if ($('#form-corporation').length > 0 && $(location).attr('href').indexOf('/new') !== -1) {
    $('#confirm-modal').on('show.bs.modal', function() {
      var $confirmModal = $(this);
      $.ajax({
        url: '/corporations/check_name_unique',
        method: 'POST',
        dataType: 'json',
        data: {'name_1': $('#form-corporation').find('input[name="corporation[name_1]"]').val()},
        success: function(response) {
          var confirmKey = response.is_existed ? 'confirm_unique_name' : 'confirm_message';
          $confirmModal.find('.message-content').html(I18n.t('corporation.main_form.' + confirmKey));
        }
      });
    });
  }

  function loadAdminDepartment(adminId) {
    return $.ajax({
      url: '/admins/' + adminId + '/admin_departments',
      method: 'GET',
      dataType: 'json'
    });
  }

  if ($(".upload-corporation-thumbnail").length) {
    $(".upload-corporation-thumbnail").on("change", function() {
      var previewThumbnailAfterSave = $(".preview_thumbnail");
      var corpCropThumbnail = new CropImage({
        input: this,
        inputName: "thumbnail",
        previewThumbnailAfterSave: previewThumbnailAfterSave,
        cropper: {
          resizeWidth: 300,
          resizeHeight: 300
        }
      });
      corpCropThumbnail.previewImage();
    });
  }
});
