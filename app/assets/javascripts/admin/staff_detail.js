$(function() {
  checkVisibleRemoveBtn();

  $(".departments a.add_fields")
    .data("association-insertion-method", "before")
    .data("association-insertion-node", "this");

  $(".departments")
    .on("cocoon:after-remove", toggleDisplay)
    .on("cocoon:after-insert", toggleDisplay);
});

function checkVisibleRemoveBtn() {
  var $row = $(".staff-departments");
  var $lastestRemoveBtn = $row.find(".remove_fields")[0];
  if($row.length == 1) $($lastestRemoveBtn).hide();
}

function toggleDisplay() {
  var $visibleRow = $(".staff-departments:visible");
  var $lastestRemoveBtn = $visibleRow.find(".remove_fields")[0];

  if($visibleRow.length == 1) $($lastestRemoveBtn).hide();
  else $($lastestRemoveBtn).show();

  $(".corporation-date-picker").datepicker({
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true
  });
}
