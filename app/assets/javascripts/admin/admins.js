$(function() {
  var opRoleId = $("#op-admin-role-id").data("opRoleId");

  $("#admin_started_at, #admin_stopped_at", ".new-admin-form").datepicker({
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true
  });

  $("#admin_avatar", ".new-admin-form form").on("change", function() {
    if ($(this).val()) {
      previewAvatar(this, "#avatar-preview");
    } else {
      var $avatarPreview = $("#avatar-preview");
      var oldAvatarUrl = $avatarPreview.attr("data-old-avatar");

      if (oldAvatarUrl) {
        $avatarPreview.attr("src", oldAvatarUrl);
      } else {
        $avatarPreview.removeAttr("src").closest(".form-group").addClass("hidden");
      }
    }
  });

  $("#delete-avatar", ".new-admin-form").on("click", function() {
    $("#admin_avatar").val("");
    $("#avatar-preview").attr("data-old-avatar", "")
      .closest(".form-group").addClass("hidden");
    $("#confirm-delete-avatar").modal("hide");
  });

  $("#submit-save-admin").on("click", function() {
    $("#admins-new-admin, #admins-edit-admin").trigger("submit");
  });

  $("#admins-new-admin, #admins-edit-admin").on("submit", function(e) {
    e.preventDefault();
    var actionURL = $(this).attr("action");
    var extraErrorElements = {
      admin_departments: ".admin-departments"
    };

    if (!$("#avatar-preview").is(":visible") && !$("#admin_avatar").val()) {
      $("[name='admin[remove_avatar]']").val("1");
    }

    $.ajax({
      url: actionURL,
      method: "POST",
      dataType: "json",
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $("#confirm-save-admin").modal("hide");
        $.lawsonAjax(response, extraErrorElements);
      }
    });
  });

  function previewAvatar(inputContent, previewElm) {
    var reader = new FileReader();

    reader.onload = function(evt) {
      $(previewElm).attr("src", evt.target.result)
        .closest(".form-group").removeClass("hidden");
    };

    reader.readAsDataURL(inputContent.files[0]);
  }
});
