$(function() {
  var date = new Date($(".wage-closing-month").text().trim());

  $(".payroll-date-picker").datepicker({
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true,
    startDate: date
  });

  $(".import-payroll-date-picker, .payroll-list-date-picker").datepicker({
    autoclose: true,
    format: DATE_FORMAT_ONLY_MONTH_YEAR,
    minViewMode: DATE_MONTH_YEAR_VIEW_MODE
  });

  $(".payroll-list-modal-date-picker").datepicker({
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true
  });

  $("#import-payment, #import-payroll").on("submit", function() {
    $("#spinner").removeClass("ng-hide");
  });
});
