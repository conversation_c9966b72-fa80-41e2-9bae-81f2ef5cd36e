$(function() {
  var $checkboxOverviewDates = $(".overview-date").find(":input[type='checkbox']");
  $.each($checkboxOverviewDates, function(index, overviewDate) {
    if (!$(overviewDate).is(":checked")) {
      disableOverviewDate(overviewDate);
    }
  });
  $checkboxOverviewDates.click(function() {
    if ($(this).is(":checked")) {
      enableOverviewDate($(this));
    } else {
      disableOverviewDate($(this));
    }
  });

  function disableOverviewDate(elementDate) {
    var currentOverviewDate = $(elementDate).parent().find(".corporation-datepicker");
    currentOverviewDate.attr("disabled", true);
    currentOverviewDate.val("");
  }

  function enableOverviewDate(elementDate) {
    $(elementDate).parent().find(".corporation-datepicker").removeAttr("disabled");
  }
});
