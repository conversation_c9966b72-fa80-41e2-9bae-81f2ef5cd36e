$(function () {
  var modalCounter = 0;
   $(".staff-complaint-modal.modal").on("shown.bs.modal", function () {
    modalCounter++;
  });
  $(".staff-complaint-modal.modal").on("hidden.bs.modal", function () {
    modalCounter--;
    if(modalCounter){
      $("body").addClass("modal-open");
    }
    else{
      $("body").removeClass("modal-open");
    }
  });

  $(".staff-complaint-select").select2({width: '100%'});

  $(".staff-complaint-date-picker").datepicker({
    autoclose: true,
    format: "yyyy/mm/dd",
    todayBtn: true,
    todayHighlight: true
  });
});
