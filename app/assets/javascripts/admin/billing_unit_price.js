$(document).ready(function() {
  $(".billing-price-submit-btn, .save-minimum-wage").on("click", function() {
    var $elements = $("input, select");
    _.forEach($elements, function(e) {
      e.setCustomValidity("");
    });

    $elements.on("invalid", function(e) {
      var message = I18n.t("admin.billing_unit_price.please_enter");
      if (_.includes(e.target.name, "new_absence_discount") && e.target.validity.rangeOverflow) {
        message = I18n.t("admin.billing_unit_price.enter_negative");
      }
      e.target.setCustomValidity(message);
    });
    $elements.on("input", function(e) {
      e.target.setCustomValidity("");
    });
  });

  $(".payment-rate-hscroll").scroll(function() {
    var width = $(".payment-rate-hscroll").width() + $(".payment-rate-hscroll").scrollLeft();
    $(".payment-rate-vscroll").width(width);
  });
});
