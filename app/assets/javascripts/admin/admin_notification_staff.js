$(function() {
  var $sentDateFieldANS = $("#admin_notification_staff_sent_date");
  var $sentNowFieldANS = $("#admin_notification_staff_is_sent_now");
  var $sentNowCheckBoxANS = $("#is_sent_now");
  var $attachmentFieldANS = $("#admin_notification_staff_attachment_url");
  var $remoteAttachmentFlagANS = $("#admin_notification_staff_remove_attachment_url");
  var $remoteAttachmentBtnANS = $("#delete-attachment-btn-admin-notifications-staff");
  var $attachmentPreviewANS = $("#attachment-preview-admin-notifications-staff");
  var $confirmDeleteattachmentPopupANS = $("#confirm-delete-attachment-admin-notifications-staff");
  var notificationIdANS = $attachmentPreviewANS.attr("data-notification-id");
  var oldFileNameANS = $attachmentPreviewANS.attr("data-old-file-name");
  var $submitAdminNotificationStaffANS = $("#submit-admin-notification-staff");
  var $adminNotificationStaffFormANS = $("#form-notification-admin-to-staff");
  var $confirmSaveFormANS = $("#confirm-save-admin-notification-staff");
  var $attachmentNameANS = $("#attachment-name-admin-notifications-staff");
  var $attachmentUrlANS = $("#link-to-attachment-url-admin-notifications-staff");
  var $selectExceptStaffIdsANS = $(".sellect-except-staff-ids");
  var $selectDepartmentIdsANS = $("#admin_notification_staff_department_ids");
  var $selectPrefectureIdsANS = $("#admin_notification_staff_prefecture_ids");
  var $title =  $("#admin_notification_staff_title");
  var $saveBtn = $("#save-btn");
  var $sendingMessage = $("#sending-message");

  $remoteAttachmentFlagANS.val(false);
  $sentNowFieldANS.val(false);

  $sentDateFieldANS.datepicker({
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true
  });


  $sentNowCheckBoxANS.on("change", function() {
    if ($(this).is(":checked")) {
      $sentNowFieldANS.val(true);
      $sentDateFieldANS.val("");
      $sentDateFieldANS.prop("disabled", true);
    } else {
      $sentNowFieldANS.val(false);
      $sentDateFieldANS.prop("disabled", false);
    }
  });

  $saveBtn.on("click", function(e) {
    e.preventDefault();
    $confirmSaveFormANS.modal("show");
  });

  $submitAdminNotificationStaffANS.on("click", function(e) {
    e.preventDefault();
    $adminNotificationStaffFormANS.trigger("submit");
  });

  $adminNotificationStaffFormANS.on("submit", function(e) {
    e.preventDefault();
    $confirmSaveFormANS.modal("hide");
    var actionURL = $(this).attr("action");
    var method = $(this).attr("method");
    $sendingMessage.show();
    $saveBtn.prop('disabled', true);
    var data = new FormData(this);
    $.ajax({
      url: actionURL,
      method: method,
      dataType: "json",
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $sendingMessage.hide();
        if (!response.status) {
          $saveBtn.prop('disabled', false);
        }
        $.lawsonAjax(response, {}, true);
      }
    });
  });

  if (notificationIdANS) {
    previewAttachmentByUrlANS(notificationIdANS, oldFileNameANS);
    $remoteAttachmentFlagANS.val(false);
  }

  $remoteAttachmentBtnANS.on("click", function() {
    $confirmDeleteattachmentPopupANS.modal("hide");
    $attachmentFieldANS.val("");
    $remoteAttachmentFlagANS.val(true);
    $attachmentPreviewANS.data("data-old-attachment", "");
    $attachmentPreviewANS.closest(".form-group").addClass("hidden");
  });

  $attachmentFieldANS.on("change", function() {
    if ($(this).val()) {
      previewAttachmentANS(this);
      $remoteAttachmentFlagANS.val(false);
    } else {
      if (notificationIdANS) {
        previewAttachmentByUrlANS(notificationIdANS, oldFileNameANS);
        $remoteAttachmentFlagANS.val(false);
      } else {
        $remoteAttachmentFlagANS.val(true);
        $attachmentPreviewANS.closest(".form-group").addClass("hidden");
      }
    }
  });

  $selectExceptStaffIdsANS.select2({
    width: "100%",
    ajax: {
      url: "/selectbox_staffs_by_departments/",
      dataType: "json",
      data: function(params) {
        return {
          search_content: params.term,
          selected: $(this).val(),
          department_ids: $selectDepartmentIdsANS.val()
        };
      },
      processResults: function(data) {
        return {results: data.results};
      }
    },
    templateResult: formatResultANS,
    templateSelection: formatSelectionANS
  }).trigger("change");

  function formatResultANS(staff) {
    return staff.account_name_with_staff_number;
  }

  function formatSelectionANS(staff) {
    return staff.text || staff.account_name_with_staff_number;
  }

  function previewAttachmentANS(inputContent) {
    $attachmentUrlANS.removeAttr("href");
    $attachmentUrlANS.removeAttr("target");
    var reader = new FileReader();
    var file = inputContent.files[0];
    reader.onload = function() {
      $attachmentNameANS.text(decodeURIComponent(file.name));
      $attachmentPreviewANS.closest(".form-group").removeClass("hidden");
    };
    reader.readAsDataURL(inputContent.files[0]);
  }

  function previewAttachmentByUrlANS(nId, fileName) {
    var actionURL = "/admin_notification_staffs/download_notification_attachment?notification_id=" + nId;
    $attachmentUrlANS.attr("href", actionURL);
    $attachmentUrlANS.attr("target", "_blank");
    $attachmentNameANS.text(decodeURIComponent(fileName));
    $attachmentPreviewANS.closest(".form-group").removeClass("hidden");
  }

  showInputFieldsByNotifyAudience();
  $('[name="admin_notification_staff[notify_audience]"]').change(function() {
    showInputFieldsByNotifyAudience();
  });

  function showInputFieldsByNotifyAudience() {
    var isDisableNormalFields = $("#admin_notification_staff_notify_audience_notify_registration").is(":checked");
    if (isDisableNormalFields) {
      $(".notify-registration").removeClass("d-none");
      $(".notify-staff").addClass("d-none");
    } else {
      $(".notify-registration").addClass("d-none");
      $(".notify-staff").removeClass("d-none");
    }
  }

  $("#form-notification-admin-to-staff :input").change(function() {
    var sentDatePresence = $sentNowFieldANS.val() != "false";
    sentDatePresence = sentDatePresence || !!$sentDateFieldANS.val();
    var isDisableNormalFields = $("#admin_notification_staff_notify_audience_notify_registration").is(":checked");
    var additionDatas = $selectDepartmentIdsANS.val();
    if (isDisableNormalFields) {additionDatas = $selectPrefectureIdsANS.val();}
    var allowSubmit = sentDatePresence && Object.keys(additionDatas).length > 0 && !!$title.val();
    $saveBtn.prop('disabled', !allowSubmit);
  });
});
