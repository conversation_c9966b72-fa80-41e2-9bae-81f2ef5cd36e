$(document).ready(function() {
  $("#form-corporation-group-tag").on("submit", function(e) {
    e.preventDefault();
    var actionURL = $(this).attr("action");

    $.ajax({
      url: actionURL,
      method: "POST",
      dataType: "json",
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $.lawsonAjax(response, {}, true);
      },
      error: function(){
        location.reload();
      }
    });
  });

  $("#save-group-tag").on("click", function() {
    var paramsData = $("#form-corporation-group-tag").serializeJSON().corporation_group_tag;
    $.ajax({
      url: "/corporation_group_tags/check_duplicate_name",
      method: "POST",
      dataType: "json",
      data: {corporation_group_tag: paramsData},
      success: function(response) {
        if (response.valid_name) {
          $("#form-corporation-group-tag").submit();
        } else {
          $("#confirm-submit-modal").modal("show");
        };
      }
    });
  });
});
