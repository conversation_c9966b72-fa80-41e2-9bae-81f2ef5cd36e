$(document).ready(function(){
  $('button.admin-btn-show-password').on('click', function() {
    var $passwordElm = $(this).closest('.input-group').find('[data-show-password]');
    var elementType = ($passwordElm.prop('type') === 'password' ? 'text' : 'password');

    $("[data-show-password]").prop("type", elementType);
    $('i', $(this)).toggleClass('fa-eye fa-eye-slash')
  });

  $(".set-department:not(#set-department-0)").on("click", function() {
    var childDepartmentLength = $(".department-list").data("department-length") - 1;
    var departmentId = $(this).val();
    var checkedState = $(this).is(":checked");
    $(".destroy-department-" + departmentId).val(!checkedState);
    if ($("#set-department-0").attr("disabled")) {
      return;
    };
    var childCheckedLength = $(".set-department:not(#set-department-0):checked").length;
    var checkedAllState = null;
    if (childCheckedLength == childDepartmentLength) {
      checkedAllState = true;
    } else if (childCheckedLength < childDepartmentLength) {
      checkedAllState = false;
    };
    if (!_.isNull(checkedAllState)) {
      $("#set-department-0").prop("checked", checkedAllState);
      $(".destroy-department-0").val(!checkedAllState);
    };
  });

  $("#set-department-0").on("click", function() {
    var parentDepartmentCheck = $(this).is(":checked");
    $(".destroy-department-0").val(!parentDepartmentCheck);
    var $childs = $(".set-department:not(#set-department-0)[disabled!='disabled']");
    $childs.prop("checked", parentDepartmentCheck);
    $childs.nextAll(".destroy-department").val(!parentDepartmentCheck);
  });

  $(".custom-file-upload").click(function() {
    $(".import-payment-price").trigger("click");
  });

  $(".import-payment-price").change(function() {
    if (!_.isEmpty($(this).val()) && !$("#btn-checking-import-payment-unit-price").data("check-updating")) {
      $(".btn-submit-payment").removeAttr("disabled");
      $(".custom-file-upload").val($(this).val());
    };
  });

  if ($(".admin-top-pages").length > 0) {
    var deviceStatus = $(".admin-top-pages").data("device-status");
    if (deviceStatus === "confirming") {
      $(".modal-confirming-device").modal("show");
    }
  }
});

function openImage(id, modelName, field){
  var actionURL = "/preview_images/" + id;
  $.ajax({
    url: actionURL,
    method: "GET",
    dataType: "json",
    data: {model_name: modelName, field: field},
    success: function(response) {
      window.open(response.src);
    }
  });
}
