$(document).ready(function() {
  var $templateCorpSelect = $('#template-corporation-select');
  var $templateLocSelect = $('#template-location-select');
  var $templateCorpAfterSelect = $('#template-corporation-after-select');
  var $templateLocAfterSelect = $('#template-location-after-select');

  $templateCorpSelect.on("change", function() {
    $templateLocSelect.empty().trigger('change');
    var corporationId = $(this).val();
    $templateCorpAfterSelect.text(corporationId);
    $templateCorpAfterSelect.attr("href", "/corporations/" + corporationId + "/edit");

    if (!!corporationId) {
      loadLocations(corporationId).done(function(response) {
        var selectedId = $templateLocSelect.data("selected");
        var selectedLocation = _.find(response.locations, {id: selectedId});
        $.each(response.locations, function(_, location) {
          var option = new Option(location.name, location.id, true, true);
          $templateLocSelect.append(option);
        });
        if (!selectedLocation && response.locations[0]) {
          var selectedId = response.locations[0].id;
        }
        $templateLocSelect.val(selectedId).trigger("change");
        $templateLocAfterSelect.text(selectedId);
        $templateLocAfterSelect.attr("href", "/locations/" + selectedId + "/edit");
      });
    }
  }).trigger("change");

  function loadLocations(corporationId) {
    return $.ajax({
      url: "/corporations/"+ corporationId + "/locations",
      method: "GET",
      dataType: "json"
    });
  };

  $('#form-billing-payment-template').keypress(function(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 13 && e.target.tagName != "TEXTAREA") {
      e.preventDefault();
      $("input").blur();
    }
  });

  $('#form-billing-payment-template').submit(function(e) {
    e.preventDefault();
    var actionURL = $(this).attr('action');
    $.ajax({
      url: actionURL,
      method: 'POST',
      dataType: 'json',
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $.lawsonAjax(response, {}, true, true);
      }
    });
  });
});
