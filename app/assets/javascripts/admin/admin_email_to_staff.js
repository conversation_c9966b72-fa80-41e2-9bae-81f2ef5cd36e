$(function() {
  initSelect();
  $("#admin_email_to_staff_to_audience_to_registration, #admin_email_to_staff_to_audience_to_staff").click(function() {
    $("#admin_email_to_staff_target_staff_ids").empty();
    initSelect()
  })

  function initSelect() {
    if ($("#admin_email_to_staff_to_audience_to_registration").is(":checked")) {
      selectRegistration();
    } else {
      selectStaff();
    }
    $("#admin_email_to_staff_target_staff_ids").trigger('change')
  }

  function selectStaff() {
    $("#admin_email_to_staff_target_staff_ids").select2({
      width: "100%",
      ajax: {
        url: "/selectbox_staffs_by_departments/",
        dataType: "json",
        data: function(params) {
          return {
            search_content: params.term,
            selected: $(this).val(),
            staff_levels: staffLevels(),
            staff_status: staffStatuses(),
            department_ids: $("#admin_email_to_staff_department_ids").val()
          };
        },
        processResults: function(data) {
          return {results: data.results};
        }
      },
      templateResult: formatResultStaff,
      templateSelection: formatSelectionStaff
    }).trigger("change");
  }

  function formatResultStaff(staff) {
    return staff.account_name_with_staff_number;
  }

  function formatSelectionStaff(staff) {
    return staff.text || staff.account_name_with_staff_number;
  }

  function selectRegistration() {
    $("#admin_email_to_staff_target_staff_ids").select2({
      width: "100%",
      ajax: {
        url: "/selectbox_registrations_by_prefectures/",
        dataType: "json",
        data: function(params) {
          return {
            search_content: params.term,
            selected: $(this).val(),
            prefecture_ids: $("#admin_email_to_staff_prefecture_ids").val()
          };
        },
        processResults: function(data) {
          return {results: data.results};
        }
      },
      templateResult: formatResultRegistrations,
      templateSelection: formatSelectionRegistrations
    }).trigger("change");
  }

  function formatResultRegistrations(staff) {
    return staff.account_name;
  }

  function formatSelectionRegistrations(staff) {
    return staff.text || staff.account_name;
  }

  function staffLevels() {
    var staff_levels = []
    $("input[name='admin_email_to_staff[staff_levels]']:checked").each(function() {
      staff_levels.push($(this).val());
    });
    return staff_levels.join(",")
  }

  function staffStatuses() {
    var staff_statuses = []
    $("input[name='admin_email_to_staff[staff_status]']:checked").each(function() {
      staff_statuses.push($(this).val());
    });
    return staff_statuses.join(",")
  }
})