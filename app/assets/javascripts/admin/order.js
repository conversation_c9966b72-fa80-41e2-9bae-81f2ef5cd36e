$(document).ready(function() {
  $("#form-create-order").submit(function(e){
    return false;
  });

  $(".btn-submit-send-order").on("click", function() {
    $(this).addClass("disabled");
  });
});

function filterInputTypeNumber(event) {
  var keyCode = ('which' in event) ? event.which : event.keyCode;
  return !(keyCode == 69 || keyCode == 101);
};

function handlePasteIntoInputTypeNumber(e) {
  var clipboardData, pastedData;
  clipboardData = e.clipboardData || window.clipboardData;
  pastedData = clipboardData.getData('Text').toUpperCase();
  if (pastedData.indexOf('E') > -1) {
    e.stopPropagation();
    e.preventDefault();
  }
};
