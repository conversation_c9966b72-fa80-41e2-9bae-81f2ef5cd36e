$(function() {
  initEmergencyForm();

  $(".emergency-postal-code-search-item").on("keyup", function(e) {
    if ($(".can-edit-emergency").is(":checked")) {
      var postalCode = $(this).val();
      if (canSearchPostalCode(postalCode)) {
        getDataPostalCode(postalCode.replace("-", ""), false, ".emergency");
      }
    }
  });

  $("[data-copyable='true']", "#overview-staff-address").on("change keyup keypress", function() {
    if (!$(".can-edit-emergency").is(":checked")) {
      var currentVal = $(this).val();
      var targetElement = $(this).data("target");
      $("." + targetElement).val(currentVal);
    }
  });

  $(".can-edit-emergency").change(function() {
    if ($(this).is(":checked")) {
      $(".emergency-copy-address :input").removeAttr("readonly");
      $(".emergency-prefecture, .postal-search-emergency").removeClass("disabled");
    } else {
      var inputs = ["postal-code-search-item", "prefecture", "city",
        "street-number", "building", "name-kana", "tell", "home-tel"];
      referFromAddress(inputs, ".emergency");
      $(".emergency-copy-address :input:not(:checkbox)").attr("readonly", true);
      $(".emergency-prefecture, .postal-search-emergency").addClass("disabled");
    }
  });

  $(".modal-postal-code").on("click", "tr", function() {
    var currentTabActive = $("#basic-tab .overview-nav-tab .active").data("nav-id");
    var postalCode = $(this).data("current-postal");
    if(currentTabActive === 1) {
      fillDataAddress(postalCode, false, ".emergency");
      $(".emergency-postal-code-search-item").val(postalCode.postal_code);
    } else if(currentTabActive === 0) {
      fillDataAddress(postalCode, true);
      $(".address-postal-code-search-item").val(postalCode.postal_code).trigger("keypress");
    }
    $("#list-postal-code").modal("hide");
  });

  function initEmergencyForm() {
    if (!$(".can-edit-emergency").is(":checked")) {
      $(".emergency-copy-address :input:not(:checkbox)").attr("readonly", true);
      $(".emergency-prefecture, .postal-search-emergency").addClass("disabled");
    }
  }
})
