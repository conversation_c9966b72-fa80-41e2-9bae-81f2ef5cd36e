$(function() {
  var $btnConfirmDelete = $(".btn-admin-delete-user");
  var totalRows = 0;
  var USER_GROUPS_TYPE = "user_groups";
  var TYPE_OWNER = "owner";

  $btnConfirmDelete.on("click", function(e) {
    e.preventDefault();
    var userID = $(this).attr("user-id");
    $.ajax({
      url: "/users/" + userID,
      method: "DELETE",
      dataType: "json",
      contentType: false,
      success: function() {
        location.reload();
      },
      error: function() {
        location.reload();
      }
    })
  });

  $(document).on("click", ".btn-admin-delete-user", function(e) {
    var userID = $(this).data("user-id");
    $btnConfirmDelete.attr("user-id", userID);
  });

  var $corporationSelect = $("#corporation-select");
  var usrLocationArr = [], usrCorpGrpArr = [], userGroupArr = [];
  var userId = $("#user_edited_id").val() || "0";
  var isStoreComputerUser = $("#is_store_computer_account").val() == "true";

  if ($("#admins-new-user, #admins-edit-user").length > 0) {
    loadSelectedCorpGrpAndLocation(userId).done(function(resp) {
      usrCorpGrpArr = resp.data.user_corporation_groups || [];
      userGroupArr = resp.data.user_groups || [];
      usrLocationArr = resp.data.user_locations || [];
      $corporationSelect.on("change", function() {
        var corporationId = $corporationSelect.val();

        if (corporationId) {
          loadCorporationGroupAndLocation(corporationId).done(function(response) {
            var allRows = buildCorpGrpLocationTblRows(response.data);
            $("#admin-users-pic-tbl tbody").empty().append(allRows);
            var isChecked = totalRows == getChkboxesWithCondition("checked").length ? true : false;
            $("#all-corporation-grp-location-chkbox").prop("checked", isChecked);
            if ($(".user-role-id").val() == TYPE_OWNER) {
              $("#admin-users-pic-tbl").find("input[type='checkbox']").toggleClass('disabled');

              if (!$("#all-corporation-grp-location-chkbox").is(":checked")) {
                $("#all-corporation-grp-location-chkbox").trigger("click");
              }
            }
            if (isStoreComputerUser) {
              $("#admin-users-pic-tbl").find("input[type='checkbox']").toggleClass('disabled');
            }
          });
        } else {
          $("#admin-users-pic-tbl tbody").empty();
        }
      }).trigger("change");
    });
  }

  $("#user_started_at").datepicker({
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true
  });

  $("#user_avatar", ".new-user-form form").on("change", function() {
    if ($(this).val()) {
      previewAvatar(this, "#avatar-preview");
    } else {
      var $avatarPreview = $("#avatar-preview");
      var oldAvatarUrl = $avatarPreview.attr("data-old-avatar");

      if (oldAvatarUrl) {
        $avatarPreview.attr("src", oldAvatarUrl);
      } else {
        $avatarPreview.removeAttr("src").closest(".form-group").addClass("hidden");
      }
    }
  });

  $("#delete-avatar", ".new-user-form").on("click", function() {
    $("#user_avatar").val("");
    $("#avatar-preview").attr("data-old-avatar", "")
      .closest(".form-group").addClass("hidden");
    $("#confirm-delete-avatar").modal("hide");
  });

  $("#submit-save-user").on("click", function() {
    $("#admins-new-user, #admins-edit-user").trigger("submit");
  });

  $("#admins-new-user, #admins-edit-user").on("submit", function(e) {
    e.preventDefault();
    var actionURL = $(this).attr("action");

    if (!$("#avatar-preview").is(":visible") && !$("#user_avatar").val()) {
      $("[name='user[remove_avatar]']").val("1");
    }

    $.ajax({
      url: actionURL,
      method: "POST",
      dataType: "json",
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $("#confirm-save-user").modal("hide");
        var extraErrorElements = {
          user_corporation_groups: ".user-group-location"
        };
        $.lawsonAjax(response, extraErrorElements);
      }
    });
  });

  $("#accept-reset-password").on("click", function(e) {
    e.preventDefault();
    var url = $(this).data("url");
    $.ajax({
      url: url,
      method: "POST",
      data: {"data-method": "post"},
      success: function(response) {
        var extraErrorElements = {user_corporation_groups: ".user-group-location"};
        $.lawsonAjax(response, extraErrorElements);
      }
    });
    $("#confirm-reset-password").modal("hide");
  });

  $(document).on("click", ".corporation-group-chkbox", function() {
    var corporationGrpId = $(this).val();
    var checkedState = $(this).is(":checked");
    $(".location-chkbox-" + corporationGrpId).prop("checked", checkedState);
    $(".location-chkbox-" + corporationGrpId).parent()
      .find(".destroy-corp-grp-loc").val(!checkedState);
  });

  function checkStateForChkboxAll(chkbox) {
    var isChkboxAllChecked = $("#all-corporation-grp-location-chkbox").is(":checked");
    var isCurrentChkboxChecked = $(chkbox).is(":checked");
    var notDisabledChkboxes = getChkboxesWithCondition("not-disabled");
    var checkedChkboxes = getChkboxesWithCondition("not-disabled-and-checked");

    if((!isCurrentChkboxChecked && isChkboxAllChecked) ||
      (isCurrentChkboxChecked != isChkboxAllChecked && notDisabledChkboxes.length == checkedChkboxes.length)) {
      $("#all-corporation-grp-location-chkbox").prop("checked", isCurrentChkboxChecked);
    }
  }

  function getChkboxesWithCondition(condition) {
    var $targetElem = $("[class*=location-chkbox-], .corporation-group-chkbox");
    switch(condition) {
      case "checked":
        return $targetElem.filter(function() { return $(this).is(":checked"); });
      case "not-disabled":
        return $targetElem.filter(function() { return !this.disabled; });
      case "not-disabled-and-checked":
        return $targetElem.filter(function() { return $(this).is(":checked") && !this.disabled; });
    }
  }

  $(document).on("click", "[class*=location-chkbox-]", function() {
    var isDestroy = !$(this).is(":checked");
    $(this).parent().find(".destroy-corp-grp-loc").val(isDestroy);
    checkStateForCorGrpChkbox(this);
    checkStateForChkboxAll(this);
  });

  function checkStateForCorGrpChkbox(location) {
    var corGrpId = location.className.split("location-chkbox-")[1];
    var corGrp = $("#user_corporation_groups_attributes_" + corGrpId);
    if(corGrp.disabled) return false;
    else {
      isLocationChked = $(location).is(":checked");
      isCorGrpChked = $(corGrp).is(":checked");

      if(!isLocationChked && isCorGrpChked) {
        $(corGrp).prop("checked", !isCorGrpChked);
        $(corGrp).parent().find(".destroy-corp-grp").val(isCorGrpChked);
      }
    }
  }

  $(".user-role-id").on("change", function() {
    $("#admin-users-pic-tbl").find("input[type='checkbox']").removeClass("disabled");
    if ($(this).val() == TYPE_OWNER) {
      if (!$("#all-corporation-grp-location-chkbox").is(":checked")) {
        $("#all-corporation-grp-location-chkbox").trigger("click");
      }
      $("#admin-users-pic-tbl").find("input[type='checkbox']").toggleClass("disabled");
    }
  });

  $("#all-corporation-grp-location-chkbox").on("click", function() {
    var checkedState = $(this).is(":checked");
    var notDisabledChkboxes = $("[class*=location-chkbox-], .corporation-group-chkbox").filter(function() {
      return !this.disabled;
    });

    notDisabledChkboxes.prop("checked", checkedState);
    notDisabledChkboxes.parent().find(".destroy-corp-grp-loc").val(!checkedState);
    notDisabledChkboxes.parent().find(".destroy-corp-grp").val(!checkedState);
  });

  $(document).on("click", ".corporation-group-chkbox", function() {
    var isDestroy = !$(this).is(":checked");
    $(this).parent().find(".destroy-corp-grp").val(isDestroy);

    checkStateForChkboxAll(this);
  });

  function previewAvatar(inputContent, previewElm) {
    var reader = new FileReader();

    reader.onload = function(evt) {
      $(previewElm).attr("src", evt.target.result)
        .closest(".form-group").removeClass("hidden");
    }

    reader.readAsDataURL(inputContent.files[0]);
  }

  function loadCorporationGroupAndLocation(corporationId) {
    return $.ajax({
      url: "/corporations/" + corporationId + "/load_corporation_groups_and_locations",
      method: "GET",
      dataType: "json"
    });
  };

  function loadSelectedCorpGrpAndLocation(userId) {
    return $.ajax({
      url: "/users/" + userId + "/load_selected_corporation_groups_and_locations",
      method: "GET",
      dataType: "json",
      cache: false
    });
  };

  function buildCorpGrpLocationTblRows(data) {
    var corpGrpRow, locationRow, allRows, groupType, relationIdName = "";

    $.each(data, function(index, corpGrp) {
      groupType = corpGrp.type;
      relationIdName = groupType == USER_GROUPS_TYPE ? "corporation_group_tag_id" : "corporation_group_id";
      var locationIterateIdx = 0, corpGrpIterateIdx = 0, corpGrpIndex, usrCorpGrpId;
      if (groupType == USER_GROUPS_TYPE) {
        corpGrpIndex = userGroupArr.findIndex(function(userGroup) {
          return userGroup.corporation_group_tag_id == corpGrp.id;
        });
        if (corpGrpIndex != -1) usrCorpGrpId = userGroupArr[corpGrpIndex].id;
      } else {
        corpGrpIndex = usrCorpGrpArr.findIndex(function(usrCorpGrp) {
          return usrCorpGrp.corporation_group_id == corpGrp.id;
        });
        if (corpGrpIndex != -1) usrCorpGrpId = usrCorpGrpArr[corpGrpIndex].id;
      }
      var corpGrpIdInp = "";
      corpGrpIterateIdx = corpGrp.id;
      if (corpGrpIndex != -1) {
        corpGrpIdInp = '\
          <input type="hidden" name="user[' + groupType + '_attributes][' + corpGrpIterateIdx + '][id]"\
            id="user_corporation_groups_attributes_' + corpGrpIterateIdx + '_hidden' + '"\
            value="' + usrCorpGrpId + '"/>';
      }
      var isCheckedCorpGrp = corpGrpIndex != -1;
      corpGrpRow = '\
        <tr>\
          <td class="col-md-1">' + corpGrpIdInp + '\
            <input type="checkbox" name="user[' + groupType + '_attributes][' + corpGrpIterateIdx + '][' + relationIdName + ']"\
              id="user_corporation_groups_attributes_'+ corpGrpIterateIdx + '"\
              value="' + corpGrp.id + '"\
              class="corporation-group-chkbox"\
              ' + (isCheckedCorpGrp ? 'checked' : '') +'/>\
            <input type="hidden" name="user[' + groupType + '_attributes][' + corpGrpIterateIdx + '][_destroy]"\
              id="user_corporation_group_attributes_' + corpGrpIterateIdx + '__destroy"\
              class="destroy-corp-grp"\
              value="false"/>\
          </td>\
          <td class="col-md-5">' + corpGrp.full_name.stripTags() + '</td>\
          <td class="col-md-4"></td>\
        </tr>';
      totalRows += 1;
      $.each(corpGrp.locations, function(_index, location) {
        var locationIndex = usrLocationArr.findIndex(function(usrLocation) {
          return usrLocation.location_id == location.id;
        });
        var isCheckedLocation = locationIndex != -1;
        var usrLocationId, locationPicIdInp = "";
        locationIterateIdx = location.id;
        var corporationGroupId = corpGrp.id;
        if (groupType == USER_GROUPS_TYPE) corporationGroupId = location.corporation_group_id;
        if (locationIndex != -1) {
          usrLocationId = usrLocationArr[locationIndex].id;
          locationPicIdInp = '\
            <input type="hidden" name="user[user_locations_attributes][' + locationIterateIdx + '][id]"\
              id="user_locations_attributes_' + locationIterateIdx + '_corporation_group_id"\
              value="' + usrLocationId + '"/>';
        }
        var corporaitonGroupName = _.isUndefined(location.corporation_group_full_name) ? "" : "(" + location.corporation_group_full_name + ")";
        locationRow = '\
          <tr>\
            <td class="col-md-1">' + locationPicIdInp + '\
              <input type="checkbox" name="user[user_locations_attributes][' + locationIterateIdx + '][location_id]"\
                id="user_locations_attributes_' + locationIterateIdx + '_location_id"\
                value="' + location.id + '"\
                class="location-chkbox location-chkbox-' + corpGrp.id + '"\
                ' + (isCheckedLocation ? 'checked' : '') +'/>\
              <input type="hidden" name="user[user_locations_attributes][' + locationIterateIdx + '][corporation_group_id]"\
                id="user_locations_attributes_' + locationIterateIdx + '_corporation_group_id"\
                value="' + corporationGroupId + '"/>\
              <input type="hidden" name="user[user_locations_attributes][' + locationIterateIdx + '][_destroy]"\
                id="user_locations_attributes_' + locationIterateIdx + '__destroy"\
                class="destroy-corp-grp-loc"\
                value="false"/>\
            </td>\
            <td class="col-md-5"></td>\
            <td class="col-md-4">' + location.name.stripTags() + corporaitonGroupName.stripTags() + '</td>\
          </tr>';
        corpGrpRow += locationRow;
        totalRows += 1;
      });
      allRows += corpGrpRow;
    });
    return allRows;
  }
});
