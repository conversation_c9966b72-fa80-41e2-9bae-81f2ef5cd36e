$(document).ready(function() {
  var $organizationSelect = $('#organization-select');
  var $departmentSelect = $('#department-select');
  var $priorityStaffsSelect = $(".priority-staffs-select");
  $('#form-location').on('submit', function(e) {
    e.preventDefault();
    var actionURL = $(this).attr('action');
    $organizationSelect.remove('#params-organization');
    if($organizationSelect.val() == undefined){
      $organizationSelect.append($('<input>', {value: "", name: "location[organization_id]",
        type: "hidden", id: "params-organization"}));
    }
    if ($departmentSelect.val() == undefined) {
      $departmentSelect.append($('<input>', {value: "", name: "location[department_id]",
        type: "hidden", id: "params-department"}));
    }
    $.ajax({
      url: actionURL,
      method: 'POST',
      dataType: 'json',
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $.lawsonAjax(response, {}, true, true);
      }
    });
  });

  $organizationSelect.select2({width: "100%"});
  $("#corporation-group-select").select2({width: "100%"});

  $("#corporation-select").on("change", function() {
    $("#corporation-group-select").empty().trigger('change');
    var corporationId = $(this).val();
    $("#corporation-after-select").text(corporationId);
    $("#corporation-after-select").attr("href", "/corporations/" + corporationId + "/edit");

    if (corporationId) {
      loadCorporationGroup(corporationId).done(function(response) {
        var selectedData = $("#corporation-group-select").data("selected");
        var selectedCorporationGroup = _.find(response.data, {id: selectedData});
        $.each(response.data, function(index, corporation_groups) {
          var option = new Option(corporation_groups.full_name, corporation_groups.id, true, true);
          $("#corporation-group-select").append(option);
        });
        if (!selectedCorporationGroup && response.data[0]) {
          var selectedData = response.data[0].id;
        }
        $("#corporation-group-select").val(selectedData).trigger("change");
        $("#corporation-group-after-select").text(selectedData);
        $("#corporation-group-after-select").attr("href", "/corporations/" + corporationId +
          /corporation_groups/ + selectedData + "/edit");
      });
    }
    initThumbnail(corporationId)
  }).trigger("change");

  $(".disable-create-survey__checkbox").on("change", function(){
    var inputElements = $(".admin-location-survey__table textarea, .admin-location-survey__table select, .admin-location-survey__table input");
    var setDisableForForm = function(elements, isDisable){
      _.each(elements, function(e){
        e.disabled = isDisable;
      })
    }
    if($(this).is(":checked")){
      setDisableForForm(inputElements, true);
    } else {
      setDisableForForm(inputElements, false);
    }
  }).trigger("change");

  function initThumbnail(corporationId){
    if(corporationId){
      loadJobCategories(corporationId).done(function(response){
        if(response.is_lawson){
          $(".outside-lawson").hide();
          $(".jacket-check-box").prop("checked", true);
        } else {
          $(".outside-lawson").show();
          $(".jacket-check-box").prop("checked", false);
        }
      })
    } else {
      $(".outside-lawson").hide();
    }
  }

  $("#corporation-group-select").on("change", function() {
    $organizationSelect.empty().trigger("change");
    var corporationGroupId = $(this).val();
    var corporationId = $("#corporation-select").val();
    $("#corporation-group-after-select").text(corporationGroupId);
    $("#corporation-group-after-select").attr("href", "/corporations/" + corporationId +
      /corporation_groups/ + corporationGroupId + "/edit");

    if (corporationGroupId) {
      loadOrganization(corporationGroupId).done(function(response) {
        var selectedData = $organizationSelect.data("selected");
        var selectedOrganization = _.find(response.data, {id: selectedData});
        $.each(response.data, function(index, organizations) {
          var option = new Option(organizations.full_name, organizations.id, true, true);
          $organizationSelect.append(option);
        });
        if (!selectedOrganization && response.data[0]) {
          var selectedData = response.data[0].id;
        }
        $organizationSelect.val(selectedData).trigger("change");
      });
    }
  }).trigger("change");

  $('.station-select').select2({
    width: '100%',
    ajax: {
      url: '/locations/find_station',
      dataType: 'json',
      data: function(params) {
        var selectedStationId = $(this).val();
        return { search_content: params.term, selected_station_id: selectedStationId }
      },
      processResults: function(data) {
        return { results: data.results };
      }
    },
    templateResult: formatResult,
    templateSelection: formatSelection,
  }).trigger('change');

  function formatResult(station) {
    return station.full_name;
  }

  function formatSelection(station) {
    return station.text || station.full_name;
  }

  function loadOrganization(corporationGroupId) {
    return $.ajax({
      url: '/corporation_groups/'+ corporationGroupId +'/load_organizations',
      method: 'GET',
      dataType: 'json'
    });
  };

  function loadCorporationGroup(corporationId) {
    return $.ajax({
      url: "/corporations/"+ corporationId + "/load_corporation_groups",
      method: "GET",
      dataType: "json"
    });
  };

  function loadJobCategories(corporationId) {
    return $.ajax({
      url: "/corporations/"+ corporationId + "/load_job_categories",
      method: "GET",
      dataType: "json"
    });
  }

  $('.location-name').on('change', function(e){
    $('#location-name-selected').val(this.value);
  });

  $("#delete-location").on("click", function(e){
    var actionURL = $(this).closest("#form-location").attr("action");

    $.ajax({
      url: actionURL,
      method: 'DELETE',
      success: function(response) {
        $.lawsonAjax(response, {}, true, true);
        if (!response.status) {
          location.reload();
        }
      }
    })
  });

  $('.select-walking-time input[type="number"]').focusout(function(){
    if($(this).val() < 0 ) {
      $(this).val(0)
    }
  });

  $('#form-location').keypress(function(e) {
    var key = e.charCode || e.keyCode || 0;     
    if (key == 13 && e.target.tagName != "TEXTAREA") {
      e.preventDefault();
      $("input").blur();
    }
  });

  if ($(".upload-location-thumbnail").length) {
    $(".upload-location-thumbnail").on("change", function() {
      var previewThumbnailAfterSave = $(".preview_thumbnail");
      var locCropThumbnail = new CropImage({
        input: this,
        inputName: "thumbnail",
        previewThumbnailAfterSave: previewThumbnailAfterSave,
        cropper: {
          resizeWidth: 280,
          resizeHeight: 157
        }
      });
      locCropThumbnail.previewImage();
    });
  }

  if ($(".upload-location-thumbnail-background").length) {
    $(".upload-location-thumbnail-background").on("change", function() {
      var previewThumbnailAfterSave = $(".preview_thumbnail_background");
      var locCropThumbnailBackground = new CropImage({
        input: this,
        inputName: "thumbnail_background",
        previewThumbnailAfterSave: previewThumbnailAfterSave,
        cropper: {
          resizeWidth: 728,
          resizeHeight: 210
        }
      });
      locCropThumbnailBackground.previewImage();
    });
  }

  if ($(".upload-location-category-thumbnail").length) {
    $(".upload-location-category-thumbnail").on("change", function() {
      var previewThumbnailAfterSave = $(".preview_thumbnail");
      var locCropThumbnail = new CropImage({
        input: this,
        inputName: "thumbnail",
        previewThumbnailAfterSave: previewThumbnailAfterSave,
        cropper: {
          resizeWidth: 320,
          resizeHeight: 320
        }
      });
      locCropThumbnail.previewImage();
    });
  }
  selectForPriorityStaffs($priorityStaffsSelect);

  $(".priority_staffs").on("cocoon:after-insert", function(_, addedTask){
    var element = addedTask.find("select:first");
    selectForPriorityStaffs(element);
  });

  function selectForPriorityStaffs(element) {
    element.select2({
      ajax: {
        url: "/selectbox_limit_datas/?class_name=Staff&with_staff_number=true",
        dataType: "json",
        data: function(params) {
          return {
            search_content: params.term,
            selected: $(this).val()
          };
        },
        processResults: function(data) {
          return {results: data.results};
        }
      },
      templateResult: formatResultANS,
      templateSelection: formatSelectionANS
    }).trigger("change");
  }

  function formatResultANS(staff) {
    return staff.account_name_with_staff_number;
  }

  function formatSelectionANS(staff) {
    return staff.text || staff.account_name_with_staff_number;
  }

  $("#delete-location-payment-rates").on("click", function(e){
    $.ajax({
      url: "/location_billing_unit_prices/delete_all",
      data: {location_id: $("#location_id").val()},
      method: 'DELETE',
      success: function(response) {
        $.lawsonAjax(response, {}, true, true);
        if (response.status) {
          location.reload();
        }
      }
    })
  });
});
