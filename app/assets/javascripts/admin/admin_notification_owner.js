$(function() {
  var $sentDateField = $("#admin_notification_owner_sent_date");
  var $sentNowField = $("#admin_notification_owner_is_sent_now");
  var $sentNowCheckBox= $("#is_sent_now");
  var $attachmentField = $("#admin_notification_owner_attachment_url");
  var $remoteAttachmentFlag = $("#admin_notification_owner_remove_attachment_url");
  var $remoteAttachmentBtn = $("#delete-attachment-btn");
  var $attachmentPreview = $("#attachment-preview");
  var $confirmDeleteattachmentPopup = $("#confirm-delete-attachment");
  var notificationId = $attachmentPreview.attr("data-notification-id");
  var oldFileName = $attachmentPreview.attr("data-old-file-name");
  var $submitAdminNotificationOwner = $("#submit-admin-notification-owner");
  var $adminNotificationOwnerForm = $("#form-notification-admin-to-owner");
  var $confirmSaveForm = $("#confirm-save-admin-notification-owner");
  var $attachmentName = $("#attachment-name");
  var $attachmentUrl = $("#link-to-attachment-url");
  var $selectExceptCorporationIds = $(".sellect-except-corporation-ids");
  var $selectCorporationType = $("input[name ='admin_notification_owner[corporation_type]']");
  var $selectDepartmentIds = $("#admin_notification_owner_department_ids");

  $remoteAttachmentFlag.val(false);
  $sentNowField.val(false);

  $sentDateField.datepicker({
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true
  });

  $sentNowCheckBox.on("change", function() {
    if ($(this).is(":checked")) {
      $sentNowField.val(true);
      $sentDateField.val("");
      $sentDateField.prop("disabled", true);
    } else {
      $sentNowField.val(false);
      $sentDateField.prop("disabled", false);
    }
  });

  $submitAdminNotificationOwner.on("click", function() {
    $adminNotificationOwnerForm.trigger("submit");
  });

  if (notificationId) {
    previewAttachmentByUrl(notificationId, oldFileName);
    $remoteAttachmentFlag.val(false);
  }

  $remoteAttachmentBtn.on("click", function() {
    $confirmDeleteattachmentPopup.modal("hide");
    $attachmentField.val("");
    $remoteAttachmentFlag.val(true);
    $attachmentPreview.data("data-old-attachment", "");
    $attachmentPreview.closest(".form-group").addClass("hidden");
  });

  $adminNotificationOwnerForm.on("submit", function(e) {
    e.preventDefault();
    var actionURL = $(this).attr("action");
    var method = $(this).attr("method");
    var data = new FormData(this);
    $.ajax({
      url: actionURL,
      method: method,
      dataType: "json",
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $.lawsonAjax(response, {}, true);
      }
    });
    $confirmSaveForm.modal("hide");
  });

  $attachmentField.on("change", function() {
    if ($(this).val()) {
      previewAttachment(this);
      $remoteAttachmentFlag.val(false);
    } else {
      if (notificationId) {
        previewAttachmentByUrl(notificationId, oldFileName);
        $remoteAttachmentFlag.val(false);
      } else {
        $remoteAttachmentFlag.val(true);
        $attachmentPreview.closest(".form-group").addClass("hidden");
      }
    }
  });

  $selectCorporationType.on("change", function() {
    $selectExceptCorporationIds.empty();
  });

  $selectExceptCorporationIds.select2({
    width: "100%",
    ajax: {
      url: "/selectbox_corporations_by_departments/",
      dataType: "json",
      data: function(params) {
        var selectCorporationTypeValue = $("input[name ='admin_notification_owner[corporation_type]']:checked").val();
        return {
          search_content: params.term,
          selected: $(this).val(),
          corporation_type: selectCorporationTypeValue,
          department_ids: $selectDepartmentIds.val()
        };
      },
      processResults: function(data) {
        return {results: data.results};
      }
    },
    templateResult: formatResult,
    templateSelection: formatSelection
  }).trigger("change");

  function formatResult(corporation) {
    return corporation.full_name;
  }

  function formatSelection(corporation) {
    return corporation.text || corporation.full_name;
  }

  function previewAttachment(inputContent) {
    $attachmentUrl.removeAttr("href");
    $attachmentUrl.removeAttr("target");
    var reader = new FileReader();
    var file = inputContent.files[0];
    reader.onload = function() {
      $attachmentName.text(decodeURIComponent(file.name));
      $attachmentPreview.closest(".form-group").removeClass("hidden");
    };
    reader.readAsDataURL(inputContent.files[0]);
  }

  function previewAttachmentByUrl(nId, fileName) {
    var actionURL = "/admin_notification_owners/download_notification_attachment?notification_id=" + nId;
    $attachmentUrl.attr("href", actionURL);
    $attachmentUrl.attr("target", "_blank");
    $attachmentName.text(decodeURIComponent(fileName));
    $attachmentPreview.closest(".form-group").removeClass("hidden");
  }
});
