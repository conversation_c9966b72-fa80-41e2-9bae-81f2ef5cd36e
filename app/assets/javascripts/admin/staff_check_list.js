$(function() {
  var REQURIED_INPUTS = ["staff-checklist-license", "staff-checklist-is-identify-other"];

  init();

  function init() {
    initForRadio("my_number_receipt");
    checkChangeForRadio("my_number_receipt");

    REQURIED_INPUTS.forEach(function(field) {
      initForInput(field);
      checkChange(field);
    });
  }

  function initForRadio(field) {
    var isChecked = $("#form-staff input[name$='["+ field +"]']:checked").val();
    var input = $("#" + field.replace(/_/g, "-") + "-depend");

    if(!_.isUndefined(isChecked)) input.attr("disabled", !(isChecked == "true"));
  }

  function checkChangeForRadio(field) {
    $("#form-staff input[name$='["+ field +"]']").change(function() {
      $("#" + field.replace(/_/g, "-") + "-depend").attr("disabled", !(this.value == "true"));
    });
  }

  function initForInput(field) {
    var isChecked = document.getElementById(field);
    var input = $("#" + field + "-depend");

    if(!_.isNull(isChecked)) input.attr("disabled", !isChecked.checked);
  }

  function checkChange(field) {
    $("#" + field).change(function() {
      $("#" + field + "-depend").attr("disabled", !this.checked);
    });
  }
});
