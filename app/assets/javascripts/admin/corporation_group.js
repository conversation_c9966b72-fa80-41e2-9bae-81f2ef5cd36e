$(document).ready(function() {
  var $removeViolationFileBtn = $("#remove-violation-file-btn");
  var $remoteViolationFileFlag = $("#corporation_group_remove_violation_file");
  var $violationFileField = $("#corporation_group_violation_file");
  var $violationFileUrl = $("#link-to-violation-file");
  var $violationFilePreview = $("#violation-file-preview");
  var corporationId = $violationFilePreview.attr("data-corporation-group-id");
  var oldFileName = $violationFilePreview.attr("data-old-file-name");
  var $violationFileName = $("#violation-file-name");
  var $currentCorporationGroup = $("#data-current-corporation-group");
  $remoteViolationFileFlag.val(false);

  $('#form-corporation-group').on('submit', function(e) {
    e.preventDefault();
    var actionURL = $(this).attr('action');
    var $picDepartmentSelect = $('#pic-department-select');

    $picDepartmentSelect.remove('#params-department');
    if ($picDepartmentSelect.val() == undefined) {
      $picDepartmentSelect.append($('<input>', {value: "", name: "corporation_group[pic_department_id]",
        type: "hidden", id: "params-department"}));
    }

    $.ajax({
      url: actionURL,
      method: 'POST',
      dataType: 'json',
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $.lawsonAjax(response);
      }
    })
  });

  if ($('#form-corporation-group').length > 0 && $(location).attr('href').indexOf('/new') !== -1) {
    $('#confirm-modal').on('show.bs.modal', function() {
      var $confirmModal = $(this);
      $.ajax({
        url: '/corporation_groups/check_name_unique',
        method: 'POST',
        dataType: 'json',
        data: {'name_1': $('#form-corporation-group').find('input[name="corporation_group[name_1]"]').val()},
        success: function(response) {
          var confirmKey = response.is_existed ? 'confirm_unique_name' : 'confirm_message';
          $confirmModal.find('.message-content').html(I18n.t('corporation.main_form.' + confirmKey));
        }
      });
    });
  }

  var textboxIndex = $('.all-owner-code').find('.nested-form').length;
  $('#add-owner-code').on('click', function() {
    var textboxOwnerCode = '\
      <div class="form-group fix-nested-form">\
        <div class="nested-form">\
          <div class="input-group owner-code">\
            <input type="hidden" name="corporation_group[corporation_group_owner_codes_attributes]['+ textboxIndex +'][id]"">\
            <input class="form-control owner-code" data-toggle="tooltip" data-placement="top" \
              tabindex="90" name="corporation_group[corporation_group_owner_codes_attributes][' + textboxIndex + '][owner_code]" \
              id="corporation_group_corporation_group_owner_codes_attributes_' + textboxIndex + '_owner_code">\
            <div class="input-group-btn fix-top-button">\
              <button class="btn btn-default btn-flat delete-owner-code btn-remove" type="button">\
                <i class="fa fa-times"></i>\
              </button>\
            </div>\
          </div>\
        </div>\
      </div>';
    $('.all-nested-input').append(textboxOwnerCode);
    textboxIndex++;
  });

  $('.all-owner-code').on('click', '.delete-owner-code', function() {
    $(this).closest('.form-group').hide();
  });

  $('.delete-owner-code').on('click', function(e){
    var $this = $(this);
    $this.closest('.nested-form').find('.destroy-corporation').val(true);
    $this.closest('.nested-form').find('.help-block').remove();
    $this.closest('.form-group').hide();
    e.preventDefault();
  });

  var url = document.location.toString();
  if (url.match('#')) {
    $('.nav-tabs a[href="#' + url.split('#')[1] + '"]').tab('show');
  };

  if (oldFileName) {
    previewAttachmentByUrl(corporationId, oldFileName);
  } else {
    $currentCorporationGroup.addClass("hidden");
  }

  $removeViolationFileBtn.on("click", function() {
    $violationFileField.val("");
    $remoteViolationFileFlag.val(true);
    $violationFilePreview.closest(".form-group").addClass("hidden");
  });

  $violationFileField.on("change", function() {
    if ($(this).val()) {
      previewAttachment(this);
      $remoteViolationFileFlag.val(false);
    } else {
      if (corporationId) {
        previewAttachmentByUrl(corporationId, oldFileName);
        $remoteViolationFileFlag.val(false);
      } else {
        $remoteViolationFileFlag.val(true);
        $violationFilePreview.closest(".form-group").addClass("hidden");
      }
    }
  });

  function previewAttachment(inputContent) {
    $violationFileUrl.removeAttr("href");
    $violationFileUrl.removeAttr("target");
    var reader = new FileReader();
    var file = inputContent.files[0];
    reader.onload = function() {
      $violationFileName.text(decodeURIComponent(file.name));
      $violationFilePreview.closest(".form-group").removeClass("hidden");
    };
    reader.readAsDataURL(inputContent.files[0]);
  }

  function previewAttachmentByUrl(nId, fileName) {
    var actionURL = "/corporation_groups/download_violation_file?id=" + nId;
    $violationFileUrl.attr("href", actionURL);
    $violationFileUrl.attr("target", "_blank");
    $violationFileName.text(decodeURIComponent(fileName));
    $violationFilePreview.closest(".form-group").removeClass("hidden");
  }

  $('input.upload-violation-file-log').on('change', function (event) {
    var $this = $(this);
    var files = event.target.files;
    var formData = new FormData();
    formData.append('violation_file', files[0]);
    var logId = $this.parents('.violation-date-log').data("logId");
    var url = '/violation_date_logs/upload_violation_file?violation_date_log_id=' + logId;
    $.ajax({
     url: url,
     method: 'POST',
     processData: false,
     contentType: false,
     cache: false,
     data: formData,
     success: function(response) {
      if (response.status) {
        $this.val("");
        $this.removeClass("error");
        $this.addClass("inactive");
        $this.parents("tr").find(".show-log-violation-file").removeClass("inactive");
        var $attachment = $this.parents("tr").find(".violation-file-url");
        $attachment.html(response.file_name);
      } else {
        $this.val("");
        $this.addClass("error");
        $this.removeClass("inactive");
        $this.parents("tr").find(".show-log-violation-file").addClass("inactive");
        showErrorPopUp($this, response.errors);
      }
     }
    });
  });

  $(".remove-violation-file").on('click', function(e){
    $this = $(this);
    var logId = $this.parents('.violation-date-log').data("logId");
    var url = '/violation_date_logs/delete_violation_file';
    var data = {
      violation_date_log_id: logId
    };
    $.ajax({
      url: url,
      method: 'DELETE',
      type: "json",
      data: data,
      success: function(response) {
        if (response.status) {
          $this.parents("tr").find(".upload-violation-file-log").removeClass("inactive");
          $this.parents("tr").find(".show-log-violation-file").addClass("inactive");
        } else {
          $this.parents("tr").find(".upload-violation-file-log").addClass("inactive");
          $this.parents("tr").find(".show-log-violation-file").removeClass("inactive");
          showErrorPopUp($this, response.errors);
        }
      }
    });
  });

  $(".edit-log").on('click', function(e){
    $this = $(this);
    $this.parents("td").find(".edit-violation-log").addClass("inactive");
    $this.parents("td").find(".edit-violation-log").val("");
    $this.parents("td").find("i").removeClass("inactive");
    $this.addClass("inactive");
    $this.parents("td").find("span").addClass("inactive");
    var $inputBox = $this.parents("td").find("input")
    $inputBox.removeClass("inactive");
    $inputBox.val($(this).data("value"));
    $inputBox.focus();
  });


  var $inputViolationLog = $("input.edit-violation-log");
  $inputViolationLog.blur(function() {
    $element = $(this);
    setTimeout(function () {
      changeFieldEdit($element);
    }, 100);
  });

  $inputViolationLog.datepicker({
    onClose: function() {
      changeFieldEdit($(this));
    }
  });

  function changeFieldEdit(elem) {
    $this = elem;
    var logId = $this.parents(".violation-date-log").data("logId");
    var data = {
      violation_date_log_id: logId
    };
    var inputValue = $this.val();
    data[$this.data("type")] = inputValue;

    var url = "/violation_date_logs/update_violation_log";
    $.ajax({
      url: url,
      method: "POST",
      type: "json",
      data: data,
      success: function(response) {
        if (response.status) {
          $this.addClass("inactive");
          $this.removeClass("error");
          $this.parents("td").find("span").removeClass("inactive");
          $this.parents("td").find("span").html(inputValue);
          $this.parents("td").find("i").removeClass("inactive");
          $this.parents("td").find("i").data("value", inputValue);
        } else {
          showErrorPopUp($this, response.errors);
        }
      }
    });
  }

  $("#corporation_group_violation_extended_date,#corporation_group_haken_acceptance_started_at,#corporation_group_violation_day").on("change", function() {
    $violationFileField.val("");
    $remoteViolationFileFlag.val(true);
    $violationFilePreview.closest(".form-group").addClass("hidden");
  });

  function showErrorPopUp(element, messages) {
    element.addClass("error");
    $("#modal-error-message").html(messages);
    $("#modal-error").modal("show");
  }
});
