$(function() {
  JAPAN_ID = 1282;
  EDITED_EXPECTATION = {};

  var $allStationSelect = $('.admin-staff-station-select');

  setFormatFullDate();
  setFormatDate();
  setFormatOnlyTime();
  setContactHistoryTimePicker();
  setPositionTimePicker($('.contact-history-time-picker'));
  compateDateOfHire("#date-of-hire");
  disableOrEnableContractEndDate($('#staff_indefinite_employment_flag').val() !== 'term');
  disableOrEnableContractStartDate($('#staff_indefinite_employment_flag').val());
  disableOrEnableRetirementReason($('#staff_retirement_date').val() === '');

  $allStationSelect.select2({width: '100%'});

  $('#staff_prefecture_id').on('change', function() {
    var prefectureId = $(this).val();

    if (!prefectureId) return;

    $.ajax({
      url: "/stations?prefecture_id=" + prefectureId,
      method: 'GET',
      dataType: 'json',
      success: function(response) {
        $.each(response.stations, function (i, station) {
          $allStationSelect.append($('<option>', {
            value: station.id,
            text : station.name
          }));
        });
        $allStationSelect.each(function() {
          $(this).val($(this).data('value'));
        });
        $allStationSelect.data('value', null);
      }
    });
  }).trigger('change');

  $('.body_staff_education_backgrounds, .body_staff_work_experiences').on('cocoon:after-insert', function() {
    setFormatDate();
  });

  $('.body_staff_contact_history').on('cocoon:after-insert', function(event, insertItem) {
    setFormatFullDate();
    var idNumber = insertItem.find(".staff-date-picker").attr("id").replace(/[^0-9.]/g, "");
    var elementTimes = insertItem.find(".contact-history-time-picker");
    elementTimes.each(function(index, elem){
      var newIdElement = $(elem).attr("id").replace(/[0-9]/g, "") + idNumber;
      $(elem).attr("id", newIdElement);
      $(elem).find("input").attr("data-target", "#" + newIdElement);
    });
    setContactHistoryTimePicker();
    setPositionTimePicker(elementTimes);
  });

  $("#date-of-hire").datepicker().on("change", function(){
    compateDateOfHire(this);
  });

  $('#staff_indefinite_employment_flag').on('change', function() {
    var val = $('#staff_indefinite_employment_flag').val();
    disableOrEnableContractEndDate(val !== 'term');
    disableOrEnableContractStartDate(val);
  });

  $(document).on("click", ".btn-close-modal-detail-mail-history", function(e) {
    $("#modal-detail-mail-history .message-content").html("");
  });

  $('#staff_retirement_date').datepicker().on('change', function() {
    var val = $('#staff_retirement_date').val();
    disableOrEnableRetirementReason(val === '');
  });

  function disableOrEnableContractEndDate(isDisable) {
    $('#staff_contract_end_date, #staff_contract_type').attr('disabled', isDisable);
    if (isDisable) {
      $('#staff_contract_end_date, #staff_contract_type').val('')
    }
  }

  function disableOrEnableContractStartDate(indefiniteEmploymentFlag) {
    var isDisable = indefiniteEmploymentFlag === 'employment_flag_blank'
    var currentStartDate = $('#staff_contract_start_date').val();
    $('#staff_contract_start_date').attr('disabled', isDisable);
    if (isDisable) {
      $('#staff_contract_start_date').val('');
    } else if (indefiniteEmploymentFlag === 'term') {
      $('#staff_contract_start_date').val(currentStartDate);
    } else {
      var newStartDate = currentStartDate || (new Date()).toISOString().slice(0,10).replace(/\-/g, '/');
      $('#staff_contract_start_date').val(newStartDate);
    }
    $('#staff_contract_start_date').datepicker('setDate', $('#staff_contract_start_date').val());
  }

  function disableOrEnableRetirementReason(isDisable) {
    $('#staff_retirement_reason_type, #staff_retirement_reason').attr('disabled', isDisable);
    if (isDisable) {
      $('#staff_retirement_reason_type, #staff_retirement_reason').val('')
    }
  }

  function compateDateOfHire(selector) {
    var chooseDate = new Date($(selector).val());
    var chooseDateMM = moment(chooseDate);
    var currentDate = new Date(Date.now());
    var currentDateMM = moment(currentDate).startOf("day");
    var different = moment.duration(currentDateMM.diff(chooseDateMM));
    var showText = "";

    if (chooseDateMM.isBefore(currentDateMM)) {
      if (!_.isEqual(different.years(), 0)) {
        showText += different.years().toString() + I18n.t("common.date_time.year");
      }
      if (!_.isEqual(different.months(), 0)) {
        showText += different.months().toString() + I18n.t("common.date_time.month");
      }
      if (!_.isEqual(different.days(), 0)) {
         showText += different.days().toString() + I18n.t("common.date_time.day");
      }
    }

    $("#compare-date-of-hire").empty().append(showText);
  }

  $("#form-staff").on("submit", function(e) {
    $("#spinner").removeClass("ng-hide");
    e.preventDefault();
    var actionURL = $(this).attr("action");
    $.ajax({
      url: actionURL,
      method: "POST",
      dataType: "json",
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        if (!response.status && response.reload){
          return location.reload()
        }

        var extraErrorElements = {
          japanese_level: ".japanese-level-staff-check-item",
          my_number_receipt: ".my-number-receipt-staff-check-item",
          base: [".base-staff-expectation", ".base-staff-check-item"]
        };
        var radioErrors = ["gender_id", "my_number_receipt", "account_type"];
        var originErrorModels = ["staff_contract_histories"];
        $.lawsonAjaxStaffEditForm(response, extraErrorElements, true, radioErrors, originErrorModels);
        $("#spinner").addClass("ng-hide");
      }
    });
  });

  $(".staff-birthday").on("change", function() {
    var birthDay = new Date($(this).val());
    var toDay = new Date();
    var age = toDay.getFullYear() - birthDay.getFullYear();
    var m = toDay.getMonth() - birthDay.getMonth();
    if (age > 0 && (m < 0 || (m === 0 && toDay.getDate() < birthDay.getDate()))) {
      age--;
    }
    $(".staff-age").html(age);
  });

  $(".staff-nationality").on("change", function() {
    var $japanLevel = $(".select-japanese-level");
    if ($(this).val() == JAPAN_ID) {
      $japanLevel.addClass("disabled");
      $japanLevel.val("");
    } else {
      $japanLevel.removeClass("disabled");
    }
  });

  $(".staff-expectation input[type='text']").each(function() {
    EDITED_EXPECTATION[$(this).attr("id")] = $(this).val();
  });

  $(".staff-expectation input[type='checkbox']").on("change", function() {
    if ($(this).is(':checked')) {
      var inputs = $(this).parents(".group-relation").find("input[type='text']");
      inputs.prop("readonly", false);
      inputs.each(function(_, input) {
        $(input).val(EDITED_EXPECTATION[$(input).attr("id")]);
      });
    } else {
      $(this).parents(".group-relation").find("input[type='text']").prop("readonly", true);
      $(this).parents(".group-relation").find("input[type='text']").val("");
    }
  });
});

function setFormatDate() {
  $(".education-date-picker").datepicker({
    autoclose: true,
    format: DATE_FORMAT_ONLY_MONTH_YEAR,
    minViewMode: DATE_MONTH_YEAR_VIEW_MODE
  });
}

function setFormatFullDate() {
  $(".staff-date-picker").datepicker({
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true
  });
}

function setFormatOnlyTime() {
  $(".only-time-picker").datetimepicker({
    format: TIME_PICKER_FORMAT,
    useCurrent: "roundedMin",
    roundedMinTo: 10
  });
}

function setContactHistoryTimePicker() {
  $(".contact-history-time-picker").datetimepicker({
    format: TIME_PICKER_FORMAT,
    useCurrent: "roundedMin",
    roundedMinTo: 10,
    widgetParent: ".admin-staff-form"
  });
}

function setPositionTimePicker(elementTimes) {
  elementTimes.on("show.datetimepicker", function(e) {
    var currentElementOffset = $(e.target).offset();
    $(".bootstrap-datetimepicker-widget").css({
      top: currentElementOffset.top + 35,
      left: currentElementOffset.left - 10
    });
  });
}
