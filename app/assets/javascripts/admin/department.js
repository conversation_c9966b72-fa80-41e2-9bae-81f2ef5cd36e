$(function() {
  if ($("form#form-department").length) {
    $("form#form-department").on("click", "#department_department_pics_assigned_at, #department_department_pics_resigned_at", function() {
      $(this).datepicker({
        format: DEFAULT_DATE_TIME_FORMAT,
        todayBtn: true,
        todayHighlight: true,
        autoclose: true
      }).datepicker("show");
    })
  }

  $("#form-department").on("submit", function(e) {
    e.preventDefault();
    var actionURL = $(this).attr("action");

    $.ajax({
      url: actionURL,
      method: "POST",
      dataType: "json",
      data: new FormData(this),
      processData: false,
      contentType: false,
      success: function(response) {
        $.lawsonAjax(response, {}, true);
      }
    });
  });

  $(".btn-delete-modal").on("click", function(){
    var actionURL = $("#" + $(this).data("form-id")).attr("action");
    $.ajax({
      url: actionURL,
      method: "DELETE",
      success: function(response) {
        $.lawsonAjax(response, {}, true);
        if (!response.status) {
          location.reload();
          window.scrollTo(0, 0);
        };
      }
    });
  });
});
