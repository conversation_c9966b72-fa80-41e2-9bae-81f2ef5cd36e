$(function() {
  var XLSX_TYPE = "xlsx"
  var CSV_TYPE = "csv"

  if ($(".admin-staff-search").length) {
    $("a.btn-export-staffs").on("click", function(e) {
      var staffs = $("input[name='ids[]']:checked").serializeArray();
      var staffIds = _.map(staffs, "value");
      $.asyncDownload(e, "staffs", JSON.stringify({id: staffIds}), XLSX_TYPE, {});
    });
  };

  if ($(".extend-order-list").length) {
    $("a.btn-export-order").on("click", function(e) {
      var paramsSearch = JSON.parse($(this).attr("data-search"));
      var staffExpressOrderId = $(".staff-express-order-id").val();
      $.asyncDownload(e, "orders", paramsSearch, XLSX_TYPE, {staff_express_order_id: staffExpressOrderId});
    });
  };

  $("button#btn-export-payment-unit-price").on("click", function(e) {
    $.asyncDownload(e, "payment_unit_prices", {}, CSV_TYPE);
  });

  if ($(".btn-export-staffs").length) {
    $("a.btn-export-staffs").on("click", function(e){
      var staffs = $("input[name='ids[]']:checked").serializeArray();
      var staffIds = _.map(staffs, "value");
      $.asyncDownload(e, "staffs", JSON.stringify({id: staffIds}), XLSX_TYPE);
    })
  };

  if ($(".btn-export-entries").length) {
    $("a.btn-export-entries").on("click", function(e){
      var staffs = $("input[name='ids[]']:checked").serializeArray();
      var staffIds = _.map(staffs, "value");
      $.asyncDownload(e, "entries", JSON.stringify({id: staffIds}), XLSX_TYPE);
    })
  };

  if ($(".btn-export-location-survey").length) {
    $(".btn-export-location-survey").on("click", function(e){
      var locationSurveyId = $("input[name='admin_location_survey[location_survey_id]']").val();
      json_object = {id: locationSurveyId, file_type: XLSX_TYPE}
      json_str = JSON.stringify(json_object)
      $.asyncDownload(e, "location_surveys", json_str, XLSX_TYPE, json_object);
    })
  };

  if ($(".btn-download-status-location-survey").length) {
    $("a.btn-download-status-location-survey").on("click", function(e){
      var searchParams = JSON.parse($(this).attr("data-search"));
      $.asyncDownload(e, "locations", searchParams, CSV_TYPE);
    })
  };
});
