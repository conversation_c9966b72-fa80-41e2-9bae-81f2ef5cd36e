$(function () {
  var modalCounter = 0;

  $(".confirm-arrange-modal.modal").on("shown.bs.modal", function () {
    modalCounter++;
  });
  $(".confirm-arrange-modal.modal").on("hidden.bs.modal", function () {
    modalCounter--;
    if(modalCounter){
      $("body").addClass("modal-open");
    }
    else{
      $("body").removeClass("modal-open");
    }
  });

  $(".arrangement-search-result .unfrozen-view__rows").on("scroll mousewheel", function() {
    var scrollLeft = $(this).scrollLeft();
    var scrollTop = $(this).scrollTop();

    $(".arrangement-search-result .unfrozen-view__header").scrollLeft(scrollLeft);
    $(".arrangement-search-result .unfrozen-view__footer").scrollLeft(scrollLeft);
    $(".arrangement-search-result .frozen-view__rows").scrollTop(scrollTop);
  })

  $(".arrangement-search-result .frozen-view__rows").on("scroll mousewheel", function() {
    var scrollLeft = $(this).scrollLeft();
    var scrollTop = $(this).scrollTop();
    $(".arrangement-search-result .frozen-view__header").scrollLeft(scrollLeft);
    $(".arrangement-search-result .frozen-view__footer").scrollLeft(scrollLeft);
    $(".arrangement-search-result .unfrozen-view__rows").scrollTop(scrollTop);
  })

  $("#form-create-arrangement").submit(function(e){
    return false;
  });

  $(".btn-submit-send-order").on("click", function() {
    $(this).addClass("disabled");
  });
});
