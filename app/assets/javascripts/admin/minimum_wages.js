$(function() {
  $(".wage-dropdown").on("shown.bs.dropdown", function () {
    handleSaveBtn();
  });

  $(".wage-dropdown").on("hidden.bs.dropdown", function () {
    handleSaveBtn();
  });

  $(".wage-date-dropdown, .current-wage-dropdown").on("click", function (e) {
    if (!$(e.target).hasClass("input-all-approve-date")) {
      return false;
    }
  });

  function handleSaveBtn() {
    if($(".wage-dropdown.open").length > 0) {
      $(".save-minimum-wage").attr("disabled", true);
    } else {
      $(".save-minimum-wage").removeAttr("disabled");
    }
  }

  $(document).click(function(e) {
    if (!$(e.target).hasClass("input-all-approve-date")) {
      $(".wage-date-dropdown").hide();
    }
    $(".current-wage-dropdown").hide();
  });

  allowNumber($(".minimum-wage-wrap-content"), $(".current-wage-number"));
  allowNumber($(".minimum-wage-wrap-content"), $(".new-wage-number"));
});
