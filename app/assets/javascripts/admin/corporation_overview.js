$(function() {
  $(".corporation-datepicker").datepicker({
    autoclose: true,
    format: DEFAULT_DATE_TIME_FORMAT,
    todayBtn: true,
    todayHighlight: true
  });
  initBillingForm();
  initThumbnail();

  // Overview Address tab
  $(".address-postal-code-search-item").bind("change keyup input", function(e) {
    if (KEY_UP_CTRL_C_CODE.includes(e.keyCode)) {
      return;
    } else {
      var postalCode = $(this).val();
      $parentDiv = $(this).parents(".address-div");
      if (canSearchPostalCode(postalCode)) {
        getDataPostalCode(postalCode.replace(/\-/g, ""), true, undefined, $parentDiv);
      }
    }
  });

  $(".search-postal-code").click(function(e) {
    e.preventDefault();
  });

  $(".modal-postal-code").on("click", "tr", function() {
    var currentTarget = _.find($(".open-modal-postal-code"), function(a){ return $(a).data("modal-active") == "1" });
    var postalCode = $(this).data("current-postal");
    if (_.isUndefined(currentTarget)) {
      var currentTabActive = $("#overview .overview-nav-tab .active").data("nav-id");
      if(_.isUndefined(currentTabActive)) currentTabActive = $("#overview").data("nav-id");

      if(currentTabActive === 1) {
        fillDataAddress(postalCode, false);
        $(".billing-postal-code-search-item").val(postalCode.postal_code);
      } else if(currentTabActive === 0) {
        fillDataAddress(postalCode, true);
        $(".address-postal-code-search-item").val(postalCode.postal_code).trigger("keypress");
      }
    } else {
      var addressDiv = $(currentTarget).parents(".address-div");
      fillDataAddressWithAddressDiv(postalCode, addressDiv);
      addressDiv.find(".address-postal-code-search-item").val(postalCode.postal_code).trigger("keypress").trigger("change");
    }
    $("#list-postal-code").modal("hide");
  });

  // Overview Billing tab
  function initBillingForm() {
    if (!$(".can-edit-billing").is(":checked")) {
      $(".billing-copy-address :input:not(:checkbox)").attr("readonly", true);
      $(".billing-prefecture, .postal-search-billing").addClass("disabled");
    }
    if (!$(".can-edit-billing-name").is(":checked")) {
      $(".billing-copy-main-info :input:not(:checkbox)").attr("readonly", true);
      $(".billing-title").addClass("disabled");
    }
  }

  function initThumbnail(){
    var isLawsonElement = $(".coporation-main-info .lawson");
    var anotherLawsonElement = $(".coporation-main-info .outside-lawson");
    // Radio Button (isLawson or anotherLawson)
    if(isLawsonElement.is(":checked")){
      isLawsonElement.disabled = true;
      $(".corporation-outside-lawson").hide();
    }
    if(anotherLawsonElement.is(":checked")){
      anotherLawsonElement.disabled = false;
      $(".corporation-outside-lawson").show();
    }
  }

  $(".coporation-main-info .input-checkbox").on("click", function(e){
    initThumbnail();
  });

  $("[data-copyable='true']", "#overview-address").on("change keyup keypress", function() {
    if (!$(".can-edit-billing").is(":checked")) {
      var currentVal = $(this).val();
      var targetElement = $(this).data("target");
      $("." + targetElement).val(currentVal);
    }
  });

  $("[data-copyable='true']", ".coporation-main-info").on("change keyup keypress", function() {
    if (!$(".can-edit-billing-name").is(":checked")) {
      var currentVal = $(this).val();
      var targetElement = $(this).data("target");
      $("." + targetElement).val(currentVal);
    }
  });

  $(".can-edit-billing").change(function() {
    if ($(this).is(":checked")) {
      $(".billing-copy-address :input").removeAttr("readonly");
      $(".billing-prefecture, .postal-search-billing").removeClass("disabled");
    } else {
      var inputs = ["postal-code-search-item", "prefecture", "city", "street-number", "building", "tell", "fax"];
      referFromAddress(inputs);
      $(".billing-copy-address :input:not(:checkbox)").attr("readonly", true);
      $(".billing-prefecture, .postal-search-billing").addClass("disabled");
    }
  });

  $(".can-edit-billing-name").change(function() {
    if ($(this).is(":checked")) {
      $(".billing-copy-main-info :input").removeAttr("readonly");
      $(".billing-title").removeClass("disabled");
    } else {
      referFromMainInfo();
      $(".billing-copy-main-info :input:not(:checkbox)").attr("readonly", true);
      $(".billing-title").addClass("disabled");
    }
  });

  $(".billing-postal-code-search-item").on("keyup", function(e) {
    if ($(".can-edit-billing").is(":checked")) {
      var postalCode = $(this).val();
      if (canSearchPostalCode(postalCode)) {
        getDataPostalCode(postalCode.replace(/\-/g, ""), false);
      }
    }
  });

  function referFromMainInfo() {
    var inputs = ["main-name-1", "main-name-2", "main-name-3", "main-name-title"];
    $.each(inputs, function(index, item) {
      $item = $("." + item);
      var target = $item.data("target");
      $("." + target).val($item.val());
    });
  }
});

function fillDataAddress(postalCode, isAddress, copyAddress) {
  var copyAddress = _.isUndefined(copyAddress) ? ".billing" : copyAddress;
  if (isAddress) {
    $(".address-prefecture").val(postalCode.prefecture_id).trigger("keypress").trigger("change");
    $(".address-city").val(postalCode.city).trigger("keypress");
    if (!postalCode.duplicate_code) {
      $(".address-street-number").val(postalCode.street_number).trigger("keypress");
    } else {
      $(".address-street-number").val("").trigger("keypress");
    }
  } else {
    $(copyAddress + "-prefecture").val(postalCode.prefecture_id);
    $(copyAddress + "-city").val(postalCode.city);
    if (!postalCode.duplicate_code) {
      $(copyAddress + "-street-number").val(postalCode.street_number);
    } else {
      $(copyAddress + "-street-number").val("").trigger("keypress");
    }
  }
}

function canSearchPostalCode(postalCode) {
  var validCode = postalCode.replace(/\-/g, "").trim();
  return window.config.postalCode.test(validCode) && validCode.length === POSTAL_CODE_SEARCH_NUMBER;
}

function getDataPostalCode(postalCode, isAddress, copyAddress, addressDiv) {
  $.ajax({
    url: "/postal_codes?postal_code="+ postalCode,
    method: "GET",
    dataType: "json",
    success: function(response) {
      var postalCodes = response.postal_codes;
      if (postalCodes.length > 0) {
        if (_.isUndefined(addressDiv) || addressDiv.length == 0)
          fillDataAddress(postalCodes[0], isAddress, copyAddress);
        else
          fillDataAddressWithAddressDiv(postalCodes[0], addressDiv);

      }
    }
  });
}

function referFromAddress(inputs, copyAddress) {
  var copyAddress = _.isUndefined(copyAddress) ? ".billing" : copyAddress;
  $.each(inputs, function(index, item) {
    $(copyAddress + "-" + item).val($(".address-" + item).val());
  });
}

function fillDataAddressWithAddressDiv(postalCode, addressDiv) {
  addressDiv.find(".address-prefecture").val(postalCode.prefecture_id).trigger("keypress").trigger("change");
  addressDiv.find(".address-city").val(postalCode.city).trigger("keypress").trigger("change");
  if (!postalCode.duplicate_code) {
    addressDiv.find(".address-street-number").val(postalCode.street_number).trigger("keypress").trigger("change");
  } else {
    addressDiv.find(".address-street-number").val("").trigger("keypress").trigger("change");
  }
}
