:root {
  --color-bg-blue-1: #4DB2CE;
  --color-bg-blue-2: #4DB2CE;
  --color-bg-primary: #fff;
  --color-inactive-button-text: #777777;
  --color-bg-shop: #3581A4;
  --color-bg-datebar: #4CB1CE;
  --color-bg-leftnum: #3581A4;
  --color-button: #4CB1CE;
  --color-text-gray: #373737;
  --color-inactive-btn: #F7F7F7;
  --color-text-soft-gray: #696969;
  --color-underline: #D8D8D8;
  --color-radio: rgba(0, 117, 110, 0.5);
  --color-button-light: rgba(77, 178, 206, 0.5);;
  --color-button-second: #4CB1CE;
  --color-button-grey: #eee;
  --color-background-grey: #F6F5F3;
  --color-flag-grey: rgba(0, 0, 0, 0.1);
  --color-tooltips-bg: #3581A4;
  --color-fire: #00859B;
  --color-pink: #E685B7;
  --color-inactive-pink: #808080;
  --color-arranged: #84E6DD;
  --color-applyed: #FAE0BE;
  --color-ended: #A0C9D3;
  --color-cancel:#DDDDDD;
  --color-notify-bg: #F1F1F1;
  --color-fade-modal: rgba(0, 0, 0, 0.5);
  --color-rest-time: rgba(0, 0, 0, 0.6);
  --color-over-gadident: #f8e6cf;
  --color-finish: #999999;
}
$mobile-width: 480px;
$xs-mobile-width: 320px;
@mixin iphone5 { @media (max-width: #{$xs-mobile-width}) {
  @content;
 }
}
@mixin mobile { @media (max-width: #{$mobile-width - 1px}) {
  @content;
 }
}

@font-face {
  font-family: 'fontawesome-free';
  src: asset-url('fa-solid-900.woff') format('woff'), asset-url('fa-solid-900.ttf')  format('truetype'), asset-url('fa-solid-900.svg#fa-solid-900') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'icomoon2';
  src: asset-url('icomoon2.woff') format('woff'), asset-url('icomoon2.ttf')  format('truetype'), asset-url('icomoon2.svg#icomoon2') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'icomoon';
  src: asset-url('icomoon.woff') format('woff'), asset-url('icomoon.ttf')  format('truetype'), asset-url('icomoon.svg#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "Ionicons";
  src: asset-url("ionicons.eot?v=2.0.0");
  src: asset-url("ionicons.eot?v=2.0.0#iefix") format("embedded-opentype"), asset-url("ionicons.ttf?v=2.0.0") format("truetype"), asset-url("ionicons.woff?v=2.0.0") format("woff"), asset-url("ionicons.svg?v=2.0.0#Ionicons") format("svg");
  font-weight: normal;
  font-style: normal;
}

body {
  font-family: "Barlow", "ヒラギノ角ゴ Pro W3", "Hiragino Kaku Gothic Pro",Osaka, "メイリオ", Meiryo, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif !important;
}

.icon {
  font-family: "icomoon2" !important;
  font-style: normal;
}

.icon-clock:before {
  content: "\e901";
}
.st-main-menu__link {
  .icon-clock {
    margin-right: 10px;
  }
}
.main-content {
  padding: 0;
  min-height: calc(100vh - 0px);
  background: var(--color-bg-blue-1);
}

.st-sidebar {
  z-index: 1000;
}

.overlay-mobile {
  z-index: 999;
}

.invisible {
  visibility: hidden;
}

.main-body-order-list {
  min-height: calc(100% - 120px);
}

.icon-newset {
  font-family: "icomoon" !important;
  font-style: normal;
  margin-right: 3px;
}

.icon-bell:before {
  content: "\e951";
}

.icon-fire:before {
  content: "\e9a9";
}

.icon-location:before {
  content: "\e947";
}

.icon-equalizer:before {
  content: "\e992";
}

.icon-heart:before {
  content: "\e9da";
}

.icon-list:before {
  content: "\e9ba";
}

.icon-spinner6:before {
  content: "\e97f";
}

.icon-spinner7:before {
  content: "\e980";
}

.icon-flag:before {
  content: "\e9cc";
}
.sp-work-detail__favor-icon {
  display: block;
  cursor: pointer;
  margin-top: -14px;
}
.sp-work-detail__favor-icon::before {
  content: '\f387';
  font-family: "Ionicons";
  font-size: 33px;
  color: #7F7F7F;
  transition: all .25s ease;
}
.sp-work-detail__favor-icon.active-animate {
  transition: all .2s ease;
  animation: favoriteAnimate .2s forwards;
  animation-delay: .1s;
}
.sp-work-detail__favor-icon.favorited-jobs::before {
  content: '\f388';
  font-family: "Ionicons";
  text-shadow: none;
  color: var(--color-pink);
}

.map-location {
  font-size: 10px;
  font-weight: 300;
  margin: 0;
  padding-left: 8px;
  padding-top: 4px;
  text-align: left;
  i {
    font-size: 24px;
    color: #7F7F7F;
  }
}
.main-header {
  height:56px;
  background-color:#fff;
  border-bottom:1px solid #f0f0f0;
  position:fixed;
  padding: 0 6px;
  top:0;
  left:0;
  width:100%;
  z-index:100;

  .button__icon {
    font-size: 24px;
    min-width: 21px;
  }
  
  .button {
    font-weight: 600;
    font-size: 13px;
    line-height: 40px;
    height: 40px;
    border-radius: 2px;
    padding: 0 19px;
    transition: all .25s ease;
    min-width: 140px;
  }

  .button-hambur {
    line-height: 18px;
    border-radius: 2px;
    padding: 6px 0;
    cursor: pointer;
    color: var(--color-bg-leftnum);
    font-size: 24px;
    font-weight: 400;
    background: transparent;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
  }

  .button--primary {
    background-color: var(--color-button);
    color: #fff;
    box-shadow: 0 2px 10px 0 var(--color-button-light);
    &:hover {
      background-color: var(--color-button);
      color: #fff;
    }
    &.button--disabled:hover {
      background-color: var(--color-button);
      color: #fff;
    }
  }
  .button--olight {
    background: #EEEEEE;
    font-weight: 400;
    color: #777777;
    border-radius: 3px;
    &:hover {
      background: var(--color-button);
      color: #777777;
    }
    &.button--disabled:hover {
      background: var(--color-button);
      color: #777777;
    }
    i , span {
      color: #777777;
      font-family: 'HiraKakuProN';
      font-size: 14px;
      line-height: 16px;
    }
  }
}

.button {
  font-weight: 600;
  font-size: 13px;
  line-height: 40px;
  height: 40px;
  border-radius: 2px;
  padding: 0 20px;
  transition: all .25s ease;
  min-width: 140px;
}
.button--primary {
  background-color: var(--color-button);
  color: #fff;
  box-shadow: 0 2px 10px 0 var(--color-button-light);
  &:hover {
    background-color: var(--color-bg-shop);
    color: #fff;
  }
  &.button--disabled:hover {
    background-color: var(--color-button);
    color: #fff;
  }
}
.modal-open .modal {
  background: var(--color-fade-modal);
}
.custom-job-modal {
  .modal-content {
    border-radius: 10px;
  }
  .modal-dialog {
    margin-top: 100px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .modal-header {
    border-bottom: none;
    background-color: #FFFFFF;
    border-radius: 10px 10px 0 0;
    padding: 30px;
    .modal-title {
      font-size: 18px;
    }
  }
  .modal-body {
    padding-left: 20px;
    padding-right: 20px;
  }
  .prefecture-name, .job-type, .staff-route {
    font-size: 13px;
    margin-bottom: 12px;
    border-bottom: 1px solid var(--color-underline);
    font-weight: 600;
  }
  .staff-route {
    font-size: 16px;
    line-height: 24px;
  }
  .modal-footer {
    border-top: none;
    padding-left: 20px;
    padding-right: 20px;
    button {
      width: 100%;
      font-weight: bold;
      padding: 12px 21px;
      font-size: 16px;
      line-height: 24px;
      background: var(--color-bg-datebar);
      color: #FFFFFF;
    }

  }
  .input-radio-custom label {
    font-size: 16px;
    line-height: 19px;
    color: #000000;
    &::after{
      top: 1px;
    }
  }
  .staff-route-title {
    font-size: 17px;
  }
  .route-notify {
    margin-top: 15px;
    background-color: var(--color-notify-bg);
    padding: 15px;
    text-align: center;
  }
  .input-checkbox-custom label {
    font-size: 16px;
  }
  .input-checkbox-custom label::before {
    border-radius: 15px;
  }
  .input-checkbox-custom label::after {
    content: "\f058";
    font-family: "icomoon2" !important;
    font-style: normal;
  }
  .data-form {
    width: 100%;
    float: left;
    margin-bottom: 20px;
    .col-xs-6 {
      width: 50%;
      padding: 10px;
      float: left;
      &.no-padding {
        padding: 0;
      }
    }
  }
  .input-freeword-label {
    width: 100%;
    .input-freeword {
      width: 100%;
      border: 1px solid #CCCCCC;
      border-radius: 2px;
    }
  }
}

.button--small_custom {
  min-width: unset !important;
  padding: 0 10px;
  border-radius: 5px;
}

.space-end-1 {
  width: 100%;
  height: 60px;
  background: -webkit-linear-gradient(#4DB2CE 0%, #3491AA 100%);
  background: -o-linear-gradient(#4DB2CE 0%, #3491AA 100%);
  background: linear-gradient(#4DB2CE 0%, #3491AA 100%);
}

.space-end-2 {
  width: 100%;
  height: 41px;
  background: -webkit-linear-gradient(#3491AA 0%, #4DB2CE 100%);
  background: -o-linear-gradient(#3491AA 0%, #4DB2CE 100%);
  background: linear-gradient(#3491AA 0%, #4DB2CE 100%);
}

.next-day-jobs, .load-more-btn, .login-btn {
  width: 100%;
  margin-top: -90px;
  padding: 20px;
  &.fixed-bottom {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 888;
  }
  button, a {
    width: 100%;
    background: #fff;
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    color: #4CB1CE;
    padding: 10.5px;
    i {
      font-size: 24px;
      bottom: -3px;
      position: relative;
      font-weight: 300;
      margin-left: 5px;
    }
  }
}
.job-list-by-date {
  &.public{
    #jobs-by-date {
      padding-bottom: 30px;
    }
    .next-day-jobs {
      padding-top: 10px;
      padding-bottom: 10px;
      margin-bottom: 60px;
      background: var(--color-bg-blue-2);
    }
    .login-btn {
      margin-top: 0;
      width: 100%;
      position: fixed;
      bottom: 0;
      left: 0;
      z-index: 888;
      padding: 10px 20px;
      background: var(--color-bg-blue-2);
      display: block;
      a {
        font-weight: normal;
        color: #000;
        background: #ffd42d;
        border-radius: 0.25rem;
      }
    }
  }
}
.mobile-order-list {
  touch-action: manipulation;
  font-family: "Barlow", "ヒラギノ角ゴ Pro W3", "Hiragino Kaku Gothic Pro",Osaka, "メイリオ", Meiryo, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif !important;
  margin:0;
  padding: 0;

  .interview_video_link {
    position: fixed;
    right: 10px;
    bottom: 90px;
    z-index: 1000;
    .btn_video_link {
      padding: 10px 20px;
      color: #fff;
      background: linear-gradient(141.93deg, #009CC3 9.94%, #01D39A 78.77%, #01D39A 78.77%);
      border: 1px solid #CCC;
      border-radius: 20px;
    }
  }

  .mobile-container {
    width: 100%;
    text-align: center;
    background: var(--color-bg-blue-2);
    background: -moz-linear-gradient(-180deg, var(--color-bg-blue-1) 0%,  var(--color-bg-blue-2) 100%);
    background: -webkit-linear-gradient(-180deg, var(--color-bg-blue-1) 0%, var(--color-bg-blue-2) 100%);
    background: linear-gradient(-180deg, var(--color-bg-blue-1) 0%, var(--color-bg-blue-2) 100%);
    background-attachment: scroll;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr= var(--color-bg-blue-1),endColorstr=var(--color-bg-blue-2),GradientType=1);
    padding-top: 170px;
    max-width: 900px;
    min-height: calc(100vh - 60px);
    margin-left: auto;
    margin-right: auto;
  }

  .mobileContainer {
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 60px;
  }

  .mobileContainerHeader {
    max-width:900px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }

  .shiftColumnLeft img {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    border-radius: 5px 0 0 0;
    height: 85px;
    width: 120px;
    min-height: 85px;
    max-width: 120px;
    object-fit: cover;
  }

  .shiftColumnRight {
    display: grid;
    grid-template-rows: min-content auto ;
    grid-template-areas: "topHalf" "bottomHalf";
  }

  .role {
    font-size: 14px;
    font-weight: 600;
    margin: 7px 0 0 2px;
    text-align: left;
    float:left;
  }

  .topHalf {
    display: grid;
    grid-template-columns: 50px calc(100vw - 223px) 40px;
    grid-template-areas: "chainLogo shopDetails location";
    border-radius: 0 5px 0 0;
  }

  .chainLogo img {
    padding: 3px;
    height: 50px;
    width: 50px;
  }

  .shopDetails {
    padding-left: 5px;
  }

  .shopName {
    font-size: 14px;
    font-weight: 700;
    margin: 0;
    text-align: left;
    margin-top: 5px;
  }

  .location {
    font-size: 10px;
    font-weight: 300;
    margin: 0;
    text-align: left;
    line-height: 17px;
  }

  .bottomHalf {
    border-radius: 0 0 5px 0;
    padding: 0 5px;
    float: left;
  }

  .JobCategories {
    float: left;
    width: calc(100vw - 180px);
    text-align: left;
    padding: 9px 5px;
    p {
      margin: 0;
      font-weight: bold;
      font-size: 14px;
      line-height: 14px;
      color: var(--color-text-gray);
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  .favourite {
    color: var(--color-button);
    width: 40px;
    float: right;
  }

  .shopRow {
    display: grid;
    grid-template-columns: 120px auto;
    grid-template-areas: "shiftColumnLeft shiftColumnRight";
    background: var(--color-background-grey);
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
  }

  .shopRowFirst {
    margin-bottom: 0 !important;
  }

  .shopRowLast {
    margin-top: 0 !important;
  }

  .shiftRow {
    border-radius:5px;
    margin: 5px;
  }

  .shiftRow2 {
    font-family: "Barlow";
    display: grid;
    grid-template-columns: 45px auto max-content;
    grid-template-areas: "shiftDetailsDuration shiftDetailsTime shiftDetailsPayment";
    background-color: #FFF;
    &.arranged {
      background-color: var(--color-arranged);
    }
    &.recruiting {
      background-color: #FFF;
    }
    &.adjusting {
      background-color: #FFF;
    }
    &.arranged-jobs {
      background-color: var(--color-arranged);
    }
    &.applied-jobs {
      background: repeating-linear-gradient(
        45deg,
        var(--color-applyed),
        var(--color-applyed) 17px,
        var(--color-over-gadident) 13px,
        var(--color-over-gadident) 21px
      );
    }
    &.finish_recruiting {
      background-color: var(--color-cancel);
    }
    &.ended-jobs {
      background-color: var(--color-ended);
    }
    &.cancel {
      background-color: var(--color-cancel);
    }
  }


  .shiftApplied, .shiftApproved, .shiftWorked {
    border-bottom: none;
  }

  .shiftRowMiddle {
    border-radius: 0;
  }

  .hidden {
    display: none;
  }

  .finalRow {
    background: var(--color-bg-shop);
    font-size: 12px;
    padding: 10px 5px;
    border:0;
    border-radius: 0 0 5px 5px;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-areas: "totalToday";
  }

  .hourWage {
    margin:0;
    padding: 0;
    font-family: "Barlow";
    font-size: 14px;
    line-height: 16px;
    @include iphone5 {
      font-size: 12px;
      line-height: 16px;
    }
  }

  .totalWage {
    margin:0;
    padding: 0;
    margin-top: 3px;
    font-family: "Barlow";
    margin-left: 4px;
    font-weight: 500;
    font-size: 18px;
    line-height: 18px;
    min-width: 62px;
    @include iphone5 {
      margin-left: 1px;
      font-size: 15px;
      line-height: 18px;
      min-width: 52px;
    }
  }

  .wageLabel {
    font-size: 12px;
    margin:0;
    align-self: end;
    @include iphone5 {
      font-size: 11px;
    }
  }

  .heart {
    font-size: 22px;
    margin: 0;
    margin-right: -2px;
  }

  .shiftTime {
    font-size: 14px;
    line-height: 17px;
    font-weight: 600;
    color: var(--color-text-gray);
    margin: 5px 0px 0px 2px;
    text-align: left;
    float:left;
    @include iphone5 {
      font-size: 12px;
    }
  }

  .restTime {
    font-size: 14px;
    line-height: 17px;
    font-weight: 600;
    color: var(--color-rest-time);
    margin: 5px 0px 0px 5px;
    text-align: left;
    float: left;
    min-width: 50px;
    @include iphone5 {
      font-size: 12px;
      min-width: 43px;
    }
  }

  .restIcon {
    font-size: 14px;
    line-height: 17px;
    @include iphone5 {
      font-size: 12px;
    }
    margin-bottom: 2px;
  }

  .shiftDetailDuration {
    border-radius: 4px;
    margin: 3px;
    padding: 0 5px 0 5px;
    background: var(--color-bg-leftnum);
  }

  .finish_recruiting .shiftDetailDuration {
    background: var(--color-finish);
  }

  .shiftDetailTime {
    margin-top: 10px;
    margin-bottom: auto;
  }

  .shiftDetailPayment {
    text-align: right;
    margin-top: auto;
    margin-bottom: auto;
    margin-right: 4px;
    display: grid;
    grid-template-columns: max-content auto;
    min-width: 75px;
  }

  .wageLabels {
    text-align: left;
    float: left;
    margin-right: 2px;
    display: grid;
    grid-template-areas: "wageLabel" "wageLabel";
    grid-template-rows: min-content auto;
    width: 25px;
    .active {
      color: var(--color-fire);
    }
  }

  .wageNumbers {
    text-align: right;
    float: durationLabelright;
    .active {
      color: var(--color-fire);
    }
  }

  .duration {
    font-family: "Barlow";
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
    color: white;
  }

  .durationLabel {
    font-size: 10px;
    line-height: 12px;
    color: white;
    margin-bottom: 2px;
  }

  .dateBarDayName, .dateBarDateNum {
    width: 12.5vw;
    margin: 0;
    padding: 0;
    border: 0;
    white-space: normal;
    text-align: center;
    font-size: 12px;
    line-height: 18px;
    &.day_0, &.day_6 {
      color: #E685B7;
    }
  }

  .days {
    display: flex;
    width: inherit;
    max-width: inherit;
    position: absolute;
    margin-top: 7px;
    z-index: 5;
    pointer-events: none;
  }

  .dates {
    scroll-snap-type: x mandatory;
    display:flex;
    -webkit-overflow-scrolling: touch;
    overflow-x: scroll;
  }

  .dateBarDay {
    max-width: inherit;
    width: inherit;
    scroll-snap-align: start;
    display: flex;
  }

  .dateBarDateNum {
    background: #fff;
    padding-top: 19px;
    padding-bottom: 6px;
    margin-top: 4px;
  }

  .monthLabel {
    position: absolute;
    left: 0;
    top: 0px;
    border-radius: 0 5px 5px 0;
    margin: 0;
    background: var(--color-button-light);
    width: calc(12.5vw + 1.3vw);
    height: 40px;
    @include iphone5 {
      font-size: 16px;
    }
    z-index: 5;
    margin-top: 7px;
    vertical-align: middle;
    text-align: center;
    span {
      line-height: 40px;
      font-family: "Barlow";
      font-style: normal;
      font-weight: normal;
      font-size: 12px;
    }
  }

  .dateBar {
    width: inherit;
    max-width: inherit;
    position: relative;
    display: grid;
    grid-template-rows: min-content auto;
    grid-template-areas: "days" "dates";
    position: relative;
    background: white;
  }

  .gridContainer {
    height: 100vh;
    display: grid;
    grid-template-rows: min-content min-content auto;
    grid-template-areas: "navbar" "dateBar" "container";
  }

  .disabledText {
    color:#CCC;
    pointer-events: none;
  }

  .selectedDay {
    color: white;
    background: var(--color-bg-datebar);
    border-radius: 5px 5px 0 0;
  }

  .arranged {
    background: var(--color-arranged);;
  }

  .applied {
    background: var(--color-applyed),
  }

  .sortBarContainer {
    width: inherit;
    max-width: inherit;
    position: fixed;
    top: 109px;
    z-index: 999;
    background: var(--color-bg-datebar);
  }

  .sortBar {
    border-radius: 5px;
    background: #FFF;
    margin: 5px;
    margin-top: 9.5px;
    padding: 3px;
    display: grid;
    grid-template-columns: min-content auto max-content;
    grid-template-areas: "inputArea settings2 switch";
    height:48px;
    z-index: 10;
    position: relative;
  }

  .sortBarToggle {
    grid-template-areas: "inputArea settings1 switch";
  }

  .segmentedControl {
    background: #DDD;
    border-radius: 3px;
    padding: 3px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-areas: "sortTime sortWage";
    height:36px;
  }

  .sortWage, .sortTime {
    width: 36px;
    height: 36px;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-content: center;
    align-items: center;
    border-radius: 2px;
  }

  .sortTime span, .sortWage span {
    padding: 0;
    margin: 0;
    line-height: 0;
  }

  .settings1 {
    display: grid;
    grid-template-columns: auto max-content;
    grid-template-areas: "slider filter";
  }

  .inputArea {
    width:40px;
    text-align: center;
    line-height: 1;
    margin: 4px;
    padding-top: 5px;
  }

  .sliderArea {
    padding: 5px 15px 15px 10px;
  }

  .sortSelected {
    background: var(--color-button-second);
    color: white;
  }

  .switch {
    position: relative;
    display: inline-block;
    width: 78px;
    height: 42px;
    padding-top: 0px;
    text-align: right;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slidingPart {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #eee;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 5px;
  }

  .slidingPart:before {
    position: absolute;
    content: "";
    height: 36px;
    width: 36px;
    left: 4px;
    bottom: 3px;
    background-color: var(--color-button-second);
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 3px;
  }

  input:checked + .internals > .icon1 {
    color: var(--color-text-gray);
    transition: .4s;
  }

  input:checked + .internals > .icon2 {
    -webkit-transform: translateX(-2px);
    -ms-transform: translateX(-2px);
    transform: translateX(-2px);
    color: #FFF;
    transition: .4s;
  }

  input:checked + .internals > .slidingPart:before {
    -webkit-transform: translateX(34px);
    -ms-transform: translateX(34px);
    transform: translateX(34px);
  }

  .icon1 {
    position: absolute;
    left: 13px;
    top: 10px;
    color: #FFF;
    transition: color .4s ease;
    font-size: 18px;
  }

  .icon2 {
    position: absolute;
    left: 49px;
    top: 10px;
    color: var(--color-text-gray);
    -webkit-transition: .4s;
    transition: color .4s ease;
    font-size: 18px;
  }

  .sortTypes {
    display: flex;
    position: relative;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    position: relative;
    z-index: 1;
    margin: 0 5px 0 0;
    font-size: 17px;
    border: 3px solid #eee;
    border-radius: 5px;
    color: var(--color-text-gray);
    background: #eee;
  }

  .sortTypes > input {
    display: none;
  }

  .sortTypes > input:checked + label {
    color: #FFF;
  }

  .sortTypes > input:nth-of-type(1):checked ~ label:last-of-type:before {
    transform: translateX(calc(0% + 0px));
  }

  .sortTypes > input:nth-of-type(2):checked ~ label:last-of-type:before {
    transform: translateX(calc(100% + 0px));
  }

  .sortTypes > input:nth-of-type(3):checked ~ label:last-of-type:before {
    transform: translateX(calc(200% + 0px));
  }
  .sortTypes > input:nth-of-type(4):checked ~ label:last-of-type:before {
    transform: translateX(calc(300% + 0px));
  }
  .sortTypes > input:nth-of-type(5):checked ~ label:last-of-type:before {
    transform: translateX(calc(400% + 0px));
  }

  .sortTypes label {
    flex: 1;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    transition: color 250ms cubic-bezier(0, 0.95, 0.38, 0.98);
    line-height: 36px;
    margin-bottom: 0;
  }

  .sortTypes label:last-of-type:before {
    content: "";
    display: block;
    max-width: calc(33.3333% - 0px);
    margin: 0px;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    transform: translateX(0);
    padding-top: 10px;
    border-radius: 3px
  }

  .sortTypes label:before {
    background: var(--color-button-second);
    transition: all 250ms cubic-bezier(0, 0.95, 0.38, 0.98);
    padding-top: 10px;
  }

  .heart a:hover, .heart a:active, .heart a:visited, .heart a:link {
    color: var(--color-button-second) !important;
  }

  .separator {
    color: white;
    margin: 0;
    padding: 0;
    display: flex;
    text-align: center;
    padding: 0 6px;
    padding-top: 3px;
    padding-bottom: 6px;
    .separator-left {
      text-align: left;
      width: 34%;
      span {
        font-size: 18px;
        @include iphone5 {
          font-size: 16px;
        }
      }
    }
    .separator-center {
      text-align: center;
      width: 33%;
      padding-top: 2px;
      h6 {
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 0;
      }
      i {
        font-size: 14px;
      }
    }
    .separator-right {
      text-align: right;
      width: 33%;
      padding-top: 4px;
      .js-map-switch {
        visibility: hidden;
      }
      label{
        margin-bottom: 0;
      }
      .notification-label {
        font-size: 16px;
        line-height: 19px;
        font-weight: 600;
      }
    }
  }

  .alert-fcm__switch-button {
    position: relative;
    display: inline-block;
    width: 35px;
    height: 23px;
    bottom: 2px;
  }

  .fcm-switch {
    visibility: hidden;
  }

  input + .st-work-result__slider::before {
    height: 19px;
    width: 19px;
    left: 2px;
    bottom: 2px;
  }

  input:checked + .st-work-result__slider {
    background-color: var(--color-radio);
  }

  input:checked + .st-work-result__slider::before {
    left: -5px;
  }

  .dateSub{
    pointer-events: none;
    padding: 3px 7px;
    margin-left: auto;
    margin-right: auto;
    border-radius: 5px;
    border: none;
    width: min-content;
    font-size: 12px;
    line-height: 18px;
    font-weight: 600;
  }

  .dayApplied{
    color: white;
    background:var(--color-bg-datebar);
    border: 1px solid white;
  }

  .dayApproved {
    color: white;
    background: var(----color-button-second);
    border: 1px solid white;
  }

  .dayRecruited {
    color: white;
    background: #70DBD1;
    border: 1px solid white;
  }

  .shiftApproved {
    background: rgb(211,255,251);
    background: -moz-linear-gradient(-180deg, rgba(211,255,251,1) 0%, rgba(112,219,209,1) 100%);
    background: -webkit-linear-gradient(-180deg, rgba(211,255,251,1) 0%, rgba(112,219,209,1) 100%);
    background: linear-gradient(-180deg, rgba(211,255,251,1) 0%, rgba(112,219,209,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#d3fffb",endColorstr="#70dbd1",GradientType=1);
  }

  .shiftApplied{
    background: repeating-linear-gradient( 45deg, #ffe5ca, #ffe5ca 10px, #ffdebc 10px, #ffdebc 20px );
  }

  .shiftWorked{
    background: #AAA;
  }

  .noResult{
    font-size: 17px;
    color:white;
    padding-top: 170px;
  }

  .otherResult{
    color:white;
    span {
      font-size: 17px;
    }
  }


  .totalNumber{
    color:white;
    background: #8e8e8e;
    border-radius: 3px;
    padding: 2px 10px;
  }

  .totalAll, .totalToday{
    font-size: 12px;
  }

  .totalToday span{
    margin-left: 6px;
  }
  .totalToday .fas{
    color: #8a8a8a;
  }

  .tooltips{
    position: absolute;
    top: calc(100% - 110px);
    left: 0;
    height:30px;
    width: inherit;
    color: white;
    text-align: center;
    font-size: 18px;
  }

  .tooltipInnards {
    background: var(--color-tooltips-bg);
    margin: 5px;
    padding:15px 6px;
    border-radius: 5px 5px 0 0;
  }

  .tooltipInnards .kanji{
    font-size: 16px;
  }

  .train {
    font-size: 18px;
  }

  .icon-genkin {
    font-size: 22px;
  }

  .shiftIcon{
    float: right;
    color: grey;
    padding-left: 1px;
    padding-right: 3px;
    @include iphone5 {
      padding-left: 0;
      padding-right: 0;
      margin-left: 1px;
      margin-right: 1px;
    }
    .type-experience-text {
      background: var(--color-bg-leftnum);
      color: #fff;
      font-size: 12px;
      line-height: 12px;
      font-weight: 300;
      text-align: center;
      padding: 5px 3px;
      border-radius: 2px;
    }
    &.fire {
      margin-left: 3px;
      margin-right: 3px;
      @include iphone5 {
        margin-left: 1px;
        margin-right: 0;
      }
      &.small-icon {
        margin-left: 0px;
        margin-right: 0px;
        @include iphone5 {
          margin-left: 1px;
          width: 13px;
        }
      }
      i {
        margin-right: 0px;
        font-size: 20px;
        color: var(--color-fire);
      }
    }
    .finish-job {
      padding-top: 3px;
    }
    .job-flag {
      border-radius: 5px;
      padding: 3px;
      font-size: 10px;
      @include iphone5 {
        font-size: 8px;
        padding: 3px;
      }
      background-color: var(--color-flag-grey);
      color: #000000;
    }
    &.changeable, &.regular {
      margin-top: 4px;
      @include iphone5 {
        margin-top: 2px;
      }
    }
    &.finish {
      display: contents;
      .job-big-flag {
        margin-left:auto;
        margin-right:auto;
        font-size: 16px;
        line-height: 27px;
        text-align: right;
        color: var(--color-finish);
        @include iphone5 {
          margin-right: 16px;
        }
      }
    }
  }

  .shopIcon{
    margin: 7px 7px 0 0;
    float: right;
    color: grey;
  }

  .fixedHeader{
    position: fixed;
    top: 56px;
    height: 56px;
    z-index: 999;
  }
  .footer{
    background: white;
    position: fixed;
    bottom:0;
    border-top: var(--color-bg-blue-1) 1px solid;
  }

  .timeToggle > p-on{
    padding: 12px 16px;
  }

  .sortText2{
    border-bottom: 1px solid black;
    margin-bottom: 2px;
    padding-bottom: 1px;
    font-size: 7px;
  }

  .sortText1{
    font-size: 14px;
    padding: 5px 7px !important;
  }

  .regionButtonContainer{
    margin-top: 5px;
  }

  .regionButton{
    color:#444;
    background:var(--color-button-grey);
    padding: 15px;
    text-align: center;
    border-radius: 5px;
    color:#333;
  }

  .icon {
    font-family: "icomoon2" !important;
    font-style: normal;
  }

  .icon-yen1:before {
    content: "\e900";
  }

  .icon-th-list:before {
    content: "\f00b"
  }

  .icon-train:before {
    content: "\e907";
  }

  .icon-check-circle:before {
    content: "\f058";
  }

  .icon-flag:before {
    content: "\f024";
  }

  .icon-question:before {
    content: "\f128";
  }

  .icon-genkin:before {
    content: "\e908";
  }

  .icon-wakaba {
    font-size: 17px;
  }

  .icon-wakaba:before {
    content: "\e904";
  }

  .icon-chevron-down:before {
    content: "\f078";
  }

  .fas {
    font-family: "fontawesome-free" !important;
  }

  .fa-mug-hot:before {
    content: "\f0f4";
  }

  .mt-2px {
    margin-top: 2px;
  }

  .smallDecimal {
    font-size: 15px;
    letter-spacing: -1.0px;
  }

.slider {
  display: inline-block;
  vertical-align: middle;
  position: relative;

  &.slider-horizontal {
    width: 100%;
    height: 30px;

    .slider-track {
      height: 4px;
      width: 100%;
      margin-top: -6px;
      top: 50%;
      left: 0;
    }

    .slider-selection, .slider-track-low, .slider-track-high {
      height: 100%;
      top: 0;
      bottom: 0;
    }

    .slider-tick, .slider-handle {
      margin-left: -12px;
    }

    .slider-tick.triangle, .slider-handle.triangle {
      position: relative;
      top: 50%;
      transform: translateY(-50%);
      border-width: 0 12px 12px 12px;
      width: 0;
      height: 0;
      border-bottom-color: #f2f2f2;
      margin-top: 0;
    }

    .slider-tick-container {
      white-space: nowrap;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }

    .slider-tick-label-container {
      white-space: nowrap;
      margin-top: 24px;

      .slider-tick-label {
        display: inline-block;
        padding-top: 28.8px;
        text-align: center;
      }
    }

    &.slider-rtl {
      .slider-track {
        left: initial;
        right: 0;
      }

      .slider-tick, .slider-handle {
        margin-left: initial;
        margin-right: -12px;
      }

      .slider-tick-container {
        left: initial;
        right: 0;
      }
    }
  }

  &.slider-vertical {
    height: 210px;
    width: 24px;

    .slider-track {
      width: 12px;
      height: 100%;
      left: 25%;
      top: 0;
    }

    .slider-selection {
      width: 100%;
      left: 0;
      top: 0;
      bottom: 0;
    }

    .slider-track-low, .slider-track-high {
      width: 100%;
      left: 0;
      right: 0;
    }

    .slider-tick, .slider-handle {
      margin-top: -12px;
    }

    .slider-tick.triangle, .slider-handle.triangle {
      border-width: 12px 0 12px 12px;
      width: 1px;
      height: 1px;
      border-left-color: #f2f2f2;
      margin-left: 0;
    }

    .slider-tick-label-container {
      white-space: nowrap;

      .slider-tick-label {
        padding-left: 4.8px;
      }
    }

    &.slider-rtl {
      .slider-track {
        left: initial;
        right: 25%;
      }

      .slider-selection {
        left: initial;
        right: 0;
      }

      .slider-tick.triangle, .slider-handle.triangle {
        border-width: 12px 12px 12px 0;
      }

      .slider-tick-label-container .slider-tick-label {
        padding-left: initial;
        padding-right: 4.8px;
      }
    }
  }

  &.slider-disabled {
    .slider-handle {
      background-color: #cfcfcf;
      background-image: linear-gradient(to bottom, #DFDFDF, #BEBEBE);
      background-repeat: repeat-x;
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#DFDFDF', endColorstr='#BEBEBE',GradientType=0);
    }

    .slider-track {
      background-color: #e7e7e7;
      background-image: linear-gradient(to bottom, #E5E5E5, #E9E9E9);
      background-repeat: repeat-x;
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#E5E5E5', endColorstr='#E9E9E9',GradientType=0);
      cursor: not-allowed;
    }
  }

  input {
    display: none;
  }

  .tooltip-inner {
    white-space: nowrap;
    max-width: none;
  }

  .tooltip {
    pointer-events: none;

    &.top {
      margin-top: -36px;
    }
  }

  .tooltip-inner {
    white-space: nowrap;
    max-width: none;
  }

  .hide {
    display: none;
  }
}
.slider-track {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background-color: #f0f0f0;
  position: absolute;
  cursor: pointer; }

.slider-selection {
  background-color: var(--color-bg-blue);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
  border-radius: 4px;
  position: absolute; }

.slider-selection.tick-slider-selection {
  background-color: white;
  background-image: linear-gradient(to bottom, white, white);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='white', endColorstr='white',GradientType=0); }

.slider-track-low, .slider-track-high {
  box-sizing: border-box;
  border-radius: 4px;
  position: absolute;
  background: transparent; }

.slider-handle {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 0;
  width: 24px;
  height: 24px;
  background-color: #fff;
  border: 1px solid #f0f0f0; }
  .slider-handle:hover {
    cursor: pointer; }
  .slider-handle.round {
    border-radius: 24px; }
  .slider-handle.triangle {
    background: transparent none; }
  .slider-handle.custom {
    background: transparent none; }
    .slider-handle.custom::before {
      line-height: 24px;
      font-size: 20px;
      content: '\2605';
      color: #726204; }

.slider-tick {
  background-color: #f7f7f7;
  background-image: linear-gradient(to bottom, #F5F5F5, #F9F9F9);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#F5F5F5', endColorstr='#F9F9F9',GradientType=0);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
  position: absolute;
  cursor: pointer;
  width: 24px;
  height: 24px;
  filter: none;
  opacity: 0.8;
  border: 0px solid transparent; }
  .slider-tick.round {
    border-radius: 50%; }
  .slider-tick.triangle {
    background: transparent none; }
  .slider-tick.custom {
    background: transparent none; }
    .slider-tick.custom::before {
      line-height: 24px;
      font-size: 20px;
      content: '\2605';
      color: #726204; }
  .slider-tick.in-selection {
    background-color: white;
    background-image: linear-gradient(to bottom, white, white);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='white', endColorstr='white',GradientType=0);
    opacity: 1; }


  .slider-handle.round {
    border-radius: 3px;
    width: 18px;
    height: 32px;
    background-color: #DDD;
  }

  .slider-handle {
    border: unset;
  }

  .slider.slider-horizontal{
    .slider-track {
      height: 13px;
    }
    .slider-handle {
      margin-left: -9px;
    }
  }

  .slider-selection {
    background: var(--color-button-second);
  }

  .shiftBlock .dataRow {
    &:last-child {
      border-radius: 0px 0px 5px 5px !important;
    }
    &:only-child {
      border-radius: 0px 0px 5px 5px !important;
    }
  }

  .ellipsis-text {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100%;
  }

  .object-fit-cover {
    object-fit: cover;
  }

  #time-preview {
    font-family: "Barlow";
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 14px;
  }

  .locationOrderList {
    .shopHeaderWrapper {
      position: fixed;
      width: inherit;
      max-width: inherit;
      padding:  5px;
    }

    .sortBarContainer {
      top: 145px;
    }

    .dataRow {
      border-radius: unset;
    }

    .shiftBlock .dataRow {
      &:first-child {
        border-radius: 5px 5px 0px 0px !important;
      }
      &:last-child {
        border-radius: 0px 0px 5px 5px !important;
      }
      &:only-child {
        border-radius: 5px !important;
      }
    }

    .shopRow {
      border-radius: 5px;
    }

    .shopHeaderWrapper {
      background: linear-gradient(-180deg, var(--color-bg-blue-1) 0%, var(--color-bg-datebar) 100%);
    }

    .sortBar {
      margin: 5px 0px 0px 0px;
    }
  }
}

#location-jobs {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 11111;
  margin: 0;
  background: var(--color-bg-blue-1);
  #location-info {
    margin: 0;
    padding-top: 62px;
  }
  #time-location-tooltips {
    .locationTooltipInnards {
      background: var(--color-tooltips-bg);
      margin: 5px;
      padding:15px 6px;
      border-radius: 5px 5px 0 0;
    }
    .tooltipInnards .kanji{
      font-size: 16px;
    }
  }
  .heart {
    margin-left: 8px;
  }
  .navigation-header {
    background: #fff;
    width: 100%;
    text-align: center;
    padding: 16px 8px;
    height: 55px;
    position: fixed;
    z-index: 10000;
    .navigation-back {
      float: left;
      padding: 0;
      position: absolute;
      left: 10px;
      top: 16px;
      i {
        font-size: 18px;
        font-weight: 500;
      }
      span {
        font-size: 14px;
        font-weight: 400;
      }
    }
    .page-header {
      font-weight: 500;
      font-size: 16px;
    }
    .location-info {
      margin: 0;
    }
  }
  .mobile-container {
    padding-top: 200px;
  }
}
#angular-ng-repeat-loader-jobs {
  background: var(--color-fade-modal);
  display: inherit;
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  z-index: 9999;
  bottom: 0;
  .load-jobs {
    width: 50px;
    height: 40px;
    position: fixed;
    text-align: center;
    top: 50%;
    left: 50%;
    margin: -30px 0 0 -30px;
    img {
      width: 50px;
    }
  }
}
#jobs-by-date, .show-load-more {
  padding-bottom: 40px;
}
.load-more {
  margin-bottom: 15px;
  padding: 12px;
  font-size: 14px;
  font-weight: 400;
  padding-top: 12px;
  background: var(--color-background-grey);
  color: #7F7F7F;
  border-radius: 0px 0px 5px 5px !important;
  .count-loading {
    background: #fff;
    border-radius: 5px;
    padding: 3px 6px;
    font-size: 12px;
    margin-left: 5px;
    color: var(--color-background-grey);
    background: #7F7F7F;
  }
  i {
    margin-right: 5px;
    font-size: 18px;
    font-weight: normal;
    font-weight: lighter;
    position: relative;
    top: 3px;
  }
}
.last-jobs {
  border-radius: 0px 0px 5px 5px !important;
}
#modal-welcome-workz {
  .modal-body {
    height: auto;
    min-height: 630px;
    padding: 20px;
    padding-top: 0px;
    .carousel {
      min-height: 570px;
      .carousel-inner .big {
        font-size: 26px;
      }
      .carousel-indicators {
        bottom: -20px;
      }
    }
  }
  img {
    width: 74%;
  }
  .carousel-control-prev, .carousel-control-next {
    position: fixed;
  }
  .welcome-staff-3 {
    text-align: left;
    margin-left: 10%;
  }
}
#interview-calendar-sp {
  &.mobile-order-list .switch {
    width: 47px;
  }
  .slidingPart {
    background: var(--color-button-second);
    .icon1 {
      left: 16px;
    }
  }
}
#sp-interviews {
  .interview-data {
    margin-top: 3px;
    margin-bottom: 4px;
  }
  .interview-content {
    padding-left: 20px;
    padding-right: 20px;
    .interview {
      background: #fff;
      padding: 10px;
      font-size: 14px;
      line-height: 18px;
      font-weight: 600;
      font-family: "Barlow";
      border-radius: 10px;
      box-shadow: 0 2px rgba(64, 64, 64, 0.5);
      &.applied {
        background: #0a76be;
        color: #fff;
      }
    }
  }
}
#interview-info-popup {
  text-align: center;
  .modal-body {
    border: none;
    text-align: left;
    padding-top: 40px;
    padding-bottom: 40px;
    width: 100%;
    height: auto;
    float: left;
    span {
      font-size: 17px;
      font-weight: 500;
    }
  }
  .modal-footer {
    text-align: center;
    display: block;
    border: none;
    button {
      width: 100%;
      background: var(--color-bg-datebar);
      font-weight: bold;
      color: #FFFFFF;
      float: none;
      height: 48px;
      padding: 12px 21px;
      padding: 0 45px;
      font-size: 16px;
      border: none;
    }
  }
}

#interview-error-popup {
  .modal-body {
    padding: 30px;
  }
}

#interview-calendar-sp {
  &.mobile-order-list .dateSub {
    &.available {
      background-color: #e6f5ff;
      border: solid 1px #fff;
      color: #000;
    }
    &.applied {
      background-color: #79d2d6;
      border: solid 1px #fff;
      color: #000;
    }
  }
}
.mobile-order-list .disabledText .dateSub{
  background: #fff;
  border: none;
  color: #CCC;
}
.white-text {
  color: #fff;
}
#popup-register-step  {
  .modal-dialog {
    margin-top: 200px;
  }
}

#unauthenticate-header {
  button {
    display: none!important;
  }
}
.pdr-15 {
  padding-right: 15px;
}