.employment-condition {
  .sign-content {
    display: inline-block;
    position: absolute;
  }

  .sign-label {
    display: inline-block;
  }

  .employment-condition__phone {
    width: 20%;
  }

  @media (max-width: 1359px) {
    .employment-condition__phone {
      width: 25%;
    }
  }
  .employment-condition__table {
    tr {
      page-break-inside: avoid !important;
      .employment-condition__rowspan {
        border-bottom: 1px solid #e5e5e5;
        padding-left: 15px;
        &:last-child {
          border: none;
        }
      }
      .employment-condition__rowspan.employment-condition-rowspan__text {
        padding: 15px;
      }
      .ptb-1 {
        padding-top: 10px;
        padding-bottom: 10px;
      }
      td {
        div.contact-header {
          padding: 10px 0;
        }
        div.contact-content {
          border: 1px solid #e5e5e5;
          padding: 10px 15px;
          &:empty {
            height: 100px;
          }
        }
      }
      .total-minutes {
        margin-right: 60px;
      }
    }
  }
}

.contract__img {
  position: absolute;
  width: 80%;
  transform: translateX(-40%);
  left: 10%;
  z-index: -1;
  top: 150px;
}

.free-words-area {
  height: 100px;
  p {
    border-bottom: 1px dotted #000;
  }
}

@for $i from 6 through 20 {
  .ml-#{$i}, .mx-#{$i}  {
    margin-left: #{$i}rem !important;
  }
}

.employment-condition__sign {
  .employment-condition__sign-wrapper {
    .employment-condition__table {
      &.no-style {
        tr, td {
          border: 0;
          background: initial;
          padding: 10px 0;
        }
        td {
          &:first-child {
            width: 210px;
          }
        }
      }
      .free-words-area {
        height: auto;
      }
    }
  }
}

.employment-condition__phone {
  -ms-word-break: break-all;
  word-break: break-all;
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
  hyphens: auto;
}

.violation_file_template {
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  h1 {
    font-size: 80px
  }
  span {
    font-size: 20px;
  }
  .header-violation-file {
    margin-bottom: 80px;
  }
  .body-violation-file {
    border: 1px solid #000;
    padding: 80px;
    margin-top: 80px;
    margin-bottom: 80px;
    .row  {
      padding: 15px;
    }
  }
  .columm-name {
  }
  .columm-data {
    color: red;
    border-bottom: 1px solid #000;
  }
  .red-text {
    color: red;
  }
  .sign-violation-file {
    margin-bottom: 80px;
  }
  .notice-violation-file {
    padding: 30px;
    background: #8abee4;
    font-weight: 600;
    font-size: 20px;
    ul {
      list-style: inherit;
      margin-bottom: 0;
      padding-inline-start: 15px;
    }
  }
  .row {
    float: left;
    width: 100%;
    display: block;
  }
  .col-md-12 {
    float: left;
    width: 100%;
  }
  .col-md-6 {
    float: left;
    width: 50%;
  }
  .col-md-offset-6 {
    margin-left: 50%;
  }
}
