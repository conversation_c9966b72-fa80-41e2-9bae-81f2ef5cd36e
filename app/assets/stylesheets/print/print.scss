@charset "UTF-8";
@media print {
  * {
    -webkit-print-color-adjust: exact;
    color: exact; }
  body {
    font-family: 'Helvetica', 'Arial', 'Segoe UI', 'Tahoma', sans-serif;
    font-size: 9px; }
  @page {
    size: A4; } }

body {
  font-family: 'Hiragino <PERSON> Pro', 'ヒラギノ角ゴ ProN W3', 'Meiryo', 'メイリオ', 'ＭＳ Ｐゴシック', 'Lucida Grande', 'sans-serif';
  font-size: 14px; }

.employment-condition {
  color: #1a1a1a;
  padding: 20px 15px;
  position: relative; }
  @media print {
    .employment-condition {
      font-size: 9px;
      padding: 0;
      position: relative; } }
  .employment-condition__img {
    position: absolute;
    width: 80%;
    transform: translateX(-40%);
    left: 40%;
    z-index: -1;
    top: 150px; }
    @media print {
      .employment-condition__img {
        height: 500px;
        left: 53%; } }
  .employment-condition__title {
    font-size: 18px;
    text-align: center;
    font-weight: 600; }
    @media print {
      .employment-condition__title {
        font-size: 16px; } }
  .employment-condition .table > tbody > tr > td,
  .employment-condition .table > tbody > tr > th,
  .employment-condition .table > tfoot > tr > td,
  .employment-condition .table > tfoot > tr > th,
  .employment-condition .table > thead > tr > td,
  .employment-condition .table > thead > tr > th {
    padding: 10px 15px;
    vertical-align: middle;
    position: relative; }
    @media print {
      .employment-condition .table > tbody > tr > td,
      .employment-condition .table > tbody > tr > th,
      .employment-condition .table > tfoot > tr > td,
      .employment-condition .table > tfoot > tr > th,
      .employment-condition .table > thead > tr > td,
      .employment-condition .table > thead > tr > th {
        position: relative;
        padding: 5px; } }
  .employment-condition .table > tbody > tr > td {
    padding-right: 0; }
  @media print {
    .employment-condition__table {
      font-size: 9px; } }
  .employment-condition__table tr {
    border-right: 1px solid #e5e5e5;
    border-left: 1px solid #e5e5e5;
    display: table-row; }
    .employment-condition__table tr:last-child {
      border-bottom: 1px solid #e5e5e5; }
  .employment-condition__table th {
    width: 210px;
    vertical-align: middle;
    font-weight: 600;
    background: #f3f4f5;
    border-right: 1px solid #e5e5e5; }
    @media print {
      .employment-condition__table th {
        width: 110px;
        background: #f3f4f5 !important;
        padding: 4px 10px; } }
  .employment-condition__table td {
    vertical-align: middle;
    position: relative; }
    @media print {
      .employment-condition__table td {
        padding: 4px 10px;
        background: transparent !important; } }
  .employment-condition__table-organization {
    width: 100%; }
    .employment-condition__table-organization thead th {
      width: 54px;
      border: 0;
      border-right: 1px solid #e5e5e5;
      border-bottom: 1px solid #e5e5e5;
      padding: 10px; }
      @media print {
        .employment-condition__table-organization thead th {
          position: relative;
          padding: 5px; } }
    .employment-condition__table-organization tr:last-child {
      border-bottom: 0; }
    .employment-condition__table-organization td {
      padding: 10px;
      box-sizing: border-box; }
      @media print {
        .employment-condition__table-organization td {
          position: relative;
          padding: 5px; } }
  .employment-condition__text {
    padding-top: 14px;
    vertical-align: middle;
    width: 65%;
    float: left; }
    @media print {
      .employment-condition__text {
        width: 3.9in;
        padding-top: 8px;
        float: none;
        display: inline-block; } }
    .employment-condition__text--with-phone {
      padding-top: 10px;
      padding-bottom: 10px; }
      @media print {
        .employment-condition__text--with-phone {
          width: 3.9in;
          display: inline-block;
          padding-top: 7px;
          padding-bottom: 0; } }
    @media print {
      .employment-condition__text--short {
        width: 4in; } }
    @media print {
      .employment-condition__text--veryshort {
        width: 2.5in; } }
  .employment-condition__phone {
    margin: 0;
    display: table-cell;
    padding: 0;
    height: 100%;
    width: 35%; }
    @media print {
      .employment-condition__phone {
        padding: 0;
        display: inline-block;
        width: 1.8in;
        float: right; } }
    @media print {
      .employment-condition__phone--long {
        width: 3in; } }
    .employment-condition__phone span {
      display: inline-block;
      background: #f3f4f5;
      text-align: center;
      font-weight: 600;
      margin-right: 15px;
      height: 50px;
      line-height: 50px;
      width: 80px; }
      @media print {
        .employment-condition__phone span {
          background: #f3f4f5 !important;
          margin-right: 10px;
          height: 30px;
          line-height: 30px;
          width: 80px; } }
      .employment-condition__phone span.phone-large {
        height: 60px;
        line-height: 60px; }
        @media print {
          .employment-condition__phone span.phone-large {
            height: 40px;
            line-height: 40px;
            background: #f3f4f5 !important; } }
      .employment-condition__phone span.long-text {
        width: 117px; }
        @media print {
          .employment-condition__phone span.long-text {
            width: 100px; } }
  .inline-block{
    display: inline-block;
  }
  .employment-condition__give {
    position: relative;
    display: inline-block;
    background: #f3f3f3;
    border-radius: 2px;
    padding: 5px 30px;
    margin: 0; }
    @media print {
      .employment-condition__give {
        background: #f3f3f3 !important; } }
    .employment-condition__give span {
      position: absolute;
      top: 7px;
      left: 6px;
      background: #4a4a4a;
      color: #fff;
      width: 15px;
      height: 15px;
      line-height: 10px;
      font-size: 10px;
      padding: 3px;
      text-align: center;
      border-radius: 2px; }
      @media print {
        .employment-condition__give span {
          top: 6px;
          background: #4a4a4a !important;
          color: #fff !important; } }
  @media print {
    .employment-condition__argree {
      padding-top: 100px; } }
  .employment-condition__sign {
    margin-top: 50px; }
    .employment-condition__sign h3 {
      font-size: 14px;
      margin-bottom: 30px;
      font-weight: 600; }
    .employment-condition__sign-wrapper {
      display: inline-block;
      box-sizing: border-box;
      width: 49.5%;
      vertical-align: top; }
      @media print {
        .employment-condition__sign-wrapper {
          width: 45%; }
          .employment-condition__sign-wrapper:first-child {
            margin-right: 5%; } }

  .employment-condition-training__table {
    .employment-condition-training__label {
      position: relative;
      display: inline-block;
      border-radius: 2px;
      margin: 0;
      span {
        display: inline-block;
        &:first-child {
          padding-right: 10px;
        }
        &:last-child {
          width: 130px;
        }
      }
    }
    .employment-condition-training__value {
      display: inline-flex;
    }
    .employment-condition-training__child-value {
      display: inline-flex;
      padding-left: 60px;
    }
  }

  .employment-condition-single-haken__table {
    tbody {
      tr {
        td {
          p {
            display: inline-block;
            padding-right: 20px;
            margin: 0;
            .label {
              font-weight: 700;
              padding-right: 10px;
            }
            .pull-right {
              position: relative;
              float: right;
              right: 40%;
            }
          }
        }
      }
      table {
        tbody {
          tr {
            .title {
              padding: 0 !important;
              width: 15%;
              text-align: center;
            }
          }
        }
      }
    }
  }

.border-right {
  border-right: 1px solid #dee2e6 !important; }

.p-0 {
  padding: 0 !important; }
  @media print {
    .p-0 {
      padding: 0 !important; } }

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important; }

.pl-0,
.px-0 {
  padding-left: 0 !important; }

.pr-0,
.px-0 {
  padding-right: 0 !important; }

.pb-1,
.py-1 {
  padding-bottom: .25rem !important; }

.pt-1,
.py-1 {
  padding-top: .25rem !important; }

.pl-2,
.px-2 {
  padding-left: .5rem !important; }

.pl-3,
.px-3 {
  padding-left: 1rem !important; }

.pr-3,
.px-3 {
  padding-right: 1rem !important; }

.pl-4,
.px-4 {
  padding-left: 1.5rem !important; }

.ml-2,
.mx-2 {
  margin-left: .5rem !important; }

.ml-4,
.mx-4 {
  margin-left: 1.5rem !important; }

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important; }

.ml-3,
.mx-3 {
  margin-left: 1rem !important; }

.mr-3,
.mx-3 {
  margin-right: 1rem !important; }

.border-0 {
  border: 0 !important; }

.border-bottom {
  border-bottom: 1px solid #dee2e6 !important; }

/*# sourceMappingURL=print.css.map */
#location_time_sheet {
  @media print {
    font-size: 14px;
  }
  .employment-condition__title {
    font-size: 40px;
    text-decoration: underline;
  }
  .employment-condition__img {
    left: 20%;
    width: 60%;
    transform: none;
    top: 90px;
  }
  .base-info {
    position: relative;
    width: 100%;
    top: -35px;
    .corporation {
      width: 50%;
      text-align: left;
    }
    .lawson-info {
      width: 50%;
      text-align: right;
    }
  }
  .order-case-info {
    table {
      width: 100%;
      &.basic-info {
        position: relative;
        top: -60px;
      }
      &.arrangements-info {
        position: relative;
        top: -40px;
      }
      .width-20 {
        width: 20%;
      }
      tr {
        td, th {
          font-weight: normal;
          text-align: center;
          background: #fff;
          &.highlight{
            background: #dddddd;
          }
          border: 1px solid #000;
          min-width: 50px;
          &.blank-space {
            border: none;
            background: none;
          }
          &.wrap-line {
            white-space: pre-wrap;
          }
        }
      }
    }
  }
} 