.custom-st-user-profile > div {
  display: flexbox;
}
.custom-st-user-profile {
  text-align: center;
  padding: 5px 0 5px;
  .custom-nav-st-user-profile__number {
    font-size: 12px;
    color: #8a8a8a;
  }
 .custom-nav-st-user-profile__name {
    font-weight: 600;
  }
  .custom-show-st-user-profile__number {
    font-size: 16px;
  }
  .custom-show-st-user-profile__name {
    font-weight: 600;
    font-size: 20px;
    color: #fff;
  }
}

.st-user-address__info {
  padding: 0px 0px 5px 0px;
  .custom-nav-st-user-map_pin {
    font-size: 13px;
    color: #8a8a8a;
    .fa-map-pin {
      padding: 5px;
    }
  }
}

.st-user-profile {
  .st-user-profile__avatar {
    img {
      height: 80px;
      width: 80px;
    }
  }
}

.st-work-filter__form {
  .custom-st-work-filter__datepicker {
    margin-right: 0;
  }
  .custom-span-st-work-filter__datepicke {
    margin: 0 10px;
    padding: 10px 0;
  }
}

@media (max-width: 767px) {
  .st-work-filter__form {
    .custom-span-st-work-filter__datepicke {
      margin: 0 2px;
      padding: 10px 0;
    }
  }
}

#modal-welcome-workz {
  .modal-close {
    i {
      color: #fff;
    }
  }
  .modal-dialog {
    margin-top: 60px;
    max-width: 700px;
  }
  .modal-content {
    border-radius: 10px;
  }
  .modal-body {
    border-radius: 10px;
    height: 550px;
    background: linear-gradient(141.93deg, #009CC3 9.94%, #01D39A 78.77%, #01D39A 78.77%);
    .close-container {
      padding: 20px;
    }
    .carousel {
      height: 100%;
      img {
        margin: 0 auto;
      }
      .carousel-indicators {
        bottom: 50px;
        li {
          width: 10px;
          height: 10px;
          border-radius: 100%;
          background-color: #FFFFFF;
        }
        .active {
          background-color: #7F7F7F;
        }
      }
      .carousel-inner {
        text-align: center;
        p, span {
          font-weight: 600;
          font-size: 22px;
          line-height: 24px;
          color: #FFFFFF;
        }
        .small {
          font-size: 16px;
        }
        .normal {
          font-size: 21px;
        }
        .big {
          font-size: 30px;
        }
        .highlight-container {
          display: inline-block;
        }
        .highlight::before {
          display: block;
          content: "・"
        }
      }
    }
  }
}