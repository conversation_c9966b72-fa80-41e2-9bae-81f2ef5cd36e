.staff-logo {
  display: block;
  background: asset-url("crew_logo.svg") no-repeat;
  background-size: contain;
  height: 21px;
  width: 260px;
}
.staff-logo-full {
  display: block;
  background: asset-url("crew_logo.svg") no-repeat;
  margin: 0px auto;
  height: 23px;
  width: 290px;
}

.custom-selectbox__item {
  cursor: pointer;
}

.form-error label.custom-selectbox__label, .form-error label {
  color: #ef9ba2;
}

.selection-box-error-notice, .error-notice {
  color: #f74f64;
  position: relative;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin: 8px 0;

  &::before {
    content: '\f071';
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    margin-right: 10px;
  }
}

.file-field-error-notice {
  border-color: #f64f63 !important;
}

.error-end-date {
  border-bottom-color: #f74f64 !important;
}

[ng-cloak] {
  display: none !important;
}

.custom-position-required {
  margin-top: -25px;
  margin-bottom: 40px;
}

.staff-avatar__change-avatar {
  .staff-avatar_image {
    #staff-avatar-img {
      height: 200px;
      width: 200px;
    }
  }
}

.reset-password-confirm{
  margin-top: 75px;
}

.btn-submit-form {
  &:disabled {
    pointer-events: none;
    background-color: #e5e5e5;
    color: #fff;
  }
}

#location-map{
  min-width: 500px;
  height: 400px;
}

.form-group.has-error {
  .help-block {
    color: #f5748b;
    position: relative;
  }
  label {
    color: #f5748b;
  }
  .form-control {
    border-bottom-color: #f5748b;
  }
  .form-control:focus {
    border-bottom-color: #f5748b;
  }
  .select-box {
    .help-block {
      position: relative;
    }
    .custom-select {
      border-bottom: 1px solid #f5748b;
    }
  }
}

.input-disabled {
  pointer-events: none;
  border-radius: 0;
  background: none;
  padding: 8px 0;
  line-height: 17px;
  border-top: 1px solid transparent;
  border-left: 1px solid transparent;
  border-right: 1px solid transparent;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  margin-bottom: 8px;
}

.total-mapping {
  width: auto;
  padding: 0 5px;
}

.word-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.order-case-detail-container {
  .review-popup {
    cursor: pointer;
  }
  .action-btns {
    width: 100%;
    float: left;
    background: #fafafa;
    .action-btn{
      width: 50%;
      text-align: center;
      float: left;
      padding: 5px;
    }
  }
}

.disp-block {
  display: block;
}

.margin-top-0 {
  margin-top: 0px;
}

.staff-worklist-disable {
  opacity: .5;
}

.st-work-history__top {
  padding-top: 20px;
}

.st-work-history-title {
  border-bottom: 1px solid #d8d8d8;
}

#modal-apply-error {
  .st-work-modal__office {
    padding: 30px;
  }
}

#modal-contact-op-center, #modal-contact-op-center-contract-renewal {
  .st-work-modal__office {
    padding: 30px;
  }
}

.img-pdf-export {
  cursor: pointer;
}

.st-keeplist .custom-selectbox__toggle, .st-keeplist {
  .btn-cancel {
    background-color: #555;
  }
  .btn-submit {
    background-color: #4CB1CE;
  }
}

.st-frontpage .normal-notice__item.loadmore {
  &::after {
    content: '\f078';
  }
}

.st-frontpage .payment-request {
  padding: 24px;
  margin: 0px 0px 40px;
  justify-content: space-between; }
  @media (max-width: 1090px) {
    .st-frontpage .payment-request {
      box-shadow: none;
      background-color: transparent;
      padding: 0 0 16px;
      margin-bottom: 0px; } }
  .st-frontpage .payment-request__original {
    margin-right: 7px;
    padding: 16px 16px 8px;
    flex: 0 1 40%;
    max-width: 40%;
    @media (max-width: 1090px) {
      flex: 0 1 100%;
      max-width: 100%;
      margin: 0 0 5px;
    }
    @media (max-width: 320px) {
      padding: 13px 13px 8px;
    }
  }
  .st-frontpage .payment-request__warning {
    font-size: 12px;
    align-self: flex-end;
    line-height: 18px;
    @media (max-width: 767px) {
      font-size: 11px;
      line-height: 17px;
    }
    @media (max-width: 320px) {
      font-size: 10px;
      line-height: 15px;
    }
  }
  .st-frontpage .payment-request__requestable {
    margin: 0 7px;
    padding: 16px 16px 8px;
    flex: 0 1 40%;
    max-width: 40%; }
    @media (max-width: 1090px) {
      .st-frontpage .payment-request__requestable {
        flex: 0 1 100%;
        max-width: 100%;
        margin: 5px 0; } }
  .st-frontpage .payment-request__link {
    margin-left: 7px;
    padding: 16px;
    background-color: #4CB1CE;
    color: #fff;
    font-size: 18px;
    font-weight: 800;
    border-radius: 4px;
    flex: 0 1 18%;
    max-width: 16%;
    display: flex;
    justify-content: center;
    align-items: center; }
    @media (max-width: 1090px) {
      .st-frontpage .payment-request__link {
        flex: 0 1 100%;
        max-width: 100%;
        margin: 5px 0 0 } }
      .st-frontpage .payment-request__link:hover {
        background-color: #3eaeb4; }
  .st-frontpage .payment-request__amount {
    display: block;
    font-size: 18px;
    margin: 5px 0 0 48px; }
    @media (max-width: 1090px) {
      .st-frontpage .payment-request {
        font-size: 18px; } }

@media (min-width: 767px) {
  #apply-all-keeping-jobs {
    .modal-content {
      min-width: 400px;
    }
  }
}

.disable-next-prev-schedule {
  span {
    background-color: #fafafa !important;
    color: #e1e3ea !important;
  }
  pointer-events: none;
}

.disable-link-click {
  pointer-events: none;
  span {
    color: #8fdee1;
  }
}

.cursor-pointer {
  cursor: pointer;
}

.custom-scroll {
  overflow-y: scroll;
  max-height: 250px;
}

#error-all-keeping-jobs {
  .modal-dialog {
    max-width: 900px;
  }
  table {
    td {
      word-break: break-all;
    }

    .work-day {
      width: 30%;
    }

    .work-place {
      width: 40%;
    }

    .work-day {
      width: 30%;
    }
  }

  .confirm-close {
    background-color: #4CB1CE !important;
  }
}

#inlinestaff {
  .bootstrap-datetimepicker-widget {
    .datepicker {
      .datepicker-days {
        .active:not(.highlighted) {
          background: none;
        }
      }
    }
  }
}

.staff-worklist {
  .worklist-status--canceled {
    background-color: #e2e2e2;
    color: #a1a1a1;
    &::before {
      content: '\f410';
    }
  }

  .st-similar__status--deadline {
    background-color: #e2e2e2;
    padding: 0 8px 0 8px !important;
  }
}

.processing-failed {
  color: #f74f64 !important;
}

.custom-circle-list {
  list-style-type: circle;
}

.disable-my-page {
  cursor: default;
  pointer-events: none;
  &:hover {
    color: #8a8a8a;
  }
}

.st-user-page .disable-link-my-page {
  cursor: default;
  pointer-events: none;
  color: #a1a1a1;
}

.staff-note-area {
  margin-top: 25px !important;
  padding-bottom: unset !important;
}

label.staff-note-text {
  top: 60px;
  font-size: 14px;
  color: #8a8a8a;
}

.on-top {
  top: 25px !important;
  color: #4CB1CE !important;
  font-size: 12px;
}

.full-width {
  width: 100%;
}

.full-width-float-left {
  width: 100%;
  float: left;
  margin-left: 0;
  .base-of-income {
    float: left;
    padding-right: 10px;
  }
  .salary-inline {
    float: left;
    min-width: 280px;
  }
  .special-offer{
    float: left;
    margin-right: 15px;
    .st-work__tag--two-edge {
      margin-left: 0px;
    }
  }
}

.step-form .form-custom__iframe {
  overflow: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  height: 300px;
  margin-bottom: 24px;
  iframe {
    width: 100%;
    height: 100%;
    vertical-align: top;
  }
}

.st-work-result__item .st-similar__item--deadline .st-similar__break i {
  color: #a1a1a1; }

.st-work__apply--break {
  padding: 0 20px; }
  @media (max-width: 767px) {
    .st-work__apply--break {
      padding: 0 10px; } }
  @media (max-width: 320px) {
    .st-work__apply--break {
      padding: 0; } }

.st-work__status--apply-break {
  color: #a1a1a1;
  display: inline-block;
  margin: 0 0 7px;
  white-space: nowrap; }
  .st-work__status--apply-break i {
    font-size: 18px;
    color: #d7b44b; }

.st-similar__break {
  display: block;
  font-weight: 600;
  margin-top: 5px; }
  @media (max-width: 767px) {
    .st-similar__break {
      font-size: 12px; } }
  .st-similar__break i {
    color: #d7b44b;
    margin-right: 3px; }

.st-work__date-time::after {
  content: none; }

.st-work__date-time .st-work__working-date {
  margin-bottom: 0px; }

@media (max-width: 767px) {
  .col-10 {
    .pr-4 {
      padding-right: 4% !important;
    }

    .form-customize input[type="text"] + label {
      width: 90%;
      font-size: 12px;
    }

    .datetimepicker.form-customize.staff-picker .datetimepicker-input {
      padding-left: 10px;
    }

    .form-customize input[type="text"] + label.label-ontop {
      font-size: 11px;
      display: none;
    }

    .datetimepicker.form-customize.staff-picker .input-field-icon {
      font-size: 14px;
      top: inherit;
      bottom: 0;
    }
  }
}

@media (max-width: 365px) {
  .modal-body .modal-footer {
    .pl-5 {
      padding-left: 2rem !important;
    }
    .pr-5 {
      padding-right: 2rem !important;
    }
  }
  .hidden-365 {
    visibility: hidden;
  }
}

@media (max-width: 340px) {
  .st-work-filter__form .form-customize {
    padding-left: 5px;
    padding-right: 5px;
  }
  .st-work-filter__button {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.alert-fcm__message {
  margin: 0 0 40px;
  @media (max-width: 1090px) {
    .alert-fcm__message {
      margin-bottom: 0px;
    }
  }
}

.alert-fcm__to-setting, .alert-fcm__disable-notification {
  color: #4CB1CE;
  cursor: pointer;
}

.alert-fcm__switch-button {
  position: relative;
  display: inline-block;
  width: 51px;
  height: 31px;
  margin-top: 0.5rem;
  margin-left: 0.5rem; }
  .alert-fcm__switch-button input {
    display: none; }

.alert-fcm__table {
  margin-bottom: 50px;
  @media (max-width: 1090px) {
    .alert-fcm__table {
      margin-bottom: 10px;
    }
  }
}
#modal-notification-info, #popup-register-step  {
  background: rgba(0, 0, 0, 0.6);
  .confirm-dialog.modal-dialog {
    margin-top: 100px;
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
  }
  .modal-body {
    .modal-bottom {
      padding: 24px;
    }
    .modal-middle {
      max-height: 300px;
      overflow: auto;
      .notification-content {
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}
#popup-register-step  {
  @media (min-width: 600px) {
    .modal-body {
      .st-work-modal__office {
        padding: 15px 45px;
      }
    }
  }
  .modal-dialog {
    margin-top: 200px;
  }
}
#notification-popup{
  background: rgba(0, 0, 0, 0.6);
  z-index: 1001;
  .confirm-dialog.modal-dialog {
    margin-top: 100px;
    width: 90%;
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
  }
  .modal-body {
    max-height: 300px;
    overflow: auto;
    .notification__list {
      padding-top: 25px;
    }
    #popup-notification-list {
      .normal-notice__list {
        margin-top: 0;
      }
    }
  }
}

.st-work__see-more {
  cursor: pointer;
}

.modal-middle__regular-time {
  max-height: 200px;
  overflow-y: scroll;
  .st-work__tag--two-edge {
    margin-left: 0;
  }
}

@media (max-width: 414px) {
  .st-work-modal .modal-middle__apply {
    padding: 0;
    max-height: 400px;
    overflow-y: scroll; }
  .st-work-modal .modal-middle__regular-time {
    overflow-y: scroll; }
  .st-work-modal--finish .modal-middle__regular-time {
    height: auto; }
  .st-work-modal--finish p {
    padding: 0 10px;
    } }

@media (max-width: 375px) {
  .modal-middle__regular-time p {
    font-size: 13px; } }

#actual-worked-time-modal {
  padding-right: 15px;
  padding-left: 15px;
  .modal-dialog {
    margin-top: 150px;
    max-width: 100%;
  }
  .show-mobile {
    display: none;
  }
  @media (max-width: 550px) {
    .show-pc {
      display: none;
    }
    .show-mobile {
      display: table;
    }
    .mobile-td-data {
      border-bottom-width: 1px;
      border-color: #e3e3e3;
    }
  }
}

@media (max-width: 767px) {
  .st-work-list__map {
    position: relative;
    padding-bottom: 75%;
    height: 0;
    overflow: hidden;
  }
  
  .st-work-list__map iframe {
    position: relative;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
  }

  .st-work-list__map #location-map {
    min-width: 100px;
    min-height: 150px;
    position: static !important;
  }
}

#modal-welcome-workz {
  padding-top: 20px;
  .modal-dialog {
    margin-top: 0px;
    max-width: 700px;
  }
  .modal-content {
    border-radius: 10px;
  }
  .modal-body {
    height: auto;
    padding: 20px;
    padding-top: 0;
    min-height: 640px;
    .carousel {
      min-height: 570px;
      .carousel-indicators {
        bottom: -30px;
      }
    }
  }
  .carousel-control-prev, .carousel-control-next {
    position: fixed;
    bottom: 0;
  }
  img {
    width: 40%;
  }
  .welcome-staff-3 {
    width: 40%;
    text-align: left;
    margin-left: 30%;
  }
}

#st-registered-profile {
  .mypage-menu__item {
    width: 50%;
  }
}

.st-registered-profile__update {
  padding-bottom: 90px;
  &.mobile{
    padding-bottom: 10px;
  }
}

#schedules-section {
  padding: 20px;
  padding-bottom: 70px;
  .no-record {
    padding: 40px;
  }
  .interview {
    margin-bottom: 10px;
  }
  .mr-xl-4 {
    border: none;
    margin: none;
  }
  .with-border {
    border: 1px solid #e3e3e3;
  }
  .index-staff {
    color: #4a4a4a;
    padding: 7px;
    border-radius: 12px;
    font-weight: 600;
    background-color: #e6f5ff;
    margin-right: 16px;
  }
}
#interview-info-popup {
  text-align: center;
 .modal-body {
    text-align: left;
    padding-top: 20px;
    padding-bottom: 20px;
    width: 100%;
    height: auto;
    float: left;
  }
  .modal-footer {
    display: block;
  }
  button {
    border: none;
  }
}

#interview-error-popup {
  .modal-body {
    padding: 30px;
  }
}

.datetimepicker.form-customize .table td{
  &.available {
    background-color: #e6f5ff; 
    border: solid 1px #fff;
  }
  &.applied {
    background-color: #79d2d6;
    color: #fff;
    font-size: bold;
    border: solid 1px #fff;
  }
  &.today {
    color: #0a76be;
  }
  &.active {
    background: #0a76be;
    color: #fff;
    border-radius: 3px;
  }
  &.disabled {
    color: #b5b5b5;
    background: #fff;
  }
}
.filter-plan__status--inprogress {
  &.applied {
    background-color: #79d2d6;
    color: #fff;
  }
}
.interview-calendar {
  a.to-url {
    padding: 16px 32px 16px 16px;
    display: block;
    background-color: #e7feff;
    position: relative;
    transition: all .25s ease;
    margin: 5px 0;
    &::after {
      content: '';
      width: 10px;
      height: 10px;
      border-top: 2px solid #a1a1a1;
      border-right: 2px solid #a1a1a1;
      position: absolute;
      top: 50%;
      right: 16px;
      transform: rotate(45deg) translateY(-50%);
    }
  }
  .no-record {
    margin: 5px 0;
    padding: 32px;
    border: 1px solid #dee2e6 !important;
  }
  .calendar-action {
    padding: 0;
    padding-right: 15px;
    margin: 5px 0;
    @media (max-width: 765px) {
     padding-left : 15px;
    }
  }
  .cancel-calendar {
    &:hover {
      background-color: #3eaeb4;
    }
    display: flex;
    padding: 16px;
    background-color: #4CB1CE;
    color: #fff;
    font-size: 18px;
    font-weight: 800;
    border-radius: 4px;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
  }
}
#staff-notification-pages {
  margin-top: 10px;
  .normal-notice {
    margin-top: 0;
  }
}
@font-face {
  font-family: 'icomoon2';
  src: asset-url('icomoon2.woff') format('woff'), asset-url('icomoon2.ttf')  format('truetype'), asset-url('icomoon2.svg#icomoon2') format('svg');
  font-weight: normal;
  font-style: normal;
}
.icon {
  font-family: "icomoon2" !important;
  font-style: normal;
}
.icon-clock:before {
  content: "\e901";
}
#completed-signup-interview {
  padding: 40px;
  .icon-clock {
    font-size: 90px;
    color: #888;
  }
  span {
    font-size: 15px;
    font-weight: 500;
    &.big-size {
      font-size: 19px;
    }
  }
  .fas {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
  }
  .btn-home_page {
    background-color: #0a76be;
    color: #fff;
    float: none;
    height: 48px;
    padding: 14px 45px;
    font-weight: 600;
    font-size: 15px;
    border: none;
    text-decoration: none;
    position: relative;
    min-width: 200px;
    top: 40px;
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 10px;
  }
  button.btn {
    min-width: 200px;
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 10px;
    @media only screen and (max-width: 800px) {
      min-width: 150px;
    }
  }
  .bottom-btn {
    display: inline;
  }
  #visit-url {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
  .bottom-btn {
    display: block;
    padding-top: 50px;
    padding-bottom: 20px;
    @media only screen and (max-width: 800px) {
      padding-top: 70px;
      padding-bottom: 110px;
    }
  }
  @media only screen and (max-width: 480px) {
    button.btn {
      margin: 0px;
    }
    #cancel-interview {
      display: block;
      float: left;
    }
    #change-interview {
      display: block;
      float: right;
    }
  }

  @media only screen and (max-width: 321px) {
    .interview-btns {
      padding: 0
    }
    button.btn {
      min-width: 140px;
    }
    span {
      font-size: 13px;
    }
  }
}
.st-main-menu__icon {
  &.icon {
    margin-right: 10px;
  }
}
#modal-confirm-update-interview, #update-interview-success, #update-interview-false {
  .modal-body {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

.interview-video-page {
  .box-wrapper {
    padding: 20px 0;
    .interview-video-wrapper {
      text-align: center;
      iframe {
        height: 500px;
        width: 100%;
      }
      @media only screen and (min-width: 1136px) and (max-width: 1185px) {
        iframe {
          height: 450px;
        }
      }
      @media only screen and (max-width: 1135px) and (min-width: 1100px) {
        iframe {
          height: 400px;
        }
      }
      @media only screen and (max-width: 1100px) and (min-width: 800px) {
        iframe {
          height: 350px;
        }
      }
      @media only screen and (max-width: 800px) {
        iframe {
          height: 300px;
        }
      }
    }
  }
  .matter_conditions_container {
    padding: 15px;
    .wrapper {
      width: fit-content;
      margin: 0 auto;
    }
    i {
      color: #4CB1CE;
    }
  }
}

.individual-number-modal-body {
  padding: 0.75rem;
  margin-bottom: 1rem;
}

#individual-number-form {
  .staff-card__upload-label {
    color: #a1a1a1;
  }
  .form-control, .custom-selectbox__label, .staff-required {
    background-color: #fafafa;
  }
}

.staff-idv-number__button .button {
  margin-top: 60px;
  margin-bottom: 60px;
  width: 85%; }
  .staff-idv-number__button .button .fas {
    font-size: 15px; }
  @media (max-width: 767px) {
    .staff-idv-number__button .button {
      margin-top: 60px;
      width: 90%; } }
  @media (max-width: 375px) {
    .staff-idv-number__button .button {
      margin-top: 30px; } }
  @media (max-width: 320px) {
    .staff-idv-number__button .button {
      margin-top: 15px; } }

#st-my-number {
  .st-user-info {
    border-top: 1px solid #d8d8d8;
  }

  .border-bottom {
    border-bottom: 1px solid #d8d8d8;
  }
}

#individual-number-modal {
  .btn-close {
    background-color: #d8d8d8;
    &:hover {
      background-color: #c4c4c4;
    }
  }
}
.st-user-info__button {
  &.mobile {
    margin-bottom: 0!important;
  }
}
.transportation-fee-notice {
  margin-top: 12px;
  font-size: 12px;
}
.st-sidebar {
  &.public-jobs {
    padding-top: 73px;
  }
}

#unauthenticate-header {
  height: 56px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  padding: 15px;
}
.shift-description {
  color: #a1a1a1;
  font-size: 11px;
}

.form-customize .valid-notice {
  color: #0ab15b;
  position: relative;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin: 8px 0;
  &::before {
    content: '\f058';
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    margin-right: 10px;
  }
}

.st-regist__body {
  .form-customize .toggle-icon {
    right: 11px;
    top: 41px;
  }
}
@media (max-width: 460px) {
  .label-login {
    font-size: 11px!important;
  }
}
.d-hidden {
  visibility: hidden;
}
.responsive-iframe {
  position: relative;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 400px;
  @media (max-width: 460px) {
    height: 300px;
  }
}

.required:after {
  content: "*";
  color: #ec3f3f;
}

#individual-number-modal {
  .confirm-dialog {
    margin-top: 50px; }
  @media (min-width: 576px) {
    .modal-dialog {
      max-width: 600px; } } }

.static-page-body {
  padding-top: 150px;
  @media (max-width: 576px) {
    padding-top: 20px }
}