.st-work__left {
  .st-work-list__item {
    .st-work-list__body {
      .st-work-list__station {
        color: #3c3c3c;
      }
    }
  }
}

.st-main-content {
  .st-similar {
    .keep-order-case-icon {
      height: 20px;
    }
  }
}

.st-similar__status--adjustable {
  float: left;
  color: red
}

.st-similar__status--regular {
  float: left;
  color: teal;
}

.st-work-list__note {
  color: #f74f64;
  padding: 0 10px;
  flex: auto
}

.st-work-location-info {
  word-break: break-word;
}

.pd-top-bot-10 {
  padding: 10px 0 15px;
  .fee-flag {
    min-height: 42px;
  }
}
.bg-lawson-store, .bg-lawson-store-work-list::after {
  background-image: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), asset-url("lawson_store.jpg");
}

.bg-nature-lawson {
  background-image: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), asset-url("nature_lawson_bg.jpg");
}

.bg-nature-lawson-work-list::after {
  background-image: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), asset-url("nature_lawson_sm.jpg");
}

#modal-register-schedule {
  iframe {
    width: 100%;
  }
  .matter_conditions_container {
    padding: 15px;
    margin-top: -70px;
    i {
      color: #4CB1CE;
    }
  }
  .st-work-modal-schedule__footer {
    padding: 8px 20px;
    @media only screen and (max-width: 320px) {
      padding: 8px 8px;
    }
  }
}

#modal-register-schedule-finished, #modal-waiting-interview {
  .staff-btn {
    line-height: 48px;
  }
}

#modal-waiting-interview {
  .st-work-modal-waiting-interview__footer {
    padding: 8px 30px;
    @media only screen and (max-width: 320px) {
      padding: 8px 8px;
    }
    button {
      float: left;
    }
    a {
      float: right;
    }
  }
}

#modal-input-insurance, #modal-input-insurance-instruction {
  .modal-body {
    padding: 20px;
  }
  .modal-dialog {
    margin-bottom: 50px;
  }
}