.st-answer__form-content {
  padding-bottom: 44px;
}

.st-answer__question {
  font-size: 14px;
  margin-top: 30px;
  margin-bottom: 20px;
  font-weight: 600;
}

.st-answer__question.required:after, .st-user-info__card-name.required:after {
  content: "*";
  color: #ec3f3f;
}

.staff-form.input-radio-custom.st-answer__radio {
  font-size: 13px;
  margin-top: 15px;
  margin-bottom: 0px;
}

.staff-form.input-checkbox-custom.st-answer__radio {
  font-size: 13px;
  margin-top: 15px;
  margin-bottom: 0px;
}

// .st-answer__disable.disabled {
//   color: #a1a1a1 !important;
// }

.st-mypage-title-bottom {
  border-bottom: 1px solid #d8d8d8;
}

.st-answer-clearfix {
  padding-top: 20px;
  clear: both;
}

// Overwrite main style

#st-answer {
  .custom-selectbox__toggle, .custom-selectbox__label {
    background-color: #fafafa;
  }
  .notices-staff {
    padding: 15px;
  }
}

#instruction-modal, #link-to-matchbox-modal, #lawson-answer-modal {
  .modal-dialog {
    max-width: 600px;
    margin-bottom: 80px;
    padding: 15px;
  }
  .modal-body {
    padding: 20px 15px;
  }
}

.st-answer__modal-header {
  padding: 15px;
  border-bottom: 1px solid #e3e3e3;
  background-color: #f5f5f5;
}

.st-answer__modal-title {
  font-size: 14px;
  font-weight: 600;
  @media (max-width: 375px) {
    font-size: 12px;
  }
}

.st-answer__modal-text {
  font-size: 14px;
  @media (max-width: 375px) {
    font-size: 12px;
  }
}

.st-answer__modal-text-explain {
  font-size: 14px;
  padding: 20px 20px 0px;
  @media (max-width: 375px) {
    font-size: 12px;
  }
}

.st-answer__modal-text-small {
  font-size: 13px;
  @media (max-width: 375px) {
    font-size: 11px;
  }
}

.st-answer__modal-text-blue {
  font-size: 14px;
  font-weight: 600;
  color: #4CB1CE;
  @media (max-width: 375px) {
    font-size: 12px;
  }
}

.st-answer__modal-text-bordered {
  border: 1px solid #4CB1CE;
  border-radius: 0.3rem;
  margin: 20px 0;
  padding: 15px;
  font-size: 13px;
  @media (max-width: 375px) {
    font-size: 11px;
    padding: 10px;
  }
}

.st-answer__modal-matchbox-ad {
  padding: 20px 20px 40px;
  font-size: 13px;
  @media (max-width: 375px) {
    font-size: 11px;
    padding: 10px;
  }
}

.st-answer__modal-background-blue {
  border-radius: 0.3rem;
  background-color: rgba(76, 177, 206, 0.3);
  padding: 15px;
  @media (max-width: 375px) {
    padding: 10px;
  } 
}

.st-answer__modal-background-grey {
  border-radius: 0.3rem;
  background-color: #f5f5f5;
  padding: 15px;
  margin: 20px 0;
  @media (max-width: 375px) {
    padding: 10px;
  }
}

.st-answer__row {
  margin-right: -10px;
  margin-left: -10px;
}

.st-answer__row-2 {
  margin-left: -10px;
  margin-right: -10px;
  @media (max-width: 375px) {
    margin-left: -5px;
    margin-right: -5px;
  }
}

.st-answer__user-icon-group {
  padding-right: 0;
  margin-bottom: 10px;
}

.st-answer__user-icon {
  font-size: 45px;
  margin: 0 0 5px;
  padding-top: 10px;
  color: #4CB1CE;
  @media (max-width: 375px) {
    font-size: 35px;
    margin: 0 0 3px;
  }
}

.st-answer__user-icon-note {
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding-left: 5px;
  height: 100%;
  @media (max-width: 375px) {
    padding-left: 3px;
  }
  @media (max-width: 320px) {
    font-size: 9px;
  }
}

.st-answer__labor-dispatch-type {
  font-size: 10px;
  padding-left: 5px;
  padding-right: 5px;
  .st-answer__labor-dispatch-type--blue {
    border-radius: 0.3rem;
    background-color: rgba(76, 177, 206, 0.3); //rgba(126, 179, 232, 0.3);
    padding: 5px;
    height: 100%;
  }
  .st-answer__labor-dispatch-type--border {
    border-bottom: 1px solid rgba(74, 144, 226, 0.3);
    padding-bottom: 5px;
    margin-bottom: 5px;
  }
  @media (max-width: 375px) {
    font-size: 9px;
    padding-left: 3px;
    padding-right: 3px;
  }
}

.st-answer__modal-divider {
  border-top: 1px solid #e3e3e3;
  margin: 20px;
  height: 0;
}

.st-answer__modal-image {
  display: block;
  width: 100%;
  height: auto;
  border-radius: 0.4rem;
  padding: 2px;
}

.st-answer__matchbox-checkbox:before {
  border: 1px solid #FF6B00 !important;
  box-shadow: none !important;
}

.button--matchbox {
  background-color: #FF6B00;
  color: #fff;
}

.st-answer__work-experience-border {
  border: 1px solid #4CB1CE;
  border-radius: 1.5rem;
  padding: 10px 15px;
  font-size: 13px;
  i {
    font-size: 17px;
    color: #a1a1a1;
    cursor: pointer;
  }
}

.st-answer__button {
  font-size: 13px;
  border-radius: 0.3rem;
  width: 150px;
}

.st-answer__selected-professions {
  max-height: 500px;
  overflow-y: auto;
  padding-right: 10px;
}