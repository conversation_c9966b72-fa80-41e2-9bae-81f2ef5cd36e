.st-regist__body {
  display: flex;
  flex-direction: column;
  padding: 0px;
  position: relative;
  background: #F9F9F9;
  padding: 44px 16px;
}

.st-regist__title {
  font-size: 18px;
  font-weight: 600;
}

.st-regist__form {
  font-size: 13px;
  .form-group {
    margin-bottom: 24px;
    position: relative;
  }
  .form-customize#birthday {
    margin-bottom: 0px;
  }
  .form-customize label {
    position: relative;
    background-color: transparent;
    font-size: 13px;
    color: #3c3c3c;
    top: 0;
    width: auto;
  }
  .form-customize.form-error label {
    color: #f74f64;
  }
  .form-control {
    height: 44px;
    font-size: 12px;
    padding: 8px 16px;
    position: relative;
    background: #FFFFFF;
    border: 1px solid #F0F0F0;
    box-shadow: 0 0 10px transparent;
    box-sizing: border-box;
    border-radius: 3px;
    overflow: hidden;
    transition: all .25s ease;
    line-height: 24px;
  }
  .form-control.form-control-with-icon {
    padding-left: 42px; 
  }
  .form-control:focus {
    box-shadow: 0 0 10px 0 rgba(0,0,0,0.1);
  }
  .required:after {
    content: "*";
    color: #ec3f3f;
  }
  .input-append {
    width: 100%;
    .input-field-icon {
      right: 13px !important;
      line-height: 45px !important;
    }
  }
}

.tel-notice {
  margin-top: 5px;
  display: flex;
  flex-direction: row;
  align-items: center;
  color: #55afd1 !important;
  .tel-notice-icon {
    display: flex;
    align-items: center;
    margin-right: 5px !important;

  }
  .tel-notice-text {
    position: relative;
  }
  a {
    text-decoration: underline;
  }
}

.st-regis__radio-btn {
  padding: 10px;
}

.st-regis__group-radio-btn {
  padding: 2px;
  border-radius: 0.25rem;
  background-color: #DFDFDF;
  .button--full label {
    display: block;
    font-size: 13px;
  }
  .button--primary, .button--primary:hover {
    background-color: #4CB1CE;
    box-shadow: none;
    label {
      display: block;
      color: #FFFFFF;
    }
  }
}

.st-regist__disabled {
  color: #8a8a8a !important;
}

.st-regist__button {
  height: 48px;
  line-height: 48px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 4px;
}

.input-field__icon {
  width: 48px;
  height: 36px;
  line-height: 94px;
  position: absolute;
  top: 2px;
  color: #888;
  text-align: center;
  font-size: 18px;
  transition: all .25s ease;
  z-index: 10;
  .st-regist__icon--postal:before {
    content: "〒";
    font-style: normal;
  }
}

.st-regist__finish--title {
  font-size: 18px;
  font-weight: 600;
  padding-top: 100px;
  padding-bottom: 10px;
  color: #373737;
}

.st-regist__finish--sent {
  height: 145px;
  position: relative;
}

.st-regist__finish--note {
  font-size: 13px;
  color: gray;
  margin-bottom: 15px;
}

.st-regist__animation--outside {
  animation: regist-outside .4s ease-in-out forwards;
}

@keyframes regist-outside {
  0% {
    width: 0; }
  100% {
    width: 192px; } 
}

.js-datepicker-mobile, .js-mobile-datepicker-birthday {
  .input-field-icon-wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
  }
  .input-field-icon {
    float: right;
  }
}

.select-birthday-wraper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .select-custom {
    top: -10px;
    width: 30%;
    display: inline-block;
    position: relative;
  }
  .select-custom-field {
    width: 30%;
    display: inline-block;
    position: relative;
    background: #fff;
    z-index: 1;
    select {
      border: 1px solid #f0f0f0;
      font-size: 12px;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: transparent;
    }
    &:before {
      content: "";
      vertical-align: middle;
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      border-top: 4px solid #b5b5b5;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      z-index: -1;
    }
  }
}

.select-birthday-wraper .select-custom select::-ms-expand {
  display: none;
}

.wrapper-birthday-container.form-customize .form-error {
  .select-field {
    border: 1px solid #f05151;
  }
}

.select-birthday-wraper.select-pc {
  .select-custom.form-error {
    border: 1px solid #f05151;
  }
}

.st-regist__select {
  .custom__selectbox__label {
    height: 0px !important;
  }
}
.single-haken-condition {
  a {
    cursor: pointer;
    color: #1E90FF !important;
  }
}

.st-regist__finish--center {
  display: flex;
  align-items: center;
  justify-content: center;
}
