.owl-carousel-custom {
  display: none;
  width: 100%;
  -webkit-tap-highlight-color: transparent;
  /* position relative and z-index fix webkit rendering fonts issue */
  position: relative;
  z-index: 1; }
  .owl-carousel-custom .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
    touch-action: manipulation;
    -moz-backface-visibility: hidden;
    /* fix firefox animation glitch */ }
  .owl-carousel-custom .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0; }
  .owl-carousel-custom .owl-stage-outer {
    position: relative;
    overflow: hidden;
    /* fix for flashing background */
    -webkit-transform: translate3d(0px, 0px, 0px); }
  .owl-carousel-custom .owl-wrapper,
  .owl-carousel-custom .owl-item {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0); }
  .owl-carousel-custom .owl-item {
    position: relative;
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none; }
  .owl-carousel-custom .owl-item img {
    display: block;
    width: 100%; }
  .owl-carousel-custom .owl-nav.disabled,
  .owl-carousel-custom .owl-dots.disabled {
    display: none; }
  .owl-carousel-custom .owl-nav .owl-prev,
  .owl-carousel-custom .owl-nav .owl-next,
  .owl-carousel-custom .owl-dot {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }
  .owl-carousel-custom .owl-nav button.owl-prev,
  .owl-carousel-custom .owl-nav button.owl-next,
  .owl-carousel-custom button.owl-dot {
    background: none;
    color: inherit;
    border: none;
    padding: 0 !important;
    font: inherit; }
  .owl-carousel-custom.owl-loaded {
    display: block; }
  .owl-carousel-custom.owl-loading {
    opacity: 0;
    display: block; }
  .owl-carousel-custom.owl-hidden {
    opacity: 0; }
  .owl-carousel-custom.owl-refresh .owl-item {
    visibility: hidden; }
  .owl-carousel-custom.owl-drag .owl-item {
    -ms-touch-action: pan-y;
        touch-action: pan-y;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }
  .owl-carousel-custom.owl-grab {
    cursor: move;
    cursor: -webkit-grab;
    cursor: grab; }
  .owl-carousel-custom.owl-rtl {
    direction: rtl; }
  .owl-carousel-custom.owl-rtl .owl-item {
    float: right; }

/* No Js */
.no-js .owl-carousel-custom {
  display: block; }

/*
 *  Owl Carousel - Animate Plugin
 */
.owl-carousel-custom .animated {
  animation-duration: 1000ms;
  animation-fill-mode: both; }

.owl-carousel-custom .owl-animated-in {
  z-index: 0; }

.owl-carousel-custom .owl-animated-out {
  z-index: 1; }

.owl-carousel-custom .fadeOut {
  animation-name: fadeOut; }

.owl-carousel-custom .owl-item {
  /**
      This is introduced due to a bug in IE11 where lazy loading combined with autoheight plugin causes a wrong
      calculation of the height of the owl-item that breaks page layouts
     */ }
  .owl-carousel-custom .owl-item .owl-lazy {
    opacity: 0;
    transition: opacity 400ms ease; }
  .owl-carousel-custom .owl-item .owl-lazy[src^=""], .owl-carousel-custom .owl-item .owl-lazy:not([src]) {
    max-height: 0; }
  .owl-carousel-custom .owl-item img.owl-lazy {
    transform-style: preserve-3d; }

/*
 *  Owl Carousel - Video Plugin
 */
.owl-carousel-custom .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000; }

.owl-carousel-custom .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url("owl.video.play.png") no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform 100ms ease; }

.owl-carousel-custom .owl-video-play-icon:hover {
  transform: scale(1.3, 1.3); }

.owl-carousel-custom .owl-video-playing .owl-video-tn,
.owl-carousel-custom .owl-video-playing .owl-video-play-icon {
  display: none; }

.owl-carousel-custom .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity 400ms ease; }

.owl-carousel-custom .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%; }

/*
 *  Default theme - Owl Carousel CSS File
 */

.owl-carousel-custom .owl-stage-outer {
  padding-bottom: 10px;
  border-radius: 4px; }

.owl-carousel-custom .owl-nav {
  position: relative; }
  .owl-carousel-custom .owl-nav button.owl-prev,
  .owl-carousel-custom .owl-nav button.owl-next {
    position: absolute;
    top: -180px;
    width: 29px;
    height: 29px;
    line-height: 30px;
    border-radius: 50%;
    transition: all .3s ease; }
    .owl-carousel-custom .owl-nav button.owl-prev i,
    .owl-carousel-custom .owl-nav button.owl-next i {
      background-color: rgba(60, 60, 60, 0.5);
      color: #fff;
      width: 29px;
      height: 29px;
      line-height: 30px;
      border-radius: 50%;
      transition: all .3s ease;
      position: relative;
      z-index: 50;
      cursor: pointer; }
      .owl-carousel-custom .owl-nav button.owl-prev i:hover,
      .owl-carousel-custom .owl-nav button.owl-next i:hover {
        background-color: rgba(87, 204, 210, 0.5); }
    .owl-carousel-custom .owl-nav button.owl-prev.disabled,
    .owl-carousel-custom .owl-nav button.owl-next.disabled {
      visibility: hidden; }
  .owl-carousel-custom .owl-nav button.owl-prev {
    left: 0; }
    .owl-carousel-custom .owl-nav button.owl-prev::before {
      width: 100px;
      height: 293px;
      content: '';
      left: -5px;
      top: -148px;
      background: linear-gradient(-90deg, rgba(226, 226, 226, 0) 0%, #fff 100%);
      position: absolute;
      border-radius: 4px 0 0 4px; }
  .owl-carousel-custom .owl-nav button.owl-next {
    right: 0; }
    .owl-carousel-custom .owl-nav button.owl-next::after {
      content: '';
      width: 100px;
      height: 293px;
      top: -148px;
      background: linear-gradient(90deg, rgba(226, 226, 226, 0) 0%, #fff 100%);
      position: absolute;
      right: -5px;
      border-radius: 0 4px  4px 0; }

.st-frontpage .favorite-store .owl-carousel-custom .owl-nav .owl-next,
    .st-frontpage .favorite-store .owl-carousel-custom .owl-nav .owl-prev,
    .st-frontpage .worked-store .owl-carousel-custom .owl-nav .owl-next,
    .st-frontpage .worked-store .owl-carousel-custom .owl-nav .owl-prev {
      top: -115px; }
    .st-frontpage .favorite-store .owl-carousel-custom .owl-nav .owl-prev::before,
    .st-frontpage .worked-store .owl-carousel-custom .owl-nav .owl-prev::before {
      height: 155px;
      top: -65px; }
    .st-frontpage .favorite-store .owl-carousel-custom .owl-nav .owl-next::after,
    .st-frontpage .worked-store .owl-carousel-custom .owl-nav .owl-next::after {
      width: 100px;
      height: 155px;
      top: -65px; }
