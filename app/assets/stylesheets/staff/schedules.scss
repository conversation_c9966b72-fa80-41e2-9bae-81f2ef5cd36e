.st-schedule-form {
  .staff-form.input-radio-custom {
    margin-bottom: 0px }
}

.st-schedule-detail {
  max-width: 600px;
  margin: 0px auto;
  a { font-size: 12px; }
}

.interview-schedule-finish__container {
  margin: 32px 16px 32px;
  padding-bottom: 100px;
}

.interview-schedule-finish__box {
  justify-content: center;
  align-items: center;
  padding: 24px 12px;
  margin: auto;
  max-width: 767px;

  /* Background/Primary-White */
  background: #FFFFFF;
  /* Main-Shadow */
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
}

.interview-schedule-finish__loading {
  height: 145px;
  position: relative;
}

.interview-schedule-finish__job-info {
  padding: 6px 8px;
  background: #EDF7FA;
  border-radius: 6px;
  overflow-wrap: break-word;
  p {
    margin-top: 1rem;
    margin-bottom: 1rem; }
  @media (max-width: 390px) {
    .shortened { max-width: 200px; }
  }
}

.interview-schedule-finish {
  &__datetime {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__btn-section {
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
  }
  &__deadline {
    p {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 11px;
    }
  }
}

.simple-divider {
  height: 1px;
  background: #D9D9D9;
  margin: 20px auto 20px;
}

.sm-offset {
  margin-left: 16px;
}

.vertical-progress-icon {
  text-align: center;
  width: 16px;
  margin-left: 15px;
}

.vertical-progress {
  width: 16px;
  margin-left: 15px;
  background-size: 1px 100%;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: linear-gradient(#e5e5e5, #e5e5e5);
  &.done {
    background-image: linear-gradient(#4CB1CE, #4CB1CE); }
  &.pending {
    background-image: linear-gradient(#f5a623, #f5a623); }
}

#about-training-modal {
  .modal-dialog { margin: 1rem; }
  .modal-middle { padding: 1rem; }
  @media (min-width: 576px) {
    .modal-dialog {
      max-width: 500px;
      margin: 1.75rem auto; }
  }
}

.cancel-btn {
  color: #E37778;
  padding: 15px 0;
  display: block;
}

#modal-confirm-cancel-training {
  padding-top: 40px !important;
  .modal-dialog .modal-body {
    .staff-form.input-radio-custom {
      margin-bottom: .9rem;
    }
  }
  .modal-header {
    padding: 15px 15px !important;
  }
  .modal-finish {
    padding: 15px 15px !important;
  }
  .form-error {
    color: #E37778 !important;
    label {
      color: #E37778 !important;
    }
  }
  .schedule-detail {
    font-weight: bold;
    font-size: 14px;
    li:first-child {
      margin-bottom: 0.4rem;
    }
  }
  hr {
    border-top: 2px solid rgba(0, 0, 0, 0.1);
  }
  .last-question {
    margin-bottom: 7px;
  }
  .survey-input-text {
    padding-right: 35px;
    input {
      border-top: none;
      border-left: none;
      border-right: none;
      margin-left: 35px;
      font-size: 12px;
    }
  }
  .modal-footer {
    display: block;
    padding: 8px 15px;
  }
}

#modal-verify-email {
  .modal-dialog .modal-body {
    padding: 40px 20px 20px;
  }
  .form-customize .toggle-icon {
    right: 11px;
    top: 37px;
  }
}

#st-training-schedule-form {
  .form-error {
    color: #E37778 !important;
  }
  .survey-text-input-option {
    margin-bottom: 7px;
  }
  .survey-text-input {
    padding-right: 35px;
    input {
      margin-top: 7px;
      border-top: none;
      border-left: none;
      border-right: none;
      margin-left: 35px;
      font-size: 12px;
    }
  }
}