@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 400;
  src: asset-url("fa-regular-400.eot");
  src: asset-url("fa-regular-400.eot?#iefix") format("embedded-opentype"), asset-url("fa-regular-400.woff2") format("woff2"), asset-url("fa-regular-400.woff") format("woff"), asset-url("fa-regular-400.ttf") format("truetype"), asset-url("fa-regular-400.svg#fontawesome") format("svg");
}
.far {
  font-family: 'Font Awesome 5 Free';
  font-weight: 400;
}
@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 900;
  src: asset-url("fa-solid-900.eot");
  src: asset-url("fa-solid-900.eot?#iefix") format("embedded-opentype"), asset-url("fa-solid-900.woff2") format("woff2"), asset-url("fa-solid-900.woff") format("woff"), asset-url("fa-solid-900.ttf") format("truetype"), asset-url("fa-solid-900.svg#fontawesome") format("svg");
}

.far, .fas {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}
.fa-calendar-alt:before {
  content: '\f073';
}
.fa-clock:before {
  content: '\f017';
}
.fa-coffee:before {
  content: '\f0f4';
}
.fa-train:before {
  content: '\f238';
}
.fa-angle-left:before {
  content: '\f104';
}
.fa-angle-right:before {
  content: '\f105';
}
.fa-angle-down:before {
  content: "\f107"
}
a:hover, a:active, a:focus {
  text-decoration: none;
  outline: none;
  color: #176BA9;
}
img {
  vertical-align: middle;
  border-style: none;
}
body {
  font-family: 'Noto Sans JP', sans-serif !important;
  font-size: 15px;
  background: #fff;
  color: #373737;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  -webkit-font-smoothing: antialiased;
}
.word-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.d-none {
  display: none;
}
.d-hidden {
  visibility: hidden;
}
.sp-break {
  @media (max-width: 520px) {
    display: block;
  }
}
.color--black {
  color: #373737;
}
.color--blue {
  color: #176BA9;
}
.justify-content-center {
  justify-content: center !important;
  display: flex;
}
.main-body {
  position: relative;
  background-color: #f9f9f9;
  min-height: calc(100vh - 60px);
  .main-content {
    padding-top: 56px;
  }
}
.top-cover {
  .cover-image {
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #fff;
    background-image: asset-url('crew_pc_cover.jpg');
    height: 425px;
    background-position: center;
    @media (max-width: 991px) {
      height: 385px;
    }
    @media (max-width: 520px) {
      background-position: left;
      position: relative;
      height: 650px;
    }
    .cover-image-content {
      width: 100%;
      position: absolute;
      text-align: center;
      top: 54px;
      height: 425px;
      justify-content: center !important;
      @media (max-width: 991px) {
        height: 385px;
      }
      @media (max-width: 520px) {
        position: initial;
        height: 350px;
      }
     .cover-image-main {
        background-image: asset-url('crew_cover_main.png');
        display: block;
        position: relative;
        height: 255px;
        width: 255px;
        background-repeat: no-repeat;
        background-size: cover;
        margin-left: auto;
        margin-right: auto;
        margin-top: 85px;
        z-index: 100;
        @media (max-width: 991px) {
          margin-right: initial;
          height: 285px;
          width: 285px;
          margin-top: 50px;
        }
        @media (max-width: 520px) {
          margin-right: auto;
          height: 260px;
          width: 260px;
        }
        @media (max-width: 320px) {
          height: 245px;
          width: 245px;
        }
      }
      .cover-image-right {
        background-image: asset-url('crew_cover_right.png');
        display: block;
        height: 426px;
        width: 426px;
        background-repeat: no-repeat;
        background-size: cover;
        float: right;
        position: relative;
        top: -345px;
        z-index: 99;
        @media (max-width: 991px) {
          display: none;
        }
      }
    }
  }
  .cover-data {
    position: absolute;
    width: 100%;
    top: 56px;
    @media (max-width: 520px) {
      top: 380px;
    }
  }
  .cover-content {
    position: relative;
    z-index: 200;
    @media (max-width: 520px) {
      position: initial;
      padding-bottom: 0px;
    }
    @media (min-width: 521px) {
      width: 55%;
      padding-left: 15px;
      padding-right: 15px;
    }
    @media (min-width: 1000px) {
      width: 40%;
    }
    @media (min-width: 1200px) {
      width: 33.33333333%;
    }
    .sp-down {
      display: none;
      @media (max-width: 520px) {
        padding-top: 10px;
        font-size: 33px;
        color: #C2C2C2;
        display: block;
        text-align: center;
      }
    }
    .page-intro {
      width: 100%;
      margin: unset;
      color: #fff;
      padding-bottom: 0;
      text-align: center;
      @media (max-width: 520) {
        margin: 0 auto;
        padding-bottom: 0;
        text-align: center;
        color: #fff;
      }
      h2.intro {
        font-size: 44px;
        font-weight: 900;
        margin-bottom: 15px;
        margin-top: 35px;
        line-height: 1.9;
        background: transparent;
        text-align: left;
        @media (max-width: 700px) {
          font-size: 33px;
        }
        @media (max-width: 520px) {
          margin-top: 0;
          padding-top: 20px;
          text-align: center;
          display: block;
          font-size: 28px;
        }
      }
      .highlight-content {
        display: inline-block;
      }
      .highlight::before {
        display: block;
        content: "・";
        color: #E7A3AC;
        font-weight: 800;
        line-height: 0px;
        margin-bottom: -5px;
      }
      .highlight-2::before {
        margin-left: -7px;
      }
      .sub-intro {
        text-align: left;
        @media (max-width: 520px) {
          text-align: center;
        }
      }
      .intro-btn {
        @media (max-width: 520px) {
          padding-left: 20px;
          padding-right: 20px;
        }
      }
      @media (max-width: 520px) {
        .intro-btn-left {
          padding-left: 20px;
          padding-right: 7px;
        }
        .intro-btn-right {
          padding-right: 20px;
          padding-left: 7px;
        }
      }
      .button {
        background: #176ba9;
        border: 1px solid #176ba9;
        box-shadow: none;
        white-space: nowrap;
        width: 100%;
        color: #fff;
        font-size: 17px;;
        font-weight: 700;
        line-height: 48px;
        height: 50px;
        transition: all .25s ease;
        justify-content: center !important;
        display: flex;
        text-align: center;
        vertical-align: middle;
        user-select: none;
        text-decoration: none;
        border-radius: 4px;
        &.register {
          background: #FFFFFF;
          border: 1px solid #176BA9;
          color: #176BA9;
        }
      }
      p {
        font-weight: 400;
        line-height: 22px;
        color: #000;
        margin-bottom: 0
      }
      .first-child {
        margin-top: 20px;
      }
    }
  }
}

.jobs-content {
  padding: 48px 0;
  background: #fff;
  @media (max-width: 520px) {
    padding: 10px 0 24px 0;
  }
  h2.title {
    font-size: 20px;
    text-align: center;
    margin-bottom: 35px;
    margin-top: 1rem;
    line-height: 1.2;
    font-weight: 700;
    @media (max-width: 520px) {
      text-align: left;
      margin-bottom: 0;
    }
  }
}
.signup-btn {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  .btn-data {
    @media (min-width: 521px){
      padding: 0;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
      width: 41.66666667%;
    }
    @media (max-width: 520px){
      width: 100%;
      padding-left: 20px;
      padding-right: 20px;
    }
    .btn {
      background: #176ba9;
      border: 1px solid #176ba9;
      box-shadow: none;
      white-space: nowrap;
      width: 100%;
      color: #fff;
      font-weight: 700;
      line-height: 24px;
      font-size: 17px;
      height: 40px;
      transition: all .25s ease;
      justify-content: center !important;
      display: flex;
      text-align: center;
      vertical-align: middle;
      user-select: none;
      text-decoration: none;
    }
  }
}
.main-middle {
  padding: 48px 0;
  @media (max-width: 520px) {
    padding: 24px 0 48px 0;
  }
  background: #FFFFFF;
  h2.title {
    font-size: 20px;
    text-align: center;
    margin-bottom: 35px;
    margin-top: 1rem;
    line-height: 1.2;
    font-weight: 700;
    @media (max-width: 520px) {
      text-align: left;
      margin-bottom: 0;
    }
  }
  .middle-item {
    padding: 15px;
    @media (min-width: 781px) {
      width: 33.3333%;
      float: left;
    }
  }
  .item {
    padding-top: 24px;
    border-radius: 10px;
    border: 1px solid #176ba9;
    img.item-image {
      height: 165px;
      min-height: 165px;
      margin-bottom: 30px;
      margin-left: auto;
      margin-right: auto;
      display: block;
      max-width: 100%;
    }
    h2.item-header {
      font-size: 20px;
      text-align: center;
      font-weight: 700;
      line-height: 1.2;
      margin-top: 0;
      margin-bottom: 15px;
      color: #176BA9;
    }
    .item-content {
      padding-left: 15px;
      padding-right: 15px;
      min-height: 102px;
      @media (max-width: 780px) {
        text-align: center;
      }
    }
    p {
      font-size: 16px;
    }
  }
}
header {
  z-index: 300;
  height: 56px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 10px 0;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  align-items: center;
  display: flex;
  .main-header {
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    @media (min-width: 768px){
      width: 750px;
    }
    @media (min-width: 992px) {
      width: 970px;
    }
    @media (min-width: 1200px) {
      width: 1170px;
    }
  }
  .main-header-content {
    width: 100%;
    align-items: center;
    justify-content: space-between;
    display: flex;
  }
  .main-header-left, .main-header-right {
    display: flex;
  }
  .btn-register, .btn-login {
    display: inline-block;
    transition: all .25s ease;
    box-shadow: none;
    min-width: unset;
    padding: 0 10px;
    border-radius: 5px;
    white-space: nowrap;
    height: 32px;
    line-height: 28px;
    font-size: 11px;
    font-weight: 500;
  }
  .btn-register {
    margin-right: 5px;
    background: #176ba9;
    border: 1px solid #176ba9;
    color: #fff;
  }
  .btn-login {
    background: #fff;
    border: 1px solid #176ba9;
    color: #176ba9;
  }
}
// Footer
footer.main-footer {
  height: 134px;
  background-color: #fff;
  position: relative;
  border-top: 1px solid #f0f0f0;
  padding: 11px 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: auto;
  padding: 40px 0 24px;
  margin-bottom: 5px;
  ul.main-footer {
    list-style: none;
    padding: 0;
    font-size: 12px;
    li.main-footer__list-item {
      display: inline-block;
      &:not(:last-child) a {
        border-right: 1px solid #5B5B5B;
      }
      a.main-footer__link {
        color: #5B5B5B;
        transition: all .2s ease;
        padding: 0 20px;
        font-weight: 400;
      }
    }
    &.pc {
      @media (max-width: 580px) {
        display: none;
      }
    }
    &.sp {
      @media (min-width: 581px) {
        display: none;
      }
    }
  }
}
.header-logo {
  img {
    object-fit: cover;
    width: 96%;
    max-width: 260px;
    @media (max-width: 400px) {
      max-width: 215px;
    }
    @media (max-width: 380px) {
      max-width: 195px;
    }
    @media (max-width: 290px) {
      max-width: 110px;
    }
  }
}
.footer-logo {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  margin-bottom: 5px;
  margin-top: 5px;
  img {
    object-fit: cover;
    max-width: 260px;
  }
}
.st-similar {
  font-size: 14px;
  font-family: "Hiragino Kaku Gothic Pro", "ヒラギノ角ゴ ProN W3", "Meiryo", "メイリオ", "ＭＳ Ｐゴシック", "Lucida Grande", "sans-serif";
  color: #3c3c3c;
  position: relative;
  flex-wrap: wrap;
  width: 100%;
  .st-similar__item {
    color: #3c3c3c;
    flex: 0 0 50%;
    max-width: 50%;
    position: relative;
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 30px;
  }
  .st-similar__header {
    font-size: 18px;
    font-weight: 600;
    margin: 50px 0 20px;
    @media (max-width: 767px) {
      margin: 15px 0 20px;
      font-size: 16px;
    }
  }
  .st-similar__wrap {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(26, 26, 26, 0.1);
    overflow: hidden;
  }
  .st-similar__top {
    background-image: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), asset-url("bg_img.png");
    &.lawson {
      background-image: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), asset-url("bg_img.png");
    }
    &.lawsonStore100 {
      background-image: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), asset-url("lawson_store.jpg");
    }
    &.natureLawson {
      background-image: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%), asset-url("nature_lawson_bg.jpg");
    }
    background-size: cover;
    background-repeat: no-repeat;
    padding-left: 15px;
    .clearfix {
      min-height: 110px;
      @media (max-width: 375px) {
        min-height: 60px;
      }
    }
    @media (max-width: 414px) {
      padding-left: 10px;
    }
  }
  .st-similar__status {
    background-color: #d3f9fb;
    border-radius: 4px;
    height: 26px;
    padding: 0 8px;
    line-height: 24px;
    margin: 3px 3px 80px 0;
    float: right;
    @media (max-width: 1280px) {
      margin: 3px 3px 60px 0;
      background-size: contain;
    }
    @media (max-width: 767px) {
      margin: 3px 3px 40px 0;
    }
    @media (max-width: 375px) {
      margin: 3px 3px 30px 0;
    }
    i {
      color: #4CB1CE;
    }
  }
  .st-similar__status--deadline {
    background-color: #e2e2e2;
    i {
      color: #a1a1a1;
    }
  }
  .st-similar__status--adjustable {
    float: left;
    color: red
  }
  .st-similar__status--regular {
    float: left;
    color: teal;
  }
  .st-similar__middle {
    padding: 0 15px;
    @media (max-width: 414px) {
      padding: 0 10px;
    }
  }
  .st-similar__title, .st-similar__distance {
    font-weight: 300;
    font-size: 14px;
    line-height: 20px;
    @media (max-width: 767px) {
      font-size: 12px;
    }
  }
  .st-similar__title {
    margin: 25px 0 15px;
    @media (max-width: 767px) {
      margin: 16px 0 5px;
    }
    @media (max-width: 375px) {
      margin: 12px 0 5px;
    }
  }
  .st-similar__date, .st-similar__time {
    display: block;
    font-weight: 300;
    @media (max-width: 767px) {
      font-size: 12px;
    }
    i {
      color: #4CB1CE;
      margin-right: 10px;
    }
  }
  .st-similar__time {
    margin-top: 5px;
    font-weight: 600;
  }
  .st-similar__distance i, .st-similar__bottom i {
    color: #a1a1a1;
    margin-right: 10px;
  }
  .st-similar__break {
    display: block;
    font-weight: 600;
    margin-top: 5px;
    @media (max-width: 767px) {
      font-size: 12px;
    }
    i {
      color: #d7b44b;
      margin-right: 3px;
    }
  }
  .st-similar__bookmark {
    color: #a1a1a1;
    padding-right: 10px;
    font-size: 20px;
    cursor: pointer;
    position: relative;
    top: 0;
    left: 0;
    @media (max-width: 414px) {
      padding-right: 5px;
    }
    @media (max-width: 320px) {
      font-size: 18px;
    }
    &.active {
      transition: all .2s ease;
      color: #f2b17a;
      font-weight: 600;
    }
    &.active-animate {
      transition: all .2s ease;
      color: #f2b17a;
      font-weight: 600;
      animation: zoomBookmark .3s forwards;
    }
  }
  .st-similar__bottom {
    padding: 10px 0 15px;
    align-items: center;
    display: flex;
    justify-content: space-between;
    @media (max-width: 1280px) {
      padding: 8px 0;
    }
    @media (max-width: 767px) {
      padding: 8px 0 10px;
    }
  }
  .fee-flag {
    min-height: 42px;
  }
  .st-work__tag {
    position: relative;
    background-color: #4CB1CE;
    color: #fff;
    font-size: 12px;
    padding: 0 10px;
    line-height: 20px;
    display: block;
    @media (max-width: 414px) {
      padding: 0 5px;
      margin-right: 5px;
    }
    @media (max-width: 320px) {
      font-size: 10px;
      padding: 0 3px;
    }
    &::after {
      position: absolute;
      content: '';
      line-height: 20px;
      height: 20px;
      width: 2px;
      top: 0;
      right: -1px;
      display: block;
      background: asset-url("img_tagprice_2.png") top right;
    }
  }
  .st-work__tag--two-edge {
    margin-left: 20px;
    &::before {
      position: absolute;
      content: '';
      line-height: 20px;
      height: 20px;
      width: 2px;
      top: 0;
      left: -1px;
      display: block;
      background: asset-url("img_tagprice.png") top left;
    }
  }
  .st-work__tag--gray{
    background-color: #a1a1a1;
    margin-left: 10px;
    @media (max-width: 320px) {
      margin-left: 0;
    }
    &::before {
      position: absolute;
      content: '';
      line-height: 20px;
      height: 20px;
      width: 2px;
      top: 0;
      left: -1px;
      display: block;
      background: asset-url("img_changetime.png") top left;
    }
    &::after {
      background: asset-url("img_changetime.png") top right;
    }
  }
  .keep-job-btn {
    visibility: hidden;
  }
}
.owl-carousel {
  display: none;
  width: 100%;
  -webkit-tap-highlight-color: transparent;
  position: relative;
  z-index: 1;
}
.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
  touch-action: manipulation;
  backface-visibility: hidden;
  -moz-backface-visibility: hidden;
}
.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}
.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  /* fix for flashing background */
  transform: translate3d(0px, 0px, 0px);
  -webkit-transform: translate3d(0px, 0px, 0px);
}
.owl-carousel .owl-wrapper,
.owl-carousel .owl-item {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
}
.owl-carousel .owl-item {
  position: relative;
  min-height: 1px;
  float: left;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}
.owl-carousel .owl-item img {
  display: block;
  width: 100%;
}
.owl-carousel .owl-nav.disabled,
.owl-carousel .owl-dots.disabled {
  display: none;
}
.owl-carousel .owl-nav .owl-prev, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-dot {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.owl-carousel .owl-nav button.owl-prev, .owl-carousel .owl-nav button.owl-next, .owl-carousel button.owl-dot {
  background: none;
  color: inherit;
  border: none;
  padding: 0 !important;
  font: inherit;
}
.owl-carousel.owl-loaded {
  display: block;
}
.owl-carousel.owl-loading {
  opacity: 0;
  display: block;
}
.owl-carousel.owl-hidden {
  opacity: 0;
}
.owl-carousel.owl-refresh .owl-item {
  visibility: hidden;
}
.owl-carousel.owl-drag .owl-item {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.owl-carousel.owl-grab {
  cursor: move;
  cursor: -webkit-grab;
  cursor: grab;
}
.owl-carousel.owl-rtl {
  direction: rtl;
}
.owl-carousel.owl-rtl .owl-item {
  float: right;
}
.no-js .owl-carousel {
  display: block;
}
.owl-carousel .animated {
  animation-duration: 1000ms;
  animation-fill-mode: both;
}
.owl-carousel .owl-animated-in {
  z-index: 0;
}
.owl-carousel .owl-animated-out {
  z-index: 1;
}
.owl-carousel .fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.owl-height {
  transition: height 500ms ease-in-out;
}
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity 400ms ease;
}
.owl-carousel .owl-item .owl-lazy[src^=""], .owl-carousel .owl-item .owl-lazy:not([src]) {
  max-height: 0;
}
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d;
}
.owl-theme .owl-nav {
  margin-top: 10px;
  text-align: center;
  -webkit-tap-highlight-color: transparent;
}
.owl-theme .owl-nav [class*='owl-'] {
  color: #FFF;
  font-size: 14px;
  margin: 5px;
  padding: 4px 7px;
  background: #D6D6D6;
  display: inline-block;
  cursor: pointer;
  border-radius: 3px;
}
.owl-theme .owl-nav [class*='owl-']:hover {
  background: #869791;
  color: #FFF;
  text-decoration: none;
}
.owl-theme .owl-nav .disabled {
  opacity: 0.5;
  cursor: default;
}
.owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 10px;
}
.owl-theme .owl-dots {
  text-align: center;
  -webkit-tap-highlight-color: transparent;
}
.owl-theme .owl-dots .owl-dot {
  display: inline-block;
  zoom: 1;
  *display: inline;
}
.owl-theme .owl-dots .owl-dot span {
  width: 10px;
  height: 10px;
  margin: 5px 7px;
  background: #D6D6D6;
  display: block;
  backface-visibility: visible;
  -webkit-backface-visibility: visible;
  transition: opacity 200ms ease;
  border-radius: 30px;
}
.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
  background: #869791;
}

.owl-carousel .owl-nav button.owl-prev, .owl-carousel .owl-nav button.owl-next {
  height: 44px;
  width: 40px;
  background: rgba(0,0,0,0.4);
  border-radius: 2px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  margin: 0;
  &.disabled {
    visibility: hidden;
  }
  &.owl-next {
    right: 0;
  }
  &.owl-prev {
    left: 0;
  }
}
.main-footer__copyright {
  text-align: center;
  font-size: 15px;
  line-height: 21px;
  color: #888888;
  margin-top: 5px;
  font-weight: 400;
}
.total_applied {
  visibility: hidden;
}

.search-content {
  padding: 48px 0;
  background-color: #FDF8F8;
  .title-search {
    text-align: center;
    font-size: 20px;
    line-height: 24px;
    font-weight: 700;
    margin-bottom: 30px;
    img {
      position: relative;
      margin-left: 20px;
      top: 10px;
    }
  }
  @media (max-width: 520px) {
    padding: 35px 0;
    .title-search {
      text-align: left;
      padding: 0 20px;
      img {
        margin-left: 0;
        float: right;
        position: relative;
        top: 0;
      }
    }
  }
}

.dropdown {
  .dropdown-content {
    display: none;
    position: absolute;
    background-color: #FFFFFF;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
    a {
      color: #176BA9;
      padding: 12px 21px;
      text-decoration: none;
      display: block;
      &:hover{
        background-color: #C2C2C2;
        color: #176BA9;
      }
    }
  }
  .dropbtn {
    background: #FFFFFF;
    border: 1px solid #176BA9;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 700;
    line-height: 16px;
    padding: 0 15px;
    min-width: 335px;
    .prefecture-name {
      position: relative;
      top: 16px;
      float: left
    }
    .btn-inline {
      float: right;
      .select-arrow {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 0px;
        position: relative;
        width: 50px;
        height: 50px;
        right: -15px;
        top: 0px;
        background: #176BA9;
        border-radius: 0px 8px 8px 0px;
        span {
          color: #FFFFFF;
          font-size: 30px;
          font-weight: 800;
        }
      }
    }
  }
  &.selected {
    color: #176BA9;
  }
  &.hovering {
    color: #C2C2C2;
  }
  &.active {
    .dropdown-content {
      display: block;
      min-width: 334px;
      text-align: left;
    }
    .dropbtn {
      background-color: #FFFFFF;
    }
  }
}
