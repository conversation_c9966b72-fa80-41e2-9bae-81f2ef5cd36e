:root {
  --soft-pink: #F0F0F0;
  --hard-pink: #F9F9F9;
  --soft-grey: #C4C4C4;
  --super-soft-grey: #E9E9E9;
  --hard-grey: #888888;
  --light-grey: #E5E5E5;
  --soft-green: #A0F1CD;
  --hard-green: #00B7AF;
  --leaf-green: #77C47C;
  --normal-blue: #4A90E2;
  --flat-blue: #0a76be;
  --blue: #4A90E2;
  --soft-blue: #4A90E2;
  --purple: #BD9FEE;
  --black: #000000;
  --white: #FFFFFF;
  --hard-black: #373737;
  --sort-black: #B5B5B5;
  --sort-black-rgba: rgba(0, 0, 0, 0.6);
  --soft-red: #F05151;
  --soft-orange: #FF6B00;
  --flat-gray: #ecf0f5;
  --soft-yellow: #fefeb6;
}
@font-face {
  font-family: 'customicons';
  src: asset-url('customicons.woff') format('woff'), asset-url('customicons.ttf')  format('truetype'), asset-url('customicons.svg#icomoon2') format('svg');
  font-weight: normal;
  font-style: normal;
}
#new-batch-arrange-index {
  padding-left: 20px;
  padding-right: 20px;
  font-family: "Noto Sans JP", "Barlow", "ヒラギノ角ゴ Pro W3", "Hiragino Kaku Gothic Pro", "Osaka", "メイリオ", "Meiryo", "ＭＳ Ｐゴシック", "MS PGothic", sans-serif !important;
  .icon {
    font-family: 'customicons'!important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .icon-coin-yen:before {
    content: "\e93e";
  }
  .icon-phone:before {
    content: "\e942";
  }
  .icon-user:before {
    content: "\e971";
  }
  .icon-spinner:before {
    content: "\e97a";
  }
  .icon-spinner6:before {
    content: "\e97f";
  }
  .icon-search:before {
    content: "\e986";
  }
  .icon-lock:before {
    content: "\e98f";
  }
  .icon-bin:before {
    content: "\e9ac";
  }
  .icon-bookmark:before {
    content: "\e9d2";
  }
  .icon-bookmarks:before {
    content: "\e9d3";
  }
  .icon-star-empty:before {
    content: "\e9d7";
  }
  .icon-star-half:before {
    content: "\e9d8";
  }
  .icon-star-full:before {
    content: "\e9d9";
  }
  .icon-heart:before {
    content: "\e9da";
  }
  .icon-plus:before {
    content: "\ea0a";
  }
  .icon-cross:before {
    content: "\ea0f";
  }
  .icon-checkmark:before {
    content: "\ea10";
  }
  .icon-checkmark2:before {
    content: "\ea11";
  }
  .icon-loop2:before {
    content: "\ea2e";
  }
  .icon-checkbox-checked:before {
    content: "\ea52";
  }
  .icon-checkbox-unchecked:before {
    content: "\ea53";
  }
  .icon-bell:before {
    content: "\e900";
  }
  .icon-checked-round:before {
    content: "\e903";
  }
  .icon-clock:before {
    content: "\e904";
  }
  .icon-close-round:before {
    content: "\e905";
  }
  .icon-coffee:before {
    content: "\e906";
  }
  .icon-email:before {
    content: "\e907";
  }
  .icon-list:before {
    content: "\e909";
  }
  .icon-page:before {
    content: "\e90a";
  }
  .icon-people:before {
    content: "\e90b";
  }
  .icon-phone1:before {
    content: "\e90c";
  }
  .icon-refresh:before {
    content: "\e90d";
  }
  .icon-search1:before {
    content: "\e90e";
    color: #979797;
  }
  .icon-shop:before {
    content: "\e90f";
  }
  .icon-unchecked:before {
    content: "\e910";
    color: var(--sort-black);
  }
  .icon-union:before {
    content: "\e911";
  }
  .icon-yen:before {
    content: "\e912";
  }
  .fas {
    font-family: "FontAwesome"!important;
    font-weight: 900;
  }
  span, p {
    font-family: "Barlow";
  }
  width: 100%;
  float: left;
  .batch-section {
    width: 100%;
    float: left;
    background: var(--soft-pink);
  }
  .haft-width {
    width: 50%;
    padding-left: 1px;
    padding-right: 1px;
    float: left;
  }
  .col-3 {
    width: 33.333%;
    float: left;
  }
  .col-6 {
    width: 66.666%;
    float: left;
  }
  .grid-3 {
    display: grid!important;
    grid-template-areas: "left-2 mid-4 right-3";
    grid-template-columns: 33.33% 33.33% auto;
  }
  .grid-243 {
    display: grid!important;
    grid-template-areas: "left-2 mid-4 right-3";
    grid-template-columns: 22.22% 44.44% auto;
  }
  .grid-442 {
    display: grid!important;
    grid-template-areas: "left-3 mid-3 right-3";
    grid-template-columns: 35% 45% auto;
  }
  .grid-63 {
    display: grid!important;
    grid-template-areas: "left-6 right-3";
    grid-template-columns: 66.66% auto;
  }
  .grid-36 {
    display: grid!important;
    grid-template-areas: "left-3 right-6";
    grid-template-columns: 28.33% auto;
  }
  .grid-12223 {
    display: grid!important;
    grid-template-areas: "box-1 box-2 box-2 box-2 box-3";
    grid-template-columns: 10% 20% 20% 20% auto;
  }
  .grid-row-2 {
    display: grid!important;
    grid-template-areas: "top-1" "bot-1";
    grid-template-rows: min-content auto;
  }
  .font-barlow {
    font-family: "Barlow"!important;
  }
  .font-noto-san {
    font-family: "Noto Sans JP"!important;
  }
  .pd-left-7 {
    padding: 2px;
    padding-left: 7px;
  }
  .radius-5 {
    border-radius: 5px 5px 0 0!important;
  }
  .bold-text {
    font-weight: bold;
    color: var(--black);
  }
  .thin-text {
    font-weight: 200;
  }
  .weight-600 {
    font-weight: 600;
  }
  .weight-nomal {
    font-weight: normal;
  }
  .big-text {
    font-size: 17px;
    line-height: 22px;
  }
  .medium-text {
    font-size: 15px;
  }
  .text-size-14 {
    font-size: 12px;
    line-height: 17px;
  }
  .normal-text {
    font-size: 13px;
    line-height: 17px;
  }
  .small-text {
    font-size: 12px;
  }
  .extra-small-text {
    font-size: 10px;
    line-height: 12px;
  }
  .super-small-text {
    font-size: 7px;
    line-height: 12px;
  }
  .color-gray {
    color: var(--sort-black-rgba);
  }
  .width-60-percent {
    width: 60%;
    float: left;
    margin-right: 0;
    padding: 7px 10px 0 10px;
    padding-right: 0px;
  }
  .width-45-percent {
    padding-top: 9px;
    width: 45%;
    float: left;
    padding: 7px 10px 0 10px;
  }
  .width-40-percent {
    padding-top: 9px;
    width: 40%;
    float: left;
    margin-right: 0;
    padding: 7px 10px 0 10px;
    padding-right: 0px;
  }
  .width-30-percent {
    width: 30%;
    float: left;
    margin-right: 0;
    padding: 7px 10px 0 10px;
    padding-right: 0px;
  }
  .width-25-percent {
    width: 25%;
    float: left;
    margin-right: 0;
    padding: 7px 10px 0 10px;
    padding-right: 0px;
  }
  .width-20-percent {
    width: 20%;
    float: left;
    margin-right: 0;
    padding: 7px 10px 0 10px;
  }
  .width-15-percent {
    width: 15%;
    float: left;
    margin-right: 0;
    padding: 7px 10px 0 10px;
  }
  .width-10-percent {
    width: 10%;
    float: left;
    margin-right: 0;
    padding: 7px 10px 0 10px;
  }
  .no-pd-top {
    padding-top: 0px;
  }
  .invisible {
    visibility: hidden;
    pointer-events: none;
  }
  .hidden {
    display: none;
  }
  .btns-group {
    button {
      float: right;
      padding: 10px;
      font-weight: bold;
      font-size: 14px;
      line-height: 20px;
      margin-right: 5px;
      margin-left: 5px;
      border-radius: 3px;
      box-shadow: none;
      border: none;
      span {
        margin-right: 10px;
      }
    }
    .btn-white {
      background: var(--white);
      border: 1px solid var(--normal-blue);
      color: var(--normal-blue)
    }
    .btn-blue {
      color: var(--white);
      background: var(--normal-blue);
    }
    .btn-gray {
      background: #C9E4FF;
      color: var(--hard-black);
    }
    .inactive {
      opacity: 0.65;
      cursor: not-allowed;
      pointer-events: none;
    }
  }
  .round-tag {
    margin-bottom: 10px;
    float: left;
    text-align: center;
    padding: 5px 10px;
    border: 1px solid var(--sort-black);
    box-sizing: border-box;
    border-radius: 20px;
    background: var(--white);
    margin-left: 2%;
    margin-right: 2%;
  }
  #search-header {
    width: 100%;
    #date-section {
      padding: 5px;
      margin-bottom: 5px;
      span {
        font-size: 14px;
      }
      .report-block {
        padding-top: 10px;
        .icon-refresh {
          padding: 1px;
          border-radius: 2px;
          color: var(--black);
          background: var(--soft-grey);
        }
        .count-report-block {
          font-weight: 300;
          font-size: 16px;
          line-height: 19px;
          padding: 10px;
          border: 3px solid var(--hard-grey);
          border-radius: 3px;
        }
      }
    }
    #search-condition-section {
      padding-top: 8px;
      padding-bottom: 8px;
      float: left;
      width: 100%;
      table {
        width: 100%;
        th {
          width: 20%;
        }
        td {
          width: 20%;
          vertical-align: top;
        }
        button.condition-btn {
          float: left;
          text-align: center;
          padding: 6.5px 20px;
          border: 1px solid var(--sort-black);
          box-sizing: border-box;
          border-radius: 20px;
          background: var(--white);
          &:focus {
            outline: none;
          }
          &:hover {
            background: var(--sort-black);
            span {
              color: var(--white);
            }
          }
          span {
            font-size: 14px;
            line-height: 20px;
            color: var(--hard-black);
          }
          .icon {
            margin-left: 5px;
          }
        }
        .condition-tag {
          margin: 2px;
          display: inline-block;
          float: left;
          .condition-selected-tag {
            font-size: 12px;
            text-align: left;
            padding: 6.5px;
            border: 1px solid var(--sort-black);
            box-sizing: border-box;
            border-radius: 20px;
            background: var(--white);
            display: inline-flex;
            .fas {
              margin-left: 5px;
            }
          }
        }
      }
    }
    #status-section {
      padding-top: 12px;
      padding-bottom: 12px;
      width: 100%;
      float: left;
      background: var(--light-grey);
      padding: 3px 2px;
      border-radius: 5px;
      .status-tab {
        font-family: "Barlow";
        width: 25%;
        float: left;
        text-align: center;
        height: 40px;
        border-radius: 5px;
        padding-top: 11px;
        span {
          color: var(--hard-black);
          display: inline-block;
        }
        &.active-tab {
          background: var(--hard-grey);
          span {
            color: var(--white);
          }
        }
      }
    }
    #advance-search-conditions {
      padding-top: 15px;
      width: 100%;
      float: left;
      .conditions {
        display: inline-flex;
        .condition {
          padding-left: 10px;
          padding-right: 10px;
        }
      }
    }
    .tab-info {
      width: 100%;
      float: left;
      padding-top: 10px;
      padding-bottom: 28px;
      padding-left: 4px;
      .big-text {
        margin-right: 40px;
      }
      .tab {
        display: inline;
      }
      #search-batch-arrangements {
        background: var(--normal-blue);
        text-align: center;
        padding: 7px 35px;
        float: right;
        border: none;
        box-shadow: none;
        border-radius: 5px;
        span {
          font-weight: 500;
          color: var(--white);
          margin: 3px;
          &.icon-search1:before {
            color: var(--white);
          }
        }
      }
    }
    #search-input-section {
      width: 100%;
      float: left;
      .input-custom-field {
        width: 100%;
        border-radius: 10px;
        background: var(--white);
        min-height: 43px;
        padding-top: 5px;
        padding-bottom: 5px;
        border: 1px solid var(--hard-grey);
        box-sizing: border-box;
        border-radius: 10px;
        display: inline-block;
        .input-label {
          width: 20%;
          float: left;
          padding-top: 6px;
          padding-left: 10px;
          display: inline-block;
          .icon {
            margin-left: 5px;
          }
        }
        .tags-select {
          width: 80%;
          float: left;
          .select2-selection--multiple {
            border: none;
            display: flex;
            flex-wrap: wrap;
          }
          .select2-selection__rendered {
            padding: 0 3px;
            width: 98%;
          }
          .select2-selection__choice {
            background: var(--white);
            color: var(--black);
            border-color: var(--hard-grey);
            margin-top: 0px;
            height: 31px;
            font-size: 15px;
            font-weight: bold;
            line-height: 22px;
            padding-top: 3px;
          }
          .select2-selection__choice__remove {
            float: right;
            color: var(--hard-grey);
            margin-left: 6px;
            margin-right: 0px;
            font-size: 22px;
          }
        }
      }
    }
  }
  .search-actions {
    margin-top: 20px;
    right: 0px;
    width: 100%;
    padding: 9px;
    background: var(--white);
    border: 0.5px solid black;
    box-sizing: border-box;
    float: left;
    .total {
      background: var(--soft-grey);
      border-radius: 21.5px 0 0 21.5px;
      padding: 15px;
      .number {
        font-weight: 600;
        font-size: 27px;
        line-height: 32px;
        position: relative;
        bottom: -4px;
        margin-right: 10px;
      }
      .evaluation {
        font-weight: 600;
        font-size: 15px;
        line-height: 18px;
      }
    }
  }
  .batch-arrange-popup {
    text-align: center;
    .modal-close {
      float: right;
      font-size: 20px;
      font-weight: normal;
      position: relative;
      z-index: 100000;
      cursor: pointer;
      bottom: 20px;
    }
    .modal-content {
      border-radius: 10px;
      padding: 33px 58px;
    }
    .modal-header {
      border-bottom: 1px solid var(--sort-black);
      .popup-title {
        font-weight: 600;
        font-size: 17px;
        line-height: 20px;
        color: var(--hard-black);
      }
      .sub-title {
        padding: 20px;
        font-weight: 200;
        font-size: 12px;
        line-height: 14px;
      }
    }
    .modal-body {
      text-align: left;
      padding-top: 40px;
      padding-bottom: 40px;
      width: 100%;
      height: auto;
      float: left;
      .data-form {
        width: 100%;
        float: left;
      }
      .width-20-percent {
        padding: 0px;
        .line {
          margin-bottom: 30px;
        }
        h5 {
          margin: 0;
          font-weight: 600;
          font-size: 11px;
          line-height: 20px;
          color: black;
        }
      }
      .condition-errors {
        .conditions {
          padding: 5px;
          color: red;
          border: red solid;
          border-radius: 14px;
          ul {
            margin: 10px;
            padding-inline-start: 10px;
            li {
              margin-bottom: 5px;
            }
          }
        }
      }
    }
    .modal-footer {
      text-align: center;
      button {
        float: none;
      }
    }
  }
  .search-by-locations {
    height: 300px;
  }
  .input-checkbox-custom {
    position: relative;
    margin-bottom: 8px;
    height: 22px;
    input[type='checkbox'] {
      position: absolute;
      visibility: hidden;
    }
    label {
      padding-left: 25px;
      margin-bottom: 0;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      color: #1a1a1a;
      position: relative;
      &.ellipsis {
        position: initial;
      }
      &::before {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        background-color: var(--white);
        border: 1px solid var(--normal-blue);
        top: -2px;
        left: 0;
        border-radius: 3px;
      }
      &::after {
        content: '\ea10';
        font-family: 'customicons';
        font-weight: 600;
        position: absolute;
        background-color: var(--white);
        color: var(--flat-blue);
        font-style: normal;
        position: absolute;
        font-size: 13px;
        opacity: 0;
        top: -1px;
        left: 3px;
      }
      span {
        font-weight: 600;
        font-size: 11px;
        line-height: 20px;
        color: var(--hard-grey);
        position: relative;
        bottom: 5px;
      }
    }
    input[type='checkbox']:checked ~ label::before {
    }
    input[type='checkbox']:checked ~ label::after {
      opacity: 1;
    }
  }
  .input-custom-search {
    width: 100%;
    border-radius: 10px;
    background: var(--white);
    height: 43px;
    padding-top: 5px;
    padding-bottom: 5px;
    border: 1px solid var(--hard-grey);
    box-sizing: border-box;
    border-radius: 10px;
    display: inline-block;
    .input-label {
      width: 30px;
      float: left;
      padding-top: 6px;
      padding-left: 10px;
      display: inline-block;
    }
    .tags-select {
      width: calc(100% - 30px);
      float: left;
      text-align: center;
      .select2-selection--multiple {
        display: flex;
        height: 30px;
        border: none;
        flex-wrap: wrap;
        &::after {
          content: '\ea10';
          font-family: 'customicons';
          font-weight: 600;
          position: absolute;
          background-color: #fff;
          color: var(--flat-blue);
          font-style: normal;
          position: absolute;
          font-size: 13px;
          opacity: 0;
          top: -1px;
          left: 3px;
        }
      }
      .select2-selection__rendered {
        padding: 0 3px;
        width: 98%;
        margin-top: 42px;
        display: inline-table;
        text-align: center;
        float: left;
        input.select2-search__field {
          position: absolute;
          left: 0;
          top: 0;
        }
      }
      .select2-selection__choice {
        background: var(--white);
        font-weight: bold;
        font-size: 13px;
        line-height: 19px;
        padding: 5px 10px;
        color: var(--hard-grey);
        border: 1px solid var(--sort-black);
        box-sizing: border-box;
        border-radius: 20px;
        border-color: var(--hard-grey);
      }
      .select2-selection__choice__remove {
        float: right;
        color: var(--hard-grey);
        margin-left: 6px;
        margin-right: 0px;
        font-size: 22px;
      }
    }
  }
  #search-staff-condition {
    .select-section {
      margin-bottom: 10px;
      .form-label {
        font-weight: 600;
        font-size: 12px;
        line-height: 14px;
      }
    }
    #cars {
      width: 100%;
      height: 32px;
      border: 1px solid var(--hard-grey);
      float: left;
      border-radius: 10px;
    }
    .input-custom-field {
      width: 100%;
      border: 1px solid var(--hard-grey);
      float: left;
      border-radius: 10px;
      .input-label {
        float: left;
        width: 20px;
        position: relative;
        top: 8px;
        left: 5px;
        span {
          color: var(--hard-grey);
        }
      }
      .tags-select {
        width: calc(100% - 30px);
        float: left;
        .select2-selection--multiple {
          border: none;
        }
      }
    }
   .mini-title {
      font-weight: 600;
      font-size: 10px;
      line-height: 12px;
    }
    .result-section {
      margin-top: 30px;
      .hightlight-section {
        background: var(--light-grey);
        padding: 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 17px;
      }
    }
    .action-section {
      margin-top: 20px;
      .light-btn {
        display: inline-flex;
        padding: 12px;
        border: 1px solid var(--sort-black);
        box-sizing: border-box;
        border-radius: 3px;
        font-weight: 600;
        font-size: 16px;
        line-height: 19px;
        span {
          margin-left: 10px;
          margin-right: 10px;
        }
        .icon {
          margin: 0px;
          margin-right: 3px;
          color: var(--hard-grey);
        }
      }
    }
    .bottom-btn {
      font-family: "Noto Sans JP";
      font-style: normal;
      font-weight: bold;
      font-size: 13px;
      line-height: 19px;
      width: 140px;
      color: var(--hard-black);

    }
  }
  .total-row {
    span {
      padding: 5px;
      border-radius: 25px;
      border: 3px solid var(--hard-grey);
      font-size: 10px;
      line-height: 18px;
    }
  }
  .ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    &:hover {
      overflow: visible;
      span, p {
        position: relative;
        background: var(--white);
        z-index: 100;
      }
    }
  }
  .diff {
    text-align: center;
    padding-left: 20px;
    padding-right: 20px;
    .hightlight-bg {
      width: 100%;
      background: var(--hard-grey);
      border-radius: 3px;
      display: inline-block;
      z-index: 10;
      position: relative;
      padding-left: 0px;
      padding-right: 0px;
      :before{
        content: '';
        position: absolute;
        width: 50%;
        left: 0;
        height: 100%;
        z-index: -1;
        top: 0;
        border-radius: 3px;
      }
      :after{
        content: '';
        position: absolute;
        width: 50%;
        left: 50%;
        height: 100%;
        z-index: -1;
        top: 0;
        border-radius: 3px;
      }
      &.normal_soon, &.late_soon, &.soon_soon {
        padding-right: 10px;
        :after{
          background: var(--hard-grey);
        }
      }
      &.soon_normal, &.soon_late, &.soon_soon {
        padding-left: 10px;
        :before{
          background: var(--leaf-green);
        }
      }
      &.late_soon, &.late_normal, &.late_late {
        padding-left: 10px;
        :before{
          background: var(--hard-grey);
        }
      }
      &.soon_late, &.normal_late, &.late_late {
        padding-right: 10px;
        :after{
          background: var(--leaf-green);
        }
      }
    }
    .hightlight-tag {
      width: 100%;
      font-size: 14px;
      line-height: 18px;
      color: var(--hard-black);
      border-radius: 3px;
      padding: 5px 7px;
      background: #D0EFD2;
      display: inline-block;
    }
  }
  #search-content-new {
    &.loading {
      height: 600px;
      display: block;
    }
    .no-data {
      width: 100%;
      float: left;
      height: 250px;
      padding-top: 150px;
      padding-bottom: 200px;
    }
    table {
      width: 100%;
      thead {
        border-bottom: 2px solid var(--soft-grey);
      }
      tr {
        width: 100%;
        margin-top: 4px;
        margin-bottom: 4px;
      }
      td {
        background: var(--white);
        border-top: 5px solid var(--flat-gray);
        vertical-align: middle;
        &.not_processed {
          &.special-staff {
            box-shadow: inset 0px 2px red, inset 0px -2px red;
          }
        }
        &.registration {
          background: #95b8db;
        }
        &.temporary_arrange {
          background:  var(--soft-yellow);
          &.special-staff {
            box-shadow: inset 0px 2px red, inset 0px -2px red;
          }
        }
        &.rejected, &.absence, &.cancel_after_arrange_has_insurance, &.cancel_after_arrange_no_insurance, &.absence_has_alternative {
          background: var(--soft-grey);
          &.special-staff {
            box-shadow: inset 0px 2px red, inset 0px -2px red;
          }
        }
      }
      th, td {
        font-family: "Barlow";
        height: 24px;
        padding-bottom: 7px;
        padding-top: 7px;
        text-align: center;
        &.total-row {
          text-align: left;
          padding-left: 7px;
        }
        &.no-record-row {
          vertical-align: center;
          padding-bottom: 7px;
          padding-top: 7px;
          border-radius: 5px;
          text-align: center;
          background: none;
          border-bottom: none;
          .no-record {
            font-size: 13px;
            line-height: 20px;
            vertical-align: center;
            text-align: center;
            color: var(--hard-black);
            height: 100%;
            width: 100%;
            background: var(--white);
            padding-top: 75px;
          }
        }
        &.workingday-row {
          vertical-align: top;
          width: 30%;
          padding-right: 10px;
          background: none;
          padding-top: 0;
          padding-bottom: 0;
          border-bottom: 5px solid var(--flat-gray);
          border-top: 5px solid var(--flat-gray);
        }
        &.select-row {
          width: 5%;
          margin-left: 5px;
          &.special-staff {
            box-shadow: inset 2px 2px red, inset 0px -2px red;
          }
          &.registration {
            &.rejected {
              .select {
                visibility: hidden;
              }
            }
          }
        }
        &.fullname-row {
          width: 15%;
          text-align: left;
        }
        &.determiner-row, &.status-row {
          widows: 10%;
        }
        .job-data {
          text-align: left;
          div {
            background: var(--white);
          }
          border-radius: 5px;
          .status-bar {
            background: none;
            border-bottom: 1px solid var(--soft-pink);
            .job-type, .job-status {
              text-align: center;
              padding: 8px 0;
            }
            .job-type {
              background: var(--soft-green);
              border-radius: 5px 0px 0px 0px;
              min-height: 37px;
              &.bg-white {
                background: var(--white);
              }
              span {
                color: var(--hard-black);
              }
            }
            .job-status {
              span {
                color: var(--soft-red);
              }
            }
            .job-contact {
              padding: 0px;
              padding-top: 6px;
              padding-right: 5px;
              text-align: right;
              border-radius: 0px 5px 0px 0px;
              display: table;
              .table-cell {
                display: table-cell;
                text-align: center;
                padding-bottom: 4px;
                padding-top: 4px;
                &.invisible {
                  visibility: hidden;
                }
              }
              span {
                display: inline-block;
                line-height: 20px;
                font-size: 10px;
                border-radius: 3px;
                padding: 6px;
              }
              .email, .call{
                padding: 6px 7px;
                background: var(--soft-pink);
                span {
                  padding: 0;
                }
              }
              .contact{
                border: 1px solid var(--hard-black);
                box-sizing: border-box;
                padding: 5px 6px;
                border-radius: 4px;
              }
            }
          }
          .location-info {
            border-bottom: 1px solid var(--soft-pink);
            .location-text {
              span {
                font-family: "Barlow";
                font-size: 12px;
                line-height: 14px;
                overflow: hidden;
              }
            }
            .location-name {
              width: 100%;
            }
            .location-store {
              // width: 100%;
              span {
                font-weight: 600;
              }
            }
          }
          .job-info {
            border-radius: 0 0 5px 5px;
            border-bottom: 1px solid var(--soft-pink);
            .line-4-info {
              padding-top: 20px;
              .extra-small-text {
                position: relative;
                bottom: -5px;
              }
              .count-docs-1 {
                width: 22.22%;
                float: left;
              }
              .count-docs-2 {
                width: 44.44%;
                float: left;
                .big-square {
                  padding: 7px 6px ;
                  font-weight: 600;
                  font-size: 15px;
                  line-height: 18px;
                  border: 1px solid var(--sort-black);
                  box-sizing: border-box;
                  border-radius: 3px;
                  position: relative;
                }
              }
              .count-docs-3 {
                width: 33.34%;
                float: right;
                display: inline;
              }
            }
          }
          .additon-info {
            width: 80%;
            border-radius: 0 0 5px 5px;
            padding-bottom: 7px;
            .green-label {
              font-size: 11px;
              line-height: 14px;
              padding: 4px 8px;
              color: var(--white);
              background: var(--hard-green);
              border-radius: 5px;
            }
          }
        }
        .untilstarted {
          background: var(--white);
          padding: 15px;
          font-weight: 600;
          font-size: 15px;
          line-height: 18px;
          color: var(--hard-black);
          border-radius: 0 0 5px 5px;
        }
        .select {
          position: relative;
          .check-box {
            font-size: 24px;
            &.checked::before {
              content: "\e903";
              color: var(--hard-green);
            }
          }
          .like-check-box {
            color: var(--normal-blue);
          }
        }
        .fullname {
          width: 100%;
          text-align: left;
          float: left;
          img {
            width: 31px;
            height: 31px;
            float: left;
            display: block;
          }
          p {
            margin: 0;
          }
          .staff-name {
            width: calc(100% - 52px);
            float: left;
            padding-left: 4px;

            &.not-show-flag {
              position: relative;
              top: 7px;
            }
            .staff-label {
              width: 100%;
              float: left;
              background: var(--soft-pink);
              padding: 3px 5px;
              border-radius: 3px;
              font-weight: 300;
              font-size: 9px;
              line-height: 11px;
              &.label-green {
                margin-left: 3px;
                background: var(--hard-green);
                color: var(--white);
              }
            }
            .staff-basic {
              width: 115px;
              float: left;
              text-align: left;
              font-weight: 600;
              font-size: 12px;
              line-height: 14px;
            }
          }
          .staff-notes {
            width: 20px;
            float: right;
            position: relative;
            top: 5px;
            display: block;
          }
          &--absent-reapplying {
            font-size: 1.5rem;
            margin-top: .75rem;
            color: #F9F5EB;
          }
        }
        .note {
          margin-right: 0;
          display: inline-flex;
          .group-tags {
            min-width: 140px;
            padding-top: 0;
            padding: 4px;
            border-radius: 5px;
            border: 0.4px solid var(--hard-grey);
            box-sizing: border-box;
            margin-right: 10px;
            background: var(--white);
            .tag-1, .tag-2, .tag-3, .tag-4, .tag-5 {
              font-size: 10px;
              line-height: 12px;
              font-weight: normal;
              padding: 4px;
              border: 1px solid var(--hard-grey);
              box-sizing: border-box;
              border-radius: 3px;
              color: var(--black);
              float: left;
              margin-left: 1px;
              margin-right: 1px;
            }
            .tag-1 {
              padding-left: 6px;
              padding-right: 6px;
            }
            .tag-2 {
              border: 1px solid var(--hard-green);
              color: var(--hard-green);
            }
            .tag-3 {
              border: 1px solid var(--hard-green);
              float: right;
            }
            .tag-4 {
              border: 1px solid var(--normal-blue);
            }
            .tag-5 {
              border: 1px solid var(--soft-grey);
            }
          }
        }
        .goingwork-arival {
          text-align: center;
          display: inline-block;
          .checked-icon {
            float: left;
            display: block;
            width: 25px;
            height: 25px;
            border: 1px solid var(--hard-grey);
            box-sizing: border-box;
            text-align: center;
            padding-top: 8px;
            .dot {
              height: 10px;
              width: 10px;
              margin: auto;
              background-color: none;
              border-radius: 50%;
              display: block;
            }
            &.checked {
              .dot {
                background-color: var(--black);
              }
            }
          }
        }
        .difference {
          text-align: center;
          padding-left: 20px;
          padding-right: 20px;
          .hightlight-bg {
            width: 100%;
            background: var(--hard-grey);
            border-radius: 3px;
            display: inline-block;
            z-index: 10;
            position: relative;
            padding-left: 0px;
            padding-right: 0px;
            :before{
              content: '';
              position: absolute;
              width: 50%;
              left: 0;
              height: 100%;
              z-index: -1;
              top: 0;
              border-radius: 3px;
            }
            :after{
              content: '';
              position: absolute;
              width: 50%;
              left: 50%;
              height: 100%;
              z-index: -1;
              top: 0;
              border-radius: 3px;
            }
            &.normal_normal {
              padding-right: 10px;
              padding-left: 10px;
              :after {
                background: #D0EFD2;
              }
              :before {
                background: #D0EFD2;
              }
            }
            &.normal_soon, &.late_soon, &.soon_soon {
              padding-right: 10px;
              :after {
                background: var(--hard-grey);
              }
            }
            &.soon_normal, &.soon_late, &.soon_soon {
              padding-left: 10px;
              :before {
                background: var(--leaf-green);
              }
            }
            &.late_soon, &.late_normal, &.late_late {
              padding-left: 10px;
              :before {
                background: var(--hard-grey);
              }
            }
            &.soon_late, &.normal_late, &.late_late {
              padding-right: 10px;
              :after {
                background: var(--leaf-green);
              }
            }
          }
          .hightlight-tag {
            width: 100%;
            font-size: 14px;
            line-height: 18px;
            color: var(--hard-black);
            border-radius: 3px;
            padding: 5px 7px;
            background: #D0EFD2;
            display: inline-block;
          }
        }
        .status {
          select {
            border: none;
            text-align-last: right;
            background: none;
            &::hover, &::active {
              border: none;
            }
          }
        }
        &.remarks-row {
          padding-right: 7px;
          &.special-staff {
            box-shadow: inset 0px 2px red, inset -2px -2px red;
          }
        }
        .remarks {
          span {
            font-size: 12px;
            line-height: 14px;
            padding: 4px;
            color: var(--black);
            border: 1px solid var(--hard-black);
            border-radius: 5px;
          }
        }
      }
    }
  }
  #staffs-list-popup {
    overflow-y: auto;
    .modal-dialog {
      width: 90%;
      display: flex;
      .modal-content {
        padding-top: 10px;
        width: 100%;
        float: left;
      }
    }
    .modal-header, .modal-body, .modal-footer {
      width: 100%;
      float: left;
    }
    .modal-header {
      border: none;
      padding: none;
      .modal-close {
        bottom: -9px;
      }
    }
    .modal-body {
      width: 100%;
      background: var(--light-grey);
      border-radius: 10px;
      padding-top: 5px;
      padding-bottom: 0;
      height: 420px;
      overflow: auto;
      .review {
        padding-top: 20px;
        padding-bottom: 0px;
        .header-review-tag {
          border: 1px solid var(--hard-grey);
          box-sizing: border-box;
          border-radius: 10px;
          padding: 7px 15px;
          font-weight: 600;
          font-size: 14px;
          line-height: 17px;
          color: var(--hard-grey);
          margin-left: 10px;
          margin-right: 10px;
          background: var(--white);
          &.active {
            background: var(--hard-green);
            color: var(--white);
            border: 1px solid var(--white);
          }
        }
      }
      .search-result {
        padding-top: 20px;
      }
      table {
        width: 100%;
        thead {
          border-bottom: 3px solid var(--light-grey);
          background: var(--white);
          padding: 3px;
          th {
            text-align: center;
            padding: 5px;
          }
        }
        tbody {
          background: var(--white);
          max-height: 300px;
          overflow: auto;
          span {
            font-weight: 600;
            font-size: 12px;
            line-height: 14px;
          }
          tr {
            text-align: center;
            background: var(--white);
            border-bottom: 3px solid var(--light-grey);
            td {
              padding: 3px;
              &.fullname-row {
                text-align: left;
                width: 15%;
                .fullname {
                  width: 100%;
                  text-align: left;
                  float: left;
                  img {
                    width: 31px;
                    height: 31px;
                    float: left;
                    display: block;
                  }
                  p {
                    margin: 0;
                  }
                  .staff-name {
                    width: calc(100% - 52px);
                    float: left;
                    padding-left: 4px;

                    &.not-show-flag {
                      position: relative;
                      top: 7px;
                    }
                    .staff-label {
                      width: 100%;
                      float: left;
                      background: var(--soft-pink);
                      padding: 3px 5px;
                      border-radius: 3px;
                      font-weight: 300;
                      font-size: 9px;
                      line-height: 11px;
                      &.label-green {
                        margin-left: 3px;
                        background: var(--hard-green);
                        color: var(--white);
                      }
                    }
                    .staff-basic {
                      width: 115px;
                      float: left;
                      text-align: left;
                      font-weight: 600;
                      font-size: 12px;
                      line-height: 14px;
                    }
                  }
                  .staff-notes {
                    width: 20px;
                    float: right;
                    position: relative;
                    top: 4px;
                    display: block;
                  }
                  &--absent-reapplying {
                    font-size: 1.5rem;
                    margin-top: .75rem;
                    color: #F9F5EB;
                  }
                }
              }
              &.select-row {
                width: 5%;
                margin-left: 5px;
                &.registration {
                  .select {
                    visibility: hidden;
                  }
                }
                .select {
                  position: relative;
                  display: inline;
                  top: 5px;
                  .check-box {
                    font-size: 24px;
                    &.checked::before {
                      content: "\e903";
                      color: var(--hard-green);
                    }
                  }
                }
                .like-check-box {
                  color: var(--normal-blue);
                }
              }
              .note {
                margin-right: 0;
                .group-tags {
                  display: inline-flex;
                  min-width: 109px;
                  padding-top: 0;
                  padding: 2px;
                  border-radius: 5px;
                  border: 0.4px solid var(--hard-grey);
                  box-sizing: border-box;
                  background: var(--white);
                  .tag-1, .tag-2, .tag-3, .tag-4, .tag-5 {
                    font-size: 10px;
                    line-height: 12px;
                    font-weight: normal;
                    padding: 5px;
                    border: 1px solid var(--hard-grey);
                    box-sizing: border-box;
                    border-radius: 3px;
                    margin-left: 1px;
                    margin-right: 1px;
                    color: var(--black);
                  }
                  .tag-1 {
                    padding-left: 6px;
                    padding-right: 6px;
                  }
                  .tag-2 {
                    border: 1px solid var(--hard-green);
                    color: var(--hard-green);
                  }
                  .tag-3 {
                    border: 1px solid var(--hard-green);
                    float: right;
                  }
                  .tag-4 {
                    border: 1px solid var(--normal-blue);
                  }
                  .tag-5 {
                    border: 1px solid var(--soft-grey);
                    float: left;
                  }
                }
              }
              .phone-progress {
                select {
                  border: none;
                  &::focus, &::hover, &::active {
                    border: none;
                  }
                }
              }
            }
          }
        }
      }
      .search-actions {
        margin-top: 20px;
        background: var(--white);
        padding: 20px;
      }
    }
    .modal-footer {
      display: block;
    }
  }
  #profile-popup {
    z-index: 6000;
    .modal-content {
      text-align: center;
      padding: 33px 58px;
      .modal-close {
        float: right;
        font-size: 20px;
        font-weight: normal;
        position: relative;
        z-index: 100000;
        cursor: pointer;
      }
      .modal-body {
        color: var(--hard-grey);
        font-weight: 600;
        font-size: 11px;
        line-height: 20px;
        text-align: left;
        .info-tag {
          width: 100%;
          border: 1px solid var(--hard-grey);
          border-radius: 2px;
          padding-left: 5px;
        }
        .line {
          margin-top: 10px;
          float: left;
          width: 100%;
        }
        .hover-on:hover {
          color: var(--black);
        }
      }
      .modal-footer {
        text-align: center;
        margin-top: 20px;
        button {
          float: none;
        }
      }
    }
  }
  #create-offer-memo-popup {
    z-index: 5000;
    textarea {
      width: 100%;
    }
  }
  #create-note-popup {
    .modal-body {
      textarea {
        width: 100%;
      }
    }
  }
  #changable-popup-modal {
    .staff-inputed {
      background: #dedede;
      padding: 8px;
      border-radius: 5px;
    }
    .work-time-warnings {
      margin-left: 10px;
      .work-time-warning {
        font-family: "Barlow";
        font-size: 12px;
        margin-left: 2px;
        font-weight: 600;
        padding: 3px 5px;
        border: 1px solid var(--hard-green);
        border-radius: 5px;
      }
    }
  }
}

body.new_batch_arranges {
  .select2-container--open .select2-dropdown--below {
    margin-top: 5px;
  }
  .content-wrapper {
    float: left
  }
}

