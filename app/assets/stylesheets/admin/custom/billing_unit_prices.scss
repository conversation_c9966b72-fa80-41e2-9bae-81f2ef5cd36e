#billing-unit-price {
  .payment-rate-table {
    table {
      border-collapse: collapse;
      background: white;
      table-layout: fixed;
      width: 100%;
    }

    th, td {
      padding: 8px 16px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .pane {
      background: #eee;
    }

    .pane-hscroll {
      overflow: auto;
    }

    .pane-vscroll {
      overflow-y: auto;
      overflow-x: hidden;
      height: 400px;
      position: relative;
    }

    .payment-input {
      height: 34px;
      width: 100%;
      padding: 6px 12px;
      font-size: 14px;
      line-height: 1.42857143;
      color: #555;
      background-color: #fff;
      background-image: none;
      border: 1px solid #ccc;
    }

    .input-disabled {
      background-color: #bbbbbb !important;
      cursor: not-allowed;
    }

    .dropdown-menu {
      text-align: center;
      border: 1px solid #ccc;
    }

    .wage-date-dropdown {
      width: 300px;
      text-align: center;
      height: 170px;

      .batch-setting-title {
        padding-bottom: 20px;
      }
    }

    .current-wage-dropdown {
      width: 250px;
      height: 150px;
    }

    .dropdown-toggle {
      cursor: pointer;
    }

    .wage-date-group {
      width: 85%;
      margin-left: 25px;
      margin-right: 78px;

      .input-all-approve-date {
        width: 100% !important;
      }
    }

    .effective_date {
      display: inline-block;
      max-width: 100%;
      font-weight: 700;
      margin-left: 28px;

      a {
        color: #333;
      }
    }
    .datepicker-auto-input {
      .datetimepicker-input {
        width: 100%;
      }
    }
    .btn-add {
      margin-left: 100px;
      width: 135px;
      float: left;
      margin-top: 25px;
    }

    .error {
      color: #dd4b39;
      white-space: normal;
    }

    .invalid-perfecture {
      white-space: normal;
    }

    .basic-fee-datepicker {
      width: 140px;
    }

    .location-link {
      display: block;
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .payment-unit-price {
    width: 85px;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
  }

  #confirm-modal-payment-rate, #confirm-option-payment-rate {
    .modal-content {
      width: 850px;
    }
  }

  .option-payment-price {
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
  }

  #peak-periods {
    margin-bottom: 3px;
    input {
      width: 240px;
    }

    .name-peak {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 300px;
    }
  }

  #peak-period-tab {
    margin-bottom: 15px;
  }

  #add-new-peak-period {
    .datepicker-auto-input .datetimepicker-input {
      width: 220px;
    }

    .modal-body {
      padding-left: 40px;
    }

    .btn-add {
      margin-left: 100px;
      width: 135px;
      float: left;
      margin-top: 25px;
    }

    .modal-content {
      width: 400px;
    }
  }

  .payment-rate-upload {
    margin-top: 20px;
  }
}

.bootstrap-datetimepicker-widget {
  bottom: 0;
  left: 0;
}
