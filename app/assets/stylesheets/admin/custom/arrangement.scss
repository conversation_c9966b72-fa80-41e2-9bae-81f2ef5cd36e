.admin-arrangements {
  &-index {
    a.btn-statistic {
      border: 1px solid #4C93E3;
      padding: 10px 30px;
      color: #4C93E3;
      &[is-show="true"] {
        border: 0;
        background: #F8F8F8;
      }
    }
    .display-settings {
      margin: 10px 0 0;
      select {
        border-color: #D2D6DE;
        padding: 4px 20px 4px 4px;
        background: #FFF;
      }
    }
    .statistic-table {
      background: #F8F8F8;
      padding: 15px;
    }
    .table-responsive {
      background: #FFF;
      .freeze-column {
        background: #F8F8F8;
      }
      &.arrangement-search-result {
        tbody {
          tr {
            position: relative;
            td.row-overlay {
              position: absolute;
              background: rgba(0, 0, 0, 0.3);
              left: 0.7em;
              right: 0.7em;
              padding: 0px;
              text-align: center;
              z-index: 2;
            }
            &.locked-arrangement {
              pointer-events: none;
              background: #b4b4b4;
              opacity: 0.9;
              .unlocked-click {
                pointer-events: auto;
                border: 1px solid #b4b4b4;
              }
              select.form-control {
                background: #b4b4b4;
              }
              .freeze-column {
                background: #b4b4b4;
              }
              td.need-working-prepare,
              td.red-background {
                background: none;
                padding: 0px;
                > div {
                  padding: 8px;
                  background: #dd4b39;
                  height: 50px;
                }
              }
              td.evaluable {
                pointer-events: auto;
                background: #F8F8F8;
                opacity: 1;
              }
            }
          }
        }
      }
    }
    .full-size {
      width: 100%;
    }
    .fix-height {
      font-size: 90%;
    }
    .working-hour {
      width: 18px;
      height: 12px;
      display: inline-block;
    }
    .sun-color {
      color: #ffd900;
    }
    $working-time-icons: (sunrise sunset night);
    .image-{
      @each $img in $working-time-icons {
        &#{$img} {
          background: asset-url("#{$img}.svg") no-repeat center center;
        }
      }
    }
    .add-location {
      display: block;
      margin-top: 3px;
    }
  }
}
.form-input-break-time, #confirm-arrange-mail-modal {
  .btn-full-width {
    width: 100%;
  }

  .btn-remove-break-time {
    color: #f5748b;
    padding-top: 7px;
    cursor: pointer;
  }

  .btn-submit, .btn-close-modal {
    width: 25%;
  }

  .disabled {
    pointer-events: none;
    background-color: #bbbbbb !important;
  }
}

.stars-outer {
  display: inline-block;
  position: relative;
  font-family: FontAwesome;
}

.stars-outer::before {
  content: "\f005 \f005 \f005 \f005 \f005";
  color: #888;
}

.stars-inner {
  position: absolute;
  top: 0;
  left: 0;
  white-space: nowrap;
  overflow: hidden;
  width: 0;
}

.stars-inner::before {
  content: "\f005 \f005 \f005 \f005 \f005";
  color: #FFA430;
}

.cursor-poiter:hover {
  cursor: pointer;
}

.staff-selected {
  background-color: #00BEEC !important;
}

.table-search-staff th {
  width: 200px;
}

.table-search-staff th:first-child {
  width: 110px;
}

.word-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  span.arrangement-note {
    pointer-events: auto;
  }
}

.ellipsis-inline {
  display: inline-block;
  max-width: 160px;
}

#portion-statuses {
  .cursor-poiter:hover {
    cursor: pointer;
  }

  .switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 28px;
  }

  .switch input {display:none;}

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
  }

  .slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 1px;
    bottom: 3px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
  }

  input:checked + .slider {
    background-color: #2196F3;
  }

  input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
  }

  input:checked + .slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
  }

  .slider.round {
    border-radius: 34px;
  }

  .slider.round:before {
    border-radius: 50%;
  }

  .select-is-urgent {
    margin-top: 72px;
  }

  .select-is-urgent-switch {
    margin-bottom: -8px;
  }

  .modal-content {
    min-width: 748px;
  }

  .has-error {
    color: #f5748b;
  }

  .location-note {
    padding-bottom: 15px;
  }
}

#confirm-arrange-job-mail-modal, #offer-job-mail-modal, #confirm-offer-job-mail-modal,
  #warning-arrange-staff-modal, #confirm-warning-arrange-staff-modal, #confirm-cancel-trigger-modal {
  .modal-content {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    box-shadow: none;
    height: 100%;
  }

  .modal-header {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
  }

  .modal-body {
    position: absolute;
    top: 50px;
    bottom: 60px;
    width: 100%;
    font-weight: 300;
    overflow: auto;
  }

  .modal-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 60px;
    padding: 10px;
  }

  .modal-dialog {
    height: 90%;
    width: 90%;
  }

  #constrained {
    height: 100%;
    overflow-y: auto;
  }

  table {
    thead, tr > th:first-child:not(.sort-working-day) {
      width: 45px;
    }
  }
}

.col-sort {
  cursor: pointer;
}

#input-payment-unit-price {
  .yen {
    padding-left: 0px;
    padding-top: 6px;
  }

  .payment-unit {
    padding-top: 10px;
  }
}

.arrangement-search-result {
  .need-working-prepare {
    background-color: #dd4b39;
  }

  /**
   * Arrengments result tables
   */
  .frozen-view {
    width: 586px;
    max-width: 36%;
    overflow: hidden;
    .frozen-view__ {
      &header {
        overflow: hidden;
        margin-right: 0;
      }
      &footer {
        overflow: hidden;
        margin-right: 0;
      }
      &rows {
        overflow-x: auto;
        max-height: 650px;
      }
    }
    .no-padding-bottom {
      padding-bottom: 0;
    }
    .font-weight-bold {
      font-weight: 700;
    }
  }
  .mg-top-15 {
    margin-top: 15px;
  }
  .unfrozen-view {
    max-width: 64%;
    overflow: hidden;
    .unfrozen-view__ {
      th, td {
        background: #F8F8F8;
      }
      &header {
        overflow: hidden;
        margin-right: 0;
      }
      &footer {
        overflow: hidden;
        margin-right: 0;
      }
      &rows {
        overflow: auto;
        max-height: 665px;
      }
    }
  }
  table.table-bordered {
    width: 100%;
    td, th {
      padding: 8px;
      height: 51px;
    }
  }
}

#offer-job-mail-modal {
  #offer-constrained {
    height: 95% !important;
    overflow-y: auto;
  }

  .total-selected-job {
    margin-left: 20px;
  }
}

#confirm-offer-job-mail-modal {
  #offer-confirm-constrained {
    height: 92% !important;
    overflow-y: auto;
  }
}

.btn-confirm-modal {
  &:disabled {
    pointer-events: none;
    background-color: #e5e5e5;
    color: #fff;
  }
}

.confirm-arrange-send-modal-container, .loading-calculate-sum, #offer-mail-sending-modal {
  .modal {
    text-align: center;
    padding: 0!important;
  }

  .modal:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -4px;
  }

  .modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
  }

  .processing-icon {
    top: 85% !important;
    left: 53% !important;
  }
}

.fix-resize-horizontal {
  resize: vertical;
  overflow: auto;
}

.add-margin {
  margin-left: 5px;
}

.btn-remove-add-more {
  margin-left: -18px;
}

.red-background {
  background-color: #fa3f3f;
  color: white;
}

.display-input-group {
  display: inline;
}

a.select-arrange-disabled {
  pointer-events: none;
  color: #e5e5e5;
}

@media (max-width: 1366px) {
  .frozen-view {
    max-width: 55% !important;
  }
  .unfrozen-view {
    max-width: 45% !important;
  }
}

@media (max-width: 1600px) and (min-width: 1367px) {
  .frozen-view {
    max-width: 45% !important;
  }
  .unfrozen-view {
    max-width: 55% !important;
  }
}

@media (max-width: 1280px) {
  .frozen-view {
    max-width: 60% !important;
  }
  .unfrozen-view {
    max-width: 40% !important;
  }
}

@media (max-width: 1440px) and (min-width: 1367px) {
  .frozen-view {
    max-width: 51% !important;
  }
  .unfrozen-view {
    max-width: 49% !important;
  }
}

@media (max-width: 1680px) and (min-width: 1601px) {
  .frozen-view {
    max-width: 42% !important;
  }
  .unfrozen-view {
    max-width: 58% !important;
  }
}

#copy-portion-modal {
  .btn-add-break-time {
    width: 100%;
  }

  .btn-remove-break-time {
    color: #f5748b;
    padding-top: 7px;
    cursor: pointer;
  }

  .btn-submit, .btn-close-modal {
    width: 25%;
  }

  .add-portion-group {
    width: 318px !important;
    margin-left: 15px;
  }
}

.order-step-2 {
  .btn-add-staff{
    margin-left: 15px;
  }
}

.add-staff-modal {
  .modal-body {
    margin-left: 15px;
  }

  .break-time-input {
    height: 50px;
  }
}

.btn-copy-portion {
  width: 80px;
}

.check-edit-case-portion-time {
  color: #3c8dbc;
  font-weight: bold;
}

.no-editable {
  pointer-events: none;
  background: #b4b4b4;
  opacity: 0.9;
}

#arrange-history-modal {
  .modal-content {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    box-shadow: none;
    height: 100%;
  }

  .modal-header {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
  }

  .modal-body {
    position: absolute;
    top: 50px;
    bottom: 60px;
    width: auto;
    font-weight: 300;
    overflow: auto;
  }

  .modal-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 60px;
    padding: 10px;
  }

  .modal-dialog {
    height: 40%;
    width: 40%;
  }

  #constrained {
    height: 100%;
    overflow-y: auto;
  }

  table {
    thead, tr > th:first-child {
      width: 150px;
    }
  }
}
.download-excel {
  display: inline-block;
  .dropdown-menu {
    margin: 0;
    padding: 0;
  }
}
.show-sum {
  margin-right: 3px;
}
.btn-full-width {
  width: 100%;
}
#update-temlate {
  .control-label {
    margin-top: 10px;
  }
}