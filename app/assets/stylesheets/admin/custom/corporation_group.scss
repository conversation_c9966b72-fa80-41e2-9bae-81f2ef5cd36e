.owner-code {
  margin-bottom: 15px;
}

.owner-name {
  padding-top: 7px;
}

.btn-back-to-list-search {
  position: absolute;
  top: 94px;
  right: 15px;
  display: inline-block;
  float: right;
  vertical-align: middle;
}

.btn-add-owner-code button{
  margin-bottom: 15px;
}

.form-horizontal .fix-nested-form {
  margin-right: 0;
  margin-left: 0;
}

.transaction-type {
  padding-left: 7px;
}

.no-margin-bottom {
  margin-bottom: 0;
}

#history-show-modal {
  overflow-y: auto;
  .modal-dialog {
    min-width: 890px;
    padding-top: 5%;
  }
  .history-table {
    max-height: 300px;
    overflow-y: auto;
    table {
      td {
        min-width: 150px;
        &.delete-area{
          min-width: 100px;
          padding: 4px;
        }
      }
    }
    .error {
      border-color: #dd4b39;
      box-shadow: none;
    }
  }
  .fa.fa-pencil-square-o {
    color: #3c8dbc;
  }
  .inactive {
    display: none
  }
  .remove-violation-file {
    padding: 4px;
    font-style: normal;
    font-size: 12px;
  }
}
.datepicker.datepicker-dropdown {
  z-index: 1070!important;
}
