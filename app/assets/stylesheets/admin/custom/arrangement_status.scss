.container-modal-center {
  text-align: center;
  padding: 0!important;
}

.container-modal-center:before {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -4px
}

.dialog-modal-center {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}

.wrap-status-selection {
  margin-top: 30px;
}

.absence-form-group {
  .form-group {
    padding: 15px;
  }
}

.public-limited-body {
  width: 80%;

  .modal-body {
    max-height: calc(100% - 120px);
  }

  .status-public-limit{
    width: 50%;
    padding-right: 15px;
    padding-left: 15px;
    label{
      white-space: nowrap;
    }
  }

  .staff-seach-form {
    padding-top: 30px;
    .form-group {
      padding: 15px;
    }
    .form-input-group {
      padding-bottom: 15px;
    }
  }

  .staff-list-area {
    max-height: 300px;
    height: 300px;
    padding-left: 15px;
    padding-right: 15px;
  }

  .result-area {
    max-height: 300px;
    overflow-y: auto;
  }

  .choose-area {
    max-height: 300px;
    overflow-y: auto;
  }

  .total-chose{
    position: fixed;
    margin-top: -20px;
  }

  .staff-public-choice {
    padding-top: 15px;
    padding-left: 15px;
  }

  .urgent-choice {
    padding-top: 15px;
    label{
      padding-left: 30px;
    }
    .urgent-swich{
      padding-left: 21px;
    }
  }
}

.inactive-label {
  font-weight: inherit;
  opacity: 0.6;
}
