.admin-staff-search {
  .staff-age {
    width: 80% !important;
  }
}

.btn-arrow {
  position: relative;
  width: 90px;
}

.btn-arrow::after {
  content: "\f107";
  color: #333;
  top: 2px;
  right: 2px;
  font-size: 20px;
  position: absolute;
  font-family: "FontAwesome"
}

.btn-arrow[aria-expanded="true"]::after {
  content: "\f106";
}

.staff-header {
  margin-top: 25px;
  .staff-item-count {
    float: left;
  }

  .operate-selection-item {
    margin-left: 5px;

    button {
      width: 160px;
    }
  }
}

#btn-url-register {
  float: right;
  margin-right: 5px;
}

.toaster-success {
  background-color: #00a65a;
}

.toast-top-full-width {
  position: absolute;
  width: 89% !important;
  right: 0px;
  top: 43px;
  margin: 0 auto;
  text-align: center;
}

.toast-top-center {
  top: 43px;
  right: 0;
  margin: 0 auto;
  > div {
    width: 600px !important;
    text-align: center;
  }
}

.staff-status {
  background: #3c8dbc;
  color: #ffffff;
  text-shadow: 0 1px 0 #3c8dbc;
}

#admin-export {
  width: 30%;
  height: auto;
  position: fixed;
  z-index: 100000;
  padding: 10px;
  right: 16px;
  bottom: 50px;
  .progress {
    margin-bottom: 0;
  }
  &.alert {
    margin: 0;
  }
}

.label-confirm-chk {
  font-weight: normal;
  padding-left: 5px;
}

.confirm-btn-group {
  padding-top: 20px;
}

.no-left-margin {
  margin-left: 0 !important;
}

.font-normal {
  font-weight: normal;
}

.station-label {
  margin-left: 25px;
}

.step4-content {
  margin-left: 25px;
}

.padding-right-0 {
  padding-right: 0;
}

.padding-right-20 {
  padding-right: 20px;
}

.padding-left-100 {
  padding-left: 100px;
}

.padding-top-5 {
  padding-top: 5px;
}

.custom-nav li a {
  background: #48454526;
  color: black;
}

.send-edit-mail-form .error-notice {
  margin-top: 6px;
  color: #dd4b39;
}

.margin-0 {
  margin: 0 !important;
}

.black-input {
  color: #555 !important;
}

.custom-error {
  border: 1px solid #dd4b39;
}

.label-align-right {
  text-align: right !important;
}

.size-comment-box {
  max-width: 100%;
  min-width: 100%;
}

.staff-slection-page {
  padding: 5px 5px;
  margin-left: 5px;
}

#staff-search, #btn__clear-search-condition {
  min-width: 270px;
  padding: 14px 30px;
}

.error-msg {
  color: #dd4b39;
  margin-bottom: 0px !important;
}

.custom-label {
  text-align: center;
  padding: 0 !important;
  margin: 0 !important;
  width: 3%;
}

.staff-departments {
  margin-left: -2px !important;
}

.padding-0 {
  padding: 0px !important;
}

.padding-left-1 {
  padding-left: 1px !important;
  padding-right: 0px !important;
}

#import-payment {
  .custom-file-upload {
    border: 1px solid #ccc;
    display: inline-block;
    padding: 6px 12px;
    width: 240px;
  }

  .label-import {
    padding-top: 6px;
  }
}

#btn-export-payment-unit-price {
  margin-left: 25px;
}

.import-export-btn {
  margin-top: 25px;
}

.star-checked {
  color: orange !important;
}

.white-star {
  color: #888;
}

.horizontal-scroll {
  width: 100%;
  overflow-x: scroll !important;
}

.sm-th {
  min-width: 70px;
}

.md-th {
  min-width: 100px;
}

.lg-th {
  min-width: 150px;
}

.double-border{
  border-bottom: double;
}

#form-staff {
  .staff-view {
    padding-top: 5px;
  }

  .disabled {
    pointer-events: none;
  }
}

.table-overflow {
  width: 100% !important;
  max-height: 320px;
  overflow-y: auto !important;
  overflow-x: auto !important;
  border: 2px !important;
}

.table-overflow th:first-child {
  min-width: 36px;
}

.table-overflow th {
  min-width: 230px;
}

.operate-staff-selection-item {
  button {
    width: 180px;
  }
}

.table-wrapper-scroll {
  display: block;
  max-height: 450px;
  overflow: auto;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

.table-staff-message, .table-staff-complaint {
  max-width: 145% !important;
  width: 145% !important;
}

.table-staff-message-location {
  max-width: 170% !important;
  width: 170% !important;
}

.table-staff-message, .table-staff-message-location, .table-staff-complaint {
  tr {
    cursor: pointer;
  }
  td {
    input, textarea {
      width: 100%;
    }
  }
  .delete-col {
    min-width: 100px;
  }
}

.cell-ellipsis-md {
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.cell-ellipsis-lg {
  max-width: 370px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.cell-middle {
  vertical-align: middle !important;
}

.admin-staff-tab-disabled {
  pointer-events: none;
  background-color: #bbbbbb !important;
}

.vacation-certificate {
  width: 30%;
  padding-top: 10px;
}

.table-notifications th{
  min-width: 200px !important;
}

.no-wrap {
  white-space: nowrap;
}

.work-expectation {
  margin-left: 90px;
}

#work-duty-tab hr {
  border-top: 1px solid #ccc;
}

.image-size {
  width: 100%;
  max-width: 425px;
  max-height: 262px;
}

.div-image {
  margin-left: 16.4%;
}

.request-profile-img-container {
  min-height: 250px;
  &.thumbnail {
    margin-bottom: 0 !important;
  }
  .request-profile-img {
    height: 240px !important;
  }
}

#request-tab {
  .title {
    padding: 0 30px;
    height: 50px;
    line-height: 50px;
    font-size: 18px;
    border-top: 1px solid #e3e3e3;
    border-bottom: 1px solid transparent;
    background-color: #f8f8f8;
    position: relative;
    cursor: pointer;
    margin-bottom: 20px;
    margin-top: 0px;
    border-bottom-color: #e3e3e3;
  }
}

.staff-version .staff-view {
  padding-top: 5px;
}

.padding-left-20 {
  padding-left: 20px;
}

.pad-10 {
  padding: 10px;
}

.modal-lg-custom {
  width: 75%;
}

.pad-left-0 {
  padding-left: 0px !important;
}

.pad-right-0 {
  padding-right: 0px !important;
}

.admin-staff-form .staff-departments {
  width: 135%;
}

textarea.resize-vertical {
  resize: vertical;
}

.valid-license-label {
  color: red;
}

.valid-license-date {
  color: red;
  border-color: red !important;
}

.width-50-percent {
  width: 50%;
}

select[disabled].select2 + .select2-container {
  pointer-events: none;
  touch-action: none;

  .select2-selection {
    background: #eee;
    box-shadow: none;
  }

  .select2-selection__arrow,
  .select2-selection__clear {
    display: none;
  }
}

#admins-edit-staff .disabled {
  pointer-events: none;
}

.white-space-pre {
  white-space: pre;
}

#modal-detail-mail-history .modal-body {
  overflow: auto;
  max-height: 700px;
}

.min-w-230 {
  min-width: 230px !important;
}

.min-w-150 {
  min-width: 150px !important;
}

.modal-with-68 {
  width: 68%;
}

.max-height-500-px {
  max-height: 500px !important;
}

.profession__choice {
  list-style: none;
  background-color: #3c8dbc;
  border-color: #367fa9;
  padding: 1px 10px;
  color: #fff;
  border: 1px solid #aaa;
  border-radius: 4px;
  display: inline-block;
  margin-top: 3px
}
.help-block {
  &.err-msg {
    width: 100%;
    float: left;
  }
}
.social-attribute {
  .help-block {
    &.err-msg {
      margin-left: 16.66666667%;
      width: 80%;
    }
  }
}
.w-70 {
  width: 170px;
  height: 60px;
  overflow: auto;
}
.btn-right {
  float: right;
}