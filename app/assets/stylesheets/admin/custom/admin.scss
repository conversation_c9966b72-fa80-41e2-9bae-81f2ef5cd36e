// Add bootstrap ulility classes
.d-none {
  display: none !important;
}

.d-flex {
  display: -ms-flexbox !important;
  display: flex !important;
}

.col-auto {
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
  max-width: none;
}

.align-items-center {
  -ms-flex-align: center !important;
  align-items: center !important;
}

.align-items-baseline {
  -ms-flex-align: baseline !important;
  align-items: baseline !important;
}

.font-weight-normal {
  font-weight: 400 !important;
}
// ------

.fix-pull-right {
  margin-right: 15px;
}

.fix-top-button {
  vertical-align: top
}

.title-password-setting {
  border-bottom: 3px double gray;
}

.btn-submit-modal, .btn-submit-size, .btn-delete-modal {
  width: 25%;
}

.btn-close-modal {
  width: auto !important;
}

.title-result {
  border-bottom: 1px solid gray;
}

.search-position {
  margin-top: 15px;
}

.fix-padding-left {
  padding-left: 0;
}

.has-feedback .form-control-feedback {
  top: 25px;
}

.form-group, .form-custom {
  .has-error {
    color: #dd4b39;
    input {
      border-color: #dd4b39;
      &:focus {
        border-color: #dd4b39;
      }
    }
    .btn-error {
      color: #dd4b39;
      border-color: #dd4b39;
    }
  }
  &.has-error {
    .select2 {
      .selection {
        .select2-selection {
          border: 1px solid #dd4b39;
        }
      }
    }
  }
}

label.has-error {
  color: #dd4b39;
}

.form-custom.has-error .help-block {
  color: #dd4b39;
}

.form-custom.has-error .input-group .input-group-addon {
  border-color: #dd4b39;
  box-shadow: none;
}

th.has-error {
  color: #dd4b39;
}

.error-tab {
  border-color: #dd4b39 !important;
}

.form-group.has-error .input-group-btn .btn {
  border-color: #dd4b39;
  box-shadow: none;
}

.has-error .input-group-btn .btn {
  color: #a94442;
}

@for $i from 1 through 10 {
  .tbl-w-#{$i} {
    width: $i * 10%;
  }
}

.tbl-w-small {
  width: 5%;
}

// Table columns fixed width in pixels
@for $i from 1 through 30 {
  $pixel-width: ($i * 25);
  .tbl-col-fixed-w-#{$pixel-width} {
    width: #{$pixel-width}px;
  }
}

@for $data-width from 31 through 400 {
  .tbl-col-fixed-w-#{$data-width} {
    width: #{$data-width}px;
  }
}

table {
  td {
    word-break: break-all;
  }
}

.no-search-result {
  margin-top: 15px;
}

.resize-none {
  resize: none;
}

.resize-w-none {
  min-width: 100%;
  max-width: 100%;
}

.sort-data:hover {
  cursor: pointer;
}

.diff-field {
  color: red;
  border: 1px red solid;
  label {
    color: red;
  }
}

.compare-header {
  border-bottom: 1px solid black;
  margin-bottom: 5px;
}

.width-15 {
  width: 15%;
}

.compare-inline {
  display: inline-flex;
  width: 100%;
}

.version-compare-inline {
  display: inline-flex;
}

.no-require {
  border-radius: 0;
  box-shadow: none;
  border-color: #d2d6de !important;
}

.no-padding {
  padding: 0px;
}

.margin-20 {
  margin: 20px;
}

.modal-image {
  width: fit-content !important;
  .modal-content {
    border-radius: 5px;
  }
}

.image-modal {
  width : 1275px;
  height: 786px;
  margin: 10px;

  @media only screen and (max-width: 420px) {
    width : 278px;
    height: 167px;
  }

  @media only screen and (max-width: 768px) and (min-width: 420px) {
    width : 509px;
    height: 306px;
  }

  @media only screen and (max-width: 991px) and (min-width: 768px) {
    width: 658px;
    height: 396px;
  }

  @media only screen and (max-width: 1139px) and (min-width: 991px) {
    width: 751px;
    height: 455px;
  }

  @media only screen and (max-width: 1366px) and (min-width: 1139px) {
    width : 935px;
    height: 686px;
  }

  @media only screen and (max-width: 1600px) and (min-width: 1366px) {
    width: 1062px;
    height: 640px;
  }

  @media only screen and (max-width: 1920px) and (min-width: 1600px) {
    width : 1275px;
    height: 786px;
  }
}

.no-width {
  min-width: unset !important;
}

.min-width-150 {
  min-width: 150px !important;
}

.checkbox-w {
  width: 3%;
}

.label-insurance {
  padding: 2px 4px;
  margin-right: 2px;
}

#haken-type-form-group {
  margin-top: 40px;
}

.send-mail-btn {
  margin-top: 3px;
}

.confirm-reject-btn, .send-mail-btn {
   margin-right: 15px;
}

.full-width {
  width: 100%;
}

.bold-300 {
  font-weight: 300
}

.plusminus-icon {
  margin: auto;
  margin-top: 5px;
  position: relative;
  width: 20px;
  height: 20px;
  cursor: pointer;
  &.active {
    &:before {
      transform: translatey(-50%) rotate(-90deg);
      opacity: 0;
    }
    &:after {
      transform: translatey(-50%) rotate(0);
    }
  }
  &:before , &:after {
    content: "";
    display: block;
    background-color: #333;
    position: absolute;
    top: 50%; left: 0;
    transition: .35s;
    width: 100%;
    height: 3px;
  }
  &:before {
    transform: translatey(-50%);
  }
  &:after {
    transform: translatey(-50%) rotate(90deg);
  }
}