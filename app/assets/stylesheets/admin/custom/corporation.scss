.page-new-corporation {
  padding-left: 15px;
}

.page-edit-corporation {
  padding-left: 15px;
}

.search-postal-code {
  padding: 4px 12px;
}

.overview-date-fixed {
  margin-top: 58px;
}

#list-postal-code {
  text-align: center;
  padding: 0!important;
  &:before {
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -4px;
  }
  .modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
  }
  #postal_code_search {
    width: auto;
  }
}

#overview-bill-to {
  .billing-label {
    padding-left: 10px;
  }
}

.page-new-corporation {
  padding-left: 15px;
}

.page-edit-corporation {
  padding-left: 15px;
}

#overview {
  .disabled {
    pointer-events: none;
  }
}

.total-item-user {
  border-bottom: 1px solid #bbbbbb;
  padding-bottom: 5px;
}

.break-word {
  word-wrap: break-word;
}

.table-search-admin-users, .group-tag-list-result {
  .admin-user-corporation-name {
    width: 30%;
  }

  td {
    word-break: break-all;
  }

  .group-btn-user-action {
    align-items: center;
    align-content: center;

    a, button {
      margin-left: 10px;
      display: inline-block;
    }
    .btn-admin-edit-user {
      margin-left: 30px;
    }
  }
}

.add-new-group {
  margin-bottom: 10px;
}

.resize-none {
  resize: none;
}

.corporation_thumbnail {
  width: 300px;
  height: 300px;
}

#modal-preview-thumbnail {
  .row {
    padding: 5px;
  }

  input {
    margin: 0 auto;
  }

  img {
    max-width: 100%;
  }

  .btn {
    width: 75%;
    padding: 10px;
  }

  .button-rotate {
    border: 1px solid;
  }
}
