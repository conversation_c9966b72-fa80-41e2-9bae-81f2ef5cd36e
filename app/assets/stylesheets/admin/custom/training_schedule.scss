$cell-height: 40px;

#training_schedule {
  font-size: 16px;
  a {
    cursor: pointer;
  }
  th {
    text-align: center;
    &.tbl-col-fixed-w-150 {
      cursor: pointer;
    }
  }
  td {
    padding: 0px !important;
    .handle_click {
      display: block;
      overflow: hidden;
      height: $cell-height;
      padding: 0px;
      cursor: pointer;
      z-index: 1;
      position: absolute;
      &.tomato_color {
        background: rgb(213, 0, 0);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(213, 0, 0);
        }
      }
      &.tangerine_color {
        background: rgb(244, 81, 30);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(244, 81, 30);
        }
      }
      &.sage_color {
        background: rgb(51, 182, 121);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(51, 182, 121);
        }
      }
      &.peacock_color {
        background: rgb(3, 155, 229);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(3, 155, 229);
        }
      }
      &.lavender_color {
        background: rgb(121, 134, 203);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(121, 134, 203);
        }
      }
      &.graphite_color {
        background: rgb(97, 97, 97);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(97, 97, 97);
        }
      }
      &.flamingo_color {
        background: rgb(230, 124, 115);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(230, 124, 115);
        }
      }
      &.banana_color {
        background: rgb(246, 191, 38);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(246, 191, 38);
        }
      }
      &.basil_color {
        background: rgb(11, 128, 67);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(11, 128, 67);
        }
      }
      &.blueberry_color {
        background: rgb(63, 81, 181);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(63, 81, 181);
        }
      }
      &.grape_color {
        background: rgb(142, 36, 170);
        color: #fff;
        border: 1px solid #fff;
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: #fff;
          color: rgb(142, 36, 170);
        }
      }
      &.no_apply_tomato_color {
        background: #fff;
        color: rgb(213, 0, 0);
        border: 2px solid rgb(213, 0, 0);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(213, 0, 0);
          color: #fff;
        }
      }
      &.no_apply_tangerine_color {
        background: #fff;
        color: rgb(244, 81, 30);
        border: 2px solid rgb(244, 81, 30);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(244, 81, 30);
          color: #fff;
        }
      }
      &.no_apply_sage_color {
        background: #fff;
        color: rgb(51, 182, 121);
        border: 2px solid rgb(51, 182, 121);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(51, 182, 121);
          color: #fff;
        }
      }
      &.no_apply_peacock_color {
        background: #fff;
        color: rgb(3, 155, 229);
        border: 2px solid rgb(3, 155, 229);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(3, 155, 229);
          color: #fff;
        }
      }
      &.no_apply_lavender_color {
        background: #fff;
        color: rgb(121, 134, 203);
        border: 2px solid rgb(121, 134, 203);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(121, 134, 203);
          color: #fff;
        }
      }
      &.no_apply_graphite_color {
        background: #fff;
        color: rgb(97, 97, 97);
        border: 2px solid rgb(97, 97, 97);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(97, 97, 97);
          color: #fff;
        }
      }
      &.no_apply_flamingo_color {
        background: #fff;
        color: rgb(230, 124, 115);
        border: 2px solid rgb(230, 124, 115);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(230, 124, 115);
          color: #fff;
        }
      }
      &.no_apply_banana_color {
        background: #fff;
        color: rgb(246, 191, 38);
        border: 2px solid rgb(246, 191, 38);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(246, 191, 38);
          color: #fff;
        }
      }
      &.no_apply_basil_color {
        background: #fff;
        color: rgb(11, 128, 67);
        border: 2px solid rgb(11, 128, 67);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(11, 128, 67);
          color: #fff;
        }
      }
      &.no_apply_blueberry_color {
        background: #fff;
        color: rgb(63, 81, 181);
        border: 2px solid rgb(63, 81, 181);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(63, 81, 181);
          color: #fff;
        }
      }
      &.no_apply_grape_color {
        background: #fff;
        color: rgb(142, 36, 170);
        border: 2px solid rgb(142, 36, 170);
        border-radius: 5px;
        box-shadow: 1px 1px 1px;
        z-index: 2;
        .training_session_number {
          background: rgb(142, 36, 170);
          color: #fff;
        }
      }
      span {
        font-size: 12px;
        display: block;
        padding-left: 5px;
        line-height: 25px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        .support-icon {
          width: 11px;
          height: 20px;
          float: left;
          padding-left: 0px;
          margin-top: 1px;
          margin-right: 3px;
        }
        i {
          float: left;
          line-height: 25px;
          margin-right: 3px;
        }
      }
    }
    .applied-info {
      overflow: hidden;
      float: left;
    }
  }
  .fix-cell {
    position: relative;
    .scale-cell {
      position: absolute;
      left: 0px;
      top: 0px;
      width: 100%;
    }
  }
  .time {
    height: $cell-height;
    div {
      width: 70px;
      height: $cell-height;
      position: relative;
      span {
        text-align: center;
        width: 70px;
        position: absolute;
        top: -13px;
      }
    }
  }
  .day {
    display: inline-block;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    background: #ddd;
    width: 32px;
    height: 32px;
    text-align: center;
    border-radius: 100%;
    line-height: 31px;
    &.current {
      background: #fdd2d6;
      border: 1px solid #fdd2d6;
      color: #fff;
    }

    &.selected {
      background: #0a76be;
      border: 1px solid #0a76be;
      color: #fff;
    }
  }
  .control {
    font-size: 36px;
    text-align: center;
    color: #3c8dbc;
    span {
      padding: 5px 10px;
    }
    i {
      padding: 5px 10px;
      cursor: pointer;
    }
  }
  .year {
    text-align: center;
    font-size: 20px;
    color: #aaa;
    margin-bottom: -15px;
  }
  .type {
    text-align: center;
    font-size: 24px;
    .first_label {
      margin-right: 150px;
    }
    &.form-group {
      font-size: 16px;
      text-align: left;
      .first_label {
        margin-right: 100px;
      }
    }
  }
  .date {
    div {
      padding-right: 0px;
    }
    .to_time {
      font-size: 24px;
      text-align: center;
    }
    .to_time_detail {
      text-align: center;
    }
  }
  .form-group {
    .day {
      margin-left: 0px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
  .modal-title {
    text-align: center;
    font-size: 24px;
  }
  .col-md-offset-3 {
    label {
      padding: 0px;
      line-height: 34px;
    }
  }
  .candidate_name {
    padding-top: 5px;
    padding-bottom: 5px;

    i {
      font-size: 24px;
      cursor: pointer;
      color: #dd4b39;
      margin-left: 30px;
    }
  }
  .wrapper-table {
    padding-top: 8px;
  }
  .wrapper-tbody {
    visibility: collapse;
  }
  .room-name {
    position: relative;
  }
  .admin-checkbox {
    position: absolute;
    top: 50px;
  }
  .training_session_number {
    float: left;
    top: 2px;
    left: -22px;
    position: absolute;
    transform: rotate(-45deg);
    padding-left: 1em;
    padding-right: 1em;
    border: 0;
    margin: 0;
    height: auto;
    width: auto;
    z-index: 999999999;
    font-weight: 900;
    box-shadow: inset 0 -3em 3em transparent, 0 0 0 2px, 0.3em 0.3em 1em rgba(255, 255, 255, 0.15)
  }
  .training-info {
    margin-top: 32px;
  }
  .download-csv-button {
    float: right;
  }
  #download-modal {
    .modal-body {
      padding: 27px;
      .form-group {
        margin-bottom: 31px;
      }
    }
  }
}

.from-room {
  label {
    span {
      color: #f5748b;
    }
  }
}

#confirm-absent-modal {
  .first_label {
    margin-right: 48px;
  }
  .modal-dialog {
    width: 400px !important;
  }
}

#confirm-joined-modal {
  .first_label {
    margin-right: 48px;
  }
  .modal-dialog {
    width: 400px !important;
  }
}

#lb-filled-slots-out-of-total {
  margin-top: 5px;
}

#btn-update-training-schedule {
  margin-bottom: 15px;
}

#group-form-modal {
  .modal-dialog .modal-content {
    .training-group {
      &__group-section {
        .error-notice {
          color: #f5748b;
        }
      }
      &__list-wrapper {
        max-height: 50vh;
        overflow: clip scroll;
      }
      &__item {
        // Override default box style
        .box {
          margin-bottom: 10px;
          box-shadow: unset;
          border-radius: unset;
          border-top-width: 1px;
          .box-header {
            padding: unset;
            padding: 10px 0;
            .box-title {
              font-size: 16px;
            }
          }
          .box-body {
            padding: unset;
          }
        }
        // ------
        &-btn-wrapper {
          .btn {
            &--duplicate {
              font-weight: 600;
              font-size: 14px;
              line-height: 18px;
              border-radius: 2px;
              padding: 6px 20px;
              transition: all 0.3s ease;
              background-color: #0a76be;
              color: #fff;
            }

            &--remove {
              font-weight: 600;
              font-size: 14px;
              line-height: 18px;
              border-radius: 2px;
              padding: 6px 20px;
              transition: all 0.3s ease;
              background-color: #f5748b;
              color: #fff;
            }
          }
        }
      }
    }
  }
}

.training-schedule-assign-to-me-btn {
  padding-left: 0px;
  padding-top: 6px;
}

// Bootstrap Loading Skeleton
%loading-skeleton {
  color: transparent;
  appearance: none;
  -webkit-appearance: none;
  background-color: #eee;
  border-color: #eee;

  &::placeholder {
    color: transparent;
  }
}
@keyframes loading-skeleton {
  from {
    opacity: .4;
  }
  to {
    opacity: 1;
  }
}
.loading-skeleton {
  pointer-events: none;
  animation: loading-skeleton 1s infinite alternate;

  img {
    filter: grayscale(100) contrast(0%) brightness(1.8);
  }
  h1, h2, h3, h4, h5, h6,
  p, li, a, span,
  .btn,
  label,
  .form-control {
    @extend %loading-skeleton;
  }
}

.training-schedule-remaining-slots {
  .table-text {
    padding-top: 17px !important;
  }
  .btn-training-schedule {
    width: 70px !important;
  }
  .icon {
    font-family: 'customicons'!important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .icon-page:before {
    content: "\e90a";
  }
}
