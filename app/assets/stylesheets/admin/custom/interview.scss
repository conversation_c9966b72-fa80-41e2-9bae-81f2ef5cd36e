$cell-height: 40px;
#interview {
  font-size: 16px;
  a {
    cursor: pointer;
  }
  th {
    text-align: center;
    &.tbl-col-fixed-w-150 {
      cursor: pointer;
    }
  }
  td {
    padding: 0px !important;
    .handle_click {
      display: block;
      height: $cell-height;
      padding: 0px;
      cursor: pointer;
      z-index: 1;
      position: absolute;
      &.no_experience {
        background: rgba(68, 136, 197, 0.8);
        border: 1px solid #fff;
        border-radius: 5px;
        z-index: 2;
      }
      &.experience {
        background: rgba(237, 128, 119, 0.8);
        border: 1px solid #fff;
        border-radius: 5px;
        z-index: 2;
      }
      span {
        font-size: 12px;
        color: #fff;
        display: block;
        padding-left: 5px;
        line-height: 25px;
        .support-icon {
          width: 11px;
          height: 11px;
          float: left;
          padding-left: 0px;
          margin-top: 1px;
          margin-right: 3px;
        }
        i {
          float: left;
          line-height: 25px;
          margin-right: 3px;
        }
      }
    }
    .applied-info {
      overflow: hidden;
      float: left;
      .applied-data {
        color: black;
        line-height: 15px;
      }
    }
  }
  .fix-cell {
    position: relative;
    .scale-cell {
      position: absolute;
      left: 0px;
      top: 0px;
      width: 100%;
    }
  }
  .time {
    height: $cell-height;
    div {
      width: 70px;
      height: $cell-height;
      position: relative;
      span {
        text-align: center;
        width: 70px;
        position: absolute;
        top: -13px;
      }
    }
  }
  .day {
    display: inline-block;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    background: #ddd;
    width: 32px;
    height: 32px;
    text-align: center;
    border-radius: 100%;
    line-height: 31px;
    &.current {
      background: #fdd2d6;
      border: 1px solid #fdd2d6;
      color: #fff;
    }

    &.selected {
      background: #0a76be;
      border: 1px solid #0a76be;
      color: #fff;
    }
  }
  .control {
    font-size: 36px;
    text-align: center;
    color: #3c8dbc;
    span {
      padding: 5px 10px;
    }
    i {
      padding: 5px 10px;
      cursor: pointer;
    }
  }
  .year {
    text-align: center;
    font-size: 20px;
    color: #aaa;
    margin-bottom: -15px;
  }
  .type {
    text-align: center;
    font-size: 24px;
    .first_label {
      margin-right: 150px;
    }
    &.form-group {
      font-size: 16px;
      text-align: left;
      .first_label {
        margin-right: 100px;
      }
    }
  }
  .date {
    div {
      padding-right: 0px;
    }
    .to_time {
      font-size: 24px;
      text-align: center;
    }
  }
  .form-group {
    .day {
      margin-left: 0px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
  .modal-title {
    text-align: center;
    font-size: 24px;
  }
  .col-md-offset-3 {
    label {
      padding: 0px;
      line-height: 34px;
    }
  }
  .candidate_name {
    padding-top: 5px;
    padding-bottom: 5px;

    i {
      font-size: 24px;
      cursor: pointer;
      color: #dd4b39;
      margin-left: 30px;
    }
  }
  .wrapper-table {
    overflow: auto;
    height: 400px;
    padding-top: 8px;
  }
  .wrapper-tbody {
    visibility: collapse;
  }
  .room-name {
    position: relative;
  }
  .admin-checkbox {
    position: absolute;
    top: 50px;
  }
}
.from-room {
  label {
    span {
      color: #f5748b;
    }
  }
}