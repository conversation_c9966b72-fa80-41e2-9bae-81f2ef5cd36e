.batch-arrange-index {
  .batch-arranges {
    white-space: nowrap;
    padding: 8px 16px;
    margin-right: auto;
    background: #ffffff;
  }

  select {
    border-color: #D2D6DE;
    padding: 4px 20px 4px 4px;
    background: #FFF;
  }

  .display-setting {
    padding-left: 40px;
  }

  .button-action {
    margin-top: 10px;
  }

  .display-settings {
    margin-bottom: 10px;
    .input-daterange {
      .form-control {
        &:first-child,
        &:last-child {
          border-radius: 0;
        }
      }
    }
    .dropdown {
      ul.dropdown-menu-right {
        background: #F7F7F7;
        li {
          width: 360px;
          padding: 10px;
          .form-group {
            &:last-child {
              margin-bottom: 0px;
            }
          }
        }
      }
    }
  }

  .error--text {
    color: #dd4b39;
  }

  .pointer {
    cursor: pointer;
  }

  #confirm-arrange-mail-modal {
    .modal-body {
      min-height: 120px;
    }

    .store-note-group {
      margin-bottom: 30px;
    }
  }

  .before-debut {
    display: inline-block;
    &:before {
      content: "[◆]";
      color: #F00;
      margin-right: 4px;
    }
  }

  .order-case-item {
    padding: 10px;
    background: #FFF;
    margin: 1px 4px;
    height: 272px;
    width: 240px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(26, 26, 26, 0.1);
    border: 1px dashed #999;
    overflow: hidden;
    position: relative;
    .stars-outer {
      display: table;
    }
    &.selected {
      border: medium solid #3c8dbc;
    }
    &.special-status {
      border: medium solid red;
    }
    &.registration {
      pointer-events:none;
      background: #95b8db;
    }
    &.apply-arrange {
      background: #d3e4f3;
    }
    &.arrange-status {
      &.temporary_arrange {
        background: #fefeb6;
      }
      &.arranged {
        background: #d9f4d9;
      }
    }
    .error-container {
      position: absolute;
      bottom: 0;
      left: 0;
      padding: 5px;
      background: #F00;
      color: #FFF;
      white-space: initial;
      width: 100%;
      .close {
        top: 0;
        right: 0px;
        position: absolute;
        text-shadow: initial;
        color: #FFF;
        opacity: 1;
      }
      .staff-apply-error {
        padding-right: 15px;
        display: inline-block;
      }
    }
  }
  .apply-oder-cases {
    overflow-x: auto;
    padding: 0 16px;
    overflow-y: hidden;
    .row {
      white-space: nowrap;
      min-height: 280px;
      .order-case-item {
        display: inline-block;
        float: none;
      }
    }
  }
  .order-cases {
    float: left;
    .order-case-item {
      margin-bottom: 8px;
      border-style: solid;
      .urgent {
        font-size: 100%;
      }
      .training {
        padding-right: 5px;
      }
      .regular {
        padding-right: 5px;
        color: #F00;
      }
    }
  }

  .help-block {
    color: #f5748b;
    position: relative;
  }

  #finish-save-temporary-modal, #confirm-batch-arrange-modal {
    .dialog-modal-center {
      width: 400px;
    }
    .modal-body {
      p {
        margin-left: 20px;
      }
    }
  }

  .block-arrange {
    background: #bdbbbb;
  }
}

.btn-warning-arrange {
  width: 200px !important;
}

.full-width {
  width: 90%;
}

.select-2-in-dropdown .select2-container--default .select2-selection--multiple .select2-selection__rendered li{
  width: unset !important;
  padding: unset !important;
}

.button-cancel-temp-arrange {
  margin-left: 10px;
  padding: 6px;
}

.button-change-arrangement-time {
  padding: 6px;
}

#batch-arranges-select-sort-by {
  margin-left: 10%;
  button {
    background-color: #fff;
  }
  ul {
    min-width: 80px;
    background-color: #f4f4f4;
    li {
      text-align: center;
      padding: 1px 15px;
    }
  }
}

.order-case--started_at {
  display: inline-block;
  margin-right: 10px;
}

.order-case--arrangements {
  overflow-y: auto;
  height: 90px;
}

.order-case--arrangement-item {
  border: 1px solid lightgray;
  margin-bottom: 2px;
  width: 140px;
}

.batch_ellipsis_inline {
  display: inline-block;
  max-width: 110px;
}

.regular-time {
  max-height: 200px;
  overflow-y: scroll;
}

#input-notes-for-arrange-mail {
  .modal-dialog {
    width: 90%;
  }
}