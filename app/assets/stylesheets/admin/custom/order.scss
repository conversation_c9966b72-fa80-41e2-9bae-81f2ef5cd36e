.order-search-form {
  .enable-input {
    background-color: #fff;
  }

  .form-group {
    margin-bottom: 60px;
  }

  .search-info__status {
    .input-checkbox-custom {
      display: inline-block;
    }

    .search-info__status-label {
      margin-top: 25px;
    }
  }

  .button--large {
    padding: 14px 30px;
  }

  .btn-search-order {
    min-width: 270px;
    color: #fff;
  }

  #list-order-location {
    text-align: center;
    padding: 0!important;
    &:before {
      content: "";
      display: inline-block;
      height: 100%;
      vertical-align: middle;
      margin-right: -4px;
    }
    .modal-dialog {
      display: inline-block;
      text-align: left;
      vertical-align: middle;
    }
    .order-location-list {
      list-style-type: none;
      overflow: hidden;

      li {
        float: left;
        display: block;
        text-decoration: none;
        padding: 50px;
      }
    }
  }

  .form-custom__field {
    padding-bottom: 15px;
    padding-top: 35px;
  }

  .check-box-row {
    padding-left: 0;
  }

  .form-group.js-list-location {
    margin-bottom: 0;
    padding-bottom: 18px;
  }
}

.info-table .inner-table th,
.info-table .inner-table td {
  border: 0; }
.info-table .inner-table th {
  background-color: #e5e5e5;
  font-weight: 300; }
  @media (max-width: 991px) {
    .info-table .inner-table th {
      min-width: 100px; } }

.detail-price,
.order-price {
  padding: 32px 15px; }
  @media (max-width: 991px) {
    .detail-price,
    .order-price {
      padding: 16px 0; } }
  .detail-price .table th,
  .order-price .table th {
    border-bottom-width: 1px;
    border-color: #e3e3e3;
    font-size: 14px;
    min-width: 80px; }
  .detail-price .table td,
  .order-price .table td {
    border-top-color: transparent;
    font-size: 14px;
    word-break: break-word; }
    @media (max-width: 991px) {
      .detail-price .table td,
      .order-price .table td {
        min-width: 150px; } }
  .detail-price .table tfoot td,
  .order-price .table tfoot td {
    border-top-color: #e3e3e3;
    font-size: 14px; }
  @media (max-width: 767px) {
    .detail-price .total-amount,
    .order-price .total-amount {
      min-width: 180px; } }
  .detail-price .total-amount__number,
  .order-price .total-amount__number {
    font-weight: 600; }
    .detail-price .total-amount__number em,
    .order-price .total-amount__number em {
      font-style: normal;
      color: #1a1a1a;
      margin-left: -3px; }
  .detail-price .total-amount .more-info,
  .order-price .total-amount .more-info {
    outline: none; }
    .detail-price .total-amount .more-info span,
    .order-price .total-amount .more-info span {
      border-bottom: 1px solid #0a76be;
      display: inline-block;
      line-height: 18px; }
  @media (max-width: 991px) {
    .detail-price__remark,
    .order-price__remark {
      padding: 0 15px; } }

.detail-price-dialog {
  max-width: calc(100% - 30px); }
  @media (max-width: 575px) {
    .detail-price-dialog {
      max-width: 100%; } }

.detail-price {
  padding: 32px; }
  @media (max-width: 991px) {
    .detail-price {
      padding: 16px; } }
  .detail-price .table {
    margin-bottom: 0; }
    @media (max-width: 991px) {
      .detail-price .table th,
      .detail-price .table td {
        min-width: 80px; } }
  .detail-price .table-striped thead tr {
    background-color: #f8fafb; }
    .detail-price .table-striped thead tr th {
      vertical-align: middle; }
  .detail-price .inner-table {
    background-color: transparent;
    margin-bottom: 0; }
    .detail-price .inner-table tbody tr {
      background-color: transparent; }
  .detail-price .collapse-row {
    position: relative;
    cursor: pointer;
    min-width: 1000px; }
    .detail-price .collapse-row:after {
      content: '\f106';
      font-size: 12px;
      font-family: "FontAwesome";
      font-weight: 600;
      color: #8a8a8a;
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%); }
    .detail-price .collapse-row td {
      color: #0a76be; }
    .detail-price .collapse-row.collapsed {
      border-bottom-color: #e3e3e3; }
      .detail-price .collapse-row.collapsed:after {
        content: '\f107'; }
      .detail-price .collapse-row.collapsed td {
        color: #1a1a1a; }
  .detail-price .panel-collapse {
    padding: 30px;
    border-top: 1px solid #e3e3e3;
    border-bottom: 1px solid #e3e3e3;
    background-color: #f8f8f8; }
    .detail-price .panel-collapse .table {
      border: 1px solid #e3e3e3; }
    .detail-price .panel-collapse .table-striped thead tr th {
      border-top: 0; }

.form-edit-create-order {
  .button {
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    border-radius: 2px;
    padding: 6px 20px;
    transition: all .3s ease; }
    .button--primary {
      background-color: #0a76be;
      color: #fff; }
      .button--primary:hover {
        background-color: #3090cf;
        box-shadow: 0 0 6px 0 #59a6d9, 0 2px 10px 0 rgba(0, 0, 0, 0.2); }
    .button--secondary {
      background-color: #555;
      color: #fff; }
      .button--secondary:hover {
        background-color: #6f6f6f;
        box-shadow: 0 0 6px 0 #555, 0 2px 10px 0 rgba(0, 0, 0, 0.2); }
    .button--danger {
      background-color: #f5748b;
      color: #fff; }
      .button--danger:hover {
        background-color: #f4879a;
        box-shadow: 0 0 6px 0 #f4879a, 0 2px 10px 0 rgba(0, 0, 0, 0.2); }
    .button--disabled {
      background-color: #e5e5e5;
      color: #fff; }
    .button--extra-small {
      padding: 4px 20px;
      font-size: 10px; }
    .button--small {
      padding: 6px 25px;
      font-size: 12px; }
    .button--large {
      padding: 14px 30px;
      min-width: 270px; }
    .button--full {
      display: block;
      width: 100%; }
    .button__icon {
      color: inherit;
      font-size: 12px;
      margin-right: 12px;
      display: inline-block;
      vertical-align: middle;
      margin-top: -.125em; }
      .button__icon--after {
        margin-left: 12px; }
  .box {
    margin-bottom: 10px;
    border-top: 1px solid #d2d6de;
  }
  .box-header {
    border-bottom: 1px solid #d2d6de;
    background-color: #f8f8f8;
    cursor: pointer;
  }
  .sub-title {
    font-size: 14px;
  }
  .order-step-1 {
    .box {
      box-shadow: none;
      border-top: none;
    }
    .order-organization {
      padding: 18px;
      .order-organization-detail {
        padding-bottom: 40px;
      }
    }
    .form-custom--radiobtn {
      .registered-contact-person {
        margin-top: 30px;
        margin-bottom: 20px;
      }
      .difference-pic {
        margin-bottom: 40px;
      }
    }
    .create-order__message {
      border-top: 1px solid #d2d6de;
      padding: 20px;
      font-size: 14px;
    }
    .location-pic-block {
      margin-top: 20px;
    }
    .error-notice, .error--text {
      color: #dd4b39;
    }

    label {
      span {
        color: #f5748b;
      }
    }
  }

  .order-detail__bottom {
    margin-top: 60px;
  }

  .order-detail__group-btn, .create-order__group-btn {
    margin-bottom: 20px;
  }

  .nav-tabs__item {
    padding: 0;
  }

  .employee-number-icon {
    margin-left: 12px;
  }

  .time-changable {
    label {
      color: #333;
    }
  }

  .order-detail__term-link {
    margin-bottom: 20px;
  }

  .txt--underline {
    text-decoration: underline;
  }

  .create-order__bottom {
    margin-top: 20px;
  }

  .price em {
    font-style: normal;
    color: #1a1a1a;
    margin-left: -3px;
  }

  .list-day {
    .help-block {
      display: inline-block;
    }
  }

  .special-offer-box {
    border: 1px solid #f0f0f0;
    padding-bottom: 40px;

    .special-offer-row {
      padding: 15px 20px;
    }

    .control-label {
      white-space: nowrap;
    }
  }

  .create-order-step2 {
    label {
      span {
        color: #f5748b;
      }
    }
    .error--text {
      color: #dd4b39;
    }
  }
}

.color--blue {
  color: #0a76be; }

.color--pink {
  color: #f5748b; }

.modal-full-width {
  min-width: calc(100% - 30px);
}

.input-radio-custom {
  position: relative;
  margin-bottom: 8px; }
  .input-radio-custom--inline {
    display: inline-block; }
    .input-radio-custom--inline:not(:first-child) {
      margin-left: 20px; }
  .input-radio-custom label {
    position: relative;
    padding-left: 35px;
    margin-bottom: 0;
    cursor: pointer;
    font-size: 14px;
    font-weight: 300;
    color: #555; }
    .input-radio-custom label:before {
      content: '';
      position: absolute;
      top: -3px;
      left: 0px;
      width: 24px;
      height: 24px;
      background-color: #fff;
      border: 2px solid #8a8a8a;
      border-radius: 50%; }
    .input-radio-custom label:after {
      content: '\f192';
      font-family: "FontAwesome";
      font-weight: 900;
      position: absolute;
      top: -8px;
      left: 1px;
      color: #0a76be;
      font-size: 25px;
      opacity: 0; }
  .input-radio-custom input[type='radio'] {
    position: absolute;
    visibility: hidden; }
    .input-radio-custom input[type='radio']:checked ~ label {
      color: #0a76be; }
      .input-radio-custom input[type='radio']:checked ~ label:before {
        background-color: #fff;
        border-color: #0a76be; }
      .input-radio-custom input[type='radio']:checked ~ label:after {
        opacity: 1; }

.create-order, #form-create-order, #form-edit-create-order {
  .disabled {
    pointer-events: none;
    background-color: #bbbbbb !important;
  }
}

.collapse-title {
  padding: 0 30px;
  height: 50px;
  line-height: 50px;
  font-size: 18px;
  border-top: 1px solid #e3e3e3;
  border-bottom: 1px solid transparent;
  background-color: #f8f8f8;
  position: relative;
  cursor: pointer;
  margin-bottom: 0; }
  .collapse-title:after {
    content: '\f067';
    font-size: 18px;
    font-family: "FontAwesome";
    font-weight: 600;
    color: #8a8a8a;
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%); }
  .collapse-title.collapsed {
    border-bottom-color: #e3e3e3; }
    .collapse-title.collapsed::after {
      content: '\f067'; }
.collapsed.collapse {
  display: none;
}

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important; }

.ml-2,
.mx-2 {
  margin-left: 0.5rem !important; }

.ml-5, .mx-5 { 
  margin-left: 3rem !important; }

.create-info-order {
  margin-bottom: 40px; }
  .create-info-order__top-info {
    padding: 60px; }
    @media (max-width: 1139px) {
      .create-info-order__top-info {
        padding: 40px 20px; } }
    @media (max-width: 991px) {
      .create-info-order__top-info {
        padding: 20px 16px; } }
  .create-info-order__label {
    margin-bottom: 24px;
    display: block; }
  @media (max-width: 767px) {
    .create-info-order__period .input-checkbox-custom, .create-info-order__time .input-checkbox-custom {
      margin-bottom: 32px; } }
  @media (max-width: 320px) {
    .create-info-order__period .ic-eq, .create-info-order__time .ic-eq {
      font-size: 12px; } }
  .create-info-order__bottom-info {
    padding: 15px 15px;
    background-color: #fafafa; }
    @media (max-width: 1139px) {
      .create-info-order__bottom-info {
        padding: 20px 0; } }
    @media (max-width: 1139px) {
      .create-info-order__bottom-info .input-checkbox-custom {
        margin: 0 20px; } }
    @media (max-width: 991px) {
      .create-info-order__bottom-info .input-checkbox-custom {
        margin: 0 16px; } }
  .create-info-order__member .button {
    padding: 14px 24px;
    text-align: center; }
    @media (min-width: 1200px) and (max-width: 1254px) {
      .create-info-order__member .button {
        padding: 14px 20px; } }
    @media (max-width: 320px) {
      .create-info-order__member .button {
        padding: 12px 18px; } }
    .create-info-order__member .button__icon {
      margin-right: 0; }
  .create-info-order__price {
    font-weight: 600;
    font-size: 18px;
    text-align: right; }
    @media (max-width: 1139px) {
      .create-info-order__price {
        font-size: 14px;
        text-align: left;
        padding: 24px 20px 0;
        border-top: 1px solid #e4e7e9; } }
    @media (max-width: 991px) {
      .create-info-order__price {
        padding: 24px 16px 0; } }
    .create-info-order__price-number em {
      font-style: normal;
      color: #1a1a1a;
      margin-left: -3px; }
  .create-info-order .right-col {
    text-align: right; }
    @media (max-width: 1139px) {
      .create-info-order .right-col {
        text-align: center;
        margin-top: 24px; } }
    .create-info-order .right-col .button {
      margin-bottom: 24px;
      min-width: 48px; }
      @media (max-width: 1139px) {
        .create-info-order .right-col .button {
          margin: 0 8px; } }
      @media (max-width: 991px) {
        .create-info-order .right-col .button {
          padding: 10px 24px;
          min-width: 120px; } }
      @media (max-width: 320px) {
        .create-info-order .right-col .button {
          min-width: 100px; } }
      .create-info-order .right-col .button__icon {
        font-size: 14px; }
  .create-info-order .list-day {
    padding-left: 0;
    margin-bottom: 32px; }
    .create-info-order .list-day__item {
      display: inline-block;
      width: 32px;
      height: 32px;
      border-radius: 100%;
      background-color: #f8f8f8;
      font-weight: 300;
      margin-right: 8px;
      text-align: center;
      line-height: 32px; }
      .create-info-order .list-day__item--selected {
        color: #fff;
        background-color: #0a76be; }
.create-order {
  margin-bottom: 60px; }
  @media (max-width: 991px) {
    .create-order {
      margin-bottom: 32px; } }
  .create-order__stepbystep {
    border-top: 1px solid #e3e3e3;
    background-color: #fafafa;
    padding: 40px 0;
    justify-content: center;
    display: flex; }
  .create-order .list-step {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none; }
    @media (max-width: 767px) {
      .create-order .list-step {
        padding: 0 20px; } }
    .create-order .list-step__item {
      width: 180px; }
      .create-order .list-step__item--active .list-step__link,
      .create-order .list-step__item--active .list-step__icon {
        color: #0a76be;
        font-weight: 600; }
    .create-order .list-step__link {
      font-weight: 300;
      color: #1a1a1a;
      position: relative;
      display: block;
      text-align: center; }
      .create-order .list-step__link::before {
        content: '';
        width: 100%;
        height: 1px;
        border-top: 1px solid #e3e3e3;
        position: absolute;
        left: 0;
        top: 9px;
        z-index: 1; }
    .create-order .list-step__icon {
      display: block;
      color: #e8e8e8;
      font-size: 20px;
      margin-bottom: 16px;
      z-index: 10;
      position: relative; }
  .create-order__main-info {
    padding: 40px;
    border-top: 1px solid #e3e3e3; }
    @media (max-width: 767px) {
      .create-order__main-info {
        padding: 20px; } }
  .create-order__more-info {
    padding: 40px 40px 16px;
    border-bottom: 1px solid #e3e3e3; }
    @media (max-width: 767px) {
      .create-order__more-info {
        padding: 20px 20px 16px; } }
    .create-order__more-info .input-radio-custom {
      margin-bottom: 24px; }
    .create-order__more-info .form-custom--radiobtn {
      margin-bottom: 40px; }
  .create-order__message {
    padding: 32px 40px; }
    @media (max-width: 767px) {
      .create-order__message {
        padding: 24px 20px 16px; } }
    .create-order__message span {
      margin-left: 10px; }
  .create-order__group-btn {
    padding: 32px 0 40px; }
    @media (max-width: 767px) {
      .create-order__group-btn {
        padding: 24px 0 20px; } }
    .create-order__group-btn .button {
      min-width: 270px; }
  .create-order__stepdetail {
    border-top: 1px solid #e3e3e3; }
  .create-order .nav-tabs {
    border-bottom: 1px solid #e3e3e3; }
    .create-order .nav-tabs__link {
      display: block;
      padding: 20px 0 15px;
      border-bottom: 3px solid transparent; }
      .create-order .nav-tabs__link.active {
        border-bottom-color: #0a76be; }
        .create-order .nav-tabs__link.active label {
          color: #0a76be; }
          .create-order .nav-tabs__link.active label::before {
            background-color: #fff;
            border-color: #0a76be; }
          .create-order .nav-tabs__link.active label::after {
            opacity: 1; }
  .create-order .tab-pane {
    padding: 24px 40px; }
    @media (max-width: 1139px) {
      .create-order .tab-pane {
        padding: 24px 20px; } }
    @media (max-width: 991px) {
      .create-order .tab-pane {
        padding: 20px 16px; } }
  .create-order__stepdetail .collapse-title {
    background-color: #fff;
    padding: 0;
    border: 0;
    margin-top: 24px; }
    .create-order__stepdetail .collapse-title.collapsed {
      border-bottom: 1px solid #e3e3e3; }
  .create-order__add-more {
    border: 1px solid #e3e3e3;
    background-color: #fafafa;
    color: #555;
    padding: 24px;
    margin-top: 40px;
    cursor: pointer; }
    .create-order__add-more-icon {
      display: block;
      font-size: 24px;
      margin-bottom: 16px; }
    .create-order__add-more-text {
      display: block;
      font-size: 14px;
      font-weight: 600;
      transition: color .25s ease; }
      .create-order__add-more-text:hover {
        color: #0a76be; }
  .create-order__note {
    margin-bottom: 40px; }
    @media (max-width: 991px) {
      .create-order__note {
        margin-bottom: 20px; } }
    .create-order__note .form-control {
      font-size: 14px; }
      @media (max-width: 767px) {
        .create-order__note .form-control {
          min-height: 95px; } }
  .create-order__group-btn .button {
    min-width: 270px; }
  @media (max-width: 991px) {
    .create-order__group-btn {
      text-align: center; }
      .create-order__group-btn .button {
        margin-bottom: 16px; } }
  .create-order__term-link label {
    position: relative; }
  .create-order__term-link a {
    border-bottom: 1px solid #0a76be;
    display: inline-block;
    line-height: 18px;
    margin-right: 3px; }
  .create-order__bottom-confirm {
    padding-top: 60px;
    border-top: 1px solid #e3e3e3; }

[ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak], .ng-cloak, .x-ng-cloak {
  display: none !important;
}

.order-result__number-item {
  margin-bottom: 10px;

  .order-slection-page {
    width: 80px;
    padding: 5px 5px;
  }

  .select-box {
    display: inline-block;
  }

  .display-name {
    margin-left: 10px;
  }

  .per-page-settings {
    margin-left: 15px;
  }
}

.fa,
.fas {
  font-family: "FontAwesome";
  font-weight: 900;
}

.order-search-list {
  th {
    cursor: pointer;
  }
}

.order-status {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  display: inline-block;
  height: 28px;
  line-height: 28px;
  padding: 0 16px;
  border-radius: 2px;
  vertical-align: middle;
  margin-right: 16px; }
  .order-status--new {
    background-color: #0a76be; }
  .order-status--unsure {
    background-color: #0a76be; }
  .order-status--waiting {
    background-color: #f5a623; }
  .order-status--draft {
    background-color: #555; }

.btn-export-order {
  min-width: 100px;
  padding: 6px;
  margin-left: 5px;
  height: 31px;
}

.text-nowrap {
  white-space: nowrap;
}

@media (max-width: 1600px) {
  .group-action {
    text-align: center;
    a, button {
      display: inline-block;
      vertical-align: middle;
      text-align: center;
    }
  }
  .btn-delete-order {
    margin-top: 3px;
  }
}

.order-search-form, .staff-search-form {
  .collapse-title {
    padding: 0 30px;
    height: 50px;
    line-height: 50px;
    font-size: 18px;
    border-top: 1px solid #e3e3e3;
    border-bottom: 1px solid transparent;
    background-color: #f8f8f8;
    position: relative;
    cursor: pointer;
    margin-bottom: 20px;
  }

  .collapse-title::after {
    content: "\f068";
    font-size: 18px;
    right: 30px;
    top: 50%;
    position: absolute;
    transform: translateY(-50%);
    font-family: "FontAwesome";
  }

  .collapse-title.collapsed {
    border-bottom-color: #e3e3e3; }
    .collapse-title.collapsed::after {
      content: "\f067";
  }

  .order-list__action {
    padding: 56px 0 40px;
    display: flex;
    align-items: center;
    justify-content: center !important;
  }
}

.admin-order-result {
  .total-order-case {
    width: 5%;
    white-space: nowrap;
  }

  .export-status {
    width: 15%;
  }
}

.notice--error {
  color: #f5748b;
  background: #fef0f3;
  border: 1px solid #fbc3cd;
}

.edit-order-noti-error {
  padding: 10px 10px 10px 30px;
}

.create-info-order__remove-btn {
  display: inline-block;
  vertical-align: middle;
  line-height: 21px;
  transition: all .25s ease;
  margin-left: 24px;
  color: #f5748b;
  cursor: pointer;
}

.step2-bottom-button {
  display: inline-grid;
  .error-notice {
    color: #dd4b39;
  }
}

.btn-back-step1 {
  margin-top: -3px;
}

.staff-express-order-id {
  width: 50%;
}

a.btn-export-order {
  width: 30%;
}

.order-special-offer-note {
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 150px;
  display: block;
  overflow: hidden;
  font-size: 10px;
  color: red;
}

#step3-with-template {
  margin: 30px 40px 0 40px;
}
