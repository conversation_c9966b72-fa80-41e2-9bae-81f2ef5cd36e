#modal-search-form {
  .modal-content {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    box-shadow: none;
    height: 100%;
  }

  .modal-header {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
  }

  .modal-body {
    position: absolute;
    top: 50px;
    bottom: 60px;
    width: 100%;
    font-weight: 300;
    overflow: auto;
  }

  .modal-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 60px;
    padding: 10px;
  }

  .modal-dialog {
    height: 80%;
    width: 50%;
  }

  #constrained {
    height: 100%;
    overflow-y: auto;
  }

  table {
    thead, tr > th:first-child:not(.sort-working-day) {
      width: 45px;
    }
  }

  .search-ordercase-row {
    cursor: pointer;
    &:hover {
      background-color: #f4f4f4;
    }
  }
  .chose-order-case {
    background-color: #f4f4f4;
  }
}
