@media(min-width: 768px) {
  .navbar-nav {
    >li {
      >a {
        padding-top: 5px;
        padding-bottom: 5px;
      }
    }
  }
}
.skin-black {
  .main-header {
    .navbar {
      &-custom-menu {
        .navbar-nav {
          > li {
            > a {
              height: 50px;
              &.navbar-username {
                line-height: 40px;
              }
              img {
                &.user-image {
                  float: none;
                  margin-top: -34px;
                }
              }
              span {
                &.admin-name {
                  height: 40px;
                }
              }
            }
          }
        }
      }
    }
    .logo {
      &-mini {
        img {
          width: 30px;
        }
      }
    }
  }
}

.datepicker {
  &.datepicker-dropdown {
    z-index: 1031 !important;
  }
}

.custom-select-form-control {
  padding: 6px 12px;
  color: #555;
  background-color: #FFF;
  border: 1px solid #CCC;
}
