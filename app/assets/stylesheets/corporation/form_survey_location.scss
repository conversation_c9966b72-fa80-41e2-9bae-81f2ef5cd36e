.form-survey-location__table {
  background-color: #ffffff;
  table-layout: fixed;

  textarea {
    height: 100px;
  }

  .form-survey-location__footer {
    td {
      border: none;
    }
  }
  
  td {
    padding: 1.75rem 0.75rem;
    vertical-align: middle;
    border: 1px solid #dee2e6;
  }

  .sub-question {
    padding: 10px 35px;
  }

  .sub-title {
    padding: 10px 20px;
  }

  .question-text {
    font-weight: bold;
  }

  .title {
    font-size: 18px;
  }

  .btn-submit {
    padding: 20px;
  }

  .error-message {
    position: absolute;
    color: #FF0000;
    display: block;
    padding: 5px 0px;
  }

  .form-survey-location__table-question-3 {
    .ng-invalid {
      border: 1px solid #FF0000;
    }
  }

  .hide-error {
    display: none;
  }
}

.finish-survey-page {
  background-color: #ffffff;
  height: 74vh;
  .finished-survey-container {
    position: relative;
    top: 50%;
    transform: translateY(-50%);
  }
  .text-pd-10 {
    padding: 10px;
  }
}