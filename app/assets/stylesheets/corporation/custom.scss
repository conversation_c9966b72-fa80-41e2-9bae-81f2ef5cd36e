.userform__form--sendemail {
  a:hover {
    color: #fff;
  }
}

.user-avatar__change-avatar {
  .user-avatar__image {
    #user-avatar-img {
      height: 120px;
      width: 120px;
    }
  }
}

.user-profile {
  .user-profile__avatar {
    img {
      height: 50px;
      width: 50px;
    }
  }
}

ul.paginations {
  float: right;
}

.order-result {
  th {
    cursor: pointer;
  }
}

.message-show-hide {
  position: fixed;
  z-index: 9999;
  min-width: 50%;
  right: 30px;
  top: 55px;
  transition: all linear 0.5s;
}

.message-show-hide.ng-hide {
  opacity: 0;
}

.custom_link {
  font-size: 12px;
  position: relative;
  cursor: pointer;
}

.custom_link::before {
  margin-right: 5px;
}

.form-customize {
  .toggle-icon {
    z-index: 2;
  }
}
.sort-data:hover {
  cursor: pointer;
}

.order-detail__bottom  {
  .order-detail__group-btn {
    a:hover {
      color: #fff;
    }
  }
}

table {
  td {
    word-break: break-all;
  }
}

.userform__form--login {
  .owner-code-title {
    text-align: center;
    padding-bottom: 20px;
    font-weight: bold;
  }
}

.collapse-title {
  .ended-at-error {
    color: #f5748b;
    position: absolute;
  }
}

.word-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.toast-format {
  width: 90% !important;
  margin-left: 10% !important;
}

.edit-corporation-location .form-control:focus + label {
  top: -18px !important;
}

.search-order-case {
  .form-control.input-disabled {
    border-radius: 0;
    background: none;
    padding: 8px 0;
    line-height: 17px;
    border-top: 1px solid transparent;
    border-left: 1px solid transparent;
    border-right: 1px solid transparent;
    border-bottom: 1px solid #e5e5e5;
    color: #e5e5e5;
  }
}

.edit-order-noti-error {
  padding: 10px 10px 10px 30px;
}

.violation-day-fixed-width {
  width: 200px !important;
}

#home-order-cases, #usage-achievements {
  .filter-place__dropdown {
    overflow: auto;
    max-height: 650px;
    min-width: 900px;
  }

  .region-locations {
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 0;
  }
}

.corporation-order-case {
  .delete-order-portion {
    cursor: pointer;
  }
}

.owner-input-working-time {
  .disabled {
    pointer-events: none;
    color: #bbbbbb !important;
  }
  .input-disabled {
    pointer-events: none;
    border-radius: 0;
    background: none;
    border-top: 1px solid transparent;
    border-left: 1px solid transparent;
    border-right: 1px solid transparent;
    border-bottom: 1px solid #e5e5e5;
    color: #e5e5e5;
  }
}

.step2-bottom-block {
  display: grid;
}

.over-limit-error {
  margin-bottom: -23px;
}

.overflow-list {
  overflow-y: scroll;
  max-height: 200px;
}

.break-text {
  word-break: break-word;
}

.order-special-offer-note {
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 150px;
  display: block;
  overflow: hidden;
  font-size: 10px;
  color: red;
}

.homepage .accept-list {
  padding: 30px;
  border-bottom: 1px solid #e4e4e4 !important;

  .staff-info {
    color: #1a1a1a;
  }

  .diff-working-time {
    color: #0a76be;
    font-weight: 600;
  }
}

.user-account {
  .dropdown-address {
    .list-address__groups {
      padding: 16px 0 0 16px;
    }
    .list-address__item {
      padding: 0 12px;
      .list-address__text {
        font-size: 12px;
        padding-top: 14px;
        padding-left: 28px;
      }
    }
  }
}

.corporation-order-case {
  #modal-time-status {
    top: 50px;
  }
}

.homepage {
  .search-result__bottom-action {
    margin: 0 10px;
  }
}

.userpage {
  .main-ie {
    .modal-content {
      margin-top: 100px;
    }
  }
}

.filter-plan__view-plan {
  .full-width {
    width: 100%
  }

  .staff--confirmed {
    color: #0a76be;
  }

  .many-arrangements {
    height: 260px;
    overflow: scroll;
    overflow-x: hidden;
    border: 0.1 px solid #e3e3e3;

    .list-arrangements {
      padding-right: 0;
    }
  }

  .index-staff {
    height: 24px;
    line-height: 24px;
    color: #4a4a4a;
    padding: 7px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    background-color: #e6f5ff;
    margin-right: 16px;
  }

  .display-staff-name {
    max-width: 120px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .index-staff--confirmed {
    background-color: #0a76be;
  }
}

.arrangement-status--list {
  margin-bottom: 2px;
}

@media (max-width: 1368px) {
  .filter-place__dropdown {
    margin-top: 100px;
    min-width: 100%;
    overflow: auto;
    max-height: 650px;
  }
}

@media (min-width: 908) {
  .filter-place__dropplace {
    .filter-place__list {
      .filter-place__item {
        width: 30%;
      }
    }
  }
}

@media (max-width: 414px) {
  .filter-place__item {
    width: 100%;
  }
}

.datetimepicker-search td.day.disabled, th.prev.disabled {
  background-color: #bbbbbb;
}

@media (max-width: 767px) {
  .project-detail .height--waiting {
    display: flex;
    position: absolute;
  }

  .height-100 {
    height: 100px;
  }
}

.datetimepicker.form-customize .table .planpiker {
  pointer-events: none; }

.owner-order-case-search {
  padding-bottom: 20px;
}
.order-case-search-result {
  .input-checkbox-custom {
    margin-bottom: 25px;
  }
  td {
    vertical-align: middle;
  }
  th.cancel-check-box {
    min-width: auto!important;
  }
}
.owner-order-case-search-action {
  button {
    &.disabled {
      pointer-events: none;
      background-color: #bbbbbb;
      &::hover {
        background-color: #bbbbbb;
      }
    }
  }
}

#modal-notification-info {
  background: rgba(0, 0, 0, 0.6);
  .confirm-dialog.modal-dialog {
    margin-top: 100px;
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
  }
  .modal-body {
    .modal-bottom {
      padding: 24px;
    }
    .modal-middle {
      max-height: 300px;
      overflow: auto;
      .notification-content {
        white-space: pre-wrap;
      }
    }
  }
}
#notification-popup{
  background: rgba(0, 0, 0, 0.6);
  z-index: 1001;
  .confirm-dialog.modal-dialog {
    margin-top: 100px;
    width: 90%;
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
  }
  .modal-body {
    max-height: 300px;
    overflow: auto;
    .notification__list {
      padding-top: 25px;
    }
  }
}
#modal-store-cant-create-order, #modal-store-invalid-step1, #modal-is-check-term-service {
  .modal-dialog {
    margin-top: 10%;
  }
}
#prefecture-not-allowed-modal {
  .modal-dialog {
    margin-top: 120px;
  }
  a {
    &:hover {
      color: #fff;
    }
  }
}
#form-edit-location {
  .input-radio-custom {
    padding-top: 10px;
  }
  .edit-location-steps {
    border-top: 1px solid #e3e3e3;
    background-color: #fafafa;
    padding: 40px 0;
    justify-content: center;
    display: flex;
    .list-step {
      margin: 0;
      padding: 0;
      display: flex;
      list-style: none;
      .list-step__item {
        width: 210px;
        @media (max-width: 714px) {
          width: 100px;
        }
        .list-step__link {
          font-weight: 300;
          color: #1a1a1a;
          position: relative;
          display: block;
          text-align: center;
           &::before {
            content: '';
            width: 100%;
            height: 1px;
            border-top: 1px solid #e3e3e3;
            position: absolute;
            left: 0;
            top: 9px;
            z-index: 1;
          }
        }
        .list-step__icon {
          display: block;
          color: #e8e8e8;
          font-size: 20px;
          margin-bottom: 16px;
          z-index: 10;
          position: relative;
        }
      }
      .list-step__item--active {
        .list-step__link, .list-step__icon {
          color: #0a76be;
          font-weight: 600;
        }
      }
    }
  }
  .edit-location-questions {
    padding-left: 80px;
    padding-right: 80px;
  }
  .edit-location-info {
    min-height: 350px;
    .form-customize {
      margin-bottom: 15px;
    }
    .datetimepicker.form-customize .ic-datetime {
      position: relative;
      top: 8px;
    }
  }
  .bottom-button {
    text-align: center;
    margin-bottom: 20px;
    padding: 32px 0 40px;
    .button {
      min-width: 270px;
    }
  }
  .violation_file_template {
    margin-bottom: 20px;
    border: 3px solid #000;
  }
  .set-table {
    width: 100% !important;
    max-height: 320px;
    overflow-y: auto !important;
    overflow-x: auto !important;
    border: 2px !important;
    td.form-group {
      min-width: 180px;
    }
  }
  .error-field {
    border: 1px solid red;
  }
  .edit-location-step3 {
    input {
      &::-webkit-input-placeholder {
        color: #ccc;
      }
      &:-ms-input-placeholder {
        color: #ccc;
      }
      &::placeholder {
        color: #ccc;
      }
    }
  }
  #violation-hint {
    .modal-dialog {
      margin-top: 10%;
      @media (min-width: 576px) {
        max-width: 600px;
      }
    }
    .modal-content {
      background: none;
    }
    .modal-body {
      background: none;
    }
    .violation-hint {
      display: block;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

.violation_file_template {
  padding: 50px;
  .header-violation-file {
    margin-bottom: 40px;
  }
  .body-violation-file {
    border: 1px solid #000;
    padding: 40px;
    margin-top: 40px;
    margin-bottom: 40px;
    .row  {
      padding: 15px;
    }
  }
  .columm-name {
  }
  .columm-data {
    color: red;
    border-bottom: 1px solid #000;
  }
  .red-text {
    color: red;
  }
  .sign-violation-file {
    margin-bottom: 40px;
  }
  .notice-violation-file {
    padding: 30px;
    background: #8abee4;
    font-weight: 600;
    font-size: 11px;
    ul {
      list-style: inherit;
      margin-bottom: 0;
      padding-inline-start: 15px;
    }
  }
}

#order-templates {
  .custom-selectbox.show > label {
    top: unset;
  }
  .button--primary {
    margin: 0 20px;
    &:hover {
      color: #fff;
    }
  }
  .btn-operation {
    color: #0a76be;
    border: 1px solid #0a76be;
    padding: 10px;
    margin: 0 5px;
    cursor: pointer;
  }
  .btn-operation.delete {
    border: 1px solid #f5748b;
    color: #f5748b;
  }
  .table-responsive {
    th, td {
      white-space: nowrap;
    }
  }
}

.new-order-template {
  margin-bottom: 60px;

  .button--primary, .button--secondary {
    &:hover {
      color: #fff;
    }
  }

  .form-customize--selectbox {
    margin-bottom: 40px; 
  }

  .form-customize--selectbox.form-error .custom-selectbox__toggle,
    .form-customize--selectbox.form-error .custom-select {
    border-bottom: 1px solid #f5748b;
  }

  .custom-selectbox.show > label {
    top: unset;
  }

  .new-order-template__field {
    padding: 0 24px 24px; 
  }
  
  .new-order-template__form {
    border-top: 1px solid #e3e3e3;
    padding: 40px 0 40px;
  }
  
  .new-order-template__confirm-label {
    margin-top: -12px;
    display: block;
    span {
      color: #f5748b;
    }
  } 
  
  p {
    font-size: 12px; 
  }

  .new-order-template__detail {
    margin: 0 40px; }
  .create-info-order__top-info {
    padding: 40px; }
  .new-order-template__section {
    margin: 0 40px 20px; }
  .new-order-template__section-title {
    font-size: 16px;
    font-weight: 600; }
  @media (max-width: 414px) {
    .new-order-template__detail {
      margin: 0 20px; }
    .create-info-order__top-info {
      padding: 20px; }
    .new-order-template__section {
      margin: 0 20px 10px; }
  }

  .input-disabled {
    pointer-events: none;
    border-radius: 0;
    background: none;
    padding: 8px 0;
    line-height: 17px;
    border-top: 1px solid transparent;
    border-left: 1px solid transparent;
    border-right: 1px solid transparent;
    border-bottom: 1px solid #e5e5e5;
    color: #e5e5e5;
    margin-bottom: 8px;
  }
}
.portion-btns {
  width: 180px;
  text-align: right;
}
.portion-btn {
  width: 180px!important;
  margin-top: 1px;
  margin-bottom: 1px;
}
.space-to {
  padding: 0;
}
#modal-move-to-order-case {
  .modal-body {
    padding-top: 30px;
    padding-bottom: 30px;
  }
  .modal-footer {
    display: block;
  }
}