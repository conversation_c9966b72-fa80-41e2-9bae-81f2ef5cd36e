.order-pic-email {
  margin-top: 73px !important;
  position: relative;
}

.order-pic-tel.has-error {
  margin-top: 60px;
  position: relative;
}

.order-pic-position {
  margin-bottom: 74px;
}

.create-order, #form-create-order, #form-edit-create-order, #modal-review, .usage-confirm__batch-download,
  .group-tag-modal {
  .disabled {
    pointer-events: none;
    background-color: #bbbbbb !important;
  }
  .input-disabled {
    pointer-events: none;
    border-radius: 0;
    background: none;
    padding: 8px 0;
    line-height: 17px;
    border-top: 1px solid transparent;
    border-left: 1px solid transparent;
    border-right: 1px solid transparent;
    border-bottom: 1px solid #e5e5e5;
    color: #e5e5e5;
    margin-bottom: 8px;
  }
}

.order-result {
  .fas.disabled {
    color: #8a8a8a;
    &:hover {
      color: #8a8a8a;
    }
  }
  a.paginations__link.active {
    &:hover {
      color: #fff;
    }
  }
}

.list-day__item:hover {
  cursor: pointer;
}

#modal-delete-order {
  .button {
    min-width: 0px;
  }
}

.create-info-order__remove-btn {
  cursor: pointer;
}

.detail-price-dialog {
  .modal-content {
    margin-top: 80px;
  }
}

@media (max-width: 767px) {
  input.datetimepicker-input[readonly] {
    border-bottom: 1px solid #afafaf !important;
    &:focus {
      border-bottom-color: #0a76be !important;
    }
  }
}

.text-time-adjusment {
  color: #0a76be;
}

.order-step-2 {
  .error--text {
    color: #f5748b;
  }
}

.create-info-new-order__top-info {
  padding: 20px;
  .create-info-new-order__special-request {
    padding: 20px 0px;
  }
}
.list-orders-wrap {
  margin-left: -15px;
  margin-right: -15px;
  .label-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    @media (max-width: 767px) {
      flex-direction: column-reverse;
      justify-content: flex-start;
      align-items: flex-start;
    }
    > label {
      color: #888;
      font-weight: 600;
    }
  }
}
.list-orders-wrap-mobile {
  .order-table {
    max-height: inherit !important;
    overflow: inherit !important;
    .table {
      overflow-x: inherit !important;
      width: auto;
      tbody {
        td {
          padding: 14px 0px;
          &:first-child {
            padding-left: 5px;
          }
          input {
            padding-left: 5px;
          }
        }
        .item-remove {
          width: 25px;
          padding-top: 23px;
        }
        .item-date {
          width: 55px;
          div {
            line-height: 14px;
            height: 40px;
            vertical-align: middle;
            display: table-cell;
            width: 60px;
            text-align: center;
          }
        }
        .item-peoples {
          width: 55px;
        }
        .item-working-hours {
          width: auto;
        }
        .item-break-time {
          width: auto;
        }
        .item-add-more {
          width: 42px;
          > a:not(:first-child) {
            margin-top: 16px;
          }
          a:first-child img{
            width: 14px;
          }
        }
        .item-add-more.form-error {
          .rest-icon {
            border: 1px solid #f5748b;
          }
        }
      }
    }
  }
}
.order-table {
  .top-10 {
    padding-top: 10px !important;
  }
  .form-group {
    margin-bottom: 0;
  }
  input[type="text"] {
    padding-right: 0px;
  }
  background: #fff;
  &.table-scroll {
    max-height:  780px;
    overflow-y: scroll;
  }
  @media (max-width: 1326px) {
    overflow: scroll;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
  }
  &::-webkit-scrollbar {
    height: 8px;
    width: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background: #dbdbdb;
    border-radius: 2px;
  }
  .table {
    border: 1px solid #e3e3e3;
    position: relative;
    color: #373737;
    @media (max-width: 991px) {
      display: block;
      width: 1200px;
      padding-bottom: 50px;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }
    >thead >tr >th{
      position: sticky;
      top: 0;
      width: 100%;
      background: #fff;
      z-index: 2;
    }
    .item-numeric {
      width: 30px;
      padding-top: 23px;
    }
    .item-remove {
      width: 57px;
      padding-top: 23px;
      a {
        color: #373737;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
      }
    }
    .item-date {
      width: 140px;
    }
    .item-peoples {
      width: 125px;
    }
    .item-working-hours {
      width: 30%;
    }
    .item-break-time {
      width: 30%;
    }
    .item-add-more {
      width: 5%;
      a {
        color: #373737;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
      }
    }
    thead {
      th {
        border: 0;
        font-weight: 700;
        text-align: center;
        padding: 19px 10px;
        white-space: nowrap;
        &.item-numeric {
          font-size: 14px;
          font-weight: 500;
        }
        &.item-peoples {
          > img {
            display: inline-block;
            width: 18px;
            margin-top: -7px;
          }
        }
        &.item-break-time {
          > img {
            display: inline-block;
            width: 18px;
            margin-top: -7px;
          }
        }
      }
    }
    tbody {
      input {
        height: 40px;
      }
      td {
        border-top: 0;
        text-align: center;
        padding: 14px 10px;
        &.item-date {
          padding: 14px 0;
          > input {
            display: inline-block;
            width: 125px;
            background: transparent;
            color: #373737;
            border-radius: 2px;
            padding:  10px 0;
            border: 0;
            height: 40px;
            text-align: center;
            font-size: 14px;
          }
        }
        &.item-peoples {
          .col-4 {
            padding: 0px;
          }
          .quantity-input {
            position: relative;
            .input-group-button {
              position: absolute;
              top: 0;
              right: -3px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: flex-end;
              .quantity-input__btn {
                width: 20px;
                height: 20px;
                padding: 0;
                line-height: 1;
                font-size: 12px;
                border-color: #ced4da;
                background: #fff;
                color: #888;
                border-radius: 0;
                opacity: 1;
                &.quantity-input--plus {
                  border-top-right-radius: 3px;
                  border-bottom: 0;
                  &:hover {
                    border-color: #0a76be;
                    color: #fff;
                    background: #0a76be;
                  }
                }
                &.quantity-input--minus {
                  border-bottom-right-radius: 3px;
                  &:hover {
                    border-color: #0a76be;
                    color: #fff;
                    background: #0a76be;
                  }
                }
              }
            }
            .input-group-field {
              min-width: 100px;
              width: 100%;
              margin: 0;
              padding: 10px 22px 10px 10px;
              background: #fff;
              border-color: #f0f0f0;
              border-radius: 3px;
            }
          }
        }
        &.item-working-hours {
          div {
            padding: 0px;
          }
          .datetimepicker .date {
            .input-append .datetimepicker-input.custom-color {
              background: #fff;
              padding: 10px 30px 10px 10px;
              text-align: left;
              &::-webkit-input-placeholder {
                font-size: 14px;
                line-height: 18px;
              }
            }
          }
          .datetimepicker.form-group.form-error .date {
            .input-append .datetimepicker-input.custom-color {
              border: 1px solid #f05151;
            }
          }
          .ic-eq {
            margin-left: -7px;
          }
          .datetimepicker .input-append .input-field-icon {
            background: #fff;
          }
        }
        &.item-break-time {
          div {
            padding: 0px;
          }
          .time-layout {
            &:not(:first-child) {
              margin-top: 16px;
            }
            .form-customize {
              .date .input-append {
                width: 100%;
              }
              .form-control {
                background: #fff;
                padding: 10px 30px 10px 10px;
                text-align: left;
              }
              .input-append .input-field-icon {
                background: #fff;
              }
            }
          }
          .datetimepicker.form-group.form-error .date {
            .input-append .datetimepicker-input.custom-color {
              border: 1px solid #f05151;
            }
          }
        }
        &.item-add-more {
          > a {
            width: 40px;
            height: 40px;
            border-radius: 3px;
            &:not(:first-child) {
              margin-top: 13px;
            }
          }
        }
      }
      .order-time-block > td {
        border-top: 1px solid #e3e3e3;
      }
      .order-time-block.tr-error.js-add-tr {
        > td {
          text-align: left;
          border-top: 0;
        }
        .row-error {
          color: #f05151;
          &:not(last-child) {
            margin-bottom: 5px;
          }
        }
      }
    }
  }
  .bootstrap-datetimepicker-widget.dropdown-menu {
    width: 200px;
    padding: 24px !important;
    .list-unstyled {
      margin-bottom: 0px;
      .table-condensed {
        width: 100%;
      }
    }
  }
  .datetimepicker .timepicker table td {
    padding: .5rem .3rem;
  }
  .datetimepicker .timepicker .timepicker-date, .datetimepicker .timepicker .timepicker-minute, .datetimepicker .timepicker .timepicker-hour {
    font-size: 18px;
    line-height: 18px;
    padding: 0;
  }
}
.create-job-form__midle {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 868px;
  margin-bottom: 40px;
  padding: 0px 0px 0px 10px;
  @media (max-width: 1366px) {
    width: 100%;
  }
  @media (max-width: 767px) {
    flex-direction: column;
    padding: 0px;
  }
  .order-calendar {
    width: 100%;
  }
  .working-info {
    width: calc(60% - 16px);
    @media (min-width: 992px) and (max-width: 1199px) {
      width: calc(64% - 8px);
    }
    @media (min-width: 768px) and (max-width: 992px) {
      width: calc(64% - 8px);
    }
    @media (max-width: 767px) {
      width: 100%;
    }
  }
  .post-work__form-wrap {
    padding: 24px 27px 24px 25px;
    margin-bottom: 40px;
    @media (min-width: 992px) and (max-width: 1080px) {
      padding: 24px 10px;
    }
    @media (max-width: 767px) {
      padding: 25px 16px;
      margin-bottom: 35px;
    }
  }
  .post-work__radio-content {
    padding-left: 0;
    padding-top: 0;
  }
  .hor-label {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    @media (max-width: 767px) {
      flex-direction: column;
    }
    > label {
      min-width: 125px;
      max-width: 125px;
      margin-right: 16px;
      padding-top: 12px;
      font-size: 13px;
      @media (min-width: 992px) and (max-width: 1040px) {
        margin-right: 2px;
      }
      > img {
        display: inline-block;
        margin-top: -3px;
      }
      &.special-label {
        padding-top: 0;
        @media (max-width: 767px) {
          br {
            display: none;
          }
        }
      }
      @media (max-width: 767px) {
        &.none-mobile-label {
          display: none;
        }
        &.text-right {
          text-align: left !important;
        }
      }
    }
    &.transportation-fee-custom {
      justify-content: flex-end;
    }
  }
  .post-work__quantity {
    .input-group {
      @media (max-width: 767px) {
        margin-bottom: 0;
      }
    }
  }
  .post-work__quantity {
    .quantity-input__btn {
      width: 44px;
      background: #fff;
      border: 1px solid #0a76be;
      color: #0a76be;
      border-radius: 3px;
      &:hover {
        opacity: 0.8;
      }
    }
    .input-group-field {
      text-align: center;
      margin: 0 7px;
      width: 93px;
      border-radius: 3px;
    }
  }
  .custom-selectbox {
    width: 144px;
    @media (min-width: 992px) and (max-width: 1289px) {
      width: 130px;
    }
    @media (max-width: 991px) {
      width: 100px;
    }
    @media (max-width: 767px) {
      width: 170px;
    }
    &.selectbox-versions {
      width: 220px;
      @media (max-width: 1662px) {
        width: 100%;
      }
    }
    .custom-selectbox__toggle {
      height: 42px;
      line-height: 42px;
      @media (min-width: 1200px) and (max-width: 1441px) {
        padding-left: 8px;
      }
    }
  }
  .js-post-order {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    width: 100%;
  }
  .mb-order-type-wrap {
    width: 100%;
    .matchbox-type-custom {
      height: auto !important;
      line-height: 1.7 !important;
    }
    .input-checkbox-custom {
      height: auto;
    }
    .input-checkbox-custom label:before {
      top: 8px;
      border-color: #0a76be;
      border-radius: 2px;
    }
    .input-checkbox-custom input[type='checkbox']:checked ~ label:before {
      border-color: #0a76be;
    }
    .input-checkbox-custom label:after {
      top: 15px;
    }
    .matchbox-type-custom {
      p {
        margin-bottom: 0;
        font-size: 11px;
        &.font-img {
          .icon-box {
            width: 13px;
            margin-right: 2px;
          }
        }
      }
    }
  }
  .input-checkbox-custom.custom-helper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  .salary-form-wrap {
    .form-group {
      margin-bottom: 0;
    }
    .input-field.form-error {
      border: 1px solid transparent;
      .form-control {
        border: 1px solid #ff5100;
        color: #ff5100;
      }
    }
    .input-field {
      .form-control {
        font-size: 12px;
        width: 111px;
      }
    }
  }
  .post-work__salary {
    &.select-salary {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    .custom-selectbox {
      width: 100px;
      margin-right: 16px;
      @media (max-width: 767px) {
        width: 100%;
      }
      .custom-selectbox__toggle {
        height: 42px;
        line-height: 42px;
      }
    }
  }
  .form-extend-field {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 0 !important;
    > span {
      font-size: 11px;
      white-space: nowrap;
    }
    .custom-selectbox {
      margin-bottom: 0 !important;
    }
  }
  .post-work__rating {
    min-width: 50%;
    margin-right: 16px;
    padding-top: 12px;
    @media (max-width: 767px) {
      margin-bottom: 8px;
    }
  }
  .rating-block {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .rate-half {
      width: 30px;
      color: #f1a336;
      margin-right: 4px;
      &.no-event {
        > label {
          color: #888;
        }
      }
      > label {
        float: none;
        color: #f1a336;
        margin: 0;
        &:before {
          content: "\e940";
          font-size: 16px;
          padding: 0;
          position: relative;
        }
      }
    }
    .suffixes {
      padding-left: 8px;
      font-size: 11px;
    }
  }
  .radio-layout-wrap {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    padding: 2px;
    background: #e5e5e5;
    border-radius: 5px;
    .input-radio-custom {
      width: 100%;
      margin-bottom: 0;
      .input-checkbox {
        opacity: 0.011;
        z-index: 100;
      }
      label {
        display: block;
        text-align: center;
        width: 100%;
        height: 100%;
        padding-left: 0;
        margin-bottom: 0;
        color: #373737;
        font-size: 11px;
        font-weight: 700;
        height: 32px;
        line-height: 32px;
        border-radius: 3px;
        &:after, &:before {
          display: none;
        }
      }
      input[type='radio']:checked ~ label {
        background: #0a76be;
        border: 1px solid #0a76be;
        color: #fff;
      }
    }
  }
  .multi-col-custom-wrap {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    width: 100%;
    .form-custom__field {
      width: auto;
      > label {
        min-width: unset;
        padding-top: 0;
        margin-bottom: 0;
      }
      .salary-form-wrap {
        width: 100%;
        .form-group {
          width: 113px;
        }
        .suffix-yen {
          letter-spacing: -2px;
        }
      }
    }
  }
  .single-col-custom-wrap {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    .form-custom__field {
      > label {
        min-width: unset;
        padding-top: 0;
        margin-bottom: 0;
      }
      .salary-form-wrap {
        width: 100%;
        .form-group {
          width: 113px;
        }
      }
    }
  }
  .bonus-checkbox-help {
    position: relative;
    display: inline-block;
    .bonus-checkbox-help-icon {
      display: inline-block;
      width: 15px;
      text-indent: -9999999999999px;
      margin-left: 0px;
    }
    .bonus-checkbox-help-show {
      display: none;
      width: 270px;
      padding: 10px;
      position: absolute;
      bottom: calc(100% + 15px);
      left: -42%;
      transform: translateX(-42%);
      border-radius: 5px;
      background: #fff;
      box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.4);
      @media (max-width: 767px) {
        left: 18px;
      }
      > p {
        margin-bottom: 0;
        font-size: 13px;
      }
      .bonus-checkbox-help-title {
        font-size: 14px;
        font-weight: 600;
        color: #0a76be;
        margin-bottom: 10px;
      }
      &:after, &:before {
        top: 100%;
        left: 47%;
        border: solid transparent;
        content: "";
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
        @media (max-width: 767px) {
          left: 41%;
        }
      }
      &:after {
        border-color: rgba(255, 255, 255, 0.3);
        border-top-color: #ffffff;
        border-width: 10px;
        margin-left: -10px;
      }
      &:before {
        border-color: rgba(255, 255, 255, 0);
        border-top-color: rgba(0, 0, 0, 0.3);
        border-width: 12px;
        margin-left: -12px;
      }
      &.show {
        display: block;
      }
    }
    @media (min-width: 768px) {
      &:hover{
        .bonus-checkbox-help-show {
          display: block;
        }
      }
    }
  }
}
.create-info-order__time-changable {
  margin-bottom: 40px;
  .time-changable-note {
    margin-bottom: 10px;
  }
}
.create-info-order__member {
  .staff-count-field {
    height: 48px;
  }
  .btn-minus {
    float: right;
  }
}
.switch-wrap {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 2px;
  border-radius: 5px;
  background: #e5e5e5;
  .switch-multi,
  .switch-range,
  .switch-time-changable,
  .switch-not-time-changable {
    width: 100%;
    height: 42px;
    line-height: 29px;
    padding: 7px 6px;
    text-align: center;
    color: #373737;
    border-radius: 3px;
    font-size: 13px;
    font-weight: 700;
    cursor: pointer;
    &.active {
      color: #fff;
      background: #0a76be;
    }
  }
}
.order-calendar {
  .order-calendar__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
  }
  .calendar-field {
    background: #fff;
    border: 1px solid #e3e3e3;
  }
  .switch-calendar {
    padding: 7px 6px;
    .switch-wrap {
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      padding: 2px;
      border-radius: 5px;
      background: #e5e5e5;
      .switch-multi,
      .switch-range {
        width: 100%;
        height: 42px;
        line-height: 29px;
        padding: 7px 6px;
        text-align: center;
        color: #373737;
        border-radius: 3px;
        font-size: 13px;
        font-weight: 700;
        cursor: pointer;
        &.active {
          color: #fff;
          background: #0a76be;
        }
      }
    }
  }
  .btn-action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 10px 10px;
    .btn-clear {
      min-width: 60px;
      width: 60px;
      padding: 0 10px;
      border: 1px solid #0a76be;
      color: #0a76be;
      background: #fff;
      border-radius: 2px;
      font-size: 11px;
      font-weight: 700;
    }
    .label-info {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      font-size: 12px;
      color: #888;
      font-weight: 600;
      @media (min-width: 768px) and (max-width: 1080px) {
        font-size: 10px;
      }
      .label-total-orders {
        margin-right: 20px;
        @media (min-width: 992px) and (max-width: 1080px) {
          margin-right: 10px;
        }
      }
      .label-total-orders,
      .label-total-partners {
        span {
          font-size: 16px;
          color: #000;
        }
      }
    }
  }
}

#scroll-calendar, #multi-scroll-calendar {
  width: 100%;
  height: 680px;
  overflow-y: scroll;
  padding: 10px 0 0;
  background: #fff;
  &.ie-accessible {
    height: auto;
  }
  @media (min-width: 768px) and (max-width: 991px) {
    height: 517px;
  }
  @media (max-width: 767px) {
    height: 450px;
  }
  @media (max-width: 374px) {
    height: 359px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
  }
  &::-webkit-scrollbar {
    height: 8px;
    width: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background: #dbdbdb;
    border-radius: 2px;
  }
  .ui-datepicker-inline {
    width: 100% !important;
    display: flex !important;
    padding: 8px 0 !important;
    flex-direction: column;
    .ui-datepicker-group {
      width: 100%;
      height: 100%;
      padding: 0 24px;
      &:not(:last-child) {
        margin-bottom: 16px;
      }
      @media (min-width: 992px) and (max-width: 1441px) {
        padding: 0 8px;
      }
      @media (max-width: 991px) {
        padding: 0 8px;

      }
    }
  }
  .ui-datepicker-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .ui-datepicker-prev,
    .ui-datepicker-next {
      font-weight: 700;
      padding: 8px 16px;
      cursor: pointer;
      &.ui-state-disabled {
        color: #999;
        opacity: 0.2;
      }
    }
    .ui-datepicker-prev {
      position: absolute;
      left: 10%;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      .ui-icon {
        display: inline-block;
        font-size: 14px;
        color: transparent;
        &:after {
          content: "<";
          color: #373737;
          font-size: 16px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
    .ui-datepicker-next {
      position: absolute;
      right: 10%;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      .ui-icon {
        display: inline-block;
        font-size: 14px;
        color: transparent;
        &:after {
          content: ">";
          color: #373737;
          font-size: 16px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
    &.ui-corner-right {
      text-align: right;
    }
    .ui-datepicker-title {
      text-align: center;
      font-size: 14px;
      font-weight: 700;
    }
  }
  .ui-datepicker-group {
    .ui-datepicker-header {
      .ui-datepicker-prev,
      .ui-datepicker-next {
        display: none;
        font-weight: 700;
        padding: 8px 16px;
        cursor: pointer;
        &.ui-state-disabled {
          color: #999;
        }
      }
      &.ui-corner-right {
        text-align: right;
      }
      .ui-datepicker-title {
        text-align: center;
        font-size: 14px;
        font-weight: 700;
      }
    }
  }
  .ui-datepicker-calendar {
    width: 100%;
    padding:0 16px;
    > thead {
      > tr {
        width: 100%;
        align-items: center;
        justify-content: space-between;
        > th {
          position: relative;
          text-align: center;
          padding: 0;
          &:after {
            content: "";
            display: block;
            padding-top: 100%;
            border: 1px solid transparent;
          }
          >span {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            font-weight: 700;
            color: #373737;
          }
        }
      }
    }
    >tbody {
      padding: 4px;
      >tr {
        justify-content: space-around;
        align-items: center;
        >td {
          position: relative;
          text-align: center;
          padding: 0;
          cursor: pointer;
          &:after {
            content: '';
            display: block;
            padding-top: 100%;
            border: 1px solid transparent;
          }
          .ui-state-default {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            font-weight: 500;
            color: #373737;
          }
          &.ui-state-disabled {
            font-size: 0;
            .ui-state-default {
              color: #888;
              opacity: 0.5;
            }
          }
          &.ui-datepicker-today {
            &:after {
              background: #fff;
              border: 1px solid #0a76be;
              border-radius: 3px;
            }
            .ui-state-default {
              color: #0a76be;
            }
            &.dp-in-range {
              &:after {
                background: rgba(10, 118, 190, 0.3);
                border: 1px solid rgba(255, 107, 0, 0.4);
                padding-top: calc(100% - 2px);
              }
            }
          }
          &.dp-highlight {
            &.dp-in-range {
              padding: 0;
              &:after {
                background: rgba(10, 118, 190, 0.3);
              }
              &.dp-start-date {
                .ui-state-default {
                  color: #fff;
                }
                &:after {
                  border-radius: 8px 0 0 8px;
                  background: #0a76be;
                }
              }
              &.dp-end-date {
                .ui-state-default {
                  color: #fff;
                }
                &:after {
                  border-radius: 0 8px 8px 0;
                  background: #0a76be;
                }
              }
            }
          }
        }
      }
    }
  }
}
#multi-scroll-calendar {
  .ui-datepicker-calendar {
    >tbody {
      >tr {
        >td {
          padding: 2px;
          &.ui-state-highlight {
            &:after {
              background: #0a76be;
              border: 1px solid #0a76be;
              border-radius: 4px;
            }
            .ui-state-default {
              color: #fff;
              &.ui-state-active {
                &:after {
                  background: #0a76be;
                  border: 1px solid #0a76be;
                  border-radius: 4px;
                }
              }
            }
          }
        }
      }
    }
  }
  .ui-datepicker-group {
    .ui-datepicker-calendar {
      >tbody {
        >tr {
          >td {
            &.ui-state-highlight {
              &:after {
                background: #0a76be;
                border: 1px solid #0a76be;
                border-radius: 4px;
              }
              .ui-state-default {
                color: #fff;
              }
            }
          }
        }
      }
    }
  }
}
.non-collapse-section-title {
  padding: 0;
  height: 50px;
  line-height: 50px;
  font-size: 18px;
  border: 0;
  background-color: #fff;
  position: relative;
  margin: 24px 0 0;
}

.new-orders-bottom {
  .create-order__group-btn {
    padding: 15px 0;
    .button {
      min-width: 200px;
    }
  }
  .button--secondary:hover {
    color: #fff;
  }
}

#modal-new-order-template, #modal-order-template {
  margin-top: 50px;
  .form-error {
    .form-control {
      border: 1px solid #f5748b;
    }
  }
  .error-notice {
    color: #f5748b;
  }
}

#modal-multiday {
  .cell-width-20 {
    width: 20%;
  }
  .cell-width-30 {
    width: 30%;
  }
  .cell-width-40 {
    width: 40%;
  }
}
