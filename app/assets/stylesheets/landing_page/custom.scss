$deep-blue: #073c7d;
$new-deep-blue: #175aa4;
$soft-blue: #205da7;
$light-blue: #0068b7;
$soft-dark-blue: #236b98;
$soft-light-blue: #3393d1;
$sky-blue: #0092b1;
$jean-blue: #7FC2D9;
$light-sky-blue: #00a4af;
$dark-sky-blue: rgba(0, 164, 175, .5);
$dark-color: #4d5b72;
$dark-gray: #B1B8CC;
$soft-gray: #d8d8d8;
$new-soft-gray: #D2D5E6;
$text-bright-color: #fdfdfd;
$text-light-color: rgba(77, 91, 114, 0.5);
$grow: #666666;
$dark-grow: #d2d5e6;
$dark-orange: #ff8f08;
$red-orange: #ff4100;
$dark-medium-orange: rgba(255, 143, 8, .5);
$white: #ffffff;
$pink: #E9899D;
$dark-overlay: rgba(0, 0, 0, 0.5) 82.06%;
$mobile-width: 480px;
$xs-mobile-width: 320px;
$tablet-width: 769px;
$desktop-width: 1024px;

@mixin gadient-blue-button {
  background-image: -webkit-linear-gradient(left, $light-sky-blue, $light-blue);
  background-image: -o-linear-gradient(left, $light-sky-blue, $light-blue);
  background-image: linear-gradient(to right, $light-sky-blue, $light-blue);
  border: 5px solid $dark-sky-blue;
  @include mobile {
    border: 3px solid $dark-sky-blue;
  }
}
@mixin gadient-orange-button {
  background-image: -webkit-linear-gradient(left, $dark-orange, $red-orange);
  background-image: -o-linear-gradient(left, $dark-orange, $red-orange);
  background-image: linear-gradient(to right, $dark-orange, $red-orange);
  border: 5px solid $dark-medium-orange;
  @include mobile {
    border: 3px solid $dark-medium-orange;
  }
}

@mixin white-pink-button {
  background: $white;
  border: 2px solid $pink;
  color: $pink;
  @include mobile {
    border: 2px solid $pink;
  }
}

@mixin pink-white-button {
  background: $pink;
  border: 2px solid $pink;
  color: $white;
  @include mobile {
    border: 2px solid $pink;
  }
}


@mixin background-image-gadient($image, $gradient-x, $gradient-y){ 
  background-image: $image, linear-gradient($gradient-x, $gradient-y);
  background-image: $image, -webkit-gradient(linear, left top, left bottom, from($gradient-x), to($gradient-y));
  background-image: $image, -moz-linear-gradient(top, $gradient-x, $gradient-y);
  background-blend-mode: multiply;
  background-repeat: no-repeat, no-repeat;
  background-position: left center, left top;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}
@mixin background-image-haft-size($image, $gradient-x, $gradient-y){ 
  background-color: $gradient-y;
  background-image: $image;
  background-image: url($image), -moz-linear-gradient(top, $gradient-x 0%, $gradient-x 100%);
  background-image: url($image), -webkit-gradient(linear, left top, left bottom, color-stop(0%, $gradient-x), color-stop(100%, $gradient-x));
  background-image: url($image), linear-gradient(to bottom, $gradient-x, $gradient-x);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=$gradient-x, endColorstr=$gradient-x, GradientType=0);
  background-position: right center;
  background-repeat: no-repeat;
  background-size: 70%;
}
@mixin background-image-fullsize { 
  background-repeat: no-repeat, no-repeat;
  background-position: left center, left top;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  @include mobile {
    -webkit-background-size: auto;
    -moz-background-size: auto;
    -o-background-size: auto;
    background-size: auto;
  }
}
@mixin big-text-size {
  font-weight: bold;
  font-size: 70px;
  line-height: 85px;
  @include tablet {
    font-size: 45px;
    line-height: 60px;
  }
  @include mobile {
    font-size: 25px;
    line-height: 40px;
  }
}

@mixin large-medium-text-size {
  font-size: 40px;
  line-height: 55px;
  @include tablet {
    font-size: 33px;
    line-height: 45px;
  }
  @include mobile {
    font-size: 17px;
    line-height: 25px;
  }
}

@mixin medium-text-size {
  font-weight: bold;
  font-size: 38px;
  line-height: 50px;
  @include tablet {
    font-size: 30px;
    line-height: 45px;
  }
  @include mobile {
    font-size: 20px;
    line-height: 35px;
  }
}
@mixin small-text-size {
  font-weight: bold;
  font-size: 45px;
  line-height: 60px;
  @include tablet {
    font-size: 25px;
    line-height: 40px;
  }
  @include mobile {
    font-size: 14px;
    line-height: 25px;
  }
}
@mixin medium-sentence-size {
  font-size: 20px;
  @include tablet {
    font-size: 25px;
  }
  @include mobile {
    font-size: 14px;
  }
  @include iphone5 {
    font-size: 11px;
  }
}
@mixin small-sentence-size {
  font-size: 15px;
  @include tablet {
    font-size: 20px;
  }
  @include mobile {
    font-size: 10.5px;
  }
  @include iphone5 {
    font-size: 9.5px;
  }
}
@mixin super-small-sentence-size {
  font-size: 13px;
  @include tablet {
    font-size: 17px;
  }
  @include mobile {
    font-size: 9.5px;
  }
}
@mixin flex-centered {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
@mixin iphone5 { @media (max-width: #{$xs-mobile-width}) {
  @content;
 }
}
@mixin mobile { @media (max-width: #{$mobile-width - 1px}) {
  @content;
 }
}
@mixin tablet { @media (max-width: #{$tablet-width - 1px}) {
   @content;
 }
}
@mixin ipad { @media (max-width: #{$desktop-width}) {
   @content;
 }
}
@mixin desktop { @media (min-width: #{$desktop-width + 1px}) {
   @content;
 }
}
body {
  font-family: -apple-system, blinkMacSystemFont, 'Helvetica Neue', 'Segoe UI', 'Hiragino Kaku Gothic ProN', Meiryo,                      /* Windowsのメイリオ */ sans-serif;
  h1, h2 {
    margin: 0;
  }
}
#lawson-marketing {
  #ui-id-1 {
    background: $white;
    width: 65%;
    @include medium-text-size;
    list-style: none;
    padding-left: 10px;
  }
  .not-found-page {
    text-align: center;
    padding: 30px;
    color: $deep-blue;
    h1 {
      margin-bottom: 40px;
    }
  }
  #lawson-marketing-layout {
    margin-bottom: 220px;
    .container {
      @include ipad {
        width: 100%;
      }
    }
    @include mobile {
      margin-bottom: 90px;
    }
    .lawson-marketing-header {
      .header-left-content {
        min-height: 70px;
        @include mobile {
          min-height: 50px;
        }
        .lawson-menu-logo {
          width: 150px;
          @include mobile {
            width: auto;
          }
        }
      }
      .lawson-brand {
        white-space: pre-line;
        font-weight: bold;
        color: $deep-blue;
        font-size: 15px;
        margin-right: 5px;
        @include mobile {
          line-height: 1;
        }
      }
      .workz-menu-logo {
        width: 150px;
        @include mobile {
          width: 110px;
        }
      }
    }
    .lawson-marketing-footer {
      padding: 15px;
      .footer-left {
        text-align: left;
        @include tablet {
          text-align: center;
        }
        a {
          font-size: 20px;
          color: $deep-blue;
          text-decoration: none;
          @include tablet {
            font-size: 14px;
          }
          &:hover {
            font-weight: bolder;
          }
        }
      }
      .footer-right {
        text-align: right;
        @include tablet {
          text-align: center;
        }
        span {
          font-size: 20px;
          font-weight: normal;
          letter-spacing: -0.02px;
          color: $deep-blue;
          @include mobile {
            font-size: 14px;
          }
        }
      }
    }
  }
  .bg-soft-gray {
    background: $new-soft-gray;
    .section-header {
      color: $deep-blue;
      @include big-text-size;
    }
  }
  .bg-white {
    background: $white;
  }
  .bg-soft-blue {
    background: $soft-blue;
  }
  .bg-deep-blue {
    background: $deep-blue;
  }
  ul.inline-list{
    width: 100%;
    float: left;
    li {
      width: auto;
      float: left;
      list-style:none;
    }
  }
  .equal {
    display: flex;
    display: -webkit-flex;
  }
  .equal-height {
    float: left;
    .half-height {
      height: 50%;
    }
  }
  .vertical-center {
    display: flex;
    align-items: center;
  }
  .no-pd-right {
    padding-right: 0;
  }
  .col-4 {
    float: left;
    width: 30%;
  }
  .col-8 {
    float: left;
    width: 70%;
  }
  .col-40-percent {
    float: left;
    width: 40%;
  }
  .col-80-percent {
    float: left;
    width: 80%;
  }
  .sx-full-width {
    @include tablet {
      padding: 0;
    }
  }
  .flex {
    display: flex;
  }
}
.section-intro {
  align-items: center;
  justify-content: center;
  @include background-image-gadient(image-url('staff.png'), $deep-blue, $deep-blue);
  padding-left: 10%;
  padding-right: 10%;
  padding-top: 15%;
  padding-bottom: 15%;
  @include tablet {
    min-height: 185px;
  }
  .introduction-text{
    .lawson-intro {
      color: $white;
      @include big-text-size;
    }
  }
}

.section-top {
  align-items: center;
  justify-content: center;
  @include background-image-gadient(image-url('staff.png'), $dark-overlay, $dark-overlay);
  padding-left: 10%;
  padding-right: 10%;
  padding-top: 13%;
  padding-bottom: 13%;
  @include tablet {
    min-height: 185px;
  }
  .introduction-text{
    .lawson-intro {
      color: $white;
      @include big-text-size;
    }
  }
  .action-buttons {
    padding: 30px 75px 0px 75px;
    @include tablet {
      padding: 30px 50px 0px 50px;
    }
    @include mobile {
      padding: 20px 45px 0px 45px;
    }
    a.action-button {
      text-decoration: none;
      border-radius: 100px;
      display: inline-block;
      padding: 15px 30px;
      width: 40%;
      margin-left: 1%;
      margin-right: 1%;
      font-size: 20px;
      @include tablet {
        font-size: 15px;
      }
      @include mobile {
        font-size: 12px;
        padding: 5px 10px;
        width: 47%;
      }
      @include iphone5 {
        font-size: 11px;
      }
      &.web-button {
        @include white-pink-button;
        -webkit-background-clip: padding-box; /* for Safari */
        background-clip: padding-box; /* for IE9+, Firefox 4+, Opera, Chrome */
      }
      &.call-button {
        @include pink-white-button;
        -webkit-background-clip: padding-box; /* for Safari */
        background-clip: padding-box; /* for IE9+, Firefox 4+, Opera, Chrome */
      }
    }
  }
}
.section-sp-showcase {
  padding-top: 5%;
  padding-bottom: 5%;
  .section-header {
  }
  .smartphone-showcase {
    padding: 0 5%;
    iframe {
      width: 100%;
      height: 410px;
      border: none;
      @include mobile {
        height: 160px;
      }
    }
    .smartphone-showcase-img {
      width: 100%
    }
  }
  .showcase-description {
    color: $deep-blue;
    @include medium-text-size;
  }
}
#marketing-job {
  .section-sp-showcase {
    background: $dark-gray
  }
}
.section-order-cases {
  padding: 15px;
  @include mobile {
    padding-bottom: 15px;
  }
  .not-found {
    @include small-sentence-size;
    text-align: center;
    padding: 30px;
  }
  .order-case {
    margin-bottom: 10px;
    border-radius: 5px;
    .department-name {
      padding: 0px;
      border-radius: 5px 0 0 0;
      position: relative;
      padding-top: 10px;
      padding-bottom: 10px;
      padding-left: 5px;
      padding-right: 5px;
      color: $white;
      @include medium-sentence-size;
      @include mobile {
        font-size: 17px;
      }
      span {
        width: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        color: $white;
        @include medium-sentence-size;
        @include mobile {
          font-size: 17px;
        }
        // @include iphone5 {
        //   position: inherit;
        // }
      }
    }
    .station-info {
      border-radius: 0 0 0 5px;
      position: relative;
      .vertical-content-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        width: 100%;
        padding-left: 10px;
        padding-right: 10px;
        @include mobile {
          padding: 5px;
          padding-bottom: 2px;
        }
        div{
          color: $white;
          &.station-name{
            @include small-sentence-size;
            @include mobile {
              font-size: 14px;
              line-height: 1.2;
            }
            @include iphone5 {
              font-size: 13px;
              line-height: 1;
            }
          }
          &.station-time {
            @include super-small-sentence-size;
            @include mobile {
              font-size: 10px;
            }
            @include iphone5 {
              font-size: 12px;
            }
          }
        }
      }
    }
    .order-case-data {
      padding: 10px 15px 10px 0px;
      .order-case-info {
        color: $dark-color;
        height: 100%;
        .col-8 {
          // padding-left: 5px;
        }
        .order-case-job-description {
          font-weight: bold;
          @include medium-sentence-size;
          margin-bottom: 5px;
          @include mobile {
            margin-bottom: 0;
          }
          @include iphone5 {
            margin-bottom: 0;
            font-size: 12px;
          }
        }
        .order-case-requirements {
          @include small-sentence-size;
          margin-bottom: 5px;
          @include mobile {
            font-size: 12.5px;
          }
          @include iphone5 {
            font-size: 11.5px;
            margin-bottom: 0;
          }
        }
        .order-case-salary {
          span {
            border-radius: 5px;
            padding: 5px 7px;
            background: $soft-gray;
            @include small-sentence-size;
            @include mobile {
              font-size: 12.5px;
            }
          }
          margin-bottom: 5px;
        }
        .button-ticket-group {
          @include flex-centered;
          min-height: 120px;
          @include tablet {
            min-height: 140px;
          }
          @include mobile {
            min-height: 70px;
          }
        }
      }
    }
  }
  .showcase-description {
    color: $deep-blue;
    font-weight: bold;
  }
}
.section-departments {
  background-color: $deep-blue;
  background-image: image-url('map.svg');
  @include background-image-fullsize;
  min-height: 185px;
  padding: 5%;
  .lawson-departments {
    color: $white;
    @include big-text-size;
    margin-bottom: 5%;
  }
  .departments-map {
    display: table-cell;
    .inline-list {
      padding-left: 0;
      margin-bottom: 25px;
      @include mobile {
        margin-bottom: 8px;
      }
      li {
        a {
          text-decoration: underline;
          color: $white;
          margin-right: 35px;
          @include small-text-size;
          @include mobile {
            font-size: 15px;
            line-height: 25px;
            margin-right: 19px;
          }
          @include iphone5 {
            font-size: 18px;
            margin-right: 15px;
            line-height: 32px;
          }
        }
      }
    }
  }
}
#marketing-department {
  .section-order-cases {
    padding: 15px;
  }
}
.section-call-to-action {
  border: none;
  box-shadow: none;
  border-top: $jean-blue 3px solid !important;
  padding: 15px;
  @include mobile {
    border: none;
    box-shadow: none;
    border-top: $jean-blue 2px solid !important;
    padding: 5px 15px 5px 15px;
  }
  &.show-fixed {
    position: fixed;
    z-index: 9999;
    width: 100%;
    left: 0;
    right: 0;
    bottom: 0;
  }
  &.hide {
    display: none;
  }
  .lawson-call-text {
    @include medium-text-size;
    color: $deep-blue;
  }
  .action-buttons {
    padding-top: 15px;
    padding-bottom: 15px;
    @include tablet {
      padding-top: 30px;
      padding-bottom: 30px;
    }
    @include mobile {
      padding-top: 5px;
      padding-bottom: 5px;
    }
    a.action-button {
      text-decoration: none;
      border-radius: 20px;
      border: solid 5px transparent;
      color: white;
      display: inline-block;
      padding: 15px 30px;
      width: 40%;
      margin-left: 1%;
      margin-right: 1%;
      @include small-text-size;
      @include tablet {
        font-size: 15px;
      }
      @include mobile {
        padding: 5px 10px;
        width: 47%;
      }
      @include iphone5 {
        font-size: 13px;
      }
      .call-icon, .web-icon {
        width: 25px;
        @include mobile {
          width: 15px;
        }
      }
      &.web-button {
        @include gadient-orange-button;
        -webkit-background-clip: padding-box; /* for Safari */
        background-clip: padding-box; /* for IE9+, Firefox 4+, Opera, Chrome */
      }
      &.call-button {
        @include gadient-blue-button;
        -webkit-background-clip: padding-box; /* for Safari */
        background-clip: padding-box; /* for IE9+, Firefox 4+, Opera, Chrome */
      }
    }
  }
}

.section-contact {
  border: none;
  box-shadow: none;
  border-top: $jean-blue 3px solid !important;
  padding: 15px;
  @include mobile {
    border: none;
    box-shadow: none;
    border-top: $jean-blue 2px solid !important;
    padding: 5px 15px 5px 15px;
  }
  &.show-fixed {
    position: fixed;
    z-index: 9999;
    width: 100%;
    left: 0;
    right: 0;
    bottom: 0;
  }
  &.hide {
    display: none;
  }
  .lawson-call-text {
    @include medium-text-size;
    color: $deep-blue;
  }
  .action-buttons {
    padding-top: 15px;
    padding-bottom: 15px;
    @include tablet {
      padding-top: 30px;
      padding-bottom: 30px;
    }
    @include mobile {
      padding-top: 5px;
      padding-bottom: 5px;
    }
    a.action-button {
      text-decoration: none;
      border-radius: 20px;
      border: solid 5px transparent;
      color: white;
      display: inline-block;
      padding: 15px 30px;
      width: 40%;
      margin-left: 1%;
      margin-right: 1%;
      @include small-text-size;
      @include tablet {
        font-size: 15px;
      }
      @include mobile {
        padding: 5px 10px;
        width: 47%;
      }
      @include iphone5 {
        font-size: 13px;
      }
      .call-icon, .web-icon {
        width: 25px;
        @include mobile {
          width: 15px;
        }
      }
      &.web-button {
       @include white-pink-button;
        -webkit-background-clip: padding-box; /* for Safari */
        background-clip: padding-box; /* for IE9+, Firefox 4+, Opera, Chrome */
      }
      &.call-button {
        @include pink-white-button;
        -webkit-background-clip: padding-box; /* for Safari */
        background-clip: padding-box; /* for IE9+, Firefox 4+, Opera, Chrome */
      }
    }
  }
}

.section-search {
  .search-box{
    margin: 0;
  }
  .search-by-department {
    padding: 45px;
    @include mobile {
      padding: 30px;
    }
    .input-search {
      height: 60px;
      width: calc(100% - 75px);
      width: -moz-calc(100% - 75px);
      width: -webkit-calc(100% - 75px);
      border-radius:  5px 0 0 5px;
      padding-left: 15px;
      border: none;
      float: left;
      margin-bottom: 0;
      @include medium-text-size;
      font-weight: bold;
      @include mobile {
        padding-left: 10px;
        height: 50px;
        width: calc(100% - 60px);
        width: -moz-calc(100% - 60px);
        width: -webkit-calc(100% - 60px);
      }
      &::-webkit-input-placeholder {
        @include medium-text-size;
        font-weight: bold;
        color: $text-light-color;
      }
      &:-ms-input-placeholder {
        @include medium-text-size;
        font-weight: bold;
        color: $text-light-color;
      }
      &:-moz-placeholder {
        @include medium-text-size;
        font-weight: bold;
        color: $text-light-color;
      }
      &::-moz-placeholder {
        @include medium-text-size;
        font-weight: bold;
        color: $text-light-color;
      }
    }
    .btn-search {
      width: 75px;
      height: 60px;
      @include mobile {
        width: 60px;
        height: 50px;
      }
      border-radius: 0 5px 5px 0;
      border: none;
      background-color: $sky-blue;
      .search-btn-icon {
        width: 20px;
        @include mobile {
          width: 8px;
        }
      }
    }
  }
}
.section-logos {
  padding: 25px 45px;
  .body-logo {
    width: 80px;
    height: 80px;
    margin-left: 5%;
    margin-right: 5%;
    @include mobile {
      width: 50px;
      height: 50px;
    }
  }
}
.section-department-show {
  .deparment-image-bg{
    width: 100%;
    @include background-image-fullsize;
    min-height: 400px;
    @include mobile {
      min-height: 131px;
    }
  }
  .deparment-name {
    padding: 25px 45px;
    @include mobile {
      padding: 13px 45px;
    }
    h1 {
      font-weight: bold;
      color: $white;
      font-size: 85px;
      @include tablet {
        font-size: 65px;
      }
      @include mobile {
        font-size: 30px;
      }
    }
  }
}
.section-stack-nav {
  padding-top: 30px;
  padding-bottom: 30px;
  color: $white;
  @include mobile {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .stack-nav {
    padding-left: 15px;
    padding-right: 15px;
    @include mobile {
      padding-left: 10px;
      padding-right: 10px;
    }
    .bread-cum {
      color: $white;
      text-decoration: none;
      @include medium-text-size;
      @include mobile {
        @include small-text-size;
      }
      font-weight: normal;
    }
    a.bread-cum {
    }
    span.bread-cum {
      @include medium-text-size;
      font-weight: normal;
      padding-left: 20px;
      padding-right: 20px;
      @include mobile {
        padding-left: 10px;
        padding-right: 10px;
      }
    }
  }
}
.section-salary-box {
  position: relative;
  @include background-image-haft-size(image-url('staff.png'), $soft-light-blue, $soft-dark-blue);
  padding: 90px;
  @include mobile {
    padding: 37px;
  }
  .over-bg {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 70%;
    background-image: -webkit-linear-gradient(left, $soft-light-blue, $soft-light-blue 50%, rgba(35, 107, 152, 0) 102%);
    background-image: -o-linear-gradient(left, $soft-light-blue, $soft-light-blue 50%, rgba(35, 107, 152, 0) 102%);
    background-image: linear-gradient(to right, $soft-light-blue, $soft-light-blue 50%, rgba(35, 107, 152, 0) 102%);
  }
  .salary-box {
    width: 50%;
    @include mobile {
      width: 60%;
    }
    .yen-salary {
      @include flex-centered;
      border-radius: 5px 0 0 5px;
      color: $white;
      background-color: $new-deep-blue;
      @include big-text-size;
      font-weight: normal;
    }
    .number-salary {
      padding: 5px;
      padding-left: 10px;
      background-color: $white;
      @include small-text-size;
      @include mobile {
        font-size: 16px;
      }
      color: $grow;
      border-radius: 0 5px 5px 0;
    }
  }
}
.section-job-info{
  padding: 40px 20px;
  background-color: $new-soft-gray;
  color: $dark-color;
  @include small-sentence-size;
  @include mobile {
    @include small-text-size;
    font-size: 16px;
    font-weight: normal;
  }
  font-weight: normal;
  h5 {
    @include small-sentence-size;
    font-weight: bold;
    @include mobile {
      @include small-text-size;
      font-size: 16px;
      font-weight: bold;
    }
  }
}
.section-job-title {
  padding: 15px;
  @include mobile {
    padding-left: 10px;
    padding-right: 10px;
  }
  @include large-medium-text-size;
  color: $deep-blue;
}
#paginator .pagination {
  margin-top: 0;
}
.pagination-section {
  padding-left: 15px;
  padding-right: 15px;
  @include ipad {
    padding: none;
  }
}
