@charset "UTF-8";

$grey: #6A6B6D;
$blue: #89ABE3;
$red: #DD0E1B;
$white: #fff;
$black: #000;
$orange: #e67e22;
$green: #2ecc71;

$color-london: #C00;
$color-losangels: #f90;
$color-sanfrancisco: #ff4000;
$color-boston: #036;
$color-newyork: #f44336;
$color-us: #383c3c;
$color-sydney: #0085B2;
$color-tokyo-summer: #390;
$color-tokyo-winter: #8B4513;
$color-osaka-summer: #c6d219;
$color-exchange: #877f3d;

//Darken
$darken-grey: #2A2B2D;
$darken-blue: #3A50C9;

//Darkest
$darkest-grey: #1A1B1D;
$darkest-blue: #1c2b36;

//Lighten
$lighten-grey: #9A9B9D;
$lighten-red: lighten($red, 10%);

//Lighter

//Lightest
$lightest-grey: #d8d8d8;
$lightest-blue: #c0ccd5;
$lightest-green: #e9f5e6;

//Inherits
$grey-lightest: $lightest-grey;
$grey-light: $lighten-grey;

$grey-dark: $darken-grey;
$grey-darkest: $darkest-grey;

$blue-lightest: $lightest-blue;
$blue-dark: $darken-blue;
$blue-darkest: $darkest-blue;

@mixin prefixer(
  $property,
  $value,
  $prefixes: ()
) {
  @each $prefix in $prefixes {
    #{"-" + $prefix + "-" + $property}: $value;
  }
  #{$property}: $value;
}

@mixin animation-delay($times...) {
  //@include _bourbon-deprecate-for-prefixing("animation-delay");

  @include prefixer(animation-delay, $times, webkit moz spec);
}


@mixin animation($animations...) {
  //@include _bourbon-deprecate-for-prefixing("animation");

  @include prefixer(animation, $animations, webkit moz spec);
}

.page-overlay {
  background: rgba(0, 0, 0, 0.5);
  display: inherit;
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  z-index: 9999;
  bottom: 0;
  &.is-black {
    background: rgba(0, 0, 0, 0.6);
  }
  .load-jobs {
    width: 50px;
    height: 40px;
    position: fixed;
    text-align: center;
    top: 50%;
    left: 50%;
    margin: -30px 0 0 -30px;
    img {
      width: 50px;
    }
  }
}

.page-overlay-footer {
  float: left;
  height: 67px;
  width: 100%;
  text-align: center;
  position: relative;
}

.page-overlay-footer.hide {
  display: none;
}

#pageloaderfooter.animate {
  width: 40px;
  height: 40px;
  text-align: center;
  left: 0;
  position: absolute;
  right: 0;
  margin: auto;
  top: 20px;

  @include animation(loaderrotate 2.0s infinite linear);

  .dot1, .dot2 {
    width: 60%;
    height: 60%;
    display: inline-block;
    position: absolute;
    top: 0;
    background-color: $blue;
    border-radius: 100%;

    @include animation(loaderbounce 2.0s infinite ease-in-out);
  }

  .dot2 {
    top: auto;
    bottom: 0px;
    @include animation-delay(-1.0s);
    background-color: $lighten-red;
  }
}

.background-load {
  position: fixed;
  width: 25%;
  min-width: 220px;
  height: 3%;
  left: 0;
  text-align: center;
  right: 0;
  bottom: 0;
  margin: auto;
}

.page-load {
  top: 0px;
  background-color: gray;
}

.title-load {
  top: -160px;
  font-size: 23px;
  font-weight: bold;
}

.percent-load {
  top: -120px;
}

.background-progress {
  background-color: gray;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#pageprogress-percent {
  font-size: 40px;
  font-weight: bold;
  color: $darken-blue;
}

.percent {
  width: 0%;
  background-color: coral;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.light-green {
  background-color: $green;
}

#pageloader.hide {
  display: none;
}

#pageloader.animate {
  width: 40px;
  height: 40px;
  position: fixed;
  text-align: center;
  top: 50%;
  left: 50%;
  margin:-30px 0 0 -30px;

  @include animation(loaderrotate 2.0s infinite linear);

  .dot1, .dot2 {
    width: 60%;
    height: 60%;
    display: inline-block;
    position: absolute;
    top: 0;
    background-color: $blue;
    border-radius: 100%;

    @include animation(loaderbounce 2.0s infinite ease-in-out);
  }

  .dot2 {
    top: auto;
    bottom: 0px;
    @include animation-delay(-1.0s);
    background-color: $lighten-red;
  }
}

@-webkit-keyframes loaderrotate { 100% { -webkit-transform: rotate(360deg) }}
@keyframes loaderrotate { 100% { transform: rotate(360deg); -webkit-transform: rotate(360deg) }}

@-webkit-keyframes loaderbounce {
  0%, 100% { -webkit-transform: scale(0.0) }
  50% { -webkit-transform: scale(1.0) }
}

@keyframes loaderbounce {
  0%, 100% {
    transform: scale(0.0);
    -webkit-transform: scale(0.0);
  } 50% {
    transform: scale(1.0);
    -webkit-transform: scale(1.0);
  }
}
