class MasterDatasController < ApplicationController
  before_action :only_admin_can_use, only: [:selectbox_limit_datas,
    :selectbox_corporations_by_departments, :selectbox_staffs_by_departments,
    :selectbox_registrations_by_prefectures]

  def bank_branches
    render json: service.bank_branches
  end

  def selectbox_limit_datas
    data = service.selectbox_limit_datas
    return if data.nil?

    render json: data
  end

  def selectbox_corporations_by_departments
    render json: service.selectbox_corporations_by_departments
  end

  def selectbox_staffs_by_departments
    render json: service.selectbox_staffs_by_departments
  end

  def selectbox_registrations_by_prefectures
    render json: service.selectbox_registrations_by_prefectures
  end

  private
  def service
    MasterDatasService.new(self)
  end

  def only_admin_can_use
    return if admin_signed_in?

    render json: {status: false, results: []}
  end
end
