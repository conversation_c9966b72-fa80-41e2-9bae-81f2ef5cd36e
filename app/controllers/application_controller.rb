class ApplicationController < ActionController::Base
  include <PERSON><PERSON>elper
  include StoreLocationHelper
  include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  before_action :authorize_mini_profiler

  LAYOUTS = {
    "staff" => "layouts/staff/base"
  }
  CORPORATION = "corporation"

  protect_from_forgery with: :exception

  delegate :check_domain, :prevent_page_caching, :set_locale, to: :application_service

  before_action :check_is_chromium_browser, :set_locale
  before_action :prevent_page_caching, :check_domain

  rescue_from CanCan::AccessDenied do
    redirect_to admin_root_path, alert: I18n.t("activerecord.errors.messages.access_denied")
  end

  private

  def save_utm_tags
    save_cookie_by_param :utm_source
    save_cookie_by_param :utm_medium
    save_cookie_by_param :utm_content
    save_cookie_by_param :utm_campaign
  end

  def save_cookie_by_param key
    cookies[key] = params[key] if cookies[key].blank? && params[key].present?
  end

  def check_reset_password_token
    redirect_to admin_session_path unless @admin&.reset_password_period_valid?
  end

  def check_user_reset_password_token
    redirect_to user_session_path unless @user&.reset_password_period_valid?
  end

  def check_staff_reset_password_token
    redirect_to staff_root_path unless @staff&.reset_password_period_valid?
  end

  def check_is_chromium_browser
    return if request.subdomain.blank? || Settings.domain.landing_page == request.domain ||
      Settings.domain.api == request.subdomain

    browser = Browser.new request.user_agent

    return if ie_accessible? || owner_safari_accessible?(browser)

    namespace = Settings.domain.find{|_, domain| domain == request.subdomain}&.first

    render "shared/#{namespace}_not_chrome", layout: "#{namespace}/unauthenticate_layout" unless
      browser.chrome?
  end

  def render_error error_code, _format = params[:format]
    params[:locale] = I18n.locale
    render "#{prefix}/errors/#{error_template_by(error_code)}",
      status: error_code, layout: LAYOUTS[prefix], formats: :html
  end

  def redirect_if_logged_in
    case request.subdomain
    when /^#{Settings.domain.admin}/
      redirect_to admin_root_path if admin_signed_in?
    when /^#{Settings.domain.corporation}/
      redirect_to corporation_root_path if user_signed_in?
    else
      redirect_to staff_root_path if staff_signed_in?
    end
  end

  def ie_accessible?
    Rails.env.test? || Settings.domain.staff == request.subdomain || Settings.ie&.accessible
  end

  def owner_safari_accessible? browser
    browser.safari? && request.subdomain == Settings.domain.corporation
  end

  def application_service
    ApplicationService.new self
  end

  def is_mobile_view?
    !!cookies[:is_workz_web_view]
  end

  def authorize_mini_profiler
    return unless Rails.env.staging? && current_admin.present?

    Rack::MiniProfiler.authorize_request
  end
end
