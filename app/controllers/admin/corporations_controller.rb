class Admin::CorporationsController < Admin::BaseController
  CAN_READ_METHOD = %w(edit load_corporation_groups)
  CAN_READ_BY_ROLE = {
    user: :load_corporation_groups_and_locations
  }
  authorize_resource except: CAN_READ_METHOD + CAN_READ_BY_ROLE.values
  before_action :authorize_read, only: CAN_READ_METHOD
  before_action ->{authorize_read User}, only: CAN_READ_BY_ROLE[:user]

  def index
    @corporations, data = service.index
    respond_to do |format|
      format.html
      format.json do
        render json: data
      end
    end
  end

  def new
    @corporation, @admins, @billing_closed_groups, @transaction_statuses, @industries,
    @postal_codes = service.new
  end

  def create
    render json: service.create
  end

  def edit
    @owner_code, @corporation, @admins, @billing_closed_groups, @transaction_statuses,
    @industries, @postal_codes = service.edit
  end

  def update
    render json: service.update
  end

  def destroy
    render json: service.destroy
  end

  def check_name_unique
    render json: service.check_name_unique
  end

  def load_corporation_groups_and_locations
    render json: service.load_corporation_groups_and_locations
  end

  def load_corporation_groups
    render json: service.load_corporation_groups
  end

  def load_job_categories
    render json: service.load_job_categories
  end

  private
  def service
    Admin::CorporationsService.new(self)
  end
end
