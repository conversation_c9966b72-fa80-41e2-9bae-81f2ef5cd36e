class Admin::StaffsController < Admin::BaseController
  CAN_READ_METHOD = %w(edit load_staff_version_history export_xlsx view_staff_infos)
  authorize_resource except: CAN_READ_METHOD
  before_action :authorize_read, only: CAN_READ_METHOD
  include AsyncDownload
  delegate :bulk_update_contract_status, :send_verification_email,
    :send_remind_login_mail, :create_staff_contract_history, to: :service

  rescue_from CustomExceptions::ReviewingStaffNotfound do
    errors = I18n.t("admin.errors.reviewing_staff_not_found")
    flash[:error] = errors
    json_response = {
      status: false,
      errors: errors,
      reload: true
    }

    render json: json_response
  end

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def new
    load_data_render_view(service.new)
  end

  def create
    render json: service.create
  end

  def edit
    response = service.edit
    return render_error(404) if response.blank?

    load_data_render_view(response)
    @staff_complaints = response[:staff_complaints]
    @last_issue_date = response[:last_issue_date]
    @staff_mail_histories = response[:staff_mail_histories]
    @bank_branches = response[:bank_branches]
    @verify_account_status = response[:verify_account_status]
    @staff_violation_dates = response[:staff_violation_dates]
    @compare_reviewing_and_master_staff = response[:compare_reviewing_and_master_staff]
    @staff_last_contract = response[:staff_last_contract]
  end

  def update
    render json: service.update
  end

  def profile
    render json: service.profile
  end

  def notes
    render json: service.notes
  end

  def export
    render json: service.export
  end

  def export_xlsx
    render json: service.export_xlsx
  end

  def load_staff_version_history
    response = service.load_staff_version_history
    return unless response

    render json: response
  end

  def approve_request_change_profile
    render json: service.approve_request_change_profile
  end

  def reject_request_change_profile
    render json: service.reject_request_change_profile
  end

  def mail_history_detail
    return render_error(404) unless request.xhr?

    response = service.mail_history_detail
    return unless response

    render json: response
  end

  def load_corporation_by_location
    response = service.load_corporation_by_location
    return unless response

    render json: response
  end

  def view_staff_infos
    render json: service.view_staff_infos
  end

  def check_admin_password
    render json: service.check_admin_password
  end

  def otp
    render json: service.otp
  end

  def more_mail_histories
    render json: service.more_mail_histories
  end

  private
  def service
    Admin::StaffsService.new self
  end

  def load_data_render_view response
    @staff = response[:staff]
    @banks = response[:banks]
    @bank_branches = response[:bank_branches]
    @types = response[:types]
    @retirement_reasons = response[:retirement_reasons]
    @businesses = response[:businesses]
    @prefectures = response[:prefectures]
    @nationalities = response[:nationalities]
    @japan = response[:japan]
    @reviewing_staff = response[:reviewing_staff]
    @reviewing_staff_registration_answer = response[:reviewing_staff_registration_answer]
    @uniform_sizes = response[:uniform_sizes]
    @pant_sizes = response[:pant_sizes]
    @shoes_sizes = response[:shoes_sizes]
    @japanese_levels = response[:japanese_levels]
    @residence_statuses = response[:residence_statuses]
    @residence_permissions = response[:residence_permissions]
    @staff_versions = response[:staff_versions]
    @registration_histories = response[:registration_histories]
    @relationships = response[:relationships]
    @current_rank = response[:current_rank]
    @current_level = response[:current_level]
    @is_from_release_entry_v2 = response[:is_from_release_entry_v2]
    @staff_contract_histories = response[:staff_contract_histories]
    @registration_answer = response[:registration_answer]
    @professions = response[:professions]
    @recent_department = response[:recent_department]
  end
end
