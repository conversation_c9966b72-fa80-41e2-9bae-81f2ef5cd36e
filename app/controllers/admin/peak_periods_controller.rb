class Admin::PeakPeriodsController < Admin::BaseController
  rescue_from(ActiveRecord::RecordInvalid) do |e|
    render json: {status: false, code: 400, message: e.record.errors.messages}
  end

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def create
    render json: service.create
  end

  def destroy
    render json: service.destroy
  end

  def get_by_target_date
    render json: service.get_by_target_date
  end

  private

  def service
    Admin::PeakPeriodsService.new(self)
  end
end
