class Admin::SessionsController < ApplicationController
  include SlashPath

  layout "admin/unauthenticate_layout"

  skip_before_action :verify_authenticity_token, only: [:new, :create]
  before_action :redirect_if_logged_in, only: [:new, :create]

  def new
  end

  def create
    authenticate_admin!
    if service.create
      redirect_to admin_root_path
    else
      redirect_to stored_location_for(:admin) || admin_root_path
    end
  end

  def destroy
    warden.logout :admin
    redirect_to admin_session_path
  end

  private

  def service
    Admin::SessionsService.new(self, cookies)
  end
end
