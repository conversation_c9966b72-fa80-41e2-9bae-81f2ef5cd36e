class Admin::UsersController < Admin::BaseController
  CAN_READ_METHOD = %w(edit load_selected_corporation_groups_and_locations)
  authorize_resource except: CAN_READ_METHOD
  before_action :authorize_read, only: CAN_READ_METHOD

  delegate :send_remind_login_mail, to: :service

  def new
    data_render_html(service.new)
  end

  def create
    render json: service.create
  end

  def edit
    data_render_html(service.edit)
  end

  def update
    render json: service.update
  end

  def index
    respond_to do |format|
      format.html do
        data_return = service.index
        @corporations = data_return[:corporations]
        @locations = data_return[:locations]
      end
      format.json do
        render json: service.index
      end
    end
  end

  def destroy
    service.destroy
  end

  def load_selected_corporation_groups_and_locations
    render json: service.load_selected_corporation_groups_and_locations
  end

  def reset_password_user
    render json: service.reset_password_user
  end

  def resend_reset_password_mail
    user = service.resend_reset_password_mail
    redirect_to edit_admin_user_path(user)
  end

  def view_owner_infos
    render json: service.view_owner_infos
  end

  private
  def service
    Admin::UsersService.new self
  end

  def data_render_html response
    @user = response[:user]
    @account = response[:account]
    @corporations = response[:corporations]
  end
end
