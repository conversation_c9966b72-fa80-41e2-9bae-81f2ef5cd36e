class Admin::LocationTimeSheetsController < Admin::BaseController
  include AsyncDownload
  layout "print/base", only: :show

  def index
    @staffs, @locations, @corporations, data = service.index
    respond_to do |format|
      format.html
      format.json do
        render json: data
      end
    end
  end

  def show
    @arrangements, is_blank = service.show
    render_error(404) if is_blank
  end

  def export
    respond_to do |format|
      format.json do
        render json: service.export
      end
    end
  end

  private
  def service
    Admin::LocationTimeSheetsService.new(self)
  end
end
