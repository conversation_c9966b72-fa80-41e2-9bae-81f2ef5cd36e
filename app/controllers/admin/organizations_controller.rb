class Admin::OrganizationsController < Admin::BaseController
  authorize_resource

  def edit
    response = service.edit
    @organization = response[:organization]
    @corporation_group = response[:corporation_group]
  end

  def update
    render json: service.update
  end

  def load_staffs
    render json: service.load_staffs
  end

  private
  def service
    Admin::OrganizationsService.new(self)
  end
end
