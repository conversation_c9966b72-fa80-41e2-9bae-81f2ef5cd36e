class Admin::InterviewsController < Admin::BaseController
  rescue_from(CustomExceptions::TimeRangeError) do |e|
    message = e.message
    render json: {status: false, errors: message}
  end

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def show
    render json: service.show
  end

  def create
    render json: service.create
  end

  def update
    render json: service.update
  end

  def destroy
    render json: service.destroy
  end

  def check_destroy
    render json: service.check_destroy
  end

  def update_admin
    render json: service.update_admin
  end

  def cancel_staff
    render json: service.cancel_staff
  end

  private
  def service
    Admin::InterviewsService.new(self)
  end
end
