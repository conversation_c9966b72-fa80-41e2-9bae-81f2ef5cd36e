class Admin::PayrollsController < Admin::BaseController
  include AsyncDownload

  authorize_resource

  def index
    @rank_level_log, @payroll_log, @payroll, data = service.index
    respond_to do |format|
      format.html do
        @is_calculating = data
      end
      format.json do
        render json: data
      end
    end
  end

  def create
    render json: service.create
  end

  def edit
    @staff, @payroll, @other_payroll = service.edit
  end

  def update
    render json: service.update
  end

  def cancel_calculate
    render json: service.cancel_calculate
  end

  def batch_cancel_calculate
    render json: service.batch_cancel_calculate
  end

  def import_payroll
  end

  def import
    render json: service.import
  end

  def export_staff_infos
    render json: service.export_staff_infos
  end

  def export
    render json: service.export
  end

  def get_job_status
    render json: service.get_job_status
  end

  def perform_calculate
    render json: service.perform_calculate
  end

  def perform_calculate_rank_and_level
    @rank_level_log, ended_at_blank, data = service.perform_calculate_rank_and_level
    return if ended_at_blank

    render json: data
  end

  def check_is_calculating_rank_level
    render json: service.check_is_calculating_rank_level
  end

  def check_is_calculating_or_exist_processing_request
    render json: service.check_is_calculating_or_exist_processing_request
  end

  def check_is_able_to_cancel_payroll
    render json: service.check_is_able_to_cancel_payroll
  end

  def check_is_able_to_perform_for_all_payrolls
    render json: service.check_is_able_to_perform_for_all_payrolls
  end

  def condition_export_payroll
    render json: service.condition_export_payroll
  end

  def export_payroll
    render json: service.export_payroll
  end

  def send_notification
    render json: service.send_notification
  end

  def check_before_send_notification
    render json: service.check_before_send_notification
  end

  def cancel_notification
    render json: service.cancel_notification
  end

  def check_before_cancel_notification
    render json: service.check_before_cancel_notification
  end

  def generate_salary_detail
    data = service.generate_salary_detail
    request.format = "html" if data.nil?
    respond_to do |format|
      format.pdf do
        data
      end
      format.all{render_error 404}
    end
  end

  private

  def service
    Admin::PayrollsService.new(self)
  end
end
