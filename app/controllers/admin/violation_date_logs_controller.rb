class Admin::ViolationDateLogsController < Admin::BaseController
  delegate :download_violation_file, to: :service

  def update_violation_log
    render json: service.update_violation_log
  end

  def upload_violation_file
    render json: service.upload_violation_file
  end

  def delete_violation_file
    render json: service.delete_violation_file
  end

  private

  def service
    Admin::ViolationDateLogsService.new(self)
  end
end
