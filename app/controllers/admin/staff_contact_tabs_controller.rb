class Admin::StaffContactTabsController < Admin::BaseController
  def validate_contact
    render json: service.validate_contact
  end

  def validate_staff_training
    render json: service.validate_staff_training
  end

  def validate_staff_stable_employment
    render json: service.validate_staff_stable_employment
  end

  def validate_staff_career
    render json: service.validate_staff_career
  end

  def validate_staff_complaint
    render json: service.validate_staff_complaint
  end

  private
  def service
    Admin::StaffContactTabsService.new self
  end
end
