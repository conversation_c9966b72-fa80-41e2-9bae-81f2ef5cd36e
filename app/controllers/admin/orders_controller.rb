class Admin::OrdersController < Admin::BaseController
  CAN_READ_METHOD = %w(edit load_corporation_group_data order_branches_details organizations)
  authorize_resource except: CAN_READ_METHOD
  include AsyncDownload
  before_action :authorize_read, only: CAN_READ_METHOD

  def index
    respond_to do |format|
      format.html do
        response = service.index
        @locations = response[:locations]
        @organizations = response[:organizations]
        @corporations = response[:corporations]
        @status_ids = response[:status_ids]
        @order_segment_options = response[:order_segment_options]
        @last_conditions = response[:last_conditions]
        @department = response[:department]
      end
      format.json do
        render json: service.index
      end
    end
  end

  def export
    response = service.export
    return unless response

    render json: response
  end

  def new
    response = service.new
    return render_error(404) unless response

    @corporation_options = response[:corporation_options]
    @order_segment_options = response[:order_segment_options]
    @order = response[:order]
    @order_detail = response[:order_detail]
  end

  def order_branches_details
    render json: service.order_branches_details
  end

  def organizations
    render json: service.organizations
  end

  def locations
    render json: service.locations
  end

  def location_data
    render json: service.location_data
  end

  def load_corporation_group_data
    render json: service.load_corporation_group_data
  end

  def load_location
    render json: service.load_location
  end

  def step_1
    response = service.step_1
    return unless response

    render json: response
  end

  def estimate_order_price
    render json: service.estimate_order_price
  end

  def order_check_midnight
    render json: service.order_check_midnight
  end

  def step_2
    response = service.step_2
    return unless response

    render json: response
  end

  def create
    render json: service.create
  end

  def edit
    load_data_render_edit(service.edit)
  end

  def update
    response = service.update
    return redirect_to response[:redirect] if response[:redirect].present?

    load_data_render_edit(response)
    @errors = response[:errors]
    render :edit
  end

  def destroy
    response = service.destroy
    render json: response
  end

  private

  def load_data_render_edit response
    @order = response[:order]
    @corporation_options = response[:corporation_options]
    @order_segment_options = response[:order_segment_options]
  end

  def service
    Admin::OrdersService.new(self)
  end
end
