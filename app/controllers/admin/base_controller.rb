class Admin::BaseController < ApplicationController
  protect_from_forgery
  before_action :store_admin_location
  before_action :authenticate_admin!
  before_action :check_device_direction

  private

  def creator_params
    {
      creator_id: current_admin.id,
      updater_id: current_admin.id
    }
  end

  def updater_params
    {updater_id: current_admin.id}
  end

  def get_success_message_for model
    I18n.t "common.admin.messages.#{action_name}", model_name: model.class.model_name.human
  end

  def render_error status
    render(file: Rails.root.join("public", "#{status}.html"), layout: false, status: status)
  end

  def store_admin_location
    store_location! :admin
  end

  def check_device_direction
    return if current_admin.can_manage_verification?

    @device = DeviceVerification.find_by(cookie_value: cookies[Settings.device.cookies.key])
    not_is_top_page = !(controller_name == "top_pages" && action_name == "show")

    if @device.nil? || @device&.status_id == "rejected"
      warden.logout :admin
      redirect_to admin_session_path
    elsif @device.status_id == "confirming" && not_is_top_page
      redirect_to admin_root_path
    end
  end

  def current_ability
    @current_ability ||= Ability.new(current_admin)
  end

  def model_name
    self.class.name.gsub("Controller", "").singularize.split("::").last.classify.safe_constantize
  end

  def authorize_read authorize_class = false
    return authorize! :read, class: authorize_class unless model_name && !authorize_class

    authorize! :read, model_name
  end

  def assign_variables result = {}
    result.each do |key, value|
      instance_variable_set("@#{key}", value)
    end
  end
end
