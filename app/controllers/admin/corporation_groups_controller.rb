class Admin::CorporationGroupsController < Admin::BaseController
  AUTHORIZE_CLASS = Corporation
  CAN_READ_METHOD = %w(edit load_organizations)
  authorize_resource class: AUTHORIZE_CLASS, except: CAN_READ_METHOD
  before_action ->{authorize_read AUTHORIZE_CLASS}, only: CAN_READ_METHOD
  before_action :load_param_search, only: :edit

  delegate :download_violation_file, to: :service

  def index
    return render_error(404) unless request.xhr?

    render json: service.index
  end

  def new
    data = service.new
    @corporation = data[:corporation]
    @corporation_group = data[:corporation_group]
    @admins, @billing_closed_groups, @transaction_statuses, @industries, @group_tags = data[:other_data]
  end

  def create
    render json: service.create
  end

  def edit
    data = service.edit
    @corporation = data[:corporation]
    @corporation_group = data[:corporation_group]
    @corp_gr_violation_date_logs = data[:corp_gr_violation_date_logs]
    @admins, @billing_closed_groups, @transaction_statuses, @industries, @group_tags = data[:other_data]
  end

  def update
    render json: service.update
  end

  def check_name_unique
    render json: service.check_name_unique
  end

  def destroy
    render json: service.destroy
  end

  def load_organizations
    render json: service.load_organizations
  end

  private
  def load_param_search
    @param_search = params[:keyword]
  end

  def service
    Admin::CorporationGroupsService.new(self)
  end
end
