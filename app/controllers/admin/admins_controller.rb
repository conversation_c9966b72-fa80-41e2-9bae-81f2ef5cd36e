class Admin::AdminsController < Admin::BaseController
  CAN_READ_METHOD = %w(edit)

  authorize_resource except: [:edit_password, :update_password] + CAN_READ_METHOD
  before_action :authorize_read, only: CAN_READ_METHOD
  before_action :redirect_if_not_super_admin, except: [:edit_password, :update_password]

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def edit_password
    @account = current_admin.account
  end

  def update_password
    @account, status = service.update_password
    return render "edit_password" unless status

    bypass_sign_in current_admin, :admin
    redirect_to root_path
  end

  def new
    @departments, @admin, @account, @admin_departments, @departments_uncheck = service.new
  end

  def create
    @account, @admin, data = service.create
    render json: data
  end

  def edit
    @departments, @admin, @account, @admin_departments, @departments_uncheck = service.edit
  end

  def update
    @account, @admin, data = service.update
    render json: data
  end

  private

  def service
    Admin::AdminsService.new(self)
  end

  def redirect_if_not_super_admin
    render_error 404 unless current_admin.is_super_admin?
  end
end
