class Admin::TrainingScheduleApplicantsController < Admin::BaseController
  include AsyncDownload

  rescue_from(CustomExceptions::TrainingScheduleIsFullError) do
    flash[:danger] = I18n.t("admin.training_schedule.errors.training_schedule_is_full")
  end

  rescue_from(CustomExceptions::InvalidTrainingScheduleError) do
    flash[:danger] = I18n.t("admin.training_schedule.message.failure")
  end

  rescue_from(CustomExceptions::MissingStaffSelectedError) do
    flash[:danger] = I18n.t("admin.errors.missing_staff_selected")
  end

  def create
    result = service.create
    if result[:status]
      flash[:success] = result[:message]
    else
      flash[:danger] = result[:message]
    end
  end

  def update_status
    result = service.update_status
    if result[:status]
      flash[:success] = result[:message]
    else
      flash[:danger] = result[:message]
    end
  end

  def export
    render json: service.export
  end

  def staff_training_survey_answers
    case request.method
    when "POST"
      render json: service.update_reason_cancel
    else
      render json: service.staff_training_survey_answers
    end
  end

  def staff_histories
    render json: service.staff_histories
  end

  private

  def service
    Admin::TrainingScheduleApplicantsService.new(self)
  end
end
