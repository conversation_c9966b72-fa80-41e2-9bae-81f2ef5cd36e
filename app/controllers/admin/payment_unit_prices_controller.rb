class Admin::PaymentUnitPricesController < Admin::BaseController
  authorize_resource
  include AsyncDownload

  def index
    @staff_payment_unit, @is_updating = service.index
  end

  def import
    service.import
    redirect_to admin_payment_unit_prices_path
  end

  def export
    respond_to do |format|
      format.json do
        render json: service.export
      end
    end
  end

  private

  def service
    Admin::PaymentUnitPricesService.new(self)
  end
end
