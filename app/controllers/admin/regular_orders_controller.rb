class Admin::RegularOrdersController < Admin::BaseController
  def new
    @corporation_options, @order_segment_options, @order, @order_detail = service.new
    render_error(404) if @order.nil?
  end

  def create
    render json: service.create
  end

  def step_1
    data = service.step_1
    return if data.nil?

    render json: data
  end

  def step_2
    data = service.step_2
    return if data.nil?

    render json: data
  end

  def step_3
    @order, @corporation_options, @order_segment_options = service.step_3
  end

  def estimate_order_price
    render json: service.estimate_order_price
  end

  private

  def service
    Admin::RegularOrdersService.new(self)
  end
end
