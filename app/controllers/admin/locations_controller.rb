class Admin::LocationsController < Admin::BaseController
  CAN_READ_METHOD = %w(edit)

  authorize_resource except: CAN_READ_METHOD
  before_action :authorize_read, only: CAN_READ_METHOD
  include AsyncDownload

  def index
    respond_to do |format|
      format.html do
        @corporation_groups = service.index
      end
      format.json do
        render json: service.index
      end
    end
  end

  def new
    response = service.new
    data_render_html response
  end

  def edit
    response = service.edit
    data_render_html response
  end

  def create
    render json: service.create
  end

  def update
    render json: service.update
  end

  def destroy
    render json: service.destroy
  end

  def location_tab
    render json: service.location_tab
  end

  def find_station
    render json: service.find_station
  end

  def load_options_for_select
    render json: service.load_options_for_select
  end

  def export
    render json: service.export
  end

  def locations_by_corporation
    render json: service.locations_by_corporation
  end

  def import_branch
  end

  def import_branches
    render json: service.import_branches
  end

  private
  def service
    Admin::LocationsService.new(self)
  end

  def data_render_html response
    @location = response[:location]
    @corporations = response[:corporations]
    @corporation_groups = response[:corporation_groups]
    @organizations = response[:organizations]
    @departments = response[:departments]
    @job_categories = response[:job_categories]
    @location_pic_types = response[:location_pic_types]
    @location_survey = response[:location_survey]
    @user_location = response[:user_location]
    @priority_staffs = response[:priority_staffs]
    @now_base_price = response[:now_base_price]
    @new_base_price = response[:new_base_price]
    @location_job_categories = response[:location_job_categories]
    @alow_create_category = response[:alow_create_category]
    @stocon_valid_dates = response[:stocon_valid_dates]
  end
end
