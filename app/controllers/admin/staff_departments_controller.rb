class Admin::StaffDepartmentsController < Admin::BaseController
  rescue_from(ActiveRecord::RecordInvalid) do |e|
    messages = e.record.errors.messages&.values&.uniq

    render json: ErrorResponseBlueprint.render(
      {
        message: messages.flatten.join(", ")
      },
      view: :record_invalid
    )
  end

  def index
    staff = Staff.type_staff.find_by(id: params[:staff_id])
    return not_found unless staff

    staff_departments = get_staff_departments(staff)

    render json: {
      status: true,
      staff_departments: StaffDepartmentBlueprint.render_as_json(staff_departments, view: :staff_details)
    }
  end

  def create
    staff = Staff.type_staff.find_by(id: params[:staff_id])
    return not_found unless staff

    staff_department = staff.staff_departments.create!(staff_department_params)
    create_staff_version(staff)
    message = I18n.t("common.admin.messages.create", model_name: Department.model_name.human)

    render json: json_response(staff_department, message)
  end

  def update
    staff = Staff.type_staff.find_by(id: params[:staff_id])
    return not_found unless staff

    staff_department = staff.staff_departments.find_by(id: params[:id])
    return not_found unless staff_department

    staff_department.update!(staff_department_params)

    create_staff_version(staff.id) if staff_department.saved_changes?

    message = I18n.t("common.admin.messages.update", model_name: Department.model_name.human)

    render json: json_response(staff_department, message)
  end

  def destroy
    staff = Staff.type_staff.find_by(id: params[:staff_id])
    return not_found unless staff

    staff_department = staff.staff_departments.find_by(id: params[:id])
    return not_found unless staff_department

    staff_department.destroy!
    create_staff_version(staff.id)
    message = I18n.t("common.admin.messages.destroy", model_name: Department.model_name.human)

    render json: json_response(staff_department, message)
  end

  def recent_department
    recent_staff_department = StaffDepartments::CurrentDepartmentAtTimeQuery.execute(params[:staff_id], ServerTime.now)
    render json: json_staff_department(recent_staff_department)
  end

  private

  def get_staff_departments staff
    staff.staff_departments.includes(:department)
      .select(:id, :affiliation_date, :department_id)
      .order(affiliation_date: :asc)
  end

  def json_response staff_department, message = nil
    {
      status: true,
      staff_department: StaffDepartmentBlueprint.render_as_json(staff_department, view: :staff_details),
      message: message
    }
  end

  def json_staff_department staff_department
    StaffDepartmentBlueprint.render(
      staff_department,
      view: :staff_details
    )
  end

  def staff_department_params
    params.require(:staff_department).permit(:affiliation_date, :department_id)
  end

  def create_staff_version staff_id
    Staffs::CreateStaffVersionWorker.perform_async(current_admin.id, staff_id)
  end

  def not_found
    render json: ErrorResponseBlueprint.render({}, view: :not_found)
  end
end
