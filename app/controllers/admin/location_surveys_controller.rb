class Admin::LocationSurveysController < Admin::BaseController
  include AsyncDownload
  def export
    respond_to do |format|
      format.json do
        render json: service.export
      end
    end
  end

  def index
    @departments = service.index
  end

  def search_locations
    render json: service.search_locations
  end

  private
  def service
    Admin::LocationSurveysService.new(self)
  end
end
