class Admin::PaymentRequestsController < Admin::BaseController
  authorize_resource
  include AsyncDownload

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def bulk_update
    render json: service.bulk_update
  end

  def export
    respond_to do |format|
      format.json do
        render json: service.export
      end
    end
  end

  def export_history
    respond_to do |format|
      format.json do
        render json: service.export_history
      end
    end
  end

  def load_data_staff
    render json: service.load_data_staff
  end

  def check_transfer_date
    response = service.check_transfer_date
    render json: response if response.present?
  end

  def load_default_transfer_date
    render json: service.load_default_transfer_date
  end

  def list_valid_update_status
    response = service.list_valid_update_status
    render json: response if response.present?
  end

  def check_valid_to_download
    render json: service.check_valid_to_download
  end

  def reschedule
    render json: service.reschedule
  end

  private
  def service
    Admin::PaymentRequestsService.new(self)
  end
end
