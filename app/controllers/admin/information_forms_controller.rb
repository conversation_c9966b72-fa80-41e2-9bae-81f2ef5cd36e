class Admin::InformationFormsController < Admin::BaseController
  include AsyncDownload
  before_action :authorize_screen, only: %w(index export)
  layout "print/base", only: :show

  def index
    @staffs, @locations, @departments, data, @screen_type = service.index
    respond_to do |format|
      format.html do
        render_error(404) if data
      end
      format.json do
        render json: data
      end
    end
  end

  def show
    authorize! :read, :employment_condition
    @arrangement = service.show
  end

  def export
    respond_to do |format|
      format.json do
        render json: service.export
      end
    end
  end

  private
  def authorize_screen
    return redirect_to(admin_root_path) unless params[:search] || params[:screen_type]

    screen_type = params[:screen_type]&.to_sym || JSON.parse(params[:search])["screen_type"]&.to_sym
    authorize! :index, screen_type
  end

  def service
    Admin::InformationFormsService.new(self)
  end
end
