class Admin::AdminNotificationOwnersController < Admin::BaseController
  include DownloadAttachment

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def new
    @departments, @notification, @except_corporation_ids, @except_corporations = service.new
  end

  def create
    render json: service.create
  end

  def edit
    @notification, @department_ids, @except_corporation_ids, @except_corporations = service.edit
  end

  def update
    render json: service.update
  end

  def destroy
    render json: service.destroy
  end

  private
  def service
    Admin::AdminNotificationOwnersService.new(self)
  end
end
