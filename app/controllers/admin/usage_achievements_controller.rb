class Admin::UsageAchievementsController < Admin::BaseController
  AUTHORIZE_CLASS = :usage_achievement
  CAN_READ_METHOD = %w(view_detail relate_corporation_data)

  authorize_resource class: AUTHORIZE_CLASS, except: CAN_READ_METHOD
  before_action ->{authorize_read AUTHORIZE_CLASS}, only: CAN_READ_METHOD
  layout "print/base", only: :view_detail

  def index
    respond_to do |format|
      format.html do
        @corporations = service.index
      end
      format.json do
        render json: service.index
      end
    end
  end

  def relate_corporation_data
    response_data = service.relate_corporation_data
    return false unless response_data

    render json: response_data
  end

  def view_detail
    response_data = service.view_detail
    return render_error(404) unless response_data

    @arrange_billings = response_data[:arrange_billings]
    @arrange_billings_hash = response_data[:arrange_billings_hash]
  end

  private
  def service
    Admin::UsageAchievementsService.new self
  end
end
