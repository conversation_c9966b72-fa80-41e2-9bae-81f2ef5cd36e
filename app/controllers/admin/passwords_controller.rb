class Admin::PasswordsController < ApplicationController
  layout "admin/unauthenticate_layout"
  before_action :redirect_if_logged_in

  def new
  end

  def create
    service.create
    redirect_to admin_finish_send_reset_password_path
  end

  def edit
    data_response = service.edit
    create_instance_data(data_response)
    redirect_to(data_response[:redirect]) if data_response[:redirect].present?
  end

  def update
    data_response = service.update
    create_instance_data(data_response)
    return redirect_to(data_response[:redirect]) if data_response[:redirect].present?

    render :edit
  end

  def finish_setting_password
  end

  private

  def create_instance_data data_response
    @token = data_response[:token]
    @admin = data_response[:admin]
    @account = data_response[:account]
  end

  def service
    Admin::PasswordsService.new(self)
  end
end
