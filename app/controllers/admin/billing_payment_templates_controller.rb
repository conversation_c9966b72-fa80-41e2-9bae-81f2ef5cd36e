class Admin::BillingPaymentTemplatesController < Admin::BaseController
  before_action :authorize_action, except: :load_location_templates

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def show
    data = service.show
    render_error 404 if data.blank?
    respond_to do |format|
      format.html do
        @template = data[:template]
        @corporations = data[:corporations]
        @locations = data[:locations]
      end
      format.json do
        render json: {
          template: data[:template_json],
          corporations: data[:corporations],
          locations: data[:locations]
        }
      end
    end
  end

  def new
    data = service.new
    render_error 404 if data.blank? || data[:template].blank?
    @template = data[:template]
    @corporations = data[:corporations]
    @locations = data[:locations]
  end

  def create
    render json: service.create
  end

  def update
    render json: service.update
  end

  def destroy
    result = service.destroy
    render json: result unless result.nil?
  end

  def load_location_templates
    render json: service.load_location_templates
  end

  private

  def authorize_action
    render_error 404 unless current_admin.can_create_billing_payment_template?
  end

  def service
    Admin::BillingPaymentTemplatesService.new(self)
  end
end
