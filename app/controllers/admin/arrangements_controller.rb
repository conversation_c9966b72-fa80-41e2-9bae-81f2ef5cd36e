class Admin::ArrangementsController < Admin::BaseController
  include AsyncDownload

  before_action :authorize_update, only: %w(temporary_arranges update_break_time
    update_order_case_segment_id temporary_arrange update_working_time_status_id update_basic_field
    send_confirm_arrange_job_mail send_confirm_arrange_job_mail_status job_available
    send_offer_mail send_offer_mail_status copy_portion resend_confirm_arrange_mail
    update_is_store_parking_area_usable
    update_order_case_invoice_target update_billing_payment_template_id)
  before_action :authorize_read, only: %w(index get_statistic_data get_arrange_history
    load_table_headers get_sum_data_columns)
  before_action :authorize_destroy, only: %w(destroy)

  rescue_from(CustomExceptions::MissingArgumentsError) do
    render json: {status: false, error: I18n.t("error.api.common.bad_request")}, status: :bad_request
  end

  def index
    @staffs, @departments = service.load_data
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def new
    @order_segment_options, @corporation_options, @order = service.new
  end

  def export
    render json: service.export
  end

  def temporary_arranges
    render json: service.temporary_arranges
  end

  def update_break_time
    render json: service.update_break_time
  end

  def update_order_case_segment_id
    result = service.update_order_case_segment_id
    render json: result unless result.nil?
  end

  def update_order_case_invoice_target
    render json: service.update_order_case_invoice_target
  end

  def temporary_arrange
    result = service.temporary_arrange
    render json: result unless result.nil?
  end

  def destroy
    result = service.destroy
    render json: result unless result.nil?
  end

  def update_working_time_status_id
    render json: service.update_working_time_status_id
  end

  def update_billing_payment_template_id
    render json: service.update_billing_payment_template_id
  end

  def update_basic_field
    render json: service.update_basic_field
  end

  def update_is_store_parking_area_usable
    render json: service.update_is_store_parking_area_usable
  end

  def send_confirm_arrange_job_mail
    render json: service.send_confirm_arrange_job_mail
  end

  def send_confirm_arrange_job_mail_status
    render json: service.send_mail_status
  end

  def get_statistic_data
    render json: service.get_statistic_data
  end

  def resend_confirm_arrange_mail
    render json: service.resend_confirm_arrange_mail
  end

  def job_available
    render json: service.job_available
  end

  def check_multiple_departments
    render json: service.check_multiple_departments
  end

  def get_selected_offer_job
    render json: service.get_selected_offer_job
  end

  def send_offer_mail
    render json: service.send_offer_mail
  end

  def send_offer_mail_status
    render json: service.send_mail_status
  end

  def get_arrange_history
    render json: service.get_arrange_history
  end

  def get_work_achievement_history
    render json: service.get_work_achievement_history
  end

  def search_staff_for_temporary_arrange
    render json: service.search_staff_for_temporary_arrange
  end

  def search_staff_for_create_arrange
    render json: service.search_staff_for_create_arrange
  end

  def load_table_headers
    render json: service.load_table_headers
  end

  def copy_portion
    render json: service.copy_portion
  end

  def create
    render json: service.create
  end

  def get_sum_data_columns
    render json: service.get_sum_data_columns
  end

  def check_valid_to_export
    render json: service.check_valid_to_export
  end

  def staff_evaluations
    render json: service.staff_evaluations
  end

  def location_evaluations
    render json: service.location_evaluations
  end

  def check_limit_export
    render json: service.check_limit_export
  end

  private
  def authorize_read
    authorize! :read, Arrangement
  end

  def authorize_update
    authorize! :update, Arrangement
  end

  def authorize_destroy
    authorize! :destroy, Arrangement
  end

  def service
    Admin::ArrangementsService.new(self)
  end
end
