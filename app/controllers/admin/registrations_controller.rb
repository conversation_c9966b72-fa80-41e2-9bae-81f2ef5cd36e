class Admin::RegistrationsController < Admin::BaseController
  include AsyncDownload

  rescue_from CustomExceptions::InvalidStaffError do
    flash[:error] = I18n.t("admin.registration.invalid_staff")
    if request.format.symbol == :html
      redirect_to(admin_registrations_path)
    else
      render json: {status: false, redirect_path: admin_registrations_path}
    end
  end

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def edit
    @more_than_60_yo, @staff, @staff_levels, @types, @is_enabled_send_auth_mail,
    @is_enabled_send_modification_request, @nationalities, @prefectures,
    @registration_histories, @relationships, @banks, @staff_edited,
    @residence_statuses, @residence_permissions, @registration_answer,
    @is_enabled_op_send_link_entry, @is_enabled_op_confirm, @is_enabled_reject,
    @updated_profile, @staff_level_options,
    @profession_ids, @professions, @industry_id, @industries, @business_circle_id,
    @business_circles, @staff_recruitment_process = service.edit
  end

  def update
    render json: service.update
  end

  def approve
    render json: service.approve
  end

  def reject
    render json: service.reject
  end

  def check_update_staff
    render json: service.check_update_staff
  end

  def send_authenticate_mail
    data = service.send_authenticate_mail
    render json: data if data.is_a?(Hash)
  end

  def send_update_registered_profile
    data = service.send_update_registered_profile
    render json: data if data.is_a?(Hash)
  end

  def export
    render json: service.export
  end

  def delete_staff_list
    render json: service.delete_staff_list
  end

  def view_registration_infos
    render json: service.view_registration_infos
  end

  def update_registration
    render json: service.update_registration
  end

  private
  def service
    Admin::RegistrationsService.new(self)
  end
end
