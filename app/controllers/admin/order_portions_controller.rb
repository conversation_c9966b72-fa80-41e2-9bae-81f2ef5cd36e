class Admin::OrderPortionsController < Admin::BaseController
  authorize_resource

  def search_staff
    render json: service.search_staff
  end

  def get_current_status
    render json: service.get_current_status
  end

  def update_status
    response = service.update_status
    return unless response

    render json: response
  end

  def batch_update_status
    render json: service.batch_update_status
  end

  def get_list_order_portion
    render json: service.get_list_order_portion
  end

  def get_staff_limited
    render json: service.get_staff_limited
  end

  private
  def service
    Admin::OrderPortionsService.new(self)
  end
end
