class Admin::LocationBillingUnitPricesController < Admin::BaseController
  delegate :download_file_example, to: :service

  def index
    respond_to do |format|
      format.html do
        @corporations = service.index
      end
      format.json do
        render json: service.index
      end
    end
  end

  def delete_all
    render json: service.delete_all
  end

  def import_file
    render json: service.import_file
  end

  private
  def service
    Admin::LocationBillingUnitPricesService.new(self)
  end
end
