class Admin::TrainingSchedulesController < Admin::BaseController
  include AsyncDownload

  rescue_from(CustomExceptions::CreateTrainingScheduleError) do |e|
    error_messages = JSON.parse(e.message)
    render json: JsonResponse.multiple_errors(error_messages)
  end

  rescue_from(CustomExceptions::RestoreTrainingScheduleError) do |e|
    error_messages = JSON.parse(e.message)
    render json: JsonResponse.multiple_errors(error_messages)
  end

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def create
    render json: service.create
  end

  def show
    render json: service.show
  end

  def edit
    result = service.edit
    return render_error(404) if result.nil?

    @training_schedule = result[:training_schedule]
    @training_schedule_applicants = result[:training_schedule_applicants]
    @filled_slots = result[:filled_slots]
    @remaining_slots = result[:remaining_slots]
    @training_staffs = result[:training_staffs]
    @survey_answers = result[:survey_answers]
    @current_admin_id = current_admin.id
    @location_name = result[:location_name]
  end

  def update
    service.update
    redirect_back(fallback_location: root_path)
  end

  def destroy
    render json: service.destroy
  end

  def bulk_destroy
    render json: service.bulk_destroy
  end

  def histories
    render json: service.histories
  end

  def list
    respond_to do |format|
      format.html
      format.json do
        render json: service.list
      end
    end
  end

  def export
    render json: service.export
  end

  def restore
    render json: service.restore
  end

  private
  def service
    Admin::TrainingSchedulesService.new(self)
  end
end
