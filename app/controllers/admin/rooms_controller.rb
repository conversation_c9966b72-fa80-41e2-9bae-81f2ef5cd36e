class Admin::RoomsController < Admin::BaseController
  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def new
    @room = service.new
  end

  def edit
    @room = service.edit
  end

  def create
    status, @room = service.create
    redirect_to admin_rooms_path if status
  end

  def update
    status, @room = service.update
    redirect_to admin_rooms_path if status
  end

  def destroy
    render json: service.destroy
  end

  private

  def service
    Admin::RoomsService.new(self)
  end
end
