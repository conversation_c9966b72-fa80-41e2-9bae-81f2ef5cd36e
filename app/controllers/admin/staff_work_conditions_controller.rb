class Admin::StaffWorkConditionsController < Admin::BaseController
  authorize_resource class: false
  include AsyncDownload
  before_action :check_staff_in_log, only: [:export_download]

  def export
    respond_to do |format|
      format.json do
        render json: service.export
      end
    end
  end

  def export_to_public
    render json: service.export_to_public
  end

  def validate_export_work_condition
    respond_to do |format|
      format.json do
        render json: service.validate_export_work_condition
      end
    end
  end

  private
  def check_staff_in_log
    render_error(404) unless service.export_download?
  end

  def service
    Admin::StaffWorkConditionsService.new self
  end
end
