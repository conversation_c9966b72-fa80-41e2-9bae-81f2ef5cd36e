class Admin::DepartmentsController < Admin::BaseController
  CAN_READ_METHOD = %w(edit)

  authorize_resource except: CAN_READ_METHOD
  before_action :authorize_read, only: CAN_READ_METHOD

  def index
    respond_to do |format|
      format.html do
        @departments = service.index
      end
      format.json do
        render json: service.index
      end
    end
  end

  def new
    @department, @departments, @department_pic_types, @areas = service.new
  end

  def create
    render json: service.create
  end

  def edit
    @department, @departments, @department_pic_types, @areas = service.edit
  end

  def update
    render json: service.update
  end

  def destroy
    render json: service.destroy
  end

  private
  def service
    Admin::DepartmentsService.new(self)
  end
end
