class Admin::ArrangeBillingsController < Admin::BaseController
  authorize_resource
  include AsyncDownload

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def update
    render json: service.update
  end

  def export
    respond_to do |format|
      format.json do
        render json: service.export
      end
    end
  end

  def cancel_billing
    render json: service.cancel_billing
  end

  def month_billings
    render json: service.month_billings
  end

  def search_download_by_staff
    render json: service.search_download_by_staff
  end

  def confirm_billings
    render json: service.confirm_billings
  end

  private
  def service
    Admin::ArrangeBillingsService.new(self)
  end
end
