class Admin::VerifyLoginIdsController < ApplicationController
  layout "admin/unauthenticate_layout"

  def edit
    @token, @admin, valid = service.edit
    return redirect_to admin_root_path unless valid

    warden.logout :admin
  end

  def update
    @token, @admin, valid, expired = service.update
    return redirect_to admin_root_path unless valid

    warden.logout :admin
    if expired
      render :edit
    else
      redirect_to admin_edit_password_path(reset_password_token: @token)
    end
  end

  private

  def service
    Admin::VerifyLoginIdsService.new(self)
  end
end
