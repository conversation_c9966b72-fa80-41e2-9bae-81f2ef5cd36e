class Admin::LocationJobCategoriesController < Admin::BaseController
  def index
    render json: [] if params[:location_id].blank?
    location_job_categories = LocationJobCategory.where(location_id: params[:location_id])
    render json: location_job_categories.as_json(
      only: [:id, :location_id, :personal_things, :clothes, :job_content, :special_note],
      methods: [:thumbnail_path, :job_category_name]
    )
  end

  def create
    location_job_category = LocationJobCategory.new(location_id: params[:location_id])
    location_job_category.assign_attributes(location_job_category_params)
    location_job_category.assign_attributes(crop_thumbnail_params)
    status = location_job_category.save
    if status
      location_job_category.assign_attributes(crop_thumbnail_params)
      location_job_category.update(thumbnail_params)
      render json: {
        status: true,
        redirect_path: edit_admin_location_path(params[:location_id])
      }
    else
      render json: {
        status: false,
        errors: location_job_category.errors.as_json
      }
    end
  end

  def new
    @location = Location.find(params[:location_id])
    except_category_ids = LocationJobCategory.where(location_id: @location.id)
      .pluck(:job_category_id)
    if except_category_ids.blank?
      @location_job_category = LocationJobCategory.location_defaut_job_category(@location)
    else
      @location_job_category = LocationJobCategory.new
    end
    @job_categories = LocationJobCategory.option_select_categories(except_category_ids)
  end

  def edit
    @location = Location.find(params[:location_id])
    @location_job_category = LocationJobCategory.find(params[:id])
    except_category_ids = LocationJobCategory.where(location_id: @location.id)
      .pluck(:job_category_id) - [@location_job_category.job_category_id]
    @job_categories = LocationJobCategory.option_select_categories(except_category_ids)
  end

  def update
    location_job_category = LocationJobCategory.find(params[:id])
    location_job_category.assign_attributes(location_job_category_params)
    if location_job_category.save
      location_job_category.assign_attributes(crop_thumbnail_params)
      location_job_category.update(thumbnail_params)
      render json: {
        status: true,
        redirect_path: edit_admin_location_path(location_job_category.location_id)
      }
    else
      render json: {
        status: false,
        errors: location_job_category.errors.as_json
      }
    end
  end

  private

  def location_job_category_params
    params.require(:location_job_category)
      .permit(:job_category_id, :personal_things, :clothes, :job_content, :special_note)
  end

  def crop_thumbnail_params
    {
      thumbnail_parameters: params.require(:thumbnail).permit(LocationJobCategory::IMAGE_ATTRIBUTES)
    }
  end

  def thumbnail_params
    params.require(:location_job_category).permit(:thumbnail)
  end
end
