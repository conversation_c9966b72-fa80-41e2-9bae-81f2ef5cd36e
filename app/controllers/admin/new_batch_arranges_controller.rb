class Admin::NewBatchArrangesController < Admin::BaseController
  authorize_resource class: false

  rescue_from(CustomExceptions::TimeRangeError) do |e|
    render json: ErrorResponseBlueprint.render(
      {errors: [e.message]},
      view: :with_errors
    )
  end

  def index
    respond_to do |format|
      format.html do
        response = service.index
        @json_last_conditions = response[:json_last_conditions]
        @departments = response[:departments]
        @locations = response[:locations]
        @staffs = response[:staffs]
        @corporations = response[:corporations]
      end
      format.json do
        render json: service.index
      end
    end
  end

  def save_search_conditions
    render json: service.save_search_conditions
  end

  def base_informations
    render json: service.base_informations
  end

  def search_staffs
    render json: service.search_staffs
  end

  def send_batch_arrange_mail_status
    render json: service.send_batch_arrange_mail_status
  end

  def save_temporary_arrange
    render json: service.save_temporary_arrange
  end

  def get_temporary_arrange_worker_data
    render json: service.get_temporary_arrange_worker_data
  end

  def admin_confirm_work_achievements
    render json: service.admin_confirm_work_achievements
  end

  def alert_message_arranged
    render json: service.alert_message_arranged
  end

  def save_arrange
    render json: service.save_arrange
  end

  def get_arrange_worker_data
    render json: service.get_arrange_worker_data
  end

  def save_temporary_arrange_by_search
    render json: service.save_temporary_arrange_by_search
  end

  def show_notes
    render json: service.show_notes
  end

  def cancel_temporary_arrange
    render json: service.cancel_temporary_arrange
  end

  def send_offer_to_staffs
    render json: service.send_offer_to_staffs
  end

  def send_batch_offer_to_staffs
    render json: service.send_batch_offer_to_staffs
  end

  def send_offer_mail_status
    render json: service.send_offer_mail_status
  end

  def select_staff_apply_order_case
    render json: service.select_staff_apply_order_case
  end

  def get_regular_order_time
    render json: service.get_regular_order_time
  end

  def get_regular_order_price
    render json: service.get_regular_order_price
  end

  def check_input_order_price
    render json: service.check_input_order_price
  end

  def get_order_case_notes
    render json: service.get_order_case_notes
  end

  def save_order_case_notes
    render json: service.save_order_case_notes
  end

  private
  def service
    Admin::NewBatchArrangesService.new self
  end
end
