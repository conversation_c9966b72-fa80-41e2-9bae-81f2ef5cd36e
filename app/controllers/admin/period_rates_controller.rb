class Admin::PeriodRatesController < Admin::BaseController
  rescue_from(ActiveRecord::RecordInvalid) do |e|
    render json: {status: false, code: 400, message: e.record.errors.messages}
  end

  rescue_from(CustomExceptions::DestroyPeriodRateError) do |e|
    render json: {status: false, code: 400, message: e.message}
  end

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def set_default
    respond_to do |format|
      format.html
      format.json do
        render json: service.set_default
      end
    end
  end

  def create
    render json: service.create
  end

  def edit
    render json: service.edit
  end

  def update
    render json: service.update
  end

  def destroy
    render json: service.destroy
  end

  def destroyed_warning
    render json: service.destroyed_warning
  end

  private

  def service
    Admin::PeriodRatesService.new(self)
  end
end
