class Admin::AdminEmailToStaffsController < Admin::BaseController
  def index
    data_response = service.index
    respond_to do |format|
      format.html do
        @departments = data_response[:departments]
        @prefectures = data_response[:prefectures]
      end
      format.json do
        render json: data_response
      end
    end
  end

  def new
    data_response = service.new
    @admin_email_to_staff = data_response[:admin_email_to_staff]
    @prefecture_ids = []
    @department_ids = []
    @departments = data_response[:departments]
    @prefectures = data_response[:prefectures]
    @target_staffs = data_response[:target_staffs]
    @target_staff_ids = data_response[:target_staff_ids]
  end

  def review
    render json: service.review
  end

  def create
    render json: service.create
  end

  def information
    data_response = service.information
    respond_to do |format|
      format.html do
        @admin_email = data_response[:admin_email]
        @prefecture_ids = data_response[:prefecture_ids]
        @department_ids = data_response[:department_ids]
        @departments = data_response[:departments]
        @prefectures = data_response[:prefectures]
        @target_staffs = data_response[:target_staffs]
        @target_staff_ids = data_response[:target_staff_ids]
      end
      format.json do
        render json: {data: data_response}
      end
    end
  end

  def pause
    render json: service.pause
  end

  def continue
    render json: service.continue
  end

  private
  def service
    Admin::AdminEmailToStaffsService.new(self)
  end
end
