class Admin::IndividualNumbersController < Admin::BaseController
  before_action :check_is_role_can_access
  include AsyncDownload
  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def show
    data = service.show
    return render_error(404) if data.nil?

    @staff, @registration, @partner = data
  end

  def approve
    render json: service.approve
  end

  def return
    render json: service.return
  end

  def export
    render json: service.export
  end

  private

  def service
    Admin::IndividualNumbersService.new(self)
  end

  def check_is_role_can_access
    render_error(404) unless current_admin.can_view_my_number
  end
end
