class Admin::AdminNotificationStaffsController < Admin::BaseController
  include DownloadAttachment

  def index
    @departments, data, @prefectures = service.index
    respond_to do |format|
      format.html
      format.json do
        render json: data
      end
    end
  end

  def new
    @departments, @notification, @except_staffs, @except_staff_ids, @prefectures = service.new
  end

  def create
    render json: service.create
  end

  def edit
    @notification, @department_ids, @except_staff_ids, @except_staffs, @prefecture_ids = service.edit
  end

  def update
    render json: service.update
  end

  def destroy
    render json: service.destroy
  end

  private
  def service
    Admin::AdminNotificationStaffsService.new(self)
  end
end
