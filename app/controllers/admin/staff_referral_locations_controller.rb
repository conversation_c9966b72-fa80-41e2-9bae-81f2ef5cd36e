class Admin::StaffReferralLocationsController < Admin::BaseController
  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def create
    respond_to do |format|
      format.json do
        render json: service.create
      end
    end
  end

  def destroy
    respond_to do |format|
      format.json do
        render json: service.destroy
      end
    end
  end

  private
  def service
    Admin::StaffReferralLocationsService.new self
  end
end
