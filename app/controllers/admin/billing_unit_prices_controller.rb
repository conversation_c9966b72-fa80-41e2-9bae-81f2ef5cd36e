class Admin::BillingUnitPricesController < Admin::BaseController
  authorize_resource

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def create
    render json: service.create
  end

  def update_payment_rate
    render json: service.update_payment_rate
  end

  private
  def service
    Admin::BillingUnitPricesService.new(self)
  end
end
