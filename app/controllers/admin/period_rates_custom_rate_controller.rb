class Admin::PeriodRatesCustomRateController < Admin::BaseController
  rescue_from(ActiveRecord::RecordInvalid) do |e|
    render json: {status: false, code: 400, message: e.record.errors.messages}
  end

  def custom_rates_by_date
    respond_to do |format|
      format.html
      format.json do
        render json: service.custom_rates_by_date
      end
    end
  end

  def new_custom_rate_by_date; end

  def create_custom_rates_by_date
    render json: service.create_custom_rates_by_date
  end
  private

  def service
    Admin::PeriodRatesCustomRateService.new(self)
  end
end
