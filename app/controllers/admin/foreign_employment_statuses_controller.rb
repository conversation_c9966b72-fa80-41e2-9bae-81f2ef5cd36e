class Admin::ForeignEmploymentStatusesController < Admin::BaseController
  include AsyncDownload
  authorize_resource class: false

  def index
    respond_to do |format|
      format.html do
        @staffs = service.index
      end
      format.json do
        render json: service.index
      end
    end
  end

  def export
    respond_to do |format|
      format.json do
        render json: service.export
      end
    end
  end

  private
  def service
    Admin::ForeignEmploymentStatusesService.new(self)
  end
end
