class Admin::HrLaborFilesController < Admin::BaseController
  def index
    document_types = HrLaborFile.document_types.keys
    respond_to do |format|
      format.html do
        @document_types = document_types
      end
      format.json do
        files = HrLaborFile.order(updated_at: :desc)
          .as_json(only: [:id, :document_type], methods: [:document_file_identifier, :formatted_updated_at])
        files_by_type = document_types.each_with_object(Hash.new) do |type, h|
          h[type] = files.find{|f| f["document_type"] == type}
        end
        render json: {files_by_type: files_by_type}
      end
    end
  end

  def create
    file = HrLaborFile.find_or_initialize_by(
      document_type: hr_labor_file_params[:document_type]
    )
    file.remove_document_file! if file.id.present? # Remove previous file if exists
    file.assign_attributes(hr_labor_file_params)
    file.save!
    refresh_regulation_file_types_cache
    render json: {status: true}
  rescue ActiveRecord::RecordInvalid
    render json: {status: false, file_error: file.errors.messages[:document_file]&.first}
  end

  def view_file
    file = HrLaborFile.find_by(id: params[:id])
    return render_error(404) if file.blank?

    file_url = file.document_file.file.path
    original_filename = file.document_file_identifier
    if Settings.environment_can_use_aws.include? Rails.env
      mime_type = MIME::Types.type_for(file_url).first.content_type
      file_data = S3_BUCKET.object(file_url).get.body.read
      send_data(file_data, type: mime_type, filename: original_filename, disposition: "inline")
    else
      send_file(file_url, file_name: original_filename, disposition: "inline")
    end
  end

  private

  def hr_labor_file_params
    params.permit([:document_file, :document_type])
  end

  def refresh_regulation_file_types_cache
    types = HrLaborFile.all.pluck(:document_type).uniq
    redis = Lawson::RedisConnector.new(Settings.redis.db.default)
    redis.set(HrLaborFile::TYPE_CACHE_KEY, types)
  end
end
