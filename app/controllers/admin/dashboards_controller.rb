class Admin::DashboardsController < Admin::BaseController
  include AsyncDownload

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def view_detail
    respond_to do |format|
      format.html
      format.json do
        render json: service.view_detail
      end
    end
  end

  def export
    respond_to do |format|
      format.json do
        render json: service.export
      end
    end
  end

  private
  def service
    Admin::DashboardsService.new(self)
  end
end
