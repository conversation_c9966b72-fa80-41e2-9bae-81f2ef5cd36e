class Admin::CorporationGroupTagsController < Admin::BaseController
  CAN_READ_METHOD = %w(edit search_corporation_groups)

  authorize_resource except: CAN_READ_METHOD
  before_action :authorize_read, only: CAN_READ_METHOD

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def new
    @corporation_group_tag = service.new
  end

  def create
    render json: service.create
  end

  def edit
    @corporation_group_tag, @corporation = service.edit
  end

  def update
    render json: service.update
  end

  def search_corporation_groups
    render json: service.search_corporation_groups
  end

  def destroy
    render json: service.destroy
  end

  def check_duplicate_name
    render json: service.check_duplicate_name
  end

  private
  def service
    Admin::CorporationGroupTagsService.new(self)
  end
end
