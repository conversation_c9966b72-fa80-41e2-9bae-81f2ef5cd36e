# ! Deprecated
class Admin::EntriesController < Admin::BaseController
  AUTHORIZE_CLASS = :entry
  CAN_READ_METHOD = %w(edit)

  authorize_resource class: AUTHORIZE_CLASS, except: CAN_READ_METHOD
  include AsyncDownload

  before_action ->{authorize_read AUTHORIZE_CLASS}, only: CAN_READ_METHOD
  before_action :load_staff, only: [:edit, :update,
    :send_authenticate_mail, :send_mail_entry, :reject_staff, :check_update_staff,
    :check_before_send_modification_mail]
  before_action :load_concern_datas, only: :edit

  def index
    render_error(404)
    # default_search_type = Staff.staff_types[:type_entry]
    # respond_to do |format|
    #   format.html
    #   format.json do
    #     staff_ids, total_count = StaffFilter.new(default_search_type,
    #       JSON.parse(params[:search])).filter_ids
    #     @staffs = Staff.includes(:account, :master_staff)
    #       .where(id: staff_ids).order_as_specified(id: staff_ids)
    #     render json: {
    #       staffs: @staffs.as_json(
    #         only: [:id, :gender_id, :status_id],
    #         methods: [:age, :name, :name_kana,
    #           :registration_date, :master_staff_number]
    #       ),
    #       total_items: total_count
    #     }
    #   end
    #   format.xlsx do
    #     staff_ids, = StaffFilter.new(default_search_type,
    #       JSON.parse(params[:search]).merge!(per_page: Staff.count)).filter_ids
    #     package = Axlsx::Package.new
    #     @workbook = package.workbook
    #     @workbook.add_worksheet(name: Staff.sheet_name) do |sheet|
    #       sheet.add_row Staff.label_i18n
    #       data_types = Staff.column_data_types
    #       Staff.includes(Staff::TABLE_RELATIONSHIP).where(id: staff_ids).find_each do |staff|
    #         sheet.add_row staff.row_data, types: data_types
    #       end
    #     end

    #     send_data package.to_stream.read, type: Settings.export.type.xlsx,
    #       filename: export_file_name(Staff.name, Settings.export.format.xlsx)
    #   end
    # end
  end

  def delete_staff_list
    ActiveRecord::Base.transaction do
      Staff.type_entry.by_ids(params[:staff_ids].split(",")).each do |entry|
        entry.destroy!
        entry.update_column(:entry_deleted_admin_id, current_admin.id)
      end
    end
    sleep(1)
    render json: {status: "success"}
  rescue ActiveRecord::RecordInvalid
    render json: {status: "error"}
  end

  def update_status_list
    staff_params = params[:status]
    status = update_staff_status(staff_params) ? "success" : "error"
    sleep(1)
    render json: {status: status}
  end

  def export
    search_params = JSON.parse(params[:search])
    staff_statuses_allow = [Staff.status_ids[:op_confirm], Staff.status_ids[:export]]
    staffs_choosed = Staff.where id: search_params["id"]
    op_export_staffs = staffs_choosed.where status_id: staff_statuses_allow
    staff_ids = op_export_staffs.pluck :id

    respond_to do |format|
      format.json do
        json_data = {is_have_op_confirm_staff: true}

        if op_export_staffs.size == staffs_choosed.size
          job_id = ExportStaffWorker.perform_async staff_ids, current_admin.id
          json_data = {jid: job_id}
        end

        render json: json_data
      end
    end
  end

  def edit
    render_error(404)
    # @staff.staff_check_item || @staff.build_staff_check_item
    # @staff.staff_departments.build unless @staff.staff_departments.exists?
  end

  def update
    return render_error_for(:approve) unless is_valid_approved?

    ActiveRecord::Base.transaction do
      @staff.update! update_staff_params
      if @staff.pic_confirm?
        staff = CloneStaffService.new(@staff).clone_staff.first
        AdminMailer.finish_staff_entry(staff, staff.set_reset_password_token).deliver_now
      end
      if @staff.op_confirm?
        @master_staff = @staff.master_staff
        @account = @master_staff.account
        @account.update! update_account_params
        @master_staff.update! update_master_staff_params
        @master_staff.create_haken_type_histories op_confirm: true
        CloneStaffService.new(@master_staff, true).clone_entry_review
        CheckStaffBankAccountService.new(@master_staff.reviewing_staff).perform
      end
    end
    flash[:success] = t "admin.staff.saved"
    json_response true, true
  rescue ActiveRecord::RecordInvalid
    json_response false, false
  end

  def reject_staff
    master_staff = @staff.master_staff
    return render_error_for :reject if !@staff.able_to_be_op_confirm?(current_admin) ||
      master_staff.nil?

    ActiveRecord::Base.transaction do
      @staff.update!(status_id: :rejected)
      master_staff.update!(status_id: :rejected)
    end
    AdminMailer.reject_staff_mail(@staff).deliver_now
    flash[:success] = t "admin.staff.saved"
  rescue ActiveRecord::RecordInvalid
    render_error_for :reject
  end

  def send_authenticate_mail
    master_staff = @staff.master_staff
    return render_error_for(:send_mail) if !@staff.able_to_send_auth_mail? || master_staff.nil?

    AdminMailer.send_authenticate_mail_staff(master_staff,
      master_staff.set_reset_password_token).deliver_now
    flash[:success] = t("admin.staff.sent_mail_successfully")
  end

  def send_mail_entry
    staff_form = SendEditEntryMailForm.new(@staff, params[:comment])
    return unless staff_form.save

    flash[:success] = t("admin.staff.sent_mail_successfully")
  end

  def check_update_staff
    return render_error_for(:approve) unless is_valid_approved?

    @staff.assign_attributes update_staff_params
    if @staff.op_confirm?
      @account = @staff.master_staff.account
      @account.assign_attributes update_account_params
      @account.valid?
      json_response (@staff.valid? && @account.valid?), false
    else
      json_response @staff.valid?, false
    end
  rescue ActiveRecord::RecordInvalid
    json_response false, false
  end

  def check_before_send_modification_mail
    render_error_for(:send_mail) unless @staff.able_to_send_modification_request?
  end

  private
  def update_staff_status staff_params
    update_status = true
    staff_params.each do |param|
      staff = Staff.find_by(id: param["id"])
      update_status = false unless staff.update(status_id: param["status_id"].to_i)
    end
    update_status
  end

  def load_staff
    @staff = Staff.type_entry.find params[:id]
  end

  def load_concern_datas
    @types = Type.by_category_ids(
      [
        Type::NATIONALITY, Type::REGISTRATION_HISTORY, Type::RELATIONSHIP,
        Type::WORKABLE_TIME, Type::UNIFORM_SIZE, Type::PANT_SIZE, Type::SHOES_SIZE
      ]
    ).group_by(&:category_id)

    @is_enabled_pic_approve = @staff.able_to_be_pic_approved?
    @is_enabled_op_confirm = @staff.able_to_be_op_confirm? current_admin
    @is_enabled_send_auth_mail = @staff.able_to_send_auth_mail?
    @is_enabled_send_modification_request = @staff.able_to_send_modification_request?
    @nationalities = @types[Type::NATIONALITY].to_json methods: :name, only: :id
    @prefectures = Prefecture.select(:id, :name).to_json
    @registration_histories = @types[Type::REGISTRATION_HISTORY].to_json methods: :name, only: :id
    @relationships = @types[Type::RELATIONSHIP].to_json methods: :name, only: :id
    @banks = Bank.order_by_code.select(:id, :name, :code).to_json
    @staff_edited = @staff.to_json(include: [:staff_account_transfer, :staff_employment_history,
      :staff_expectation, :staff_check_item])
    @residence_statuses = ResidenceStatus.select(:name, :id).to_json
    @residence_permissions = Staff.residence_permissions_for_select.to_json
  end

  def staff_params
    params.require(:staff).permit(Staff::STAFF_ATTRS)
  end

  def update_staff_params
    status_id = params[:staff][:status_id].to_i
    time_now = ServerTime.now
    update_params =
      case status_id
      when Staff.status_ids[:op_confirm]
        entry_params.merge(approved_admin_id: current_admin.id, approved_at: time_now,
          is_validate_name_and_kana: true)
      else
        staff_params
      end.merge(status_id: status_id)

    if update_params[:nationality].blank?
      update_params[:is_validate_residence] = true if @staff.nationality != Type::JAPAN_ID
    else
      update_params[:is_validate_residence] = update_params[:nationality].to_i != Type::JAPAN_ID
    end
    update_params[:is_admin_validate] = true
    update_params[:is_validate_home_station] = true
    update_params[:skip_emergency_info_validation] = true
    update_params[:staff_account_transfer_attributes][:is_validate_passbook] = true
    update_params[:staff_check_item_attributes][:is_skip_uninsured_validate] = true
    update_params
  end

  def update_master_staff_params
    update_params = update_staff_params.except(:name, :name_kana, :email,
      :is_validate_name_and_kana)

    %w(staff_employment_history staff_expectation staff_account_transfer staff_check_item)
      .each do |nested_attr|
      nested_attr_id = @master_staff.send(nested_attr)&.id
      if nested_attr_id.present?
        update_params["#{nested_attr}_attributes"][:id] = nested_attr_id
        next
      end

      update_params["#{nested_attr}_attributes"].delete(:id)
    end

    master_staff_department_ids = @master_staff.staff_departments.ids
    update_params[:staff_departments_attributes].each_value do |value|
      next unless value[:id]

      value[:id] = master_staff_department_ids[0]
      master_staff_department_ids.delete_at(0)
    end
    update_params[:staff_check_item_attributes]
      .merge! StaffConstant::DEFAULT_VALUE_STAFF_FROM_ENTRY[:staff_check_item]

    update_params.merge(
      staff_salary_attributes: StaffConstant::DEFAULT_VALUE_STAFF_FROM_ENTRY[:staff_salary]
    )
  end

  def update_account_params
    account_params = update_staff_params.permit(:name, :name_kana, :email)
      .merge(skip_password_validation: true, type: :staff, validate_email_entry: true,
      except_entry_id: @staff.id)
    account_params[:validate_email_exists] = true if @account.email != account_params[:email]
    account_params
  end

  def render_error_for action_type
    flash[:error] = t "activerecord.errors.messages.could_not_#{action_type}"
    render json: {success: false}
  end

  def is_valid_approved?
    case params[:staff][:status_id].to_i
    when Staff.status_ids[:pic_confirm]
      @staff.able_to_be_pic_approved?
    when Staff.status_ids[:op_confirm]
      @staff.able_to_be_op_confirm?(current_admin)
    else
      false
    end
  end

  def staff_departments_errors
    @staff.staff_departments.map &:errors
  end

  def json_response status, is_redirect
    errors = @staff.errors.messages
    errors = errors.merge(@account.errors.messages) if @account
    response = {
      status: status,
      errors: errors.to_json,
      nested_errors: {
        staff_check_item: @staff.staff_check_item.errors,
        staff_account_transfer: @staff.staff_account_transfer.errors,
        staff_departments: staff_departments_errors,
        staff_employment_history: @staff.staff_employment_history.errors,
        staff_expectation: @staff.staff_expectation.errors
      }.to_json
    }
    response[:redirect_path] = edit_admin_entry_path if is_redirect
    render json: response
  end

  def entry_params
    params[:staff][:staff_employment_history_attributes][:service_work_type] ||= []
    params[:staff][:staff_employment_history_attributes][:office_work_type] ||= []
    params[:staff][:staff_employment_history_attributes][:other_work_type] ||= []
    params[:staff][:career_up_id] ||= []
    params.require(:staff).permit(Staff::OP_CONFIRM_STEP_1_ATTRS, Staff::OP_CONFIRM_STEP_2_ATTRS,
      Staff::OP_CONFIRM_STEP_3_ATTRS, Staff::OP_CONFIRM_STEP_4_ATTRS,
      Staff::STEP_1_REMOVE_FILE_ATTRS, :haken_type)
  end
end
