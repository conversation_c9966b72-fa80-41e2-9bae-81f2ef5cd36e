class BaseController < ApplicationController
  def subdomain_redirect
    language = LocaleConstraint::DEFAULT_LANGUAGE
    subdomain = request.subdomain
    return redirect_to corporation_root_path(locale: language) if subdomain == Settings.domain.corporation
    return render_error 404 if subdomain == Settings.domain.api && request.format.symbol == :html
    return redirect_to staff_root_path(locale: language) if staff_signed_in?

    redirect_to staff_order_cases_path(locale: language)
  end
end
