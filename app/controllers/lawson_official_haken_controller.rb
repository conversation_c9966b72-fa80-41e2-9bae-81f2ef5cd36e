class LawsonOfficialHakenController < BaseController
  layout "lawson_official_haken/base"
  protect_from_forgery with: :exception
  before_action :save_utm_tags

  JOB_AFTER_DAY = 2
  JOB_LIMIT = 8
  UTM_TAGS = [:utm_source, :utm_medium, :utm_campaign, :utm_content]

  def index
    data = load_jobs
    respond_to do |format|
      format.html do
        @jobs = data
        @utm_params = load_utm_params
        @utm_url = @utm_params.present? ? "?" : ""
        @utm_params.each_key{|tag| @utm_url += "#{tag}=#{@utm_params[tag]}&"}
      end

      format.json do
        render json: data
      end
    end
  end

  private

  def load_utm_params
    UTM_TAGS.each_with_object(Hash.new) do |key, obj|
      obj[key] = params[key] if params[key].present?
    end
  end

  def save_utm_tags
    UTM_TAGS.each do |key|
      next if cookies[key].present? || params[key].blank?

      cookies[key] = params[key]
    end
  end

  def load_jobs
    redis = Lawson::RedisConnector.new(Settings.redis.db.location_job)
    cache_key = "crew_jobs_#{(ServerTime.now + 2.days).strftime(Settings.date.formats)}"
    prefecture_name = params[:prefecture_name]
    cache_key += "_#{prefecture_name}" if prefecture_name.present?
    jobs = redis.get(cache_key)
    return JSON.parse(jobs) if jobs.present?

    jobs = prefecture_name.present? ? find_jobs_by_prefecture_name(prefecture_name) : find_jobs
    redis.set(cache_key, jobs.to_json)
    redis.expire(cache_key, 1.day.to_i)
    jobs
  end

  def find_jobs_by_prefecture_name prefecture_name
    pic_prefecture_ids = Prefecture.where(name: prefecture_name).pluck(:id)
    return find_jobs if pic_prefecture_ids.blank?

    department_ids = Department.where(pic_prefecture_id: pic_prefecture_ids).pluck(:id)
    order_cases = OrderCase.available_apply.where("order_cases.case_started_at >= ?",
      ServerTime.now.beginning_of_day + JOB_AFTER_DAY.days)
      .by_pic_department(department_ids).limit(JOB_LIMIT)
      .order_by_started_at_asc
      .includes(:order_branch, arrangements: [:arrange_payment],
        order: [location: [stations_1: [:railway_line], corporation_group: [:corporation]]])
    format_jobs(order_cases)
  end

  def find_jobs
    lawson_100_loc_ids = Location.by_name(Settings.location_type[2]).pluck(:id)
    lawson_natural_loc_ids = Location.by_name(Settings.location_type[1])
      .where.not(id: lawson_100_loc_ids).pluck(:id)
    lawson_loc_ids = Location.by_name(Settings.location_type[0])
      .where.not(id: lawson_100_loc_ids + lawson_natural_loc_ids).pluck(:id)
    jobs = OrderCase.available_apply
      .where("order_cases.case_started_at >= ?", ServerTime.now.beginning_of_day + JOB_AFTER_DAY.days)
      .order_by_started_at_asc
    lawson_100_jobs_ids = jobs.by_owner_location(lawson_100_loc_ids).limit(2).uniq.pluck(:id)
    lawson_natural_jobs_ids = jobs.by_owner_location(lawson_natural_loc_ids).limit(2).uniq.pluck(:id)
    total_ids = lawson_natural_jobs_ids.count + lawson_100_jobs_ids.count
    lawson_jobs_ids = jobs.by_owner_location(lawson_loc_ids).limit(JOB_LIMIT - total_ids).uniq.pluck(:id)
    order_case_ids = lawson_100_jobs_ids + lawson_natural_jobs_ids + lawson_jobs_ids
    order_cases = OrderCase.where(id: order_case_ids).includes(:order_branch,
      arrangements: [:arrange_payment],
      order: [location: [stations_1: [:railway_line], corporation_group: [:corporation]]])
    format_jobs(order_cases)
  end

  def format_jobs order_cases
    jobs = []
    wages_fees = JobsWage::CalculateWage.wages_fees({order_cases: order_cases, only_flags: true}, Staff.guest)
      .group_by{|x| x[:order_case_id]}
    order_cases.each do |order_case|
      wages_fee = {}
      wages_fee = wages_fees[order_case.id][0] if wages_fees[order_case.id].present?
      jobs << formated_job(order_case, wages_fee)
    end
    jobs
  end

  def formated_job order_case, wages_fee = {}
    {
      order_case_id: order_case.id,
      order_case_url: "#{Settings.haken_staff_url}#{staff_order_case_path(id: order_case.id)}",
      segment_regular: order_case.segment_regular,
      is_time_changable: order_case.is_time_changable,
      staff_apply_count: order_case.staff_apply_count,
      started_at_format_month_day: order_case.started_at_format_month_day,
      working_time: order_case.working_time,
      break_time: order_case.break_time,
      site_name: order_case.site_name,
      location_station_1_info: order_case.location_station_1_info,
      order_fee: wages_fee[:order_fee],
      unit_fee:  wages_fee[:unit_fee],
      location_type: order_case.location_camelized_type
    }
  end
end
