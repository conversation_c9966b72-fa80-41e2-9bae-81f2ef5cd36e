class SmbcSubmissionAuthenticateController < ActionController::Base
  protect_from_forgery with: :exception

  def index
    redirect_data = service.execute
    redirect_url = redirect_data[:redirect_url]
    return render plain: redirect_data[:message] unless redirect_url

    redirect_to redirect_url
  end

  private

  def service
    SmbcSubmissionAuthenticateService.new(request, params)
  end
end
