class ApiController < ActionController::Base
  include WardenHelper
  include StoreLocationHelper
  include ApplicationHelper

  protect_from_forgery with: :exception

  delegate :check_domain, :prevent_page_caching, :set_locale, to: :application_service

  before_action :set_locale, :prevent_page_caching, :check_domain, :check_app_maintain, :check_app_update

  rescue_from StandardError, with: :render_500 # Defined before other errors to avoid 500 always override everything
  rescue_from ActiveRecord::RecordInvalid, with: :render_400
  rescue_from ActiveRecord::RecordNotFound, with: :render_404

  Error::CLASSES.each do |c|
    rescue_from c.constantize do |error|
      key = c.split("::").last.underscore
      case key.to_sym
      when :bad_request
        render(
          status: :bad_request,
          json: {messages: error&.message, status_code: Error::CODES[:bad_request]}
        )
      else
        render json: {message: error, status_code: Error::CODES[key.to_sym]}
      end
    end
  end

  def render_response result
    return render json: {data: result[:data], status_code: result[:status_code]} if result[:http_code] == 200

    render_errors(result[:http_code], result[:messages])
  end

  def render_errors http_code, messages
    self.send("render_#{http_code}", messages)
  end

  private

  def render_400 exception
    notify_exception(exception)
    render(
      status: :bad_request,
      json: {messages: exception&.message, status_code: Error::CODES[:bad_request]}
    )
  end

  def render_404 exception
    notify_exception(exception)
    render(
      status: :not_found,
      json: {messages: exception&.message, status_code: Error::CODES[:record_not_found]}
    )
  end

  def render_500 exception
    notify_exception(exception)
    render(
      status: :internal_server_error,
      json: {messages: exception&.message, status_code: Error::CODES[:internal_server_error]}
    )
  end

  def check_app_maintain
    raise Error::Api::Common::AppUnderMaintenance if Settings.app.under_maintenance
  end

  def check_app_update
    return if Settings.app.under_maintenance
    return unless wz_device_type && wz_app_version

    check_app_info_service = Staff::Api::CheckAppInfoService.new(wz_device_type, wz_app_version)
    raise Error::Api::Common::AppVersionNeedUpdate if check_app_info_service.need_update_version?
  end

  def wz_device_type
    request.headers["Device-Type"]
  end

  def wz_app_version
    request.headers["Wz-App-Version"]
  end

  def application_service
    ApplicationService.new self
  end

  def notify_exception exception
    # Include request option because Notifier's option[:env] is unavailable
    ExceptionNotifier::BaseExceptionNotifier.new.call(exception, request: request)
  end
end
