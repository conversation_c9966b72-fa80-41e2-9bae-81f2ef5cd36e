class Api::V1::Staff::ArrangementsController < Api::V1::Staff::AuthenticateController
  def update_basic_field
    cmd = Arrangements::StaffUpdateBasicFieldsCommand.new(current_staff, params[:id], arrangement_params.to_h)
    status = cmd.perform
    resources = {status: status}
    format_response(resources, 200)
  end

  private

  def arrangement_params
    params.require(:data).permit Arrangement::UPDATABLE_ATTRS
  end
end
