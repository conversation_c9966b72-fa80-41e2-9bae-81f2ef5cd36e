class Api::V1::Staff::Authe<PERSON><PERSON><PERSON>ontroller < Api::V1::Staff::BaseController
  before_action :authenticate_api!
  before_action :check_if_retired_extend_time_expired
  before_action :api_set_staff_last_activity

  private

  def auth_service
    Staff::AuthenticateService.new(self)
  end

  def api_set_staff_last_activity
    return if current_staff&.last_activity && current_staff.last_activity >= 5.minutes.ago

    current_time = ServerTime.now
    current_staff.update_last_activity_time(current_time, true)
  end
end
