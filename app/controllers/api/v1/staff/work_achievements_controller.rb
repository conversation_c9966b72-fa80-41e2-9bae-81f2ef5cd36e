class Api::V1::Staff::WorkAchievementsController < Api::V1::Staff::<PERSON>thenticateController
  rescue_from(Error::Api::Common::RecordNotFound) do
    error_messages = I18n.t("errors.messages.generic_error")
    format_exception_errors(error_messages, 200)
  end

  def show
    render_response service.show
  end

  def update_work_time
    render_response service.update_work_time
  end

  def confirmation
    render_response service.confirmation
  end

  def update_confirmation
    render_response service.update_confirmation
  end

  def update
    render_response service.update
  end

  private
  def service
    Staff::ApiWorkAchievementsService.new(self)
  end
end
