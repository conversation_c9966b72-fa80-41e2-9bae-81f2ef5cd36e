class Api::V1::Staff::PublicController < ApiController
  protect_from_forgery with: :exception
  skip_before_action :verify_authenticity_token

  def recruiting_jobs
    render_response service.recruiting_jobs
  end

  def similar_jobs
    render_response service.similar_jobs
  end

  def location_jobs
    render_response service.location_jobs
  end

  def show
    render_response service.show
  end

  def locations_jobs
    render_response service.locations_jobs
  end

  private
  def service
    Staff::ApiPublicJobsService.new(self)
  end
end
