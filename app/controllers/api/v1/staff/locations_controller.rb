class Api::V1::Staff::LocationsController < Api::V1::Staff::AuthenticateController
  skip_before_action :verify_authenticity_token, only: :favorite

  def jobs
    render json: service.jobs
  end

  def histories
    render json: service.histories
  end

  def favorites
    render json: service.favorites
  end

  def favorite
    render json: service.favorite
  end

  private
  def service
    Staff::ApiLocationsService.new(self)
  end
end
