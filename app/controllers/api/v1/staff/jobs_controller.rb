class Api::V1::Staff::JobsController < Api::V1::Staff::AuthenticateController
  # def index
  #   render_response service.search_jobs
  # end

  rescue_from(CustomExceptions::ApplyOrderCaseConditionError) do |e|
    error_response = ErrorResponseBlueprint.render_as_hash({errors: JSON.parse(e.message)},
      view: :api_apply_order_case)

    format_response(error_response, 200)
  end

  def coming_job
    render_response service.coming_job
  end

  def arranged_jobs
    render_response service.arranged_jobs
  end

  def applied_jobs
    render_response service.applied_jobs
  end

  def bookmarked_jobs
    render_response service.bookmarked_jobs
  end

  def worked_jobs
    render_response service.worked_jobs
  end

  def recruiting_jobs
    render_response service.recruiting_jobs
  end

  def similar_jobs
    render_response service.similar_jobs
  end

  def show
    render_response service.show
  end

  def pre_apply_conditions
    render_response service.pre_apply_conditions
  end

  def create_applicant
    render_response service.create_applicant
  end

  def highlight_calendar
    render_response service.highlight_calendar
  end

  def kept_jobs
    render_response service.kept_jobs
  end

  def keep
    render_response service.keep
  end

  def offered_jobs
    render_response service.offered_jobs
  end

  def create_first_applicant
    render_response service.create_first_applicant
  end

  private
  def service
    Staff::ApiJobsService.new(self)
  end
end
