class Api::V1::Staff::NotificationsController < Api::V1::Staff::AuthenticateController
  def notifications
    if params[:version] == "v2"
      render json: service.notifications_v2
    else
      render json: service.notifications
    end
  end

  def admin_notifications
    if params[:version] == "v2"
      render json: service.admin_notifications_v2
    else
      render json: service.admin_notifications
    end
  end

  def unread_notifications
    render json: service.unread_notifications
  end

  def read
    render json: service.read
  end

  def read_all
    render json: service.read_all
  end

  private
  def service
    Staff::Api::NotificationsService.new(self)
  end
end
