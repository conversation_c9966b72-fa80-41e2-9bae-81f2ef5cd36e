class Api::V1::Staff::Contract<PERSON>ontroller < Api::V1::Staff::<PERSON><PERSON><PERSON><PERSON>Controller
  def conditions
    render_response service.conditions
  end

  def validation
    render_response service.validation
  end

  def create
    render_response service.create
  end

  def update_registration_type
    render_response service.update_registration_type
  end

  private
  def service
    Staff::ApiContractService.new(self)
  end
end
