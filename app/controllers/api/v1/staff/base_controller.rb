class Api::V1::Staff::BaseController < ApiController
  include MultiLanguage
  protect_from_forgery
  before_action :default_url_options
  before_action :store_staff_location

  delegate :set_staff_last_activity, to: :staff_service

  def format_response data, http_code, status_code = 0, errors = {}
    res = {data: data}
    res = {messages: errors} if http_code != 200
    res = res.merge(http_code: http_code, status_code: status_code)

    render_response(res)
  end

  def format_exception_errors error_message, http_code, status_code = 0
    data_response = {
      data: {
        status: false,
        errors: error_message
      },
      status_code: status_code,
      http_code: http_code
    }

    render_response(data_response)
  end

  private
  def store_staff_location
    store_location! :staff
  end

  def check_if_retired_extend_time_expired
    raise Error::Api::Staff::Retired if current_staff.is_retired_extend_time_expired?
  end

  def staff_service
    Staff::StaffService.new self
  end
end
