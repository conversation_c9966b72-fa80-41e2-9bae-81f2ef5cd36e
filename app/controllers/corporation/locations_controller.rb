class Corporation::LocationsController < Corporation::BaseController
  layout "print/base", only: :violation_file

  def show
    response = service.show
    @corporation = response[:corporation]
    @location = response[:location]
    @corporation_group = response[:corporation_group]
    @location_pics = response[:location_pics]
    @stations = response[:stations]
  end

  def update
    response = service.update
    return render_error(404) unless response

    render json: response
  end

  def update_location_pic
    response = service.update_location_pic
    return render_error(404) unless response

    render json: response
  end

  def violation_file
    response = service.violation_file
    return render_error(404) unless response

    @location = response[:location]
    @corporation_group = response[:corporation_group]
    @corporation = response[:corporation]
  end

  def edit_store_location
    response = service.edit_store_location
    return render_error(404) unless response

    @current_step = response[:current_step]
    @is_store_parking_area_usable = response[:is_store_parking_area_usable]
    @location = response[:location]
    @corporation_group = response[:corporation_group]
    @corporation = response[:corporation]
    @location_pics = response[:location_pics]
  end

  def validate_step_1
    response = service.validate_step_1
    return render_error(404) unless response

    render json: response
  end

  def update_store_location
    response = service.update_store_location
    return render_error(404) unless response

    render json: response
  end

  private
  def service
    Corporation::LocationsService.new self
  end
end
