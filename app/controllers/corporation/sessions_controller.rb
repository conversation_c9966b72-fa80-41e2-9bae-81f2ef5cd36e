class Corporation::SessionsController < ApplicationController
  include SlashPath
  include ApplicationHelper

  layout "corporation/unauthenticate_layout"

  skip_before_action :verify_authenticity_token, only: [:new, :create]
  before_action :redirect_if_logged_in, only: [:new, :create]

  def new
  end

  def create
    redirect_path = service.create
    redirect_to(redirect_path)
  end

  def destroy
    redirect_path = service.destroy
    redirect_to(redirect_path)
  end

  private

  def service
    Corporation::SessionsService.new(self)
  end
end
