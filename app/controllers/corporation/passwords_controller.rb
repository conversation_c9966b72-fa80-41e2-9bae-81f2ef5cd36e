class Corporation::PasswordsController < ApplicationController
  layout "corporation/unauthenticate_layout"
  before_action :redirect_if_logged_in

  def new
    redirect_to user_session_path if service.new
  end

  def create
    service.create
    redirect_to user_finish_send_reset_password_path
  end

  def edit
    response = service.edit
    load_user response
    redirect_to user_session_path unless response[:reset_password_period_valid]
  end

  def update
    response = service.update
    load_user response
    return redirect_to user_session_path unless response[:reset_password_period_valid]

    if response[:status]
      render(:edit)
    else
      redirect_to(user_finish_setting_password_path(response[:authenticate]))
    end
  end

  def finish_setting_password
  end

  private
  def service
    Corporation::PasswordsService.new self
  end

  def load_user response
    @token = response[:token]
    @user = response[:user]
    @account = response[:account]
  end
end
