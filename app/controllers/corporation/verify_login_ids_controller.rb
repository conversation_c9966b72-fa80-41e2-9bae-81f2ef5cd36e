class Corporation::VerifyLoginIdsController < ApplicationController
  layout "corporation/unauthenticate_layout"

  def edit
    data = service.edit
    return redirect_to corporation_root_path if data.nil?

    @token, @user = data
  end

  def update
    data = service.update
    return redirect_to corporation_root_path if data.nil?

    is_success, @token, @user = data
    return render :edit unless is_success

    redirect_to edit_user_password_path(reset_password_token: @token)
  end

  private
  def service
    Corporation::VerifyLoginIdsService.new(self)
  end
end
