class Corporation::ViolationDaysController < Corporation::BaseController
  def index
    @corporation_groups = service.index
  end

  def new
    data = service.new
    return redirect_to(corporation_violation_days_path) if data.nil?

    @corporation_group, @violation_day = data
  end

  def create
    render json: service.create
  end

  def violation_day_pdf
    data = service.violation_day_pdf
    render_error(404) if data.nil?
  end

  private
  def service
    Corporation::ViolationDaysService.new(self)
  end
end
