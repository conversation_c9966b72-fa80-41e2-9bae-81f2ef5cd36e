class Corporation::HomesController < Corporation::BaseController
  include DownloadAttachment
  def index
    respond_to do |format|
      format.html do
        @is_check_term_service, @is_store_computer, @valid_create_order_type,
        @order_total_count, @total_work_achievements = service.index
      end
      format.json do
        render json: service.index
      end
    end
  end

  def get_owner_notifications
    render json: service.get_owner_notifications
  end

  def update_owner_notification_checked
    render json: service.update_owner_notification_checked
  end

  def turn_off_notification_popup
    render json: service.turn_off_notification_popup
  end

  def locations
    render json: service.locations
  end

  def choose_date
    render json: service.choose_date
  end

  def highlight_calendar
    render json: service.highlight_calendar
  end

  def agree_term_service
    render json: service.agree_term_service
  end

  private
  def service
    Corporation::HomesService.new(self)
  end
end
