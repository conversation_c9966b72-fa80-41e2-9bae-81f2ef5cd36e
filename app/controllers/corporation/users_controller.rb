class Corporation::UsersController < Corporation::BaseController
  def index
    @users = service.index
  end

  def new
    response = service.new
    return redirect_to corporation_users_path unless response

    load_user_form response
  end

  def create
    response = service.create
    render json: response
  end

  def edit
    response = service.edit
    return redirect_to corporation_users_path unless response

    load_user_form response
    @account = response[:account]
    @user_corporation_groups = response[:user_corporation_groups]
    @user_corp_grp_exists_order = response[:user_corp_grp_exists_order]
    @user_locations = response[:user_locations]
    @usr_location_exists_order = response[:usr_location_exists_order]
  end

  def update
    response = service.update
    render json: response
  end

  def destroy
    response = service.destroy
    respond_to do |format|
      format.js do
        @user = response[:user]
        @status = response[:status]
        @message = response[:message]
      end
      format.json do
        render json: response
      end
    end
  end

  def edit_avatar
    response = service.edit_avatar
    return render_error(404) unless response

    @user = response[:user]
  end

  def update_avatar
    service.update_avatar
    redirect_to edit_avatar_corporation_users_path
  end

  def edit_password
    @account = service.edit_password
  end

  def update_password
    response = service.update_password
    @account = response[:account]
    return redirect_to corporation_root_path if response[:status]

    render :edit_password
  end

  def edit_email
    response = service.edit_email
    return render_error(404) unless response

    @user = response[:user]
    @account = response[:account]
  end

  def update_email
    response = service.update_email
    @user = response[:user]
    @account = response[:account]
    return redirect_to edit_email_corporation_users_path if response[:status]

    render :edit_email
  end

  private

  def service
    Corporation::UsersService.new self
  end

  def load_user_form response
    @user = response[:user]
    @user_roles = response[:user_roles]
    @corp_corporation_groups = response[:corp_corporation_groups]
    @corporation_group_tags = response[:corporation_group_tags]
    @corporation_groups = response[:corporation_groups]
  end
end
