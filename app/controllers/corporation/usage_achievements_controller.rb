class Corporation::UsageAchievementsController < Corporation::BaseController
  layout "print/base", only: :show

  def index
    respond_to do |format|
      format.html
      format.json do
        render json: service.index
      end
    end
  end

  def show
    response = service.show
    return render_error(404) unless response

    response
  end

  private

  def service
    Corporation::UsageAchievementsService.new self
  end
end
