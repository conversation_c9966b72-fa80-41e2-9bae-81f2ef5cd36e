class Corporation::BaseController < ApplicationController
  protect_from_forgery

  include MultiLanguage
  include ApplicationHelper

  before_action :default_url_options
  before_action :store_user_location
  before_action :authenticate_user!, :get_total_notification
  before_action :check_if_prefecture_allowed

  private
  def get_success_message_for model, action = action_name, attr = nil
    model = model.class.model_name
    model_name = (attr.present? ? model.name.constantize.human_attribute_name(attr) : model.human)

    I18n.t "common.admin.messages.#{action}", model_name: model_name
  end

  def store_user_location
    store_location! :user
  end

  def get_total_notification
    @total_owner_notification = OwnerNotificationUser.search_by_user(current_user).length
  end

  def check_if_prefecture_allowed
    @prefecture_allowed = true
    return unless is_store_computer?

    locations = current_user.locations.where(code: current_user.account.location_code)
    return if locations.blank?

    location_ids = locations.pluck(:id) & Settings.locations_allow_store_computer
    return @prefecture_allowed = true if location_ids.present?

    location = locations.first
    @prefecture_allowed = location.prefecture&.allow_store_computer?.to_s.true?
  end
end
