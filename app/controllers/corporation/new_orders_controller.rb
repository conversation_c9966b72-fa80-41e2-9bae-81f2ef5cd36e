class Corporation::NewOrdersController < Corporation::BaseController
  wrap_parameters false

  def new
    @is_mobile = browser.device.mobile?
    @ie_accessible = is_store_computer?
    @organization_options, @location_options, @valid_create_order_type,
      @is_check_term_service, @order = service.new
  end

  def create
    render json: service.create
  end

  def step_2
    render json: service.step_2
  end

  def show
    result = service.show
    return redirect_to corporation_orders_path if result.nil?

    @organization_options, @location_options, @order, @is_need_confirm_order,
      @is_check_term_service = result
  end

  def edit
    @is_mobile = browser.device.mobile?
    @ie_accessible = is_store_computer?
    result = service.edit
    return redirect_to corporation_orders_path if result.nil?

    @organization_options, @location_options, @order, @valid_create_order_type,
      @is_check_term_service = result
  end

  def update
    result = service.update
    case request.format.symbol
    when :html
      if result[:order].present?
        redirect_to corporation_new_order_path(result[:order])
      else
        render :show
      end
    when :json
      render json: result
    end
  end

  private
  def service
    Corporation::NewOrdersService.new(self)
  end
end
