class Corporation::LocationSurveysController < Corporation::BaseController
  def index
    response = service.index
    return render_error 404 unless response

    @locations_surveyed = response[:locations_surveyed]
  end

  def new
    response = service.new
    return render_error 404 unless response

    @location_survey = response[:location_survey]
    @location = response[:location]
  end

  def show
    response = service.show
    return render_error 404 unless response

    @location_survey = response[:location_survey]
    @location = response[:location]
  end

  def create
    response = service.create
    return unless response

    render json: response
  end

  def locations_not_survey
    render json: service.locations_not_survey
  end

  def finish
    response = service.finish
    return render_error 404 unless response

    @location_survey_next = response[:location_survey_next]
  end

  def instruction_pdf
    response = service.instruction_pdf
    return render_error 404 unless response

    response
  end

  def load_salary_by_prefecture
    response = service.load_salary_by_prefecture
    return render_error(404) unless response

    render json: response
  end

  private
  def service
    Corporation::LocationSurveysService.new self
  end
end
