class Corporation::OrdersController < Corporation::BaseController
  def index
    respond_to do |format|
      format.html do
        @organization_options, @location_options, @locations, @status_ids = service.index
      end
      format.json do
        render json: service.index
      end
    end
  end

  def new
    redirect_to new_corporation_new_order_path
  end

  def show
    order = service.show
    redirect_to corporation_new_order_path(order)
  end

  def location_data
    render json: service.location_data
  end

  def order_branches_details
    render json: service.order_branches_details
  end

  def estimate_order_price
    render json: service.estimate_order_price
  end

  def destroy
    respond_to do |format|
      format.json{render json: service.destroy}
      format.html{redirect_to service.destroy}
    end
  end

  private
  def service
    Corporation::OrdersService.new(self)
  end
end
