class Corporation::HakenDestinationsController < Corporation::BaseController
  def index
    response = service.index
    respond_to do |format|
      format.html do
        @staffs = response[:staffs]
        @locations = response[:locations]
      end
      format.json do
        render json: response
      end
    end
  end

  def view_detail
    response = service.view_detail
    return render_error(404) unless response

    response
  end

  private

  def service
    Corporation::HakenDestinationsService.new self
  end
end
