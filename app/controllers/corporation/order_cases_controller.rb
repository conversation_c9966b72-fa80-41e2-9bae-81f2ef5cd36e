class Corporation::OrderCasesController < Corporation::BaseController
  layout "print/base", only: [:view_staff_info, :staff_contract]

  def index
    response = service.index
    respond_to do |format|
      format.html do
        @locations = response[:locations]
        @status_ids = response[:status_ids]
        @working_status_id = response[:working_status_id]
        @last_condition = response[:last_condition]
      end
      format.json do
        render json: response
      end
    end
  end

  def show
    response = service.show
    return render_error(404) unless response

    @order_case = response[:order_case]
    @arrangements = response[:arrangements]
    @work_achievements = response[:work_achievements]
    @location = response[:location]
    @order_branch = response[:order_branch]
    @order_portions = response[:order_portions]
    @other_order_cases = response[:other_order_cases]
    @order_case_price = response[:order_case_price]
    @staff_evaluations = response[:staff_evaluations]
    @order_branch_details = response[:order_branch_details]
    @job_page_view = response[:job_page_view]
  end

  def cancel_order_case
    response = service.cancel_order_case
    return render_error(404) unless response

    render json: response
  end

  def cancel_order_cases
    render json: service.cancel_order_cases
  end

  def cancel_order_case_detail
    response = service.cancel_order_case_detail
    return render_error(404) unless response

    render json: response
  end

  def calculate_summary_price
    response = service.calculate_summary_price
    return render_error(404) unless response

    render json: response
  end

  def change_order_portion_number
    response = service.change_order_portion_number
    render_error(404) unless response
  end

  def delete_order_portion
    response = service.delete_order_portion
    return render_error(404) unless response

    render json: response
  end

  def update_special_offer_fee
    response = service.update_special_offer_fee
    return render_error(404) unless response

    render json: {} if response != true
  end

  def view_staff_info
    response = service.view_staff_info
    return render_error(404) unless response

    response
  end

  def staff_contract
    response = service.staff_contract
    return render_error(404) unless response

    response
  end

  def update_changeable
    render json: service.update_changeable
  end

  private
  def service
    Corporation::OrderCasesService.new self
  end
end
