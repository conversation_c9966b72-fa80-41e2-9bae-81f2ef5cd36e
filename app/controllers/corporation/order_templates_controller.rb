class Corporation::OrderTemplatesController < Corporation::BaseController
  wrap_parameters false

  def index
    respond_to do |format|
      format.html do
        @location_options = service.index
      end
      format.json do
        render json: service.index
      end
    end
  end

  def new
    @location_options, @order_template = service.new
  end

  def create
    render json: service.create
  end

  def edit
    @location_options, @order_template = service.edit
    render_error 404 if @order_template.blank?
  end

  def update
    render json: service.update
  end

  def destroy
    render json: service.destroy
  end

  def get_order_templates_from_location
    render json: service.get_order_templates_from_location
  end

  private
  def service
    Corporation::OrderTemplatesService.new(self)
  end
end
