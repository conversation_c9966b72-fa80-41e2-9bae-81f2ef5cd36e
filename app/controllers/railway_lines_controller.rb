class RailwayLinesController < ApplicationController
  def index
    json_response = {}
    if params[:prefecture_id].present?
      @railway_lines = RailwayLine.of_prefecture params[:prefecture_id]
      @adjacent_railway_lines = RailwayLine.of_adjacent_prefecture params[:prefecture_id]

      json_response[:home_railway_lines] = @railway_lines.as_json(only: [:id, :name])
      json_response[:school_railway_lines] = @adjacent_railway_lines
        .as_json(only: [:id], methods: :full_name)
    else
      json_response[:railway_lines] = RailwayLine.pluck(:name, :id)
        .map{|name, id| {name: name, id: id}}
    end

    render json: json_response
  end

  def get_railways_by_prefecture
    json_response = {}
    if params[:prefecture_id].present?
      json_response[:railway_lines] = RailwayLine.of_prefecture(params[:prefecture_id].split(","))
        .pluck(:name, :id).map{|name, id| {name: name, id: id}}
    else
      json_response[:railway_lines] = RailwayLine.pluck(:name, :id)
        .map{|name, id| {name: name, id: id}}
    end

    render json: json_response
  end
end
