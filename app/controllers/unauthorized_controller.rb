class UnauthorizedController < ActionController::Metal
  include ActionController::UrlFor
  include ActionController::Redirecting
  include Rails.application.routes.url_helpers
  include Rails.application.routes.mounted_helpers

  delegate :flash, to: :request

  class << self
    def call env
      @respond ||= action :respond
      @respond.call env
    end
  end

  def respond
    unless request.get?
      message = request.env["warden.options"].fetch(:message, "unauthorized.user")
      flash.alert = I18n.t message if message.present?
    end

    case request.subdomain
    when /^#{Settings.domain.admin}/
      redirect_to admin_session_path
    when /^#{Settings.domain.corporation}/
      redirect_to user_session_path
    else
      redirect_to staff_session_path
    end
  end
end
