class LandingPage::TopPagesController < LandingPage::BaseController
  def home
  end

  def department
    data = service.department
    return render_404 if data.nil?

    @page, @per_page, @lp_area, @lp_search,
    @search_text, @lp_jobs, @total_page = data
  end

  def job
    data = service.job
    return render_404 if data.nil?

    @lp_job, @lp_area, @lp_training_centers, json_response = data
    respond_to do |format|
      format.html
      format.json do
        render json: json_response
      end
    end
  end

  def search
    render json: service.search
  end

  def autocomplete
    render json: service.autocomplete
  end

  private
  def service
    LandingPage::TopPagesService.new(self)
  end
end
