module LocationJobsFormat
  private
  def api_jobs_response order_case_ids, filter_settings
    jobs_data = format_available_jobs(order_case_ids, filter_settings[:formated_date], filter_settings)
    location_ids = jobs_data[:location_ids]
    jobs = jobs_data[:jobs]
    total_count = jobs_data[:total_count]
    jobs_by_location_category = jobs.group_by{|oc| oc["job_category_key"]}
    locations_response = api_format_locations(location_ids, jobs_by_location_category)
    {
      locations: locations_response,
      total_count: total_count
    }
  end

  def format_available_jobs order_case_ids, formated_date, options
    cache_jobs = JobsWage::CacheJobsWagesService.new(formated_date, order_case_ids)
    all_wages = cache_jobs.get_wages
    paging = JobsWage::PagingRemainedJobs.new(all_wages, options)
    paging_jobs_ids = paging.paging_jobs
    total_count = paging.total_count
    order_cases = OrderCase.by_ids(paging_jobs_ids)
      .includes(:location_job_category, :order, :order_branch, arrangements: [:arrange_payment])
      .index_by(&:id).values_at(*paging_jobs_ids).compact
    json_ocs = order_cases.as_json(
      only: OrderCase::NEW_JOB_SEARCH_ATTRS,
      methods: OrderCase::NEW_JOB_SEARCH_METHODS + OrderCase::IDENTIFY_KEY
    )
    json_ocs = format_wages(paging, json_ocs)
    {
      jobs: json_ocs,
      total_count: total_count,
      location_ids: order_cases.map(&:location_id).uniq
    }
  end

  def format_wages wages, json_ocs
    json_ocs.each do |oc|
      order_case_wages = wages.data_wages_by_order_case_id(oc["id"])
      oc["display_unit_price"] = order_case_wages[:display_unit_price]
      oc["display_total_price"] = order_case_wages[:display_total_price]
      oc["average_price"] = order_case_wages[:average_price]
      oc["has_unit_price_addition"] = order_case_wages[:has_unit_price_addition]
      oc["has_total_price_addition"] = order_case_wages[:has_total_price_addition]
    end
    json_ocs
  end

  def api_format_locations location_ids, jobs_by_location_category
    locations = Location.categories_by_locations(location_ids)
      .group_by{|loc| loc["job_category_key"]}
    liked_location_ids = StaffLikeLocation.where(location_id: location_ids)
      .like_locations_by_staff(current_staff.id).pluck(:location_id)
    json_locations = []
    jobs_by_location_category.each_key do |job_category_key|
      location = locations[job_category_key][0]
      location["is_favorited"] = liked_location_ids.include?(location["id"])
      location["jobs"] = jobs_by_location_category[job_category_key]
      json_locations << location
    end
    json_locations
  end
end
