module LoadDataForSelect2
  CLASS_NAME = [Location, Corporation, CorporationGroup]

  def load_data_for_select2 type_search, field_search, class_name
    return unless class_name.in? CLASS_NAME

    last_search_condition = current_admin.send(type_search).last
    if last_search_condition&.send(field_search)
      class_name.by_ids(last_search_condition.send(field_search)).map{|s| [s.full_name, s.id]}
    else
      []
    end
  end
end
