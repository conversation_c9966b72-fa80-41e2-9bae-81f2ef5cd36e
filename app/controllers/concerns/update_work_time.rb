module UpdateWorkTime
  private
  def validate_input_time arrangement, inputed_params
    check_input_time = CheckDayTimeWorking.new(arrangement.working_started_at,
      arrangement.working_ended_at)
    input_working_started_at = inputed_params[:staff_working_started_at]
    input_working_ended_at = inputed_params[:staff_working_ended_at]
    return [t("work_achievement.update_before_current_error"), false, :update_before_current_error] if
      input_working_ended_at.present? && !check_input_time.input_in_working_time?(input_working_ended_at,
        input_working_started_at)
    return [t("work_achievement.within_2_hours_before_start"), false, :within_2_hours_before_start] if
      input_working_started_at.present? && !check_input_time.valid_start_time?(input_working_started_at)
    return [t("work_achievement.switched_to_same_day"), true, :switched_to_same_day] if
      input_working_started_at.present? && input_working_ended_at.present? &&
      check_input_time.switched_to_same_day?(input_working_started_at, input_working_ended_at)

    [nil, false, nil]
  end

  def validate_update_work_achievement achievement, arr, inputed_params
    locked_message, switched_day_warning, error_key = validate_input_time(arr, inputed_params)
    return {
      status: false,
      locked_message: locked_message,
      error_key: error_key,
      message: "",
      achievement: achievement,
      break_time_warning: false,
      actual_working_time_warning: false,
      switched_day_warning: switched_day_warning
    } if locked_message.present?
    achievement.assign_attributes(inputed_params)
    achievement.assign_break_time WorkAchievement::TYPES_WITH_UNDERSCORE[0]
    locked_message = arr.is_locked? ? t("work_achievement.locked_message") : ""
    status = !arr.is_locked? && achievement.valid?
    message = status ? "" : achievement.errors.messages
    break_time_warning = params[:check_break_time] &&
      achievement.break_time_warning?(WorkAchievement::TYPES_WITH_UNDERSCORE[0])
    actual_working_time_warning = params[:check_acutal_wk_time] &&
      achievement.actual_working_time_warning?(WorkAchievement::TYPES_WITH_UNDERSCORE[0])
    {
      status: status,
      achievement: achievement,
      locked_message: locked_message,
      error_key: locked_message.present? ? :locked_message : nil,
      message: message,
      break_time_warning: break_time_warning,
      actual_working_time_warning: actual_working_time_warning,
      switched_day_warning: false
    }
  end

  def save_work_achievement achievement
    status = false
    ActiveRecord::Base.transaction do
      achievement.copy_working_data "staff"
      achievement.save
      achievement.trigger_arrange_data
      achievement.update_order_case_working_status
      OwnerNotification.work_achievment(achievement, current_staff, OwnerNotification.model_types[:order_cases])
      StaffNotification.rm_notif_confirm_work_achievement(current_staff.id, achievement.arrangement.order_case_id)
      achievement.create_work_achievement_log(:staff_input, staff_id: current_staff.id)
      status = true
    end
    {status: status, achievement: achievement}
  end

  def additional_achivement_params
    {
      updater_id: current_staff.id,
      working_time_status_id: WorkAchievement.working_time_status_ids[:owner_confirming],
      updater_type_id: WorkAchievement.updater_type_ids[:staff],
      staff_inputted_at: ServerTime.now
    }
  end
end
