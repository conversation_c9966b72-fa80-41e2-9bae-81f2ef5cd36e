module GaffeCommon
  private
  def prefix
    case request.subdomain
    when /^#{Settings.domain.admin}/
      "admin"
    when /^#{Settings.domain.corporation}/
      "corporation"
    when /^#{Settings.domain.landing_page}/
      "landing_page"
    when /^#{Settings.domain.api}/
      "api"
    else
      "staff"
    end
  end

  def error_template
    error_template_by @status_code
  end

  def error_template_by status_code
    case status_code.to_s
    when /^403|422$/
      :forbidden
    when /^4[0-9]{2}$/
      :not_found
    when /^5[0-9]{2}$/
      :internal_server_error
    end
  end
end
