module CustomExceptions
  class InvalidStaffError < Error::Base; end

  class ReviewingStaffNotfound < Error::Base; end

  class InvalidNotificationType < Error::Base; end

  class AutoRestoreReason < Error::Base; end

  class InvalidTrainingSchedules < Error::Base; end

  class InvalidTypeAction < Error::Base; end

  class MissingArgumentsError < StandardError; end

  class CannotRescheduleInterviewError < Error::Base; end

  class UnavailableInteviewError < StandardError; end

  class StaffAlreadyAppliedInterviewError < StandardError; end

  class TimeRangeError < StandardError; end

  class CreateTrainingScheduleError < StandardError; end

  class RestoreTrainingScheduleError < StandardError; end

  class TrainingScheduleIsFullError < StandardError; end

  class InvalidTrainingScheduleError < StandardError; end

  class CannotRescheduleTrainingError < StandardError; end

  class MissingStaffSelectedError < StandardError; end

  class RecordNotFoundError < StandardError; end

  class UnavailableAbsentError < StandardError; end

  class InvalidBankAccountError < StandardError; end

  class InsurancesNecessaryError < StandardError; end

  class DestroyPeriodRateError < StandardError; end

  class ExistsCustomRateByDateError < StandardError; end

  class NotSqsMessageError < StandardError; end

  class InvalidJsonError < StandardError; end

  class ApplyOrderCaseConditionError < StandardError; end

  class InvalidMethodError < StandardError; end
end
