module <PERSON><PERSON><PERSON><PERSON>
  extend ActiveSupport::Concern
  include RequestIp

  included do
    helper_method :warden, :admin_signed_in?, :current_admin, :user_signed_in?, :current_user,
      :staff_signed_in?, :current_staff, :staff_device_token, :public_current_staff
  end

  def admin_signed_in?
    current_admin.present?
  end

  def current_admin
    warden.user :admin
  end

  def warden
    request.env["warden"]
  end

  def authenticate_admin!
    warden.authenticate! :admin_login, scope: :admin
  end

  def user_signed_in?
    current_user.present?
  end

  def current_user
    warden.user :user
  end

  def authenticate_user!
    warden.authenticate! :user_login, scope: :user
    log_failed_session if !!Settings.ie.accessible
  end

  def bypass_sign_in resource, scope
    expire_data_after_sign_in! scope
    warden.session_serializer.store(resource, scope)
  end

  def expire_data_after_sign_in! scope
    return if session.empty?

    session.keys.grep(/^warden.user.#{scope}\./).each{|k| session.delete(k)}
  end

  def staff_signed_in?
    current_staff.present?
  end

  def current_staff
    update_session_by_device_token
    if api? && !(controller_name == "sessions" && action_name == "create")
      return device_token.staff if device_token.present?

      raise Error::Api::Common::InvalidAccessToken
    end
    warden.user :staff
  end

  def public_current_staff
    current_staff.presence || Staff.guest
  end

  def update_session_by_device_token
    current_session_valid = session.present? &&
      session.keys.grep(/^warden.user.staff\./).present?
    return if current_session_valid
    return if !cookies[:is_workz_web_view].to_s.true? || http_token.blank?

    begin
      staff = device_token.present? ? device_token.staff : nil
      return if staff.blank?

      warden.session_serializer.store(staff, :staff)
      cookies["_session_id"] = session.id.to_s
    rescue StandardError
      nil
    end
  end

  def staff_device_token
    @device_token
  end

  def staff_sign_out
    device_token.destroy if device_token.present?
  end

  def authenticate_staff!
    warden.authenticate!(:staff_login, scope: :staff)
  end

  def authenticate_api! # rubocop:disable Metrics/PerceivedComplexity
    if api? && !(controller_name == "sessions" && action_name == "create")
      return if device_token.present? && device_token.staff

      raise Error::Api::Common::InvalidAccessToken
    end
    login_id = params.dig(:data, :email)&.strip || params.dig(:data, :login_id)&.strip
    password = params.dig :data, :password
    return raise Error::Api::Login::InvalidEmailOrPassword if LockAccount.locked?(login_id, "staff")

    account = Account.by_login_id(login_id)
    staff = account&.staff
    return raise Error::Api::Login::InvalidEmailOrPassword if account.blank? || staff.blank?
    return raise Error::Api::Login::Rejected if staff&.rejected?
    return raise Error::Api::Staff::Retired if staff&.is_retired_extend_time_expired?

    if account && staff && staff.can_authenticate? && account.valid_password?(password)
      warden.session_serializer.store(staff, :staff)
      staff.update_tracked_fields!(request) if api?
      return staff
    end
    raise Error::Api::Login::InvalidEmailOrPassword
  end

  def device_token
    return if http_token.nil?

    payload = jwt_decode(http_token)
    staff_id = payload.try(:[], "staff_id")
    device_id = payload.try(:[], "device_id")
    @device_token ||= DeviceToken.find_by(staff_id: staff_id, device_id: device_id)
  end

  def jwt_encode payload
    JWT.encode(payload, Rails.application.credentials.secret_key_base, "HS256")
  end

  def jwt_decode token
    JWT.decode(token, Rails.application.credentials.secret_key_base, "HS256")[0]
  rescue JWT::ExpiredSignature, JWT::VerificationError, JWT::DecodeError
    raise Error::Api::Common::InvalidAccessToken
  end

  def device_type
    request.headers["WZ-Device-Type"]
  end

  def app_version
    request.headers["WZ-App-Version"]
  end

  def http_token
    return if request.headers["Authorization"].blank?

    request.headers["Authorization"].split.last
  end

  def api?
    request.subdomain.to_s.start_with?(Settings.domain.api)
  end

  def log_failed_session
    redis = redis_instance
    return unless current_user.present? && redis.get(session.id.to_s) == "\u0004\b{\u0000"

    ip_address = ip_from_request(request)
    StoConLoginFailure.save_log current_user, ip_address
  end

  def redis_instance
    Lawson::RedisConnector.new(0)
  end
end
