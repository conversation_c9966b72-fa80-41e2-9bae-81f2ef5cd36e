module AsyncDownload
  EXPORT_INFORMATION_FORMS = %w(employee_information individual_contract haken_destination
    haken_destination_2 staff)
  EXPORT_TIME_SHEETS = %w(location_time_sheet)
  EXPORT_ZIP_FILE = EXPORT_INFORMATION_FORMS + EXPORT_TIME_SHEETS
  ZIP_FILE_FORMAT = "zip"
  XLSX_FILE_FORMAT = "xlsx"
  EXPORT_LOG_MODELS = %w(arrange_billing)
  FOREIGN_EMPLOYMENT = "foreign_employment_statuse"
  PAYMENT_REQUESTS_HISTORY = "payment_requests_history"
  STAFF_WORK_CONDITION = "staff_work_condition"
  ENTRY = "entrie"
  LOCATION_SURVEY = "location_survey"
  VIOLATION_FILE = "violation_file"
  DATA_SUM = "dashboard"
  LOCATION = "location"
  REGISTRATION = "registration"
  INSURANCE_MODELS = %w(both_subsection join_subsection reject_subsection
    both_employment join_employment reject_employment)
  INSURANCE_LOGS_BY_DATE = "insurances_by_date"
  INDIVIDUAL_NUMBER = "individual_number"
  ARRANGEMENT = "arrangement"
  TRAINING_SCHEDULE = "training_schedule"
  TRAINING_SCHEDULE_APPLICANT = "training_schedule_applicant"

  def export_download
    model_name = params[:model_name].presence || request.path.split("/")[1].chop
    format_file = get_format_file model_name
    case model_name
    when STAFF_WORK_CONDITION
      model_name = model_name.pluralize
      file_name = "#{model_name}/#{params[:id]}/" \
        "#{Settings.staff_work_condition.file_name}_#{params[:id]}.#{format_file}"
    when INSURANCE_LOGS_BY_DATE
      file_name = "#{params[:insurance_type]}_insurance_log_export_#{get_job_id}.#{format_file}"
    when LOCATION_SURVEY
      if format_file.to_s == XLSX_FILE_FORMAT
        file_name = "location_survey_export_#{params[:location_survey_id]}_#{get_job_id}.xlsx"
      else
        file_name = "location_surveys_export_#{get_job_id}.#{format_file}"
      end
    when ARRANGEMENT
      if params[:format_template] == "billing_reform"
        file_name = "billing_reform_export_#{get_job_id}.#{format_file}"
      else
        file_name = "#{model_name}_export_#{get_job_id}.#{format_file}"
      end
    else
      file_name = "#{model_name}_export_#{get_job_id}.#{format_file}"
    end

    if format_file.to_s == ZIP_FILE_FORMAT
      response_to_zip model_name, file_name
    else
      response_to_file_format model_name, file_name
    end
    update_downloaded_at(model_name) if EXPORT_LOG_MODELS.include?(model_name)
    update_payment_requests if model_name == PaymentRequest.name.underscore.downcase
    update_exported_staff_status if model_name == ENTRY
  end

  def export_status
    job_status = Sidekiq::Status.get_all(params[:job_id]).symbolize_keys
    model_name = params[:model_name].presence || request.path.split("/")[1].chop

    if model_name == Arrangement.name.downcase
      response = arrangement_export_status(job_status)
      render json: response
    elsif job_status[:status] == "complete" &&
      params.try(:[], :addition_params).try(:[], "action").present? &&
      params[:addition_params]["action"] == PAYMENT_REQUESTS_HISTORY &&
      model_name == PaymentRequest.name.underscore.downcase &&
      RequestPaymentExportLog.where(sidekiq_job_id: params[:job_id]).empty?
      render json: {
        status: :cancel
      }
    else
      render json: {
        status: job_status[:status],
        percentage: job_status[:pct_complete]
      }
    end
  end

  private
  def get_job_id
    params[:id]
  end

  def export_file_name model_name, type
    export_time = ServerTime.now.strftime(Settings.datetime.file_formats)
    case model_name
    when FOREIGN_EMPLOYMENT
      "#{Settings.export.file_name[model_name]}.#{type}"
    when PaymentRequest.model_name.singular
      payment_request_name = "#{Settings.export.file_name[model_name]}#{export_time}"
      payment_request_name + "#{Settings.export_csv.custom_file_name.payment_request}.#{type}"
    when ARRANGEMENT
      if params[:format_template] == "billing_reform"
        billing_reform_file_name export_time
      else
        "#{Settings.export.file_name[model_name]}_#{export_time}.#{type}"
      end
    when ArrangeBilling.model_name.singular
      exp_name = params[:export_name].presence || Settings.export.file_name[model_name]
      "#{exp_name}_#{export_time}.#{type}"
    when STAFF_WORK_CONDITION
      staff = Staff.by_ids(params[:id]).first
      success_time = staff.staff_work_condition_export_logs
        .success_work_conditions.last.success_at.strftime(Settings.datetime.file_formats)
      [Settings.staff_work_condition.file_name, staff.id,
        success_time].join("_") << "." << type.to_s
    when PAYMENT_REQUESTS_HISTORY
      "#{Settings.export.file_name[model_name]}#{export_time}.#{type}"
    when LOCATION_SURVEY
      location = LocationSurvey.find(params[:location_survey_id]).location
      "#{location.name}_#{I18n.t('admin.location_survey.export.file_name')}_#{export_time}.#{type}"
    when DATA_SUM
      report_date = params[:report_date].present? ? params[:report_date].to_date : ServerTime.today
      "#{Settings.export.file_name[model_name]}_" \
        "#{report_date.strftime(Settings.date.year_month_jp)}_#{export_time}.#{type}"
    when LOCATION, INDIVIDUAL_NUMBER, TRAINING_SCHEDULE, TRAINING_SCHEDULE_APPLICANT
      "#{Settings.export.file_name[model_name]}_#{export_time}.#{type}"
    when REGISTRATION
      "#{Settings.export.file_name.registration}_#{export_time}.#{type}"
    when INSURANCE_LOGS_BY_DATE
      "#{Settings.export.file_name[params[:insurance_type]]}_#{export_time}.#{type}"
    when INSURANCE_MODELS
      "#{Settings.export.file_name.insurance[model_name]}_#{export_time}.#{type}"
    else
      if params[:model_name].present?
        file_name = Settings.export_csv.custom_file_name.send(model_name.to_s)
      else
        model_name = "staff" if model_name == ENTRY
        file_name = "ExportData_#{model_name.camelcase}Master"
      end
      "#{file_name}_#{export_time}.#{type}"
    end
  end

  def get_file_from_repository model_name, file_name, type, optional = {disposition: "attachment"}
    if Settings.environment_can_use_aws.include? Rails.env
      s3_folder = Settings.aws.s3.folders.admin_exports
      s3_folder = Settings.aws.s3.folders.admin_insurance_exports if INSURANCE_MODELS.include?(model_name)
      s3_obj = S3_BUCKET.object("#{s3_folder}/#{file_name}").get
      file_data = s3_obj.body.read
    elsif model_name == STAFF_WORK_CONDITION
      file_data = Rails.root.join("public", file_name).read
    elsif model_name == LOCATION_SURVEY
      file_data = Rails.root.join("tmp/location_surveys", file_name).read
    else
      file_data = Rails.root.join("tmp", file_name).read
    end
    send_data file_data, type: Settings.export.type[type],
      filename: export_file_name(model_name, type),
      disposition: optional[:disposition]
  end

  def export_zip_file model_name
    export_time = ServerTime.now.strftime(Settings.datetime.export_order_formats)
    export_time = ServerTime.now.strftime(Settings.datetime.file_formats) if EXPORT_INFORMATION_FORMS.include?(model_name)
    return ExportTimeSheetsWorker.download_zip_file_name if EXPORT_TIME_SHEETS.include?(model_name)
    return ExportViolationFilesService.export_zip_file_name(export_time) if
      VIOLATION_FILE.include?(model_name)
    return ExportLocationSurveysService.export_zip_file_name(export_time) if
      LOCATION_SURVEY.include?(model_name)

    "#{Settings.export_order.file_name[model_name]}-#{export_time}.zip"
  end

  def response_to_file_format model_name, file_name
    respond_to do |format|
      format.xlsx do
        get_file_from_repository model_name, file_name, Settings.export.format.xlsx
      end

      format.csv do
        get_file_from_repository model_name, file_name, Settings.export.format.csv
      end

      format.text do
        get_file_from_repository model_name, file_name, Settings.export.format.txt
      end

      format.pdf do
        get_file_from_repository model_name.chop, file_name, Settings.export.format.pdf
      end
    end
  end

  def response_to_zip model_name, file_name
    if Settings.environment_can_use_aws.include? Rails.env
      s3_folder = Settings.aws.s3.folders.admin_exports
      s3_obj = S3_BUCKET.object("#{s3_folder}/#{file_name}").get
      file_data = s3_obj.body.read
    else
      ex_path = Rails.root.join("tmp", "export_order_branches", file_name)
      ex_path = Rails.root.join("tmp", file_name) if
        (EXPORT_INFORMATION_FORMS + EXPORT_TIME_SHEETS +
          [LOCATION_SURVEY] + [VIOLATION_FILE]).include?(model_name)
      file_data = File.read(ex_path)
    end
    send_data file_data, type: Settings.export.type.zip, filename: export_zip_file(model_name)
  rescue StandardError
  end

  def update_downloaded_at model_name
    @export_log = "#{model_name}_export_log".classify.constantize.find_by sidekiq_job_id: get_job_id
    return unless @export_log

    @export_log.update_column :downloaded_at, ServerTime.now
  end

  def update_exported_staff_status
    staff_log = StaffExportLog.where(sidekiq_job_id: get_job_id).first
    return if staff_log.nil? || staff_log.downloaded_at.present?

    staffs = Staff.staff_entries.where id: staff_log.staff_ids
    staffs.update_all status_id: Staff.status_ids[:export]
    staff_log.update downloaded_at: ServerTime.now
  end

  def update_payment_requests
    @export_log = RequestPaymentExportLog.find_by sidekiq_job_id: get_job_id
    return if @export_log.nil? || @export_log.downloaded_at.present?

    @export_log.update_column :downloaded_at, ServerTime.now
    payment_requests = PaymentRequest.where(id: @export_log.payment_request_ids)
    payment_requests.update_all process_status: :trade_success,
      transfer_date: @export_log.transfer_date
    PaymentRequestWorker.perform_async @export_log.payment_request_ids
  end

  def get_format_file model_name
    return request.format.symbol.to_s if model_name == LOCATION_SURVEY

    if EXPORT_ZIP_FILE.include?(model_name)
      ZIP_FILE_FORMAT
    elsif request.format.to_s == Settings.export.type.txt
      Settings.export.format.txt
    else
      request.format.symbol
    end
  end

  def billing_reform_file_name export_time
    file_prefix = ""
    file_prefix = I18n.t("admin.arrangements.billing_reform.#{params[:invoice_target]}") if params[:invoice_target].present? && params[:invoice_target].split(",").reject(&:blank?).length != 2
    date_range = ""
    if params[:from] && params[:to]
      from = params[:from].to_date.strftime(Settings.datetime.file_date_formats)
      to = params[:to].to_date.strftime(Settings.datetime.file_date_formats)
      date_range = "#{from}_#{to}_"
    end
    "#{file_prefix}#{date_range}#{Settings.export.file_name.billing_reform}_#{export_time}.xlsx"
  end

  def arrangement_export_status job_status
    tracking_export = RedisModels::TrackingExport.new
    final_job_progress = tracking_export.get_final(params[:job_id])

    if final_job_progress.present?
      status = :working
      percentage = (final_job_progress["current"].to_i * 100) / final_job_progress["total"].to_i
      percentage = percentage.to_i
    else
      status = job_status[:status]
      percentage = job_status[:pct_complete].to_i
    end

    {
      status: status,
      percentage: percentage
    }
  end
end
