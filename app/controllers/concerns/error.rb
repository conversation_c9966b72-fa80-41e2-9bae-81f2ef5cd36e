module Error
  CODES = {
    invalid_email_or_password: 1,
    no_device_id: 2,
    rejected: 3,
    bad_request: 4,
    invalid_access_token: 5,
    record_not_found: 6,
    retired: 7,
    not_op_confirmed: 8,
    offered_job_not_available: 9,
    app_version_need_update: 10,
    app_under_maintenance: 11,
    cannot_delete_staff: 12,
    internal_server_error: 99
  }

  CLASSES = [
    "Error::Api::Login::InvalidEmailOrPassword",
    "Error::Api::Login::NoDeviceId",
    "Error::Api::Login::Rejected",
    "Error::Api::Common::BadRequest",
    "Error::Api::Common::InvalidAccessToken",
    "Error::Api::Common::RecordNotFound",
    "Error::Api::Staff::Retired",
    "Error::Api::Staff::NotOpConfirmed",
    "Error::Api::Job::OfferedJobNotAvailable",
    "Error::Api::Common::AppVersionNeedUpdate",
    "Error::Api::Common::AppUnderMaintenance",
    "Error::Api::Staff::CannotDeleteStaff",
    "Error::Api::Common::InternalServerError"
  ]

  class Base < RuntimeError
    def initialize message_hash = {}
      t_key = self.class.name.underscore.gsub("/", ".")
      super(I18n.t(t_key, **message_hash))
    end
  end

  module Api
    module Login
      class InvalidEmailOrPassword < Error::Base; end

      class NoDeviceId < Error::Base; end

      class Rejected < Error::Base; end
    end

    module Common
      class BadRequest < Error::Base; end

      class InvalidAccessToken < Error::Base; end

      class RecordNotFound < Error::Base; end

      class InternalServerError < Error::Base; end

      class AppVersionNeedUpdate < Error::Base; end

      class AppUnderMaintenance < Error::Base
        def initialize
          message_hash = {maintenance_time: Settings.app.maintenance_time&.to_s}
          super(message_hash)
        end
      end
    end

    module Staff
      class Retired < Error::Base; end

      class NotOpConfirmed < Error::Base; end

      class CannotDeleteStaff < Error::Base; end
    end

    module Job
      class OfferedJobNotAvailable < Error::Base; end
    end
  end
end
