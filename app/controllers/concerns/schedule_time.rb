module ScheduleTime
  def schedule_range time, schedules, staff_id
    date = time.strftime(Settings.date.formats).to_date
    beginning_cur_week = date.beginning_of_week - 1.day
    [beginning_cur_week - 1.week, beginning_cur_week, beginning_cur_week + 1.week,
      beginning_cur_week + 2.weeks, beginning_cur_week + 3.weeks, beginning_cur_week + 4.weeks]
      .to_a.map do |first_day_of_week|
        sunday = first_day_of_week.strftime(Settings.date.day_and_month)
        saturday = (first_day_of_week + 6.days).strftime(Settings.date.day_and_month)
        {
          week_range: "#{sunday} ~ #{saturday}",
          week_days: week_days(first_day_of_week, schedules, staff_id, time),
          year: (first_day_of_week + 6.days).strftime(Settings.date.year)
        }
      end
  end

  private

  def first_last_of_schedule_range time
    date = time.to_date
    beginning_cur_week = date.beginning_of_week - 1.day
    [beginning_cur_week - 1.week, beginning_cur_week + 4.weeks + 6.days]
  end

  def build_empty_schedule day, staff_id, current_time
    disable_morning, disable_afternoon, disable_evening, disable_night = disable_times(
      current_time, day
    )
    {
      id: nil,
      staff_id: staff_id,
      setting_date: day,
      is_morning: false,
      is_afternoon: false,
      is_evening: false,
      is_night: false,
      disable_morning: disable_morning,
      disable_afternoon: disable_afternoon,
      disable_evening: disable_evening,
      disable_night: disable_night
    }
  end

  def build_schedule schedules, formated_date, day, current_time
    disable_morning, disable_afternoon, disable_evening, disable_night = disable_times(
      current_time, day
    )
    schedule = schedules[formated_date]&.first
    schedule&.as_json&.merge(disable_morning: disable_morning,
      disable_afternoon: disable_afternoon, disable_evening: disable_evening,
      disable_night: disable_night)
  end

  def disable_times current_time, date
    [
      current_time > date.beginning_of_day + Settings.staff_schedule.time.end_of_morning.hours,
      current_time > date.beginning_of_day + Settings.staff_schedule.time.end_of_afternoon.hours,
      current_time > date.beginning_of_day + Settings.staff_schedule.time.end_of_evening.hours,
      current_time > date.beginning_of_day + Settings.staff_schedule.time.end_of_night.hours
    ]
  end

  def week_days date, schedules, staff_id, current_time
    (date..date + 6.days).to_a.map do |day|
      formated_date = day.strftime(Settings.date.formats)
      data = build_schedule(schedules, formated_date, day, current_time) ||
        build_empty_schedule(day, staff_id, current_time)
      {
        display_name: day.strftime(Settings.date.day_and_month),
        month: I18n.t("common.day_names.#{day.strftime(Settings.date.day).downcase}"),
        date: formated_date,
        data: data
      }
    end
  end
end
