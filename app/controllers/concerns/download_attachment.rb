module DownloadAttachment
  INLINE_FORMAT = %w(jpg jpeg gif png pdf)

  def download_notification_attachment
    case params[:controller]
    when "admin/admin_notification_owners", "corporation/homes"
      object_class = AdminNotificationOwner
    when "admin/admin_notification_staffs", "staff/top_pages"
      object_class = AdminNotificationStaff
    end
    notification = object_class.find(params[:notification_id])
    file_url = notification.attachment_url.file.path
    file_extension = notification.attachment_url.file.extension.downcase
    disposition = INLINE_FORMAT.include?(file_extension) ? "inline" : "attachment"
    original_filename = notification.get_attachment_file_name
    if Settings.environment_can_use_aws.include? Rails.env
      mime_type = MIME::Types.type_for(file_url).first.content_type
      file_data = S3_BUCKET.object(file_url).get.body.read
      send_data file_data, type: mime_type, filename: original_filename, disposition: disposition
    else
      send_file file_url, filename: original_filename, dispostion: disposition
    end
  end
end
