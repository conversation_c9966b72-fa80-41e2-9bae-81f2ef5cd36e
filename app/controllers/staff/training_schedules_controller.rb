class Staff::TrainingSchedulesController < Staff::AuthenticateController
  layout "staff/entry_layout", only: [:new]

  before_action :redirect_if_staff_public, :redirect_unless_can_view
  before_action :redirect_if_has_experience, :check_staff_maintenance_mode

  before_action :redirect_if_staff_has_schedules, only: :new
  before_action :redirect_if_no_schedules, only: [:details, :reschedule]

  rescue_from(CustomExceptions::TrainingScheduleIsFullError) do
    message = I18n.t("staff.training_schedule.errors.schedule_unavailable")
    render json: JsonResponse.redirect_path(false, message, "")
  end

  rescue_from(CustomExceptions::InvalidTrainingScheduleError) do
    message = I18n.t("staff.training_schedule.errors.schedule_unavailable")
    render json: JsonResponse.redirect_path(false, message, "")
  end

  rescue_from(CustomExceptions::MissingStaffSelectedError) do
    message = I18n.t("errors.messages.generic_error")
    render json: JsonResponse.redirect_path(false, message, "")
  end

  rescue_from(CustomExceptions::UnavailableAbsentError) do
    render json: JsonResponse.failure(I18n.t("staff.training_schedule.errors.absent_failed"))
  end

  rescue_from(CustomExceptions::RecordNotFoundError) do
    render json: JsonResponse.failure(I18n.t("staff.training_schedule.errors.absent_failed"))
  end

  rescue_from(CustomExceptions::CannotRescheduleTrainingError) do
    message = I18n.t("staff.training_schedule.errors.cannot_reschedule").html_safe
    respond_to do |format|
      format.html do
        flash[:error] = message
        redirect_back(fallback_location: details_staff_training_schedules_path)
      end
      format.json do
        render json: JsonResponse.redirect_path(false, message, "")
      end
    end
  end

  def new
    assign_variables(service.new)
  end

  def create
    render json: service.create
  end

  def details
    staff_recruitment_process = current_staff.staff_recruitment_process
    @training_process_code = staff_recruitment_process.training_process_code
    return unless staff_recruitment_process.arranged_training?

    @consider_dropping_out = staff_recruitment_process.consider_dropping_out

    data = service.details
    assign_variables(data)

    @training_process_code = "no_training_schedule" unless data
  end

  def reschedule
    case request.method
    when "GET"
      data = service.reschedule
      redirect_to new_staff_training_schedule_path unless data

      assign_variables(data)
    when "POST"
      render json: service.reschedule_schedules
    else
      assign_variables(service.reschedule)
    end
  end

  def list_by_training_center
    render json: service.list_by_training_center
  end

  def cancel
    render json: service.cancel
  end

  def submit_email
    render json: service.submit_email
  end

  def check_able_to_absent
    render json: service.check_able_to_absent
  end

  def cancellation_info
    render json: service.cancellation_info
  end

  private

  def redirect_unless_can_view
    redirect_to_root_or_off_webview unless current_staff&.can_view_interview_training_schedule?
  end

  def redirect_if_has_experience
    return unless current_staff.has_experience?

    redirect_to is_mobile_view? ? staff_close_app_webview_path : staff_jobs_path
  end

  def redirect_if_staff_has_schedules
    return if current_staff.staff_recruitment_process&.no_training_schedule? ||
      !current_staff.has_schedule_applicants?

    redirect_to details_staff_training_schedules_path
  end

  def redirect_if_no_schedules
    return unless %w(not_in_progress training_schedule_step).include?(
      current_staff.staff_recruitment_process&.registration_process_code
    )

    redirect_to new_staff_training_schedule_path
  end

  def service
    Staff::TrainingSchedulesService.new(self)
  end

  def check_staff_maintenance_mode
    return unless Settings.domain.staff == request.subdomain &&
      Settings.staff_web.under_maintenance && action_name != "maintenance"

    redirect_to staff_maintenance_path
  end
end
