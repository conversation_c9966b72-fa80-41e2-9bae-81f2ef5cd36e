class Staff::JobsController < Staff::AuthenticateController
  layout "layouts/job/base"
  skip_before_action :authenticate_staff!

  def index
    data = service.index
    return render json: data if request.format.symbol == :json
    return redirect_to(data) if data.is_a?(String)

    @prefectures = data[:prefectures]
    @last_conditions = data[:last_conditions]
    @initial_prefecture_id = data[:initial_prefecture_id]
    @day_options = data[:day_options]
    @group_day_options = data[:group_day_options]
  end

  def remained_jobs
    render json: service.remained_jobs
  end

  def next_date_has_job
    render json: service.next_date_has_job
  end

  def location_jobs
    data = service.location_jobs
    return redirect_to(data) if data.is_a?(String)

    respond_to do |format|
      format.html do
        @location_job_category = data[:location_job_category]
        @is_liked = data[:is_liked]
      end
      format.json do
        render json: data
      end
    end
  end

  def location_jobs_status
    render json: service.location_jobs_status
  end

  private

  def service
    Staff::JobsService.new(self)
  end
end
