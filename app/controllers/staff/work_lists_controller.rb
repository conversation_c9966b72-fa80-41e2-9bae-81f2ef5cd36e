class Staff::<PERSON>ListsController < Staff::<PERSON><PERSON><PERSON><PERSON>Controll<PERSON>
  def index
  end

  def work_history
    @year_options, @current_year, @current_month = service.work_history
  end

  def load_data_work_history_list
    render json: service.load_data_work_history_list
  end

  def load_data_work_list
    render json: service.load_data_work_list
  end

  def load_data_arranged_work_list
    render json: service.load_data_arranged_work_list
  end

  private
  def service
    Staff::WorkListsService.new(self)
  end
end
