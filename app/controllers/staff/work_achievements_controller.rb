class Staff::<PERSON>A<PERSON>evementsController < Staff::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>roll<PERSON>
  def input_work_time
    @achievement, @arr, @order_case, @arrangement,
      @order_case_data, @order_branch, redirect_path = service.input_work_time
    redirect_to(redirect_path) if redirect_path
  end

  def confirm_work_time
    data = service.confirm_work_time
    redirect_path, is_redirect = data
    return redirect_to(redirect_path) if is_redirect == true

    @achievement, @arr, @order_case, @break_time_warning = data
  end

  def approve_confirm_work_time
    redirect_to(service.approve_confirm_work_time)
  end

  def staff_update_working_time
    data, is_redirect = service.staff_update_working_time
    return redirect_to(data) if is_redirect

    render json: data
  end

  def update
    render json: service.update
  end

  private
  def service
    Staff::WorkAchievementsService.new(self)
  end
end
