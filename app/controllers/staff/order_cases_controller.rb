class Staff::Order<PERSON><PERSON><PERSON>ontroller < Staff::Authenti<PERSON>Controller
  protect_from_forgery with: :exception
  skip_before_action :authenticate_staff!, only: [:index, :similar_order_cases, :show]
  layout "print/base", only: :recruiting_form

  def index
    respond_to do |format|
      format.html do
        data = service.index
        return redirect_to(data) if data.is_a?(String)

        @prefectures = data
      end
      format.json do
        render json: service.index
      end
    end
  end

  def check_interview_time
    render json: service.check_interview_time
  end

  def show
    data = service.show
    if data.is_a?(Hash)
      return render_error(404) if data[:is_error_404]
      return render_unavailable_order_case if data[:is_unavailable_order_case]
    else
      @job_wages, @order_case, @order, @location, @corporation, @arrangement,
      @arrangement_regular, @regular_order_cases, @staff_apply_order_case,
      @location_evaluation, @liked_location, @current_kept_id, @is_hide_similar_order_cases,
      @location_info, @job_category_info = data
    end
  end

  def similar_order_cases
    render json: service.similar_order_cases
  end

  def recruiting_form
    data = service.recruiting_form
    return render_error(404) if data.is_a?(Hash) && data[:is_error_404]

    @order_case, @arrangement, @staff_apply_order_case = data
  end

  def check_insurance_for_work_achievement
    render json: service.check_insurance_for_work_achievement
  end

  def save_employment_insurance
    render json: service.save_employment_insurance
  end

  private
  def service
    Staff::OrderCasesService.new(self)
  end

  def render_unavailable_order_case
    render "#{prefix}/errors/unavailable_order_case", formats: :html
  end
end
