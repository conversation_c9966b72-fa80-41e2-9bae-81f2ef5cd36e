class Staff::ArrangementsController < Staff::AuthenticateController
  rescue_from(ActiveRecord::RecordInvalid) do
    render json: {status: false}
  end

  def update
    cmd = Arrangements::StaffUpdateBasicFieldsCommand.new(current_staff, params[:id], arrangement_params.to_h)
    status = cmd.perform

    render json: {status: status}
  end

  private

  def arrangement_params
    params.require(:arrangement).permit Arrangement::UPDATABLE_ATTRS
  end
end
