class Staff::<PERSON><PERSON><PERSON>ontroll<PERSON> < Staff::Authenti<PERSON><PERSON><PERSON>roller
  skip_before_action :check_if_staff_retired
  before_action :check_if_extend_time_expired
  before_action :off_my_page_and_related_link

  def index
    @staff, data = service.index
    respond_to do |format|
      format.html
      format.json do
        render json: data
      end
    end
  end

  def generate_pdf
    result = service.generate_pdf
    return redirect_back(fallback_location: root_path) if result.is_a?(Hash) && result[:no_salary]

    render_error(404) if result.is_a?(Hash) && result[:cannot_view_salary]
  end

  private
  def service
    Staff::SalariesService.new(self)
  end
end
