class Staff::SessionsController < ApplicationController
  layout "staff/unauthenticate_layout"
  before_action :save_utm_tags, :redirect_if_logged_in, only: [:new, :create]
  before_action :back_to_root, only: :maintenance

  def new
  end

  def create
    redirect_to service.create
  end

  def destroy
    service.destroy
    redirect_to staff_order_cases_path
  end

  # ! Deprecated from PWA Removal
  def delete_fcm_token
    render json: service.delete_fcm_token
  end

  def close_app_webview; end

  def maintenance; end

  private
  def service
    Staff::SessionsService.new(self)
  end

  def back_to_root
    return unless action_name == "maintenance"

    redirect_to staff_root_path unless Settings.staff_web.under_maintenance
  end
end
