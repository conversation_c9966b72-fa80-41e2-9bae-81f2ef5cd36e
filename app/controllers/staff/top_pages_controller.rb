class Staff::TopPagesController < Staff::AuthenticateController
  include DownloadAttachment

  def show
    return redirect_to staff_order_cases_path unless current_staff.op_confirm?

    respond_to do |format|
      format.html do
        @have_new_notification, @nearest_arrangement, @nearest_order_case, @hr_labor_file_types = service.show
      end
      format.json do
        render json: service.show
      end
    end
  end

  def order_cases_list
    render json: service.order_cases_list
  end

  def locations_list
    render json: service.locations_list
  end

  def notifications
    respond_to do |format|
      format.html
      format.json do
        render json: service.notifications
      end
    end
  end

  def new_notifications
    render json: service.new_notifications
  end

  def turn_off_notification_popup
    render json: service.turn_off_notification_popup
  end

  def disable_notification_display
    render json: service.disable_notification_display
  end

  private
  def service
    Staff::TopPagesService.new(self)
  end
end
