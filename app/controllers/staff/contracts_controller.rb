class Staff::<PERSON>tractsController < Staff::AuthenticateController
  before_action :check_valid_contract_code, except: [:mobileview_completion]

  def show
    respond_to do |format|
      format.html
      format.json do
        render json: service.show
      end
    end
  end

  def detail
    data = service.detail
    return render_error(404) if data.nil?

    @staff, @for_export_pdf, file_name, template, layout = data

    render pdf: file_name, template: template, layout: layout
  end

  def update_contract
    render json: service.update_contract
  end

  def update_registration_type
    data = service.update_registration_type
    return if data.nil?

    render json: data
  end

  def edit
    respond_to do |format|
      format.html
      format.json do
        render json: service.edit
      end
    end
  end

  def step_1_valid
    render json: service.step_1_valid
  end

  def mobileview_completion
    render_error(404) unless !!cookies[:is_workz_web_view]
  end

  private
  def check_valid_contract_code
    return if service.check_valid_contract_code

    render_error(404)
  end

  def service
    Staff::ContractsService.new(self)
  end
end
