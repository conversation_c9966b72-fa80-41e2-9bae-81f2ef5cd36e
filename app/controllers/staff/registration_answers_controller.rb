class Staff::RegistrationAnswersController < Staff::AuthenticateController
  layout "staff/entry_layout"

  before_action :redirect_if_staff_public, :redirect_if_staff_op_confirm
  before_action :redirect_if_regist_not_answer_step

  def new
    @business_circles, @industries, @professions, @answer = service.new
  end

  def create
    render json: service.create
  end

  private

  def service
    Staff::RegistrationAnswersService.new(self)
  end

  # * PTS
  def redirect_if_regist_not_answer_step
    return if current_staff.staff_recruitment_process&.registration_answer_step?

    if is_mobile_view?
      redirect_to staff_close_app_webview_path
    else
      redirect_to staff_jobs_path
    end
  end
end
