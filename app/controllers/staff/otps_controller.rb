class Staff::OtpsController < Staff::BaseController
  include Response
  wrap_parameters false

  layout "staff/entry_layout"

  before_action :redirect_if_logged_in

  def index
    @token, @login_id = service.index
    redirect_to staff_root_path if @token.blank?
  end

  def verify
    render json: service.verify
  end

  def resend
    render json: service.resend
  end

  private
  def service
    Staff::OtpsService.new(self)
  end
end
