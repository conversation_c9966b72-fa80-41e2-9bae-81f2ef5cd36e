class Staff::VacationsController < Staff::Authenti<PERSON>Controller
  before_action :check_if_staff_able_edit_profile, only: [:new, :create]

  rescue_from(CustomExceptions::TimeRangeError) do
    render json: {status: false}
  end

  def new
    data = service.new
    redirect_to(staff_profiles_path) if data.nil?
    @staff, @staff_long_vacations = data
  end

  def create
    data = service.create
    return redirect_to(staff_profiles_path) if data.nil?

    render json: data
  end

  private
  def service
    Staff::VacationsService.new(self)
  end
end
