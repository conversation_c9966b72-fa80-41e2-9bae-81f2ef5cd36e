class Staff::Authenti<PERSON><PERSON><PERSON>roller < Staff::Base<PERSON>ontroller
  before_action :authenticate_staff!
  before_action :check_if_staff_retired
  before_action :set_staff_last_activity
  before_action :redirect_if_staff_not_op_confirm

  def check_if_staff_able_edit_profile
    redirect_to staff_profiles_path unless auth_service.check_if_staff_able_edit_profile
  end

  private
  def auth_service
    Staff::AuthenticateService.new(self)
  end

  def redirect_if_staff_not_op_confirm
    return if request.xhr? || current_staff.blank? || current_staff&.op_confirm?
    return render_error 404 unless controller_name.in? Settings.staff_status_registered_limit

    case current_staff.staff_recruitment_process&.registration_process_code
    when "registration_answer_step" # 1
      redirect_to staff_registration_answers_path if controller_name != "registration_answers"
    when "interview_schedule_step" # 2
      redirect_to new_staff_interview_schedule_path if controller_name != "interview_schedules"
    when "profile_step" # 3, 4, 5
      nil
      # return redirect_to staff_registered_profiles_edit_path if
      #   controller_name != "registered_profiles" && force_redirect_to_registered_profile?
    when "training_schedule_step" # 6
      redirect_to new_staff_training_schedule_path if controller_name != "training_schedules"
    end
  end

  def redirect_if_staff_public
    return if current_staff.present?

    redirect_to_root_or_off_webview
  end

  def redirect_if_staff_op_confirm
    return unless current_staff&.op_confirm?

    redirect_to_root_or_off_webview
  end

  def force_redirect_to_registered_profile?
    current_staff.can_update_registered_profile? && !current_staff.has_experience?
  end

  def redirect_to_root_or_off_webview
    if is_mobile_view?
      redirect_to staff_close_app_webview_path
    else
      redirect_to staff_root_path
    end
  end
end
