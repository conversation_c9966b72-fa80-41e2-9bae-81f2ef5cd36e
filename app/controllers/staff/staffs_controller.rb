class Staff::StaffsController < Staff::BaseController
  before_action :authenticate_staff!, :off_my_page_and_related_link, :check_if_staff_retired,
    only: :show_work_condition_notice

  layout "staff/entry_layout"

  def new
    redirect_to staff_new_registration_path
  end

  def show_work_condition_notice
    return render_error(404) if current_staff.can_not_access_work_condition?

    service.show_work_condition_notice
  end

  private
  def service
    Staff::StaffsService.new(self)
  end
end
