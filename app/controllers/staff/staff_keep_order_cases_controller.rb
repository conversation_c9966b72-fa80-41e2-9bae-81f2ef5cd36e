class Staff::Staff<PERSON>eepOrderCasesController < Staff::AuthenticateController
  def index
    respond_to do |format|
      format.html do
        @staff_keep_order_cases = service.index
      end
      format.json do
        render json: service.index
      end
    end
  end

  def create
    render json: service.create
  end

  private

  def service
    Staff::StaffKeepOrderCasesService.new(self)
  end
end
