class Staff::MyPagesController < Staff::Authenti<PERSON>C<PERSON>roller
  skip_before_action :check_if_staff_retired, except: [:work_rules_pdf]
  before_action :check_if_extend_time_expired
  before_action :off_my_page_and_related_link
  delegate :work_rules, to: :service

  def show
    data = service.show
    return if data.nil?

    render json: data
  end

  private

  def service
    Staff::MyPagesService.new(self)
  end
end
