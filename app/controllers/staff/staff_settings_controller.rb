class Staff::StaffSettingsController < Staff::AuthenticateController
  layout "staff/entry_layout", if: :not_completed_registration?

  before_action :off_my_page_and_related_link, only: [:edit_password, :edit_notification]
  skip_before_action :redirect_if_staff_not_op_confirm, only: [:edit_email, :edit_tel,
    :send_email_verification, :send_tel_verification]

  def edit_avatar
    @staff = current_staff
  end

  def update_avatar
    render json: service.update_avatar
  end

  def edit_password
    @account = service.edit_password
  end

  def edit_tel
    data = service.edit_tel
    @account = data[:account]
    @tel_existed = data[:tel_existed]
  end

  def edit_email
    data = service.edit_email
    @account = data[:account]
    @email_existed = data[:email_existed]
    @profile = data[:profile]
  end

  def update_password
    @account, data = service.update_password
    return render "edit_password" unless data

    redirect_to edit_password_staff_settings_path
  end

  def send_email_verification
    data = service.send_email_verification
    @account = data[:account]
    @email_existed = data[:email_existed]
    return render "edit_email" unless data[:status]

    redirect_to staff_staff_otps_path(login_id: data[:login_id],
      token: data[:token], profile: data[:profile])
  end

  def send_tel_verification
    data = service.send_tel_verification
    @account = data[:account]
    @tel_existed = data[:tel_existed]
    return render "edit_tel" unless data[:status]

    redirect_to staff_staff_otps_path(login_id: data[:login_id], token: data[:token])
  end

  def edit_notification
    @category_settings = service.edit_notification
  end

  def update_notification
    service.update_notification
    redirect_to edit_notification_staff_settings_path
  end

  def disable_notification_display
    render json: service.disable_notification_display
  end

  # ! Deprecated from PWA Removal
  def save_token
    render json: service.save_token
  end

  # ! Deprecated from PWA Removal
  def check_token
    render json: service.check_token
  end

  # ! Deprecated from PWA Removal
  def check_permission_requested
    render json: service.check_permission_requested
  end

  # ! Deprecated from PWA Removal
  def set_permission_requested
    render json: service.set_permission_requested
  end

  private

  def load_account
    @account = current_staff.account
  end

  def service
    Staff::StaffSettingsService.new(self)
  end

  def not_completed_registration?
    current_staff&.staff_recruitment_process.present? &&
      !current_staff&.staff_recruitment_process.completed_registration?
  end
end
