class Staff::<PERSON><PERSON><PERSON>aborFilesController < Staff::<PERSON>thenticateController
  skip_before_action :authenticate_staff!, only: :view_file_with_token

  def view_file
    return render_error(404) if params[:type].blank?

    file = HrLaborFile.where(document_type: params[:type]).order(updated_at: :desc).first
    return render_error(404) if file.blank? || staff_cannot_view_files?

    render_file(file)
  end

  def view_file_with_token
    return render_error(404) if params[:type].blank? || params[:token].blank? || !valid_token?

    file = HrLaborFile.where(document_type: params[:type]).order(updated_at: :desc).first
    return render_error(404) if file.blank?

    clear_one_time_token
    render_file(file)
  end

  private

  def staff_cannot_view_files?
    current_staff.is_retired?
  end

  def valid_token?
    redis = Lawson::RedisConnector.new(Settings.redis.db.default)
    redis.get(params[:token]).present?
  end

  def clear_one_time_token
    return if params[:token].blank?

    redis = Lawson::RedisConnector.new(Settings.redis.db.default)
    redis.del(params[:token])
  end

  def render_file file
    file_url = file.document_file.file.path
    original_filename = file.document_file_identifier
    if Settings.environment_can_use_aws.include?(Rails.env)
      mime_type = MIME::Types.type_for(file_url).first.content_type
      file_data = S3_BUCKET.object(file_url).get.body.read
      send_data(file_data, type: mime_type, filename: original_filename, disposition: "inline")
    else
      send_file(file_url, file_name: original_filename, disposition: "inline")
    end
  end
end
