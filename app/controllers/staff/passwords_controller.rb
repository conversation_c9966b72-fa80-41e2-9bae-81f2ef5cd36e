class Staff::PasswordsController < ApplicationController
  layout "staff/unauthenticate_layout"
  before_action :save_utm_tags

  def new
    data = service.new
    return if data.nil?

    redirect_to data
  end

  def create
    redirect_to(service.create)
  end

  def edit
    data = service.edit
    return redirect_to(data) if data.is_a?(String)

    @token, @staff, @account = data
  end

  def update
    data = service.update
    return redirect_to(data) if data.is_a?(String)

    @token, @account, @staff, is_success = data
    return render :edit unless is_success

    if is_mobile_view?
      redirect_to staff_close_app_webview_path
    else
      redirect_to staff_root_path
    end
  end

  private
  def service
    Staff::PasswordsService.new(self)
  end
end
