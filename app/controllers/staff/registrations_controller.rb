class Staff::RegistrationsController < Staff::BaseController
  include Response
  wrap_parameters false

  layout "staff/entry_layout"

  before_action :redirect_if_logged_in, only: [:new, :create]
  before_action :check_before_save, only: [:create]

  def new
  end

  def register_phone
    render json: service.register_phone
  end

  def verify_phone
    render json: service.verify_phone
  end

  def create
    result = service.create
    options = result[:staff_number_not_unique] ? {staff_number_not_unique: true} : {}
    json_response(result[:staff], result[:redirect_path], result[:account], result[:status], options)
  end

  def check_existed_postal_code
    render json: service.check_existed_postal_code
  end

  def check_email_uniq
    render json: service.check_email_uniq
  end

  def check_tel_uniq
    render json: service.check_tel_uniq
  end

  private

  def check_before_save
    result = service.check_before_save
    render json: result unless result.nil?
  end

  def service
    Staff::RegistrationsService.new(self)
  end
end
