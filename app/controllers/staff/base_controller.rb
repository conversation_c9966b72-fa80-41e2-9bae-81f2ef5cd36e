require "browser/aliases"
Browser::Base.include Browser::Aliases

class Staff::BaseController < ApplicationController
  protect_from_forgery

  include MultiLanguage

  before_action :default_url_options, :store_staff_location, :save_utm_tags
  delegate :set_staff_last_activity, to: :staff_service

  private
  def store_staff_location
    store_location! :staff
  end

  def get_success_message_for model, action = action_name, attr = nil
    model = model.class.model_name
    model_name = (attr.present? ? model.name.constantize.human_attribute_name(attr) : model.human)
    I18n.t "common.admin.messages.#{action}", model_name: model_name
  end

  def off_my_page_and_related_link
    redirect_to staff_root_path if Settings.is_off_my_page &&
      Settings.active_my_page_departments.exclude?(current_staff.current_department_id)
  end

  def check_if_staff_retired
    return unless current_staff

    render_error 404 if current_staff.is_retired?
  end

  def check_if_extend_time_expired
    return unless current_staff

    render_error 404 if current_staff.is_retired_extend_time_expired?
  end

  def mobile_device?
    request.user_agent =~ /Mobile|webOS/
  end

  def staff_service
    Staff::StaffService.new self
  end

  def assign_variables result = {}
    return if result.blank?

    result.each do |key, value|
      instance_variable_set("@#{key}", value)
    end
  end
end
