class Staff::<PERSON>sController < Staff::AuthenticateController
  before_action :check_if_staff_able_edit_profile, only: [:edit, :update,
    :account_transfer, :update_account_transfer, :update_registration_type]
  before_action :off_my_page_and_related_link

  def show
    @staff, @account, @staff_vacations, @staff_account_transfer,
    @is_read_alert_review, @review_status, @verify_account_status = service.show
  end

  def edit
    data = service.edit
    return redirect_to(staff_profiles_path) if data.nil?

    @edited_staff,
    @edited_staff_check_item,
    @uniform_sizes,
    @pant_sizes,
    @shoes_sizes,
    @prefectures,
    @relationships,
    @residence_statuses,
    @residence_permissions,
    @specific_activity_residence_statuses,
    @japanese_levels = data
  end

  def update
    data = service.update
    return redirect_to(staff_profiles_path) if data.nil?

    render json: data
  end

  def update_educations
    data = service.update_educations
    return if data.nil?

    render json: data
  end

  def education_data
    render json: service.education_data
  end

  def work_experience_data
    render json: service.work_experience_data
  end

  def update_work_experiences
    data = service.update_work_experiences
    return if data.nil?

    render json: data
  end

  def account_transfer
    data = service.account_transfer
    return redirect_to(staff_profiles_path) if data.nil?

    @edited_staff, @bank = data
  end

  def update_account_transfer
    data = service.update_account_transfer
    return redirect_to(staff_profiles_path) if data.nil?

    render json: data
  end

  def registration_type
    @answer = service.registration_type
  end

  def update_registration_type
    data = service.update_registration_type
    return redirect_to(staff_profiles_path) if data.nil?

    render json: data
  end

  private

  def service
    Staff::ProfilesService.new(self)
  end
end
