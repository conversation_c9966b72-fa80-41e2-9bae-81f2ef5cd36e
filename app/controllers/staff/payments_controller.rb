class Staff::PaymentsController < Staff::AuthenticateController
  before_action :check_payment_request_type, except: [:not_regist_payment, :change_payment_request]
  before_action :off_my_page_and_related_link

  rescue_from(CustomExceptions::InvalidBankAccountError) do
    flash[:error] = I18n.t("staff.errors.invalid_bank")
    redirect_back(fallback_location: staff_payments_path)
  end

  def show
  end

  def confirm_request
    url, @arrangements, @payment_request, @temp_total_price,
    @system_fee, @total_price = service.confirm_request
    redirect_to staff_payments_path if url
  end

  def submit_request
    data = service.submit_request
    return redirect_to staff_payments_path if data.nil?

    render json: data
  end

  def load_data_arrangements
    render json: service.load_data_arrangements
  end

  def not_regist_payment
  end

  # Deprecated
  def change_payment_request
    render json: service.change_payment_request
  end

  def payment_request_description
  end

  private

  def check_payment_request_type
    redirect_to not_regist_payment_staff_payments_path if current_staff.not_apply?
  end

  def service
    Staff::PaymentsService.new(self)
  end
end
