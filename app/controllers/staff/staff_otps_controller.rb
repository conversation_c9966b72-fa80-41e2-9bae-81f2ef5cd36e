class Staff::Staff<PERSON>tpsController < Staff::AuthenticateController
  skip_before_action :redirect_if_staff_not_op_confirm

  include Response
  wrap_parameters false

  layout "staff/entry_layout"

  def index
    data = service.index
    return render_error(404) unless data

    @login_id = data[:login_id]
    @token = data[:token]
    @profile = data[:profile]
  end

  def resend
    render json: service.resend
  end

  def verify
    render json: service.verify
  end

  def reverify_tel
    data = service.reverify_tel
    return redirect_to edit_tel_staff_settings_path unless data

    redirect_to staff_staff_otps_path(login_id: data[:login_id], token: data[:token])
  end

  private
  def service
    Staff::StaffOtpsService.new(self)
  end
end
