class Staff::StaffApplyOrderCaseController < Staff::AuthenticateController
  rescue_from(CustomExceptions::InsurancesNecessaryError) do |e|
    render json: e.message
  end

  rescue_from(CustomExceptions::ApplyOrderCaseConditionError) do |e|
    error_response = ErrorResponseBlueprint.render({status: false, errors: JSON.parse(e.message)},
      view: :apply_order_case)
    render json: error_response
  end

  def create
    render json: service.create
  end

  # TODO(Phuong): deprecated, removed
  def apply_training_order_cases
    render json: service.apply_training_order_cases
  end

  def check_apply_condition
    result = service.check_apply_condition
    render json: result unless result.nil?
  end

  def check_insurance_of_staff
    render json: service.check_insurance_of_staff
  end

  private
  def service
    Staff::StaffApplyOrderCasesService.new(self)
  end
end
