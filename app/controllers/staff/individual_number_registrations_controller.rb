class Staff::IndividualNumberRegistrationsController < Staff::<PERSON><PERSON><PERSON><PERSON>Controll<PERSON>
  def index
    redirect_to new_staff_individual_number_registration_path unless current_staff.can_view_my_number?
    @partner_data, @registration = service.index
  end

  def new
    redirect_to staff_individual_number_registrations_path unless current_staff.can_edit_my_number?
    @is_new_form, @partner_data = service.new
  end

  def create
    render json: service.create
  end

  def check_individual_number
    render json: service.check_individual_number
  end

  private
  def service
    Staff::IndividualNumberRegistrationsService.new(self)
  end
end
