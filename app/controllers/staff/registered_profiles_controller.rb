class Staff::RegisteredProfilesController < Staff::AuthenticateController
  include Response

  layout "staff/entry_layout", if: :not_completed_registration?

  before_action :redirect_if_staff_public, :redirect_if_staff_op_confirm
  before_action :redirect_if_cannot_update, only: :edit

  def index
    @staff, @staff_account_transfer, @sizes, @profile_comment = service.index
    @account = @staff.account
  end

  def edit
    @edited_staff, @is_new_form, @edited_staff_account_transfer, @edited_staff_expectation,
      @has_experience, @profile_comment, data = service.edit
    load_data data
  end

  def update
    result = service.update
    json_response(result[0], result[1], result[2], result[3])
  end

  def step_1
    render json: service.step_1
  end

  def step_2
    render json: service.step_2
  end

  def step_3
    render json: service.step_3
  end

  def step_4
    render json: service.step_4
  end

  private
  # * PTS
  def redirect_if_cannot_update
    unless current_staff&.can_update_registered_profile?
      return redirect_to staff_close_app_webview_path if is_mobile_view?

      return redirect_to staff_jobs_path
    end
    redirect_to edit_email_staff_settings_path if current_staff.account.email.blank?
  end

  def load_data data
    @nationalities = data[:nationalities]
    @residence_statuses = data[:residence_statuses]
    @residence_permissions = data[:residence_permissions]
    @banks = data[:banks]
    @prefectures = data[:prefectures]
    @japan = data[:japan]
    @other_nationality = data[:other_nationality]
    @specific_activity_residence_statuses = data[:specific_activity_residence_statuses]
    @has_residence_permission = data[:has_residence_permission]
    @is_single_haken = data[:is_single_haken]
    @uniform_sizes = data[:uniform_sizes]
    @pant_sizes = data[:pant_sizes]
    @shoes_sizes = data[:shoes_sizes]
    @relationships = data[:relationships]
    @registration_histories = data[:registration_histories]
  end

  def service
    Staff::RegisteredProfilesService.new(self)
  end

  def not_completed_registration?
    current_staff&.staff_recruitment_process.present? &&
      !current_staff&.staff_recruitment_process.completed_registration?
  end
end
