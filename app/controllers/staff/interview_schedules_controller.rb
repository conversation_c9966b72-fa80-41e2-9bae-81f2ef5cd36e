class Staff::InterviewS<PERSON>ulesController < Staff::AuthenticateController
  layout "staff/entry_layout", only: [:new]

  before_action :redirect_if_staff_public, :redirect_unless_can_view
  before_action :redirect_if_no_experience
  before_action :redirect_if_regist_not_interview_step, only: :new
  before_action :redirect_if_direct_booking, only: :details
  before_action :load_interview_info, only: [:details, :create]

  rescue_from(CustomExceptions::CannotRescheduleInterviewError) do
    flash[:error] = cannot_reschdule_msg

    respond_to do |format|
      format.html do
        redirect_back(fallback_location: details_staff_interview_schedules_path)
      end
      format.json do
        result_json = create_result_json(false, cannot_reschdule_msg)
        result_json[:redirect_back] = true

        render json: result_json
      end
    end
  end

  rescue_from(CustomExceptions::UnavailableInteviewError) do
    error = I18n.t("staff.interview_schedule.errors.unavailable_interview")
    render json: create_result_json(false, error)
  end

  def new
    @direct_booking = current_staff.staff_recruitment_process&.not_in_progress?
    @is_email_verified = current_staff.is_email_verified?
    prefectures_and_available_interviews
  end

  def reschedule
    staff_apply_interview = current_staff.staff_apply_interviews.order(created_at: :desc).first
    current_interview_room = staff_apply_interview&.interviews_room
    current_interview = current_interview_room&.interview
    raise(CustomExceptions::CannotRescheduleInterviewError) if current_interview.nil? ||
      !current_interview.is_open?

    @selected_interview_id = current_interview.id
    @selected_prefecture_id = staff_apply_interview&.prefecture_id
    @is_email_verified = current_staff.is_email_verified?
    prefectures_and_available_interviews(@selected_interview_id)
  end

  def create
    raise(CustomExceptions::CannotRescheduleInterviewError) if @int_room && !@int_room.interview.is_open?

    status, error = apply_room
    render json: create_result_json(status, error)
  end

  def details
    @staff_recruitment_process = current_staff.staff_recruitment_process
    @can_reschedule = @int_room&.interview&.is_open?
  end

  def cancel
    cmd = Interviews::StaffCancelInterviewCommand.new(current_staff.id, params[:staff_apply_interview_id])
    status = cmd.perform
    return render json: {status: true} if status

    render json: ErrorResponseBlueprint.render({message: cannot_cancel_msg}, view: :with_message)
  end

  def submit_email
    cmd = Staffs::SubmitEmailCommand.new(current_staff.id, params[:email])
    result = cmd.perform

    render json: result
  end

  private

  def prefectures_and_available_interviews selected_interview_id = nil
    available_interviews = Interview.available_to_apply(selected_interview_id).sort_by_interview_date

    @interviews = available_interviews.map do |int|
      {id: int.id, full_date_time: int.full_date_time}
    end.to_json
    @prefectures = Prefecture.select(:id, :name).to_json
  end

  def apply_room
    result = [false, I18n.t("errors.messages.generic_error")]

    cmd = Interviews::StaffApplyInterviewCommand.new(
      params[:interview_id],
      params[:prefecture_id],
      current_staff.id
    )

    apply = cmd.perform
    return result unless apply

    update_recruitment_process!
    result = [true, ""]
    room = apply.interviews_room
    notify_to_staff(room) if result[0]
    result
  end

  def redirect_if_regist_not_interview_step
    return if current_staff.staff_recruitment_process&.new_interview?

    if is_mobile_view?
      redirect_to staff_close_app_webview_path
    else
      redirect_to staff_jobs_path
    end
  end

  def redirect_if_no_experience
    return if current_staff.has_experience?

    if is_mobile_view?
      redirect_to staff_close_app_webview_path
    else
      redirect_to staff_jobs_path
    end
  end

  def redirect_if_direct_booking
    redirect_to new_staff_interview_schedule_path if current_staff.staff_recruitment_process&.not_in_progress?
  end

  def redirect_unless_can_view
    redirect_to_root_or_off_webview unless current_staff.can_view_interview_training_schedule?
  end

  def load_interview_info
    @staff_apply_interview = current_staff.staff_apply_interviews.order(created_at: :desc).first
    @int_room = @staff_apply_interview&.interviews_room
  end

  def update_recruitment_process!
    recruitment_process = StaffRecruitmentProcess.find_or_initialize_by(staff_id: current_staff.id)
    recruitment_process.assign_attributes(
      registration_process_code: :profile_step,
      interview_process_code: :scheduled_interview
    )
    recruitment_process.save!
  end

  def notify_to_staff room
    interview_start_time = DateTimeFormatting.jp_full_date_time(room.interview&.start_time)
    date_format = room.full_date_time
    room_url = room.room_url
    account = current_staff.account
    send_email_to_staff(account, room_url, interview_start_time)
    send_sms_to_staff(account, room_url, date_format)
    send_fcm_to_staff(room_url, date_format)
  end

  def send_email_to_staff account, room_url, interview_start_time
    return if account&.email.blank?

    StaffMailer.notify_interview_applied(current_staff, room_url, interview_start_time).deliver_now
  end

  def send_sms_to_staff account, room_url, date_format
    return if account&.email.present? || account&.tel.blank?

    MessageSenderService.notify_interview_applied(account.tel, room_url, date_format)
  end

  def send_fcm_to_staff room_url, date_format
    app_notification_option = {
      staff_id: current_staff.id,
      creator_type: :by_system,
      notification_type: :applied_interview,
      params: {
        room_url: room_url,
        date_format: date_format
      }
    }
    AppSendNotificationWorker.perform_async([app_notification_option])
  end

  def create_result_json status, error
    {
      status: status,
      error: error,
      redirect_path: details_staff_interview_schedules_path
    }
  end

  def cannot_reschdule_msg
    I18n.t("staff.interview_schedule.errors.cannot_reschedule")
  end

  def cannot_cancel_msg
    I18n.t("staff.interview_schedule.errors.cannot_cancel_interview_schedule")
  end
end
