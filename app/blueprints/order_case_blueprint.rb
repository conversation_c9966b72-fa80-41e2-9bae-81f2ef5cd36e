class OrderCaseBlueprint < Blueprinter::Base
  identifier :id

  view :new_batch_arranges do
    fields :order_id, :is_urgent, :training_session_text
    field :segment_id do |oc|
      I18n.t("enum_label.order_case.segment_ids.#{oc.segment_id}")
    end

    field :segment_id, name: :segment
    field :location_id do |oc|
      oc[:location_id]
    end

    field :corporation_id do |oc|
      oc[:corporation_id]
    end

    field :location_name do |oc|
      oc[:location_name]
    end

    field :corporation_name do |oc|
      oc[:corporation_name]
    end

    field :current_location_type_text do |_oc, options|
      arrangements = options[:arrangements] || {}
      arrangements[:current_location_type_text]
    end

    field :available_portion do |oc, options|
      portion_ratio = options[:portion_ratio] || {}
      oc.total_portion - portion_ratio[:total_arranged].to_i - portion_ratio[:total_temp_arranged].to_i
    end

    field :arranged_per_location do |_oc, options|
      location_ratio = options[:location_ratio] || {}
      "#{location_ratio[:total_status].to_i}/#{location_ratio[:total_portion].to_i}"
    end

    field :arranged_per_corporation do |_oc, options|
      corporation_ratio = options[:corporation_ratio] || {}
      "#{corporation_ratio[:total_status].to_i}/#{corporation_ratio[:total_portion].to_i}"
    end

    field :arranged_per_order_case do |oc, options|
      portion_ratio = options[:portion_ratio] || {}
      "#{portion_ratio[:total_arranged].to_i}/#{oc.total_portion}"
    end

    field :location_note do |oc|
      oc[:location_note].present?
    end

    field :has_notes do |oc|
      oc[:order_note].present? || oc.special_offer_note.present?
    end

    field :work_started_day do |oc|
      oc.case_started_at.strftime("%m/%d")
    end

    field :work_day do |oc|
      I18n.l(oc.case_started_at, format: "(%a)")
    end

    field :working_time, name: :work_time
    field :work_time_with_date do |oc|
      "#{oc.started_at_format_month_date} #{oc.working_time}"
    end

    field :case_started_at do |oc|
      oc.case_started_at.to_i
    end

    field :case_started_at, name: :case_started_at_original

    field :break_time do |_oc, options|
      order_branch = options[:order_branch] || {}
      order_branch[:break_time]
    end

    field :is_changable do |_oc, options|
      order_branch = options[:order_branch] || {}
      order_branch[:is_time_changable]
    end

    field :total_called do |_oc, options|
      sum_offers = options[:sum_offers] || {}
      sum_offers[:total_called].to_i
    end

    field :total_offered do |_oc, options|
      sum_offers = options[:sum_offers] || {}
      sum_offers[:total_offered].to_i
    end

    field :order_case_status do |_oc, options|
      options[:order_case_status]
    end

    field :total_arrange_note do |_oc, options|
      arrangements = options[:arrangements] || {}
      arrangements[:total_note].to_i
    end

    field :location_stations_1_station_name do |oc|
      station_name = oc[:location_stations_1_station_name]
      next unless station_name

      "#{station_name}#{I18n.t('staff.order_cases.station')}"
    end

    field :required_time do |_oc, options|
      order_branch = options[:order_branch] || {}
      order_branch[:required_time]
    end
  end

  # Basic Job information
  view :basic_job_info do
    fields :break_time, :location_get_type, :location_thumbnail_path, :is_time_changable,
      :staff_apply_count, :location_station_1_info, :site_name, :thumbnail_path

    field :working_time do |oc|
      TimeRangeFormatting.hour_to_hour(oc.case_started_at, oc.case_ended_at)
    end

    field :started_at_format_month_day do |oc|
      DateTimeFormatting.month_day_formatted(oc.case_started_at)
    end
  end

  view :staff_jobs do
    include_view :basic_job_info

    fields :order_id, :order_branch_id, :case_started_at, :case_ended_at,
      :is_urgent, :segment_id, :started_at, :status,
      :location_latitude, :location_longitude, :location_id,
      :timeout_apply_before_2_hour,
      :placeholder_transportation_fee, :segment_regular, :location_logo,
      :peak_period_order_rate, :peak_period_unit_rate
  end

  view :similar_order_cases do
    include_view :basic_job_info

    fields :is_regular_order
  end
end
