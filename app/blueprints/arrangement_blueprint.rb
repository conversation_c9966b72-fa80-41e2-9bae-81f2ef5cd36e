class ArrangementBlueprint < Blueprinter::Base
  identifier :id

  fields :order_case_id, :order_id, :break_time

  view :index do
    fields :order_portion_id, :note, :store_note,
      :arrange_comment, :billing_payment_template_id, :is_billing_locked,
      :is_prepared, :is_arrived, :staff_id, :working_started_at, :working_ended_at,
      :is_payroll_locked, :rest1_started_at, :rest1_ended_at,
      :rest2_started_at, :rest2_ended_at, :rest3_started_at, :rest3_ended_at, :break_time

    field :staff do |arr|
      next unless arr.staff_id

      arr.staff_info
    end

    include_view :arrangement_methods
    include_view :order_case_attributes
    include_view :arrange_payment_attributes
    include_view :arrange_billing_attributes
    include_view :work_achievement_attributes
    include_view :order_portion_attributes
    include_view :payment_billing_time_format
  end

  # Methods of Arrangement
  view :arrangement_methods do
    fields :new_location?, :old_location?

    fields :is_locked?, :over_working_start_time, :need_prepare_working, :finish_apply?,
      :time_start_edit_staff_working, :available_for_recruiting

    field :working_day do |arr|
      DateTimeFormatting.month_day(arr.working_started_at)
    end

    # Methods from BaseFormatterRawQuery
    fields :working_day_full_date, :order_case_status, :status_staff_evaluation,
      :working_need_talking, :working_has_problem, :order_location_stations_1_station_name,
      :billing_started_at, :billing_ended_at, :arrange_billing_adjusment_type_id,
      :payment_start_time, :payment_end_time, :payment_end_time, :work_achievement_working_time_status_id,
      :order_case_segment_id, :location_expired?, :order_location_code, :location_name,
      :order_location_id, :order_corporation_full_name, :order_real_pic_department_name,
      :staff_department_name_at_working_time, :order_note, :order_case_special_offer_note,
      :location_evaluation_val, :staff_home_station_station_name, :deleted_user_order_pic?,
      :order_created_full_day, :working_time, :order_corporation_id, :is_lawson,
      :payment_requested

    field :business_name, name: :order_case_location_job_category_name
    field :staff_avg_evaluation, name: :avg_staff_evaluation
    field :order_segment_id, name: :order_order_segment_id
    field :regular_order?, name: :is_regular_order
    field :arrange_payment_adjusment_type_id, name: :adjusment_type_id
    field :order_case_invoice_target, name: :invoice_target
    field :location_pos_type_id, name: :order_location_pos_type_id
    field :location_is_store_parking_area_usable, name: :order_location_is_store_parking_area_usable
    field :portion_arranged?, name: :portion_arranged
    field :portion_cancel_after_arrange_has_insurance?, name: :portion_cancel_after_arrange_has_insurance
    field :portion_canceled?, name: :portion_canceled
    field :order_created_date, name: :order_created_day

    field :is_time_changable do |arr|
      BooleanConverting.convert_from_raw_query(arr.order_branches_is_time_changable)
    end

    field :required_start_time do |arr|
      arr.order_branches_required_start_time&.in_time_zone
    end

    field :required_end_time do |arr|
      arr.order_branches_required_end_time&.in_time_zone
    end
  end

  # ArrangePayment Attributes
  view :arrange_payment_attributes do
    field :arrange_payment do |arr|
      {
        id: arr[:arrange_payment_id],
        payment_basic_unit_price: arr[:payment_basic_unit_price],
        payment_night_unit_price: arr[:payment_night_unit_price],
        payment_bonus_unit_price: arr[:payment_bonus_unit_price]
      }
    end

    field :total_unit_price_addition do |arr|
      bonus_price = arr[:payment_urgent_unit_price_addition]
      bonus_price = arr[:payment_bonus_unit_price] unless
        BooleanConverting.convert_from_raw_query(arr[:order_case_is_urgent])

      arr[:payment_unit_price_addition].to_i + bonus_price.to_i
    end

    arrange_payment_attrs = %i(
      payment_unit_price_addition
      payment_total_amount
      payment_basic_amount
      payment_ot_amount
      payment_night_amount
      payment_field_1
      payment_field_2
      payment_field_3
      payment_field_4
      payment_field_5
      payment_subtraction_amount
      deduction_field_1
      deduction_field_2
      payment_actual_working_time
      payment_basic_time
      payment_ot1_time
      payment_night_time
      payment_late_time
      payment_leave_early_time
      payment_basic_unit_price
      payment_night_unit_price
      payment_field_6
      payment_field_7
      payment_field_8
      payment_field_9
      payment_field_10
      payment_field_11
      payment_field_12
      payment_field_13
      payment_field_14
      payment_field_15
      payment_field_16
      payment_field_17
      payment_field_18
      payment_field_19
      payment_field_20
      payment_basic_break_time
      payment_night_break_time
      payment_urgent_unit_price_addition
      payment_ot2_amount
      payment_ot_unit_price
      payment_bonus_unit_price
    )

    arrange_payment_attrs.each do |field_name|
      field field_name do |arr|
        arr[field_name]
      end
    end
  end

  # Arrange Billing Attributes
  view :arrange_billing_attributes do
    field :arrange_billing do |arr|
      {
        id: arr[:arrange_billing_id]
      }
    end

    arrange_billing_datetime_attrs = %i(
      billing_rest1_started_at
      billing_rest1_ended_at
      billing_rest2_started_at
      billing_rest2_ended_at
      billing_rest3_started_at
      billing_rest3_ended_at
    )

    arrange_billing_datetime_attrs.each do |field_name|
      field field_name do |arr|
        arr[field_name]&.in_time_zone
      end
    end

    arrange_billing_attrs = %i(
      billing_note
      billing_basic_unit_price
      billing_unit_price_addition
      billing_ot_unit_price
      billing_night_unit_price
      billing_total_amount
      billing_basic_amount
      billing_ot_amount
      billing_night_amount
      billing_field_1
      billing_field_2
      billing_field_3
      billing_field_4
      billing_field_5
      billing_field_6
      billing_field_7
      billing_tax_exemption
      billing_actual_working_time
      billing_basic_time
      billing_ot_time
      billing_night_time
      billing_late_time
      billing_leave_early_time
      billing_field_8
      billing_field_9
      billing_field_10
      billing_basic_break_time
      billing_night_break_time
      billing_other_addition_fee
    )

    arrange_billing_attrs.each do |field_name|
      field field_name do |arr|
        arr[field_name]
      end
    end
  end

  # WorkAchievement Attributes
  view :work_achievement_attributes do
    field :payment_started_at do |arr|
      DateTimeFormatting.date_with_time(arr[:work_achievements_working_started_at]&.in_time_zone)
    end

    field :work_achievement_rest1_time do |arr|
      TimeRangeFormatting.hour_to_hour(
        arr[:work_achievements_rest1_started_at]&.in_time_zone,
        arr[:work_achievements_rest1_ended_at]&.in_time_zone
      )
    end

    field :work_achievement_rest2_time do |arr|
      TimeRangeFormatting.hour_to_hour(
        arr[:work_achievements_rest2_started_at]&.in_time_zone,
        arr[:work_achievements_rest2_ended_at]&.in_time_zone
      )
    end

    field :work_achievement_rest3_time do |arr|
      TimeRangeFormatting.hour_to_hour(
        arr[:work_achievements_rest3_started_at]&.in_time_zone,
        arr[:work_achievements_rest3_ended_at]&.in_time_zone
      )
    end

    work_achievement_rest_date = {
      work_achievement_rest1_started_date: :work_achievements_rest1_started_at,
      work_achievement_rest1_ended_date: :work_achievements_rest1_ended_at,
      work_achievement_rest2_started_date: :work_achievements_rest2_started_at,
      work_achievement_rest2_ended_date: :work_achievements_rest2_ended_at,
      work_achievement_rest3_started_date: :work_achievements_rest3_started_at,
      work_achievement_rest3_ended_date: :work_achievements_rest3_ended_at
    }

    work_achievement_rest_date.each do |field_name, attribute|
      field field_name do |arr|
        DateTimeFormatting.date_with_time(arr[attribute]&.in_time_zone) ||
          WorkAchievements::GetWorkingDateCommand.new(
            arr[:order_case_case_started_at]&.in_time_zone,
            arr[:work_achievements_working_started_at]&.in_time_zone
          ).perform
      end
    end
  end

  # OrderCase Attributes
  view :order_case_attributes do
    field :order_case_is_special_offer do |arr|
      BooleanConverting.convert_from_raw_query(arr[:order_case_is_special_offer])
    end

    field :order_case_is_urgent do |arr|
      BooleanConverting.convert_from_raw_query(arr[:order_case_is_urgent])
    end

    field :order_case_training_session_text do |arr|
      next "" unless arr[:order_case_training_session_code]

      training_session_code = OrderCase.training_session_codes.key(arr[:order_case_training_session_code])
      I18n.t("admin.order.order_form.step1.#{training_session_code}")
    end
  end

  # OrderPortion Attributes
  view :order_portion_attributes do
    field :order_portion do |arr|
      {
        case_started_at: arr.order_portion_case_started_at,
        case_ended_at: arr.order_portion_case_ended_at
      }
    end
  end

  # Format time from ArrangePayment and ArrangeBilling
  view :payment_billing_time_format do
    payment_billing_time_fields = {
      payment_ot1_time_format: :payment_ot1_time,
      payment_late_time_format: :payment_late_time,
      payment_leave_early_time_format: :payment_leave_early_time,
      payment_basic_time_format: :payment_basic_time,
      payment_actual_working_time_format: :payment_actual_working_time,
      payment_ot_night_time_format: :payment_ot_night_time,
      payment_ot_day_time_format: :payment_ot_day_time,
      billing_ot_time_format: :billing_ot_time,
      billing_late_time_format: :billing_late_time,
      billing_leave_early_time_format: :billing_leave_early_time,
      billing_basic_time_format: :billing_basic_time,
      billing_actual_working_time_format: :billing_actual_working_time,
      billing_night_time_format: :billing_night_time
    }

    payment_billing_time_fields.each do |field_name, attribute|
      field field_name do |arr|
        TimeConverting.number_to_hour_minute(arr[attribute])
      end
    end
  end
end
