class OrderBlueprint < Blueprinter::Base
  identifier :id

  fields :organization_id, :location_id, :definition_id, :haken_destination_pic_id, :haken_destination_pic_id,
    :haken_destination_pic_position, :haken_destination_pic_tel, :mandator_id,
    :mandator_position, :mandator_tel, :claim_pic_id, :claim_pic_position,
    :claim_pic_tel, :order_pic_id, :order_pic_email, :order_pic_tel,
    :is_used_existing_pic, :note, :status_id, :order_template_id, :is_violation_day_unchanged,
    :is_location_survey_unchanged, :type_id, :created_user_id

  view :build_new_order do
    exclude :id
  end
end
