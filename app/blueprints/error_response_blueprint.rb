class ErrorResponseBlueprint < Blueprinter::Base
  field :status, default: false

  view :with_errors do
    fields :errors
  end

  view :with_message do
    fields :message
  end

  view :not_found do
    field :message, default: I18n.t("error.api.common.record_not_found")
    field :status_code, default: Error::CODES[:record_not_found]
  end

  view :record_invalid do
    field :message, default: I18n.t("error.api.common.bad_request")
    field :status_code, default: Error::CODES[:bad_request]
  end

  view :apply_order_case do
    field :apply_condition_errors do |errors|
      errors[:errors].map{|error| [error["key"], [error["message"]]]}.to_h
    end
  end

  view :api_apply_order_case do
    field :pre_apply_messages do |errors|
      results = {
        status: false
      }
      errors[:errors].each do |error|
        results[:apply_message] = [:apply_condition, [error["message"]]]
      end

      results
    end
  end
end
