class OrderBranchBlueprint < Blueprinter::Base
  identifier :id

  fields :started_at, :is_except_holiday, :working_start_time, :working_end_time, :staff_count, :is_time_changable,
    :is_special_offer, :special_offer_fee, :special_offer_note, :rest1_started_at, :rest1_ended_at, :rest1_editable,
    :rest2_editable, :rest3_editable, :required_start_time, :required_end_time

  view :build_new_order_branch do
    exclude :id
  end
end
