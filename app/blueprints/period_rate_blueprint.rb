class PeriodRateBlueprint < Blueprinter::Base
  identifier :id

  fields :unit_rate, :order_rate, :corporation_id, :prefecture_id, :is_default, :is_all

  view :index do
    fields :peak_period_id

    field :corporation_name do |rate|
      next I18n.t("admin.period_rate.values.all_corporations") if rate.is_all && rate.corporation_id.nil?

      rate[:corporation_name]
    end

    field :prefecture_name do |rate|
      rate[:prefecture_name]
    end

    field :target_date do |rate|
      next I18n.t("admin.period_rate.values.common_date") if rate.is_default

      rate[:target_date]&.in_time_zone&.strftime("%Y/%m/%d")
    end

    field :target_date_name do |rate|
      rate[:target_date_name]
    end
  end

  view :show do
    field :corporation_name do |rate|
      next I18n.t("admin.period_rate.values.all_corporations") if rate.is_all && rate.corporation_id.nil?

      rate.corporation&.full_name
    end

    field :prefecture_name do |rate|
      rate.prefecture&.name
    end
  end

  view :destroyed_warning do
    fields :allowance_type

    exclude :corporation_id
    exclude :prefecture_id
    exclude :is_default
    exclude :is_all
  end
end
