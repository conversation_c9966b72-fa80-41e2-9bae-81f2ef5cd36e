class StaffBlueprint < Blueprinter::Base
  identifier :id

  view :registration_index do
    fields :status_id, :staff_type, :registration_type, :signup_device, :staff_number,
      :name, :name_kana, :registration_answer_order_case_id,
      :registration_date, :age, :interview_time, :allow_delete,
      :registration_answer_answer_4, :registration_answer_answer_5,
      :registration_answer_registration_comment

    field :training_first_round_date do |staff, external_data|
      training_schedule_list = external_data[:training_schedule_list]
      training_schedule_booked = training_schedule_list[staff.id]
      next unless training_schedule_booked

      first_round = training_schedule_booked.find{|schedule| schedule.training_session_code == "training_first_round"}
      first_round&.start_time&.strftime(Settings.datetime.formats)
    end

    field :training_second_round_date do |staff, external_data|
      training_schedule_list = external_data[:training_schedule_list]
      training_schedule_booked = training_schedule_list[staff.id]
      next unless training_schedule_booked

      second_round = training_schedule_booked.find{|schedule| schedule.training_session_code == "training_second_round"}
      second_round&.start_time&.strftime(Settings.datetime.formats)
    end

    field :training_single_session_date do |staff, external_data|
      training_schedule_list = external_data[:training_schedule_list]
      training_schedule_booked = training_schedule_list[staff.id]
      next unless training_schedule_booked

      single_session = training_schedule_booked.find{|schedule| schedule.training_session_code == "single_session"}
      single_session&.start_time&.strftime(Settings.datetime.formats)
    end
  end

  view :staff_index do
    fields :tel, :home_tel, :total_inlate, :total_absence, :total_work_experience,
      :total_working_time, :contract_status, :contract_send_mail_status, :staff_number, :debut_date

    field :account_info do |staff|
      {
        email: staff.staff_email,
        name: staff.staff_name,
        name_kana: staff.staff_name_kana
      }
    end

    # Methods
    fields :current_department_name, :evaluation

    field :contract_type_name do |staff|
      next unless staff.staff_indefinite_employment_flag

      type_name = StaffContractHistory.indefinite_employment_flags.key(staff.staff_indefinite_employment_flag)
      I18n.t("enum_label.staff.indefinite_employment_flags.#{type_name}")
    end
  end
end
