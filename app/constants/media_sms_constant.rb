module MediaSmsConstant
  SMS_TITLE = "Workz"
  CALLBACK_NEEDED_STATUSES = {true => 1, false => 0}

  SEND_SMS_REQUEST_RESULT_STATUS = {
    "200" => "Success - 成功",
    "401" => "Authorization Required - 認証エラー",
    "402" => "Payment Required - 契約数超過",
    "414" => "URL is longer than 8190bytes - リクエストしている URL が長過ぎる (GET リクエストの場合)",
    "503" => "User reached max limit of requests/sec from the single IP address " \
      "- 単一の IP アドレスを送信元とする秒間 リクエスト数が、最大値である 80 リク エストに達した。",
    "550" => "Failure - 失敗",
    "555" => "Your IP address has been blocked - IP アドレスは不正な値としてシステム へ登録されている" \
      "(401 を連続して 10 回繰り返すと不正とみなされる)",
    "557" => "Prohibited IP address - 管理画面に登録されている IP 以外から リクエストを送信した際のエラー",
    "556" => "No audio file - 音声ファイルの設定が不正",
    "560" => "Mobile career is invalid - mobilecareer の値が不正",
    "561" => "Method is invalid - method の値が不正",
    "562" => "Interval between calls is invalid - Intervalcall の値が不正",
    "563" => "Ringing time is invalid - Ringingtime の値が不正",
    "564" => "Authentication code is invalid - authcode の値が不正",
    "565" => "Mobile number is invalid - mobilenumber の値が不正",
    "566" => "IP phone number is invalid - ipphone の値が不正",
    "567" => "Holding time is invalid - holdingtime の値が不正",
    "568" => "Repeat call is invalid - repeatcall の値が不正",
    "569" => "1st call is invalid - 1stcall の値が不正",
    "570" => "Sms text id is invalid - smstextid の値が不正",
    "571" => "Authentication time is invalid - authtime の値が不正",
    "572" => "Wait authentication code is invalid - waitauth の値が不正",
    "573" => "Status is invalid - status の値が不正",
    "574" => "Smsid is invalid - smsid の値が不正",
    "575" => "Docomo is invalid - docomo の値が不正",
    "576" => "Au is invalid - au の値が不正",
    "577" => "Softbank is invalid - softbank の値が不正",
    "579" => "Gateway is invalid - gateway の値が不正",
    "580" => "Sms title is invalid - smstitle の値が不正",
    "582" => "Sending attempts is invalid - sending attempts の値が不正",
    "583" => "Send date & time is invalid - Send date & time の値が無効",
    "585" => "Sms text is invalid - smstext の値が不正",
    "586" => "Sms text 2 is invalid - smstext2 の値が不正",
    "588" => "Resending interval is invalid - キャリアごとの TTL 設定の値が無効",
    "605" => "Docomo text is invalid - smstextdocomo の値が不正",
    "606" => "Softbank text is invalid - smstextsoftbank の値が不正",
    "614" => "Docomo SMS title is invalid - docomotitle の値が不正",
    "615" => "AU SMS title is invalid - autitle の値が不正",
    "616" => "Softbank SMS title is invalid - softbanktitle の値が不正",
    "617" => "SMS title function is disabled - SMS タイトル設定が無効状態",
    "618" => "SMS text is long for Docomo API - ドコモ宛の smstext が長すぎる(71 文 字を超えている)。 ※文字数オーバー制限設定有効時のみ",
    "619" => "SMS text is long for SDP - au 宛の smstext が長すぎる(71 文字を 超えている)。 ※文字数オーバー制限設定有効時のみ",
    "620" => "SMS text is long for Softbank API - Softbank 宛の smstext が長すぎる(71 文字を超えている)。 ※文字数オーバー制限設定有効時のみ",
    "621" => "Rakuten title is invalid - rakutentitle の値が不正",
    "622" => "Rakuten text is invalid - smstextrakuten の値が不正",
    "623" => "Rakuten is invalid - rakuten の値が不正",
    "625" => "Auth code wait is invalid - Authcodewait の値が不正"
  }

  SMS_RESULT_STATUS = {
    success: "送信済",
    processing: "処理中"
  }
end
