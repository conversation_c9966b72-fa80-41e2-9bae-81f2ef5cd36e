module StaffConstant
  CAREER_UP_IDS = {to_be_owner: 1, to_be_member: 2}

  STAFF_ACCOUNT_TRANSFER_ATTRIBUTES = [
    :id, :bank_id, :bank_branch_id, :account_first_name, :account_last_name,
    :account_middle_name_1, :account_middle_name_2, :account_middle_name_3,
    :account_first_name_kana, :account_last_name_kana, :account_middle_name_kana_1,
    :account_middle_name_kana_2, :account_middle_name_kana_3, :account_type, :account_number,
    :passbook_pic
  ]

  DATE_ATTR = [:created_at, :expired_start_date, :expired_end_date, :absence_start_date,
    :absence_end_date, :contract_start_date, :contract_end_date, :working_start_date,
    :working_end_date, :retirement_date, :job_entry_date, :debut_date, :rank_up_training_date,
    :paid_occupation_establishment_date, :hire_date, :birthday, :residence_validity,
    :residence_expiration_date, :residence_permission_validity]

  TABLE_RELATIONSHIP = [{staff_expectation: [:transportation_type]}, {staff_check_item:
    [:uniform_size_type, :pant_size_type, :shoes_size_type, :workable_time_type]},
    {staff_account_transfer: [:bank, :bank_branch]},
    :staff_prefecture, :staff_emergency_prefecture, :registration_history_type, :nationality_type,
    :emergency_relationship_type, :home_station]

  TABLE_RELATIONSHIP_XLSX = [:account, :staff_expectation, {staff_check_item: [:uniform_size_type,
    :pant_size_type, :shoes_size_type,
    :health_uninsured_reason_type, :welfare_uninsured_reason_type,
    :employment_uninsured_reason_type]},
    {staff_account_transfer: [:bank, :bank_branch]}, :last_contract, {recent_departments: [:department]},
    :staff_salary, :recent_staff_ranks, :recent_staff_levels, :staff_long_vacations,
    :use_line1, :use_line2, :use_line3, :station1_use, :station2_use, :station3_use,
    :staff_prefecture, :staff_emergency_prefecture, :registration_history_type, :nationality_type,
    :emergency_relationship_type, :home_station, :birthplace, :school_station, :registration_answer,
    :lastest_evaluation_summaries, :residence]

  TABLE_CLONE_RELATIONSHIP = [:staff_account_transfer, :staff_expectation,
    :staff_check_item, :staff_employment_history, :staff_departments]

  STEP_1_ATTRS = [:id, :birthday, :gender_id, :name, :name_kana,
    :first_name, :last_name, :first_name_kana, :last_name_kana, :email, :middle_name_1,
    :middle_name_2, :middle_name_3, :middle_name_kana_1, :middle_name_kana_2,
    :middle_name_kana_3, :postal_code, :prefecture_id, :city, :street_number, :house_number,
    :building, :home_station_id, :school_station_id, :tel, :is_validate_home_station,
    :home_tel, :social_attribute, :social_attribute_other, :registration_history,
    :is_emergency_address_differ_current_address, :emergency_postal_code, :emergency_prefecture_id,
    :emergency_city, :emergency_street_number, :emergency_house_number, :emergency_building,
    :emergency_name, :emergency_relationship_id, :emergency_tel, :is_admin_validate,
    :nationality, :residence_status, :residence_validity, :residence_expiration_date,
    :residence_permission, :residence_permission_validity, :residence_img_front,
    :other_nationality, :residence_card_name, :residence_img_back, :certificate_img,
    :execute_name_validation, :is_validate_name_and_kana, :residence_number,
    :entry_uniform_size, :entry_pant_size, :entry_shoes_size, :registration_type,
    :is_staff_validate_residence_expiration_date, {career_up_id: []}]

  STEP_1_REMOVE_FILE_ATTRS = [:remove_residence_img_front, :remove_residence_img_back,
    :remove_certificate_img]

  STEP_2_ATTRS = {staff_employment_history_attributes: [:id, :is_convenience_lawson,
    :convenience_lawson_shop, :is_convenience_7eleven, :is_convenience_family, :is_convenience_other,
    :is_partime_experience, :partime_place_name, :partime_start, :partime_end,
    {service_work_type: [], office_work_type: [], other_work_type: []}],
    staff_expectation_attributes: [:id, :is_working_car, :first_training_date,
    :short_term_note, :expectation_employment_type, :is_monday, :monday_start_time_1,
    :monday_end_time_1, :is_tuesday, :tuesday_start_time_1, :tuesday_end_time_1,
    :is_wednesday, :wednesday_start_time_1, :wednesday_end_time_1, :is_thursday,
    :thursday_start_time_1, :thursday_end_time_1, :is_friday, :friday_start_time_1,
    :friday_end_time_1, :is_saturday, :saturday_start_time_1, :saturday_end_time_1,
    :is_sunday, :sunday_start_time_1, :sunday_end_time_1, :work_condition]}

  STEP_3_ATTRS = {staff_account_transfer_attributes: [:id, :bank_id, :bank_branch_id,
    :account_name, :account_name_kana, :account_first_name, :account_last_name,
    :account_middle_name_1, :account_middle_name_2, :account_middle_name_3,
    :account_first_name_kana, :account_last_name_kana, :account_middle_name_kana_1,
    :account_middle_name_kana_2, :account_middle_name_kana_3, :account_type, :account_number,
    :passbook_pic, :execute_account_transfer_name_validation,
    :skip_account_transfer_name_validation]}
  STEP_4_ATTRS = [:is_expectation_condition, :payment_request, :is_agree_handling,
    :is_agree_contract, :accept_my_number_requirement]

  STAFF_ATTRS = [
    :status_id, :admin_approved_at, :admin_evaluation_comment,
    :pic_evaluation_comment, :is_check_residence, :is_validate_residence, :haken_type,
    {staff_account_transfer_attributes: [:id, :is_check_passbook, :is_validate_passbook],
    staff_check_item_attributes: [:id, :license, :license_date, :is_residence_card,
    :is_mynumber_card, :is_health_insurance, :is_studen_card, :is_identity_other,
    :identity_other_name, :is_residence_status, :is_residence_period,
    :uniform_size, :pant_size, :shoes_size, :is_skip_uninsured_validate,
    :is_permission, :is_check_address, :is_request_seal, :workable_time, :japanese_level,
    :working_other_place, :working_time, :is_validate_home_station],
    staff_departments_attributes: [:id, :department_id, :affiliation_date, :_destroy]}
  ]

  STAFF_CHECK_ITEM_ATTRS = {staff_check_item_attributes: [:id, :staff_id,
    :japanese_level, :uniform_size, :pant_size, :shoes_size]}

  ADMIN_STAFF_ATTRS = [:is_admin_validate, :gender_id, :birthday, :staff_type, :haken_type,
    :nationality, :level_up_training, :new_pos_training, :occupation, :position, :staff_status,
    :registration_history, :optional_item1, :optional_item2, :optional_item3,
    :optional_item4, :optional_item5, :optional_item6, :optional_item7, :optional_item8,
    :other_nationality, :staff_number, :role, :work_content_id, :work_time_id,
    :social_attribute, :social_attribute_other, :registration_type,
    {staff_check_item_attributes: [:id, :license, :license_date, :is_residence_card,
    :is_mynumber_card, :is_health_insurance, :is_studen_card, :is_identity_other,
    :identity_other_name, :is_residence_status, :is_residence_period,
    :uniform_size, :pant_size, :shoes_size, :is_skip_uniform_data,
    :is_permission, :is_check_address, :is_request_seal, :workable_time, :japanese_level,
    :working_other_place, :working_time] + StaffCheckItem::CHECKLIST_ATTRS},
    {staff_departments_attributes: [:id, :department_id, :affiliation_date, :_destroy]},
    {staff_education_backgrounds_attributes: StaffEducationBackground::EDUCATION_ATTRS},
    {staff_account_transfer_attributes: STAFF_ACCOUNT_TRANSFER_ATTRIBUTES +
    [:execute_account_transfer_name_validation, :account_name, :account_name_kana]},
    {staff_work_experiences_attributes: StaffWorkExperience::WORK_EXP_ATTRS + [:note1,
    :note2, :note3]},
    {staff_salary_attributes: StaffSalary::SALARY_ATTRS},
    {staff_contract_histories_attributes: StaffContractHistory::CONTRACT_ATTRS},
    {staff_messages_attributes: StaffMessage::ATTRS + [:_destroy]},
    :hire_date, :expired_start_date, :expired_end_date, :absence_start_date,
    :absence_end_date, :working_start_date,
    :working_end_date, :retirement_date, :job_entry_date, :debut_date, :rank_up_training_date,
    :paid_occupation_establishment_date, :expired_type, :absence_type,
    :contract_status, :retirement_reason_type, :retirement_reason, :maiden_name, :maiden_name_kana,
    {arrangement_type: [], staff_contact_histories_attributes:
    StaffContactHistory::CONTACT_HISTORY_ATTRS,
    staff_trainings_attributes: StaffTraining::TRAINING_ATTRS,
    staff_stable_employments_attributes: StaffStableEmployment::STABLE_EMPLOYMENT_ATTRS,
    staff_careers_attributes: StaffCareer::CAREER_ATTRS,
    staff_expectation_attributes: [:is_admin_validate, :id, :is_monday, :monday_start_time_1,
    :monday_end_time_1, :is_tuesday, :tuesday_start_time_1, :tuesday_end_time_1,
    :is_wednesday, :wednesday_start_time_1, :wednesday_end_time_1, :is_thursday,
    :thursday_start_time_1, :thursday_end_time_1, :is_friday, :friday_start_time_1,
    :friday_end_time_1, :is_saturday, :saturday_start_time_1, :saturday_end_time_1,
    :is_sunday, :sunday_start_time_1, :sunday_end_time_1, :expectation_employment_type,
    :is_working_car, :work_condition],
    staff_complaints_attributes: [:id, :complaint_date, :order_id, :order_case_id,
    :corporation_id, :location_id, :complaint_content, :handle_content, :staff_id, :_destroy]}]

  NAME_ATTRS = [:first_name, :middle_name, :last_name, :first_name_kana, :middle_name_kana,
    :last_name_kana, :middle_name_1, :middle_name_2, :middle_name_3,
    :middle_name_kana_1, :middle_name_kana_2, :middle_name_kana_3]

  SELECTED_ATTRS_FOR_EDIT = STEP_1_ATTRS.reject do |attr|
    attr.is_a?(Hash) || NAME_ATTRS.include?(attr) || attr == :execute_name_validation
  end +
    [:last_send_staff_code_at, :is_expectation_condition, :payment_request,
      :is_agree_handling, :accept_my_number_requirement, :is_agree_contract,
      :staff_code, :created_at, :deleted_at, :status_id, :career_up_id,
      :account_id, :staff_type] - [:is_admin_validate, :is_validate_name_and_kana,
      :is_staff_validate_residence_expiration_date, :is_validate_home_station]

  ARRANGEMENT_TYPES = {lack_of_submission: 1, attendance_ng: 2, grooming_ng: 3, arrangement_stop: 4,
    automatic_matching_ng: 5}

  CONTACT_ADDRESS = [
    :postal_code, :prefecture_id, :city, :street_number, :house_number, :building,
    :address_kana, :tel, :home_tel, :contact_memo,
    :home_station_id, :school_station_id, :use_station1,
    :use_station2, :use_station3, :use_line1_id, :use_line2_id, :use_line3_id,
    :is_emergency_address_differ_current_address, :emergency_postal_code,
    :emergency_prefecture_id, :emergency_city, :emergency_street_number, :emergency_house_number,
    :emergency_building, :emergency_name_kana, :emergency_tel, :emergency_home_tel,
    :emergency_name, :emergency_relationship_id
  ]

  RESIDENCE_ATTRS = [:residence_status, :residence_card_name, :residence_validity,
    :residence_expiration_date, :residence_permission, :residence_permission_validity,
    :foreigner_note1, :foreigner_note2, :residence_number]

  COMPARE_BASIC_INFO_ATTRS = [
    {attr_name: :name, type: :text, compared_attr_name: :account_name},
    {attr_name: :name_kana, type: :text, compared_attr_name: :account_name_kana},
    {attr_name: :birthday, type: :text},
    {attr_name: :gender_id, type: :radio},
    {attr_name: :registration_type, type: :radio},
    {attr_name: :postal_code, type: :text},
    {attr_name: :staff_prefecture_name, type: :text, label: :prefecture_id},
    {attr_name: :city, type: :text},
    {attr_name: :street_number, type: :text},
    {attr_name: :house_number, type: :text},
    {attr_name: :building, type: :text},
    {attr_name: :address_kana, type: :text},
    {attr_name: :home_station_name, type: :text, label: :home_station_id},
    {attr_name: :school_station_name, type: :text, label: :school_station_id},
    {attr_name: :tel, type: :text},
    {attr_name: :home_tel, type: :text},
    {attr_name: :uniform_size, type: :select, relation: :staff_check_item},
    {attr_name: :pant_size, type: :select, relation: :staff_check_item},
    {attr_name: :shoes_size, type: :select, relation: :staff_check_item},
    {attr_name: :residence_status, type: :select},
    {attr_name: :residence_validity, type: :date},
    {attr_name: :residence_number, type: :text},
    {attr_name: :residence_expiration_date, type: :date},
    {attr_name: :residence_permission, type: :select},
    {attr_name: :residence_permission_validity, type: :date},
    {attr_name: :japanese_level, type: :select, relation: :staff_check_item},
    {attr_name: :residence_img_front_url, type: :image, label: :residence_img_front},
    {attr_name: :residence_img_back_url, type: :image, label: :residence_img_back},
    {attr_name: :certificate_img_url, type: :image, label: :certificate_img},
    {attr_start: :start_date, attr_end: :end_date, attr_img: :vacation_certificate_img,
      attr_img_2: :vacation_certificate_img_2, type: :multi_period_with_image,
      label: :long_vacation, relation: :staff_long_vacations}
  ]

  COMPARE_EMERGENCY_ATTRS = [
    {attr_name: :is_emergency_address_differ_current_address, type: :radio},
    {attr_name: :emergency_postal_code, type: :text},
    {attr_name: :emergency_prefecture_id, type: :select},
    {attr_name: :emergency_city, type: :text},
    {attr_name: :emergency_street_number, type: :text},
    {attr_name: :emergency_house_number, type: :type},
    {attr_name: :emergency_building, type: :text},
    {attr_name: :emergency_name, type: :text},
    {attr_name: :emergency_name_kana, type: :text},
    {attr_name: :emergency_tel, type: :text},
    {attr_name: :emergency_home_tel, type: :text},
    {attr_name: :emergency_relationship_id, type: :select}
  ]

  COMPARE_ACCOUNT_TRANSFER_ATTRS = [
    {attr_name1: :bank_code, attr_name2: :bank_name, type: :text, compare_inline: true},
    {attr_name1: :bank_branch_code, attr_name2: :bank_branch_name,
      type: :text, compare_inline: true},
    {attr_name: :account_type, type: :radio},
    {attr_name: :account_number, type: :text},
    {attr_name: :account_name, type: :text},
    {attr_name: :account_name_kana, type: :text}
  ]

  STAFF_VERSION_ATTRS = [:id, :created_at, :gender_id, :birthday, :residence_expiration_date,
    :level_up_training, :new_pos_training, :occupation, :position, :staff_status,
    :registration_history, :nationality, :other_nationality, :postal_code,
    :prefecture_id, :city, :street_number, :house_number, :building, :address_kana, :tel, :home_tel,
    :contact_memo, :home_station_id,
    :school_station_id, :use_station1, :use_station2, :use_station3, :use_line1_id,
    :use_line2_id, :use_line3_id, :is_emergency_address_differ_current_address,
    :emergency_postal_code, :emergency_prefecture_id, :emergency_city, :emergency_street_number,
    :emergency_house_number, :emergency_building, :emergency_name_kana, :emergency_tel,
    :emergency_home_tel, :emergency_name, :emergency_relationship_id, :hire_date, :expired_start_date,
    :expired_end_date, :expired_type, :absence_start_date, :absence_end_date,
    :absence_type, :contract_status, :working_start_date, :working_end_date, :retirement_date,
    :retirement_reason_type, :retirement_reason, :maiden_name, :maiden_name_kana, :job_entry_date,
    :debut_date, :rank_up_training_date, :paid_occupation_establishment_date, :arrangement_type,
    :residence_status, :residence_card_name, :residence_validity, :residence_permission,
    :residence_permission_validity, :foreigner_note1, :foreigner_note2, :optional_item1,
    :optional_item2, :optional_item3, :optional_item4, :optional_item5, :optional_item6,
    :optional_item7, :optional_item8, :staff_number, :role, :residence_number]

  COPY_ATTRS = [:birthday, :gender_id, :postal_code, :prefecture_id, :city, :street_number,
    :house_number, :building, :address_kana, :home_station_id, :school_station_id, :tel, :home_tel,
    :residence_status, :residence_validity, :residence_expiration_date, :residence_number,
    :residence_permission, :residence_permission_validity, :residence_img_front_url,
    :residence_img_back_url, :certificate_img_url, :emergency_postal_code,
    :emergency_prefecture_id, :emergency_city, :emergency_street_number, :emergency_house_number,
    :emergency_building, :emergency_name, :emergency_relationship_id, :emergency_tel,
    :emergency_home_tel, :nationality, :is_admin_validate, :other_nationality, :social_attribute,
    :registration_type, :is_emergency_address_differ_current_address]

  COPY_RELATIONSHIPS = [:staff_account_transfer, :staff_check_item, :staff_long_vacations]

  HAS_MANY_COPY_RELATIONSHIPS = [:staff_long_vacations]

  ACCOUNT_NAME_ATTRS = [:name, :name_kana]

  HAS_RESIDENCE_PERMISSION = :has

  LIMIT_SELECT = 20

  EXPIRED_CONTRACT_CODE_HOUR = 18

  OP_CONFIRM_STEP_1_ATTRS = [:id, :name, :name_kana, :birthday, :gender_id,
    :postal_code, :prefecture_id, :city, :street_number, :house_number, :building,
    :home_station_id, :school_station_id, :email, :tel,
    :home_tel, :social_attribute, :social_attribute_other, :registration_history,
    :is_emergency_address_differ_current_address, :emergency_postal_code, :emergency_prefecture_id,
    :emergency_city, :emergency_street_number, :emergency_house_number, :emergency_building,
    :emergency_name, :emergency_relationship_id, :emergency_tel,
    :nationality, :residence_status, :residence_validity, :residence_expiration_date,
    :residence_permission, :residence_permission_validity, :residence_img_front,
    :residence_img_back, :certificate_img, :status_id, :admin_approved_at, :other_nationality,
    :admin_evaluation_comment, :pic_evaluation_comment, :is_check_residence, :is_validate_residence,
    :residence_card_name, :residence_number, {career_up_id: []}]

  OP_CONFIRM_STEP_2_ATTRS = {staff_employment_history_attributes: [:id, :is_convenience_lawson,
    :convenience_lawson_shop, :is_convenience_7eleven, :is_convenience_family, :is_convenience_other,
    :is_partime_experience, :partime_place_name, :partime_start, :partime_end,
    {service_work_type: [], office_work_type: [], other_work_type: []}],
    staff_expectation_attributes: [:id, :is_working_car, :first_training_date,
    :short_term_note, :expectation_employment_type, :is_monday, :monday_start_time_1,
    :monday_end_time_1, :is_tuesday, :tuesday_start_time_1, :tuesday_end_time_1,
    :is_wednesday, :wednesday_start_time_1, :wednesday_end_time_1, :is_thursday,
    :thursday_start_time_1, :thursday_end_time_1, :is_friday, :friday_start_time_1,
    :friday_end_time_1, :is_saturday, :saturday_start_time_1, :saturday_end_time_1,
    :is_sunday, :sunday_start_time_1, :sunday_end_time_1, :work_condition]}

  OP_CONFIRM_STEP_3_ATTRS = {staff_account_transfer_attributes: [:id, :bank_id, :bank_branch_id,
    :account_name, :account_name_kana, :account_type, :account_number, :passbook_pic,
    :is_check_passbook]}

  OP_CONFIRM_STEP_4_ATTRS = [:is_expectation_condition, :payment_request,
    {staff_check_item_attributes: [:id, :department_id, :license,
    :is_residence_card, :is_mynumber_card, :is_health_insurance, :is_studen_card, :license_date,
    :is_identity_other, :identity_other_name, :is_residence_status,
    :uniform_size, :pant_size, :shoes_size,
    :is_residence_period, :is_permission, :is_check_address, :is_request_seal, :workable_time,
    :japanese_level, :working_other_place, :working_time],
    staff_departments_attributes: [:id, :department_id, :affiliation_date, :_destroy],
    staff_levels_attributes: [:id, :level, :staff_id, :start_use_date, :is_from_registration]}]

  DEFAULT_VALUE_STAFF_FROM_ENTRY = {staff_check_item: {health_uninsured_reason: 1296,
    welfare_uninsured_reason: 1299, employment_uninsured_reason: 1302},
    staff_salary: {income_tax_type: :i_t_target, insurance_subsection_type:
    :not_joining, employment_insurance_type: :e_i_not_target, tax_type: :ownership,
    is_spouse: :not_spouse}}

  DEFAULT_VALUE_STAFF_NEW = {staff_salary: {income_tax_type: :i_t_target, tax_type: :ownership,
    is_spouse: :not_spouse}}

  CLONE_ONLY_ATTRS = COPY_ATTRS << ApproveRejectStaffRequestService::NESTED_PERMIT_ATTRS

  ADMIN_UPDATE_STAFF_REVIEW_ATTRS = COPY_ATTRS + [:id,
    {staff_check_item_attributes: [:id, :japanese_level, :uniform_size, :shoes_size, :pant_size,
      :is_skip_uniform_data]}, STEP_2_ATTRS, STEP_3_ATTRS]

  REGISTRATION_ATTRS = [:name, :name_kana, :gender_id, :birthday, :postal_code, :tel]
  REGISTRATION_FIRST_TAB_ATTRS = [:postal_code, :prefecture_id, :city, :street_number, :house_number, :building,
    :home_station_id, :school_station_id, :email, :tel, :home_tel, :social_attribute, :social_attribute_other,
    :registration_history, :emergency_infos, :is_emergency_address_differ_current_address, :emergency_postal_code,
    :emergency_prefecture_id, :emergency_city, :emergency_street_number, :emergency_house_number,
    :emergency_building, :emergency_name, :emergency_relationship_id, :emergency_tel, {career_up_id: []}]

  STAFF_RECRUITMENT_PROCESS_ATTRS = {staff_recruitment_process_attributes: [:id,
    :registration_process_code, :interview_process_code, :training_process_code, :consider_dropping_out]}
end
