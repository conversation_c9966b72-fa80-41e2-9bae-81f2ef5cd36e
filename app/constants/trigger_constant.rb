module TriggerConstant
  WORK_ACHIEVEMENT_PREFIX = "w_achm"
  ARRANGE_PAYMENT_PREFIX = "arr_paym"
  REGULAR_ORDER_ARRANGE_PAYMENT_PREFIX = "ro_arr_paym"
  ARRANGE_BILLING_PREFIX = "arr_bill"
  REGULAR_ORDER_ARRANGE_BILLING_PREFIX = "ro_arr_bill"

  WORK_ACHIEVEMENT_TIME_FIELDS = %w(working_started_at working_ended_at rest1_started_at
    rest1_ended_at rest2_started_at rest2_ended_at rest3_started_at rest3_ended_at break_time)

  ARRANGE_PAYMENT_TIME_FIELDS = %w(payment_actual_working_time payment_basic_time payment_ot1_time
    payment_night_break_time payment_basic_break_time payment_ot_night_time
    payment_ot_day_time payment_night_time
    payment_late_time payment_leave_early_time)
  ARRANGE_PAYMENT_AMOUNT_FIELDS = %w(payment_basic_amount payment_ot_amount payment_night_amount
    payment_total_amount)
  ARRANGE_PAYMENT_UNIT_PRICE_FIELDS = %w(payment_basic_unit_price payment_night_unit_price
    payment_urgent_unit_price_addition payment_ot_unit_price)
  ARRANGE_PAYMENT_SUBTRACTION_FIELDS = %w(payment_subtraction_amount)
  ARRANGE_PAYMENT_NUMBER_FILEDS = %w(payment_field_1 payment_field_5 payment_field_4)

  ARRANGE_BILLING_WORKING_TIME_FIELDS = %w(billing_started_at billing_ended_at)
  ARRANGE_BILLING_REST_TIME_FIELDS = %w(billing_rest1_started_at billing_rest1_ended_at
    billing_rest2_started_at billing_rest2_ended_at billing_rest3_started_at billing_rest3_ended_at)
  ARRANGE_BILLING_REALITY_TIME_FIELDS = %w(billing_night_break_time billing_basic_break_time
    billing_actual_working_time billing_ot_time billing_basic_time billing_night_time
    billing_late_time billing_leave_early_time)
  ARRANGE_BILLING_AMOUNT_FIELDS = %w(billing_basic_amount billing_night_amount billing_ot_amount
    billing_total_amount)
  ARRANGE_BILLING_UNIT_PRICE_FIELDS = %w(billing_basic_unit_price billing_unit_price_addition
    billing_ot_unit_price billing_night_unit_price)
  ARRANGE_BILLING_BILLING_FIELDS = %w(billing_field_1 billing_field_2 billing_field_3
    billing_field_4 billing_field_5 billing_field_6 billing_field_7 billing_tax_exemption)

  ARRANGE_BILLING_FIELDS = ARRANGE_BILLING_WORKING_TIME_FIELDS + ARRANGE_BILLING_REST_TIME_FIELDS +
    ARRANGE_BILLING_REALITY_TIME_FIELDS + ARRANGE_BILLING_BILLING_FIELDS +
    ARRANGE_BILLING_UNIT_PRICE_FIELDS + ARRANGE_BILLING_AMOUNT_FIELDS

  ARRANGE_PAYMENT_FIELDS = %w(payment_unit_price_addition) + ARRANGE_PAYMENT_TIME_FIELDS +
    ARRANGE_PAYMENT_UNIT_PRICE_FIELDS + ARRANGE_PAYMENT_NUMBER_FILEDS +
    ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
  ARRANGE_PAYMENT_ARRANGED_FIELDS = ARRANGE_PAYMENT_NUMBER_FILEDS +
    ARRANGE_PAYMENT_UNIT_PRICE_FIELDS + ARRANGE_PAYMENT_AMOUNT_FIELDS +
    ARRANGE_PAYMENT_SUBTRACTION_FIELDS
  ARRANGE_BILLING_ARRANGED_FIELDS = %w(billing_ot_time billing_ot_amount billing_total_amount)
  ARRANGE_PAYMENT_OT1_FIELDS = %w(payment_ot1_time payment_ot_night_time payment_ot_day_time
    payment_ot_amount payment_basic_amount payment_total_amount payment_subtraction_amount)
  ARRANGE_PAYMENT_OT2_FIELDS = %w(payment_ot2_time payment_ot2_night_time payment_ot2_day_time
    payment_ot2_amount payment_ot45_night_time payment_ot45_day_time payment_ot45_amount
    payment_ot60_night_time payment_ot60_day_time payment_ot60_amount payment_basic_amount
    payment_total_amount payment_subtraction_amount)
  ARRANGE_PAYMENT_ABSENCE_FIELDS = ARRANGE_PAYMENT_UNIT_PRICE_FIELDS + ARRANGE_PAYMENT_TIME_FIELDS +
    ARRANGE_PAYMENT_NUMBER_FILEDS + ARRANGE_PAYMENT_AMOUNT_FIELDS + %w(payment_subtraction_amount) -
    %w(payment_ot1_time)

  REST_FIELDS = %i(rest1_started_at rest1_ended_at rest2_started_at rest2_ended_at
    rest3_started_at rest3_ended_at billing_rest1_started_at billing_rest1_ended_at
    billing_rest2_started_at billing_rest2_ended_at billing_rest3_started_at billing_rest3_ended_at)
  ARRANGE_BILLING_ALL_TIME_FIELDS = ARRANGE_BILLING_WORKING_TIME_FIELDS +
    ARRANGE_BILLING_REST_TIME_FIELDS
  ARRANGE_BILLING_ABSENCE_CALCULATE_FIELDS = ARRANGE_BILLING_FIELDS -
    ARRANGE_BILLING_ALL_TIME_FIELDS - %w(billing_field_4 billing_total_amount)
  CANCEL_PAYROLL_FIELDS = %w(payment_basic_time) + ARRANGE_PAYMENT_OT2_FIELDS - %w(payment_ot2_time)
  CANCEL_AFTER_ARRANGE_HAS_INSURANCE_FIELDS = %w(payment_ot1_time payment_ot_night_time payment_ot_day_time
    payment_ot_amount payment_basic_amount payment_night_amount payment_total_amount payment_subtraction_amount)
  CANCEL_AFTER_ARRANGE_NO_INSURANCE_FIELDS = %w(payment_basic_amount payment_night_amount
    payment_total_amount payment_subtraction_amount)
  TRIGGER_FIELDS = {
    arrange_billing: {
      billing_night_unit_price: {
        arr_bill: ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_unit_price_addition: {
        arr_paym: %w(payment_unit_price_addition) + ARRANGE_PAYMENT_AMOUNT_FIELDS +
          %w(payment_subtraction_amount),
        arr_bill: ARRANGE_BILLING_UNIT_PRICE_FIELDS - %w(billing_basic_unit_price) +
          ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_basic_unit_price: {
        arr_bill: %w(billing_ot_unit_price) + ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_ot_unit_price: {
        arr_bill: ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_ot_time: {
        arr_bill: %w(billing_basic_time) + %w(billing_ot_amount billing_basic_amount
          billing_total_amount)
      },
      rest_field: {
        arr_bill: %w(billing_field_2) + ARRANGE_BILLING_REALITY_TIME_FIELDS +
          ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_ended_at: {
        arr_bill: %w(billing_field_2) + ARRANGE_BILLING_REALITY_TIME_FIELDS +
          ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_started_at: {
        arr_bill: %w(billing_field_2) + ARRANGE_BILLING_WORKING_TIME_FIELDS +
          ARRANGE_BILLING_REALITY_TIME_FIELDS + ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_field_1: {
        arr_bill: %w(billing_total_amount)
      },
      billing_field_2: {
        arr_bill: %w(billing_total_amount)
      },
      billing_field_3: {
        arr_bill: %w(billing_total_amount)
      },
      billing_field_4: {
        arr_bill: %w(billing_total_amount)
      },
      billing_field_5: {
        arr_paym: %w(payment_field_4 payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        arr_bill: %w(billing_total_amount)
      },
      billing_field_6: {
        arr_paym: %w(payment_field_5 payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        arr_bill: %w(billing_total_amount)
      },
      billing_field_7: {
        arr_bill: %w(billing_total_amount)
      },
      billing_unit_price: {
        arr_bill: ARRANGE_BILLING_UNIT_PRICE_FIELDS
      },
      billing_addition_field: {
        arr_bill: %w(billing_unit_price_addition)
      },
      payment_rate_billing_field: {
        arr_bill: %w(billing_field_1 billing_field_2 billing_total_amount)
      },
      period_rate_billing_field: {
        arr_bill: %w(billing_field_6 billing_total_amount)
      },
      option_payment_rate_billing_field: {
        arr_bill: %w(billing_field_7 billing_total_amount)
      },
      arrange_from_absence: {
        arr_bill: ARRANGE_BILLING_FIELDS,
        arr_paym: %w(payment_field_4 payment_field_5 payment_total_amount) +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      absence_status: {
        arr_bill: %w(billing_field_4 billing_total_amount),
        arr_paym: %w(payment_field_4 payment_field_5 payment_total_amount) +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      billing_basic_time: {
        arr_bill: %w(billing_basic_amount billing_total_amount)
      },
      billing_actual_working_time: {
        arr_bill: %w(billing_basic_time billing_basic_amount billing_total_amount)
      },
      billing_night_time: {
        arr_bill: %w(billing_night_amount billing_total_amount)
      },
      cal_billing_night_time: {
        arr_bill: %w(billing_night_time billing_night_amount billing_total_amount)
      },
      billing_other_addition_fee: {
        arr_bill: %w(billing_total_amount)
      }
    },
    arrange_payment: {
      payment_field_1: {
        arr_paym: %w(payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_field_4: {
        arr_paym: %w(payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_field_5: {
        arr_paym: %w(payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_field_9: {
        arr_paym: %w(payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_ot1_time: {
        arr_paym: %w(payment_ot_day_time) + ARRANGE_PAYMENT_AMOUNT_FIELDS +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        arr_bill: %w(billing_ot_time billing_basic_time) + %w(billing_ot_amount billing_basic_amount
          billing_total_amount)
      },
      payment_basic_unit_price: {
        arr_paym: %w(payment_urgent_unit_price_addition payment_ot_unit_price) +
          ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_night_unit_price: {
        arr_paym: ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      ot1_field: {
        arr_paym: ARRANGE_PAYMENT_OT1_FIELDS,
        arr_bill: %w(billing_ot_time billing_basic_time) + %w(billing_ot_amount billing_basic_amount
          billing_total_amount)
      },
      ot2_field: {
        arr_paym: ARRANGE_PAYMENT_OT2_FIELDS
      },
      payment_urgent_unit_price_addition: {
        arr_paym: ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_bonus_unit_price: {
        arr_paym: ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_unit_price_field: {
        arr_paym: %w(payment_basic_unit_price payment_night_unit_price
          payment_urgent_unit_price_addition payment_ot_unit_price) +
          ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_basic_time: {
        arr_paym: %w(payment_basic_amount payment_total_amount payment_subtraction_amount)
      },
      payment_actual_working_time: {
        arr_paym: %w(payment_ot1_time payment_ot_day_time payment_basic_time) +
          ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        arr_bill: %w(billing_ot_time billing_basic_time) + %w(billing_ot_amount billing_basic_amount
          billing_total_amount)
      },
      payment_ot_night_time: {
        arr_paym: %w(payment_ot_day_time payment_ot_amount payment_total_amount
          payment_subtraction_amount)
      },
      payment_ot_day_time: {
        arr_paym: %w(payment_ot_amount payment_total_amount payment_subtraction_amount)
      },
      absence_status: {
        arr_bill: %w(billing_ot_time billing_ot_amount billing_basic_time billing_basic_amount
          billing_total_amount)
      },
      arrange_from_absence: {
        arr_paym: ARRANGE_PAYMENT_ABSENCE_FIELDS,
        arr_bill: %w(billing_ot_time billing_ot_amount billing_basic_time billing_basic_amount
          billing_total_amount)
      },
      update_payment_night_time: {
        arr_paym: %w(payment_night_time payment_ot_night_time payment_ot_day_time payment_ot_amount
          payment_basic_amount payment_night_amount payment_total_amount payment_subtraction_amount)
      },
      cancel_payroll_trigger_fields: {
        arr_paym: CANCEL_PAYROLL_FIELDS
      },
      cancel_after_arrange_has_insurance: {
        arr_paym: CANCEL_AFTER_ARRANGE_HAS_INSURANCE_FIELDS
      }
    },
    work_achievement: {
      rest_field: {
        arr_paym: ARRANGE_PAYMENT_TIME_FIELDS + ARRANGE_PAYMENT_AMOUNT_FIELDS +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        arr_bill: %w(billing_field_2) + ARRANGE_BILLING_WORKING_TIME_FIELDS +
          ARRANGE_BILLING_REST_TIME_FIELDS + ARRANGE_BILLING_REALITY_TIME_FIELDS +
          ARRANGE_BILLING_AMOUNT_FIELDS
      },
      working_ended_at: {
        arr_paym: ARRANGE_PAYMENT_TIME_FIELDS + ARRANGE_PAYMENT_AMOUNT_FIELDS +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        arr_bill: %w(billing_field_2) + ARRANGE_BILLING_WORKING_TIME_FIELDS +
          ARRANGE_BILLING_REALITY_TIME_FIELDS + ARRANGE_BILLING_AMOUNT_FIELDS
      },
      working_started_at: {
        arr_paym: ARRANGE_PAYMENT_TIME_FIELDS + ARRANGE_PAYMENT_AMOUNT_FIELDS +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        arr_bill: %w(billing_field_2) + ARRANGE_BILLING_WORKING_TIME_FIELDS +
          ARRANGE_BILLING_REALITY_TIME_FIELDS + ARRANGE_BILLING_AMOUNT_FIELDS
      }
    }
  }

  REGULAR_ORDER_TRIGGER_FIELDS = {
    arrange_billing: {
      billing_night_unit_price: {
        ro_arr_bill: ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_unit_price_addition: {
        ro_arr_paym: %w(payment_unit_price_addition) + ARRANGE_PAYMENT_AMOUNT_FIELDS +
          %w(payment_subtraction_amount),
        ro_arr_bill: ARRANGE_BILLING_UNIT_PRICE_FIELDS - %w(billing_basic_unit_price) +
          ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_basic_unit_price: {
        ro_arr_bill: %w(billing_ot_unit_price) + ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_ot_unit_price: {
        ro_arr_bill: ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_ot_time: {
        ro_arr_bill: %w(billing_basic_time) + %w(billing_ot_amount billing_basic_amount
          billing_total_amount)
      },
      rest_field: {
        ro_arr_bill: %w(billing_field_2) + ARRANGE_BILLING_REALITY_TIME_FIELDS +
          ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_ended_at: {
        ro_arr_bill: %w(billing_field_2) + ARRANGE_BILLING_REALITY_TIME_FIELDS +
          ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_started_at: {
        ro_arr_bill: %w(billing_field_2) + ARRANGE_BILLING_WORKING_TIME_FIELDS +
          ARRANGE_BILLING_REALITY_TIME_FIELDS + ARRANGE_BILLING_AMOUNT_FIELDS
      },
      billing_field_1: {
        ro_arr_bill: %w(billing_total_amount)
      },
      billing_field_2: {
        ro_arr_bill: %w(billing_total_amount)
      },
      billing_field_3: {
        ro_arr_bill: %w(billing_total_amount)
      },
      billing_field_4: {
        ro_arr_bill: %w(billing_total_amount)
      },
      billing_field_5: {
        ro_arr_paym: %w(payment_field_4 payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        ro_arr_bill: %w(billing_total_amount)
      },
      billing_field_6: {
        ro_arr_paym: %w(payment_field_5 payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        ro_arr_bill: %w(billing_total_amount)
      },
      billing_field_7: {
        ro_arr_bill: %w(billing_total_amount)
      },
      billing_unit_price: {
        ro_arr_bill: ARRANGE_BILLING_UNIT_PRICE_FIELDS
      },
      billing_addition_field: {
        ro_arr_bill: %w(billing_unit_price_addition)
      },
      payment_rate_billing_field: {
        ro_arr_bill: %w(billing_field_1 billing_field_2 billing_total_amount)
      },
      period_rate_billing_field: {
        ro_arr_bill: %w(billing_field_6 billing_total_amount)
      },
      option_payment_rate_billing_field: {
        ro_arr_bill: %w(billing_field_7 billing_total_amount)
      },
      arrange_from_absence: {
        ro_arr_bill: ARRANGE_BILLING_FIELDS,
        ro_arr_paym: %w(payment_field_4 payment_field_5 payment_total_amount) +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      absence_status: {
        ro_arr_bill: %w(billing_field_4 billing_total_amount),
        ro_arr_paym: %w(payment_field_4 payment_field_5 payment_total_amount) +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      billing_basic_time: {
        ro_arr_bill: %w(billing_basic_amount billing_total_amount)
      },
      billing_actual_working_time: {
        ro_arr_bill: %w(billing_basic_time billing_basic_amount billing_total_amount)
      },
      billing_night_time: {
        ro_arr_bill: %w(billing_night_amount billing_total_amount)
      },
      cal_billing_night_time: {
        ro_arr_bill: %w(billing_night_time billing_night_amount billing_total_amount)
      },
      billing_other_addition_fee: {
        ro_arr_bill: %w(billing_total_amount)
      }
    },
    arrange_payment: {
      payment_field_1: {
        ro_arr_paym: %w(payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_field_4: {
        ro_arr_paym: %w(payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_field_5: {
        ro_arr_paym: %w(payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_ot1_time: {
        ro_arr_paym: %w(payment_ot_day_time) + ARRANGE_PAYMENT_AMOUNT_FIELDS +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        ro_arr_bill: %w(billing_ot_time billing_basic_time) + %w(billing_ot_amount billing_basic_amount
          billing_total_amount)
      },
      payment_basic_unit_price: {
        ro_arr_paym: %w(payment_urgent_unit_price_addition payment_ot_unit_price) +
          ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_night_unit_price: {
        ro_arr_paym: ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      ot1_field: {
        ro_arr_paym: ARRANGE_PAYMENT_OT1_FIELDS,
        ro_arr_bill: %w(billing_ot_time billing_basic_time) + %w(billing_ot_amount billing_basic_amount
          billing_total_amount)
      },
      ot2_field: {
        ro_arr_paym: ARRANGE_PAYMENT_OT2_FIELDS
      },
      payment_urgent_unit_price_addition: {
        ro_arr_paym: ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_bonus_unit_price: {
        ro_arr_paym: ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_unit_price_field: {
        ro_arr_paym: %w(payment_basic_unit_price payment_night_unit_price
          payment_urgent_unit_price_addition payment_ot_unit_price) +
          ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      },
      payment_basic_time: {
        ro_arr_paym: %w(payment_basic_amount payment_total_amount payment_subtraction_amount)
      },
      payment_actual_working_time: {
        ro_arr_paym: %w(payment_ot1_time payment_ot_day_time payment_basic_time) +
          ARRANGE_PAYMENT_AMOUNT_FIELDS + ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        ro_arr_bill: %w(billing_ot_time billing_basic_time) + %w(billing_ot_amount billing_basic_amount
          billing_total_amount)
      },
      payment_ot_night_time: {
        ro_arr_paym: %w(payment_ot_day_time payment_ot_amount payment_total_amount
          payment_subtraction_amount)
      },
      payment_ot_day_time: {
        ro_arr_paym: %w(payment_ot_amount payment_total_amount payment_subtraction_amount)
      },
      absence_status: {
        ro_arr_bill: %w(billing_ot_time billing_ot_amount billing_basic_time billing_basic_amount
          billing_total_amount)
      },
      arrange_from_absence: {
        ro_arr_paym: ARRANGE_PAYMENT_ABSENCE_FIELDS,
        ro_arr_bill: %w(billing_ot_time billing_ot_amount billing_basic_time billing_basic_amount
          billing_total_amount)
      },
      update_payment_night_time: {
        ro_arr_paym: %w(payment_night_time payment_ot_night_time payment_ot_day_time payment_ot_amount
          payment_basic_amount payment_night_amount payment_total_amount payment_subtraction_amount)
      },
      cancel_payroll_trigger_fields: {
        ro_arr_paym: CANCEL_PAYROLL_FIELDS
      },
      cancel_after_arrange_has_insurance: {
        ro_arr_paym: CANCEL_AFTER_ARRANGE_HAS_INSURANCE_FIELDS,
        ro_arr_bill: %w(billing_total_amount)
      },
      cancel_after_arrange_no_insurance: {
        ro_arr_bill: %w(billing_total_amount)
      },
      payment_field_9: {
        ro_arr_paym: %w(payment_total_amount) + ARRANGE_PAYMENT_SUBTRACTION_FIELDS
      }
    },
    work_achievement: {
      rest_field: {
        ro_arr_paym: ARRANGE_PAYMENT_TIME_FIELDS + ARRANGE_PAYMENT_AMOUNT_FIELDS +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        ro_arr_bill: %w(billing_field_2) + ARRANGE_BILLING_WORKING_TIME_FIELDS +
          ARRANGE_BILLING_REST_TIME_FIELDS + ARRANGE_BILLING_REALITY_TIME_FIELDS +
          ARRANGE_BILLING_AMOUNT_FIELDS
      },
      working_ended_at: {
        ro_arr_paym: ARRANGE_PAYMENT_TIME_FIELDS + ARRANGE_PAYMENT_AMOUNT_FIELDS +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        ro_arr_bill: %w(billing_field_2) + ARRANGE_BILLING_WORKING_TIME_FIELDS +
          ARRANGE_BILLING_REALITY_TIME_FIELDS + ARRANGE_BILLING_AMOUNT_FIELDS
      },
      working_started_at: {
        ro_arr_paym: ARRANGE_PAYMENT_TIME_FIELDS + ARRANGE_PAYMENT_AMOUNT_FIELDS +
          ARRANGE_PAYMENT_SUBTRACTION_FIELDS,
        ro_arr_bill: %w(billing_field_2) + ARRANGE_BILLING_WORKING_TIME_FIELDS +
          ARRANGE_BILLING_REALITY_TIME_FIELDS + ARRANGE_BILLING_AMOUNT_FIELDS
      }
    }
  }
end
