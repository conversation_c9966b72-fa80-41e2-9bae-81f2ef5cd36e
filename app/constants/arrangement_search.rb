module ArrangementSearch
  SEARCH_BY_INTEGER = %w(staff_id id chain_id order_id order_case_id
    payment_field_1 staff_total_work_experience)
  SEARCH_BY_STRING = %w(location_id location_code oc_segment_id order_portion_status_before_cast
    working_time_status_id payment_adjusment_type_id billing_adjusment_type_id corporation_id
    department_id current_location_type staff_department_id invoice_target)
  SEARCH_BY_BOOLEAN = %w(not_prepared not_arrived is_prepared is_arrived special_offer urgent
    diff_working_time location_is_export_timesheet already_exported is_regular_order is_lawson
    is_training_first_round is_training_second_round)
  SEARCH_BY_DATETIME = %w(working_date working_ended_date order_created_date)
  SEARCH_BY_TIME = %w(working_started_time)
  SEARCH_BY_SORT_STRING = %w(location_name staff_account_name)
  SEARCH_BY_DISPLAY_STATUS = %w(display_status_before_cast)
  SEARCH_BY_NESTED_ARRANGE_LOGS = %w(nested_arrange_logs)
  SEARCH_BY_CUSTOM_BILLING_PAYMENT_USAGE = %w(custom_billing_payment)
  ALL_SEARCH_FIELDS = SEARCH_BY_INTEGER + SEARCH_BY_DATETIME + SEARCH_BY_STRING + SEARCH_BY_BOOLEAN +
    SEARCH_BY_TIME + SEARCH_BY_SORT_STRING + SEARCH_BY_DISPLAY_STATUS +
    SEARCH_BY_NESTED_ARRANGE_LOGS + SEARCH_BY_CUSTOM_BILLING_PAYMENT_USAGE
end
