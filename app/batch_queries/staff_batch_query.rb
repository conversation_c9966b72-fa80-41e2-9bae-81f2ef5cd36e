module StaffBatchQuery
  extend ActiveSupport::Concern

  included do
    scope :requesting_contract, -> do
      joins(:staff_contracts).where(staff_contracts: {request_status: :requesting})
    end

    scope :end_contract, -> do
      server_time = ServerTime.now
      # TODO(Phuong): update or remove query
      where(last_contract_indefinite_employment_flag: :term, contract_status: :dont_update)
        .where("staffs.is_update_contract = 1")
        .where("staffs.last_contract_end_date >= ? AND staffs.last_contract_end_date <= ?",
          server_time.beginning_of_month, server_time.end_of_month)
    end

    scope :to_alert_update_contract, ->time do
      where(last_contract_indefinite_employment_flag: :term)
        .where("staffs.last_contract_end_date >= ? AND staffs.last_contract_end_date <= ?",
          time.beginning_of_month, time.end_of_month)
    end

    scope :re_expire_in, ->time do
      where(residence_expiration_date: time)
    end

    scope :to_alert_contract_end_at, ->(date) do
      where.not(last_contract_id: nil)
        .where(contract_status: :update_ok, last_contract_indefinite_employment_flag: :term)
        .where("staffs.last_contract_end_date >= ? AND staffs.last_contract_end_date <= ?",
          date.beginning_of_day, date.end_of_day)
    end

    scope :eligible_for_contract_renewal, -> do
      confirmed_staff.haken.working.can_update_staff_status.update_ok
    end
  end
end
