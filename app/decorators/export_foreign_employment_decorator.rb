module ExportForeignEmploymentDecorator
  require "nkf"

  DEFAULT_0_0_1 = "001"
  DEFAULT_0_0_2 = "002"
  ALL_ELEMENTS = 28
  NATIONALITY_CODE = "G99"
  RESIDENCE_PERMISSION = {
    "-1" => "",
    "0" => "002",
    "1" => "001"
  }
  FIELD_DATA = {
    0 => :residence_card_name_format,
    1 => :account_name_kana_format,
    2 => :residence_code,
    4 => :residence_expiration_non_separator_format,
    6 => :birthday_non_separator_format,
    7 => :gender_num,
    8 => :nationality_code,
    9 => :other_nationality_code,
    10 => :area_code,
    11 => :residence_status_num,
    12 => :residence_number,
    13 => :hire_date_non_separator_format,
    18 => :employment_insurance_e_i
  }

  def residence_card_name_format
    self.residence_card_name
  end

  def account_name_kana_format
    NKF.nkf("-X -w", self.account_name_kana)&.tr("0-9a-zA-Z", "０-９ａ-ｚＡ-Ｚ")&.tr(" ", "　")
  end

  def row_data_foreign_employment
    row_array = Array.new(ALL_ELEMENTS, nil)
    FIELD_DATA.each do |key, method|
      row_array[key] = self.send(method)
    end
    row_array[3] = row_array[5] = row_array[15] = DEFAULT_0_0_2
    row_array[14] = DEFAULT_0_0_1
    row_array
  end

  private

  def area_code
    nationality_type.code_1.to_s[0]
  end

  def gender_num
    "00#{gender_id_int}"
  end

  def birthplace_code
    birthplace&.code
  end

  def residence_status_num
    RESIDENCE_PERMISSION[residence_permission_int.to_s]
  end

  def employment_insurance_e_i
    staff_salary.employment_insurance_number if staff_salary&.e_i_target?
  end

  def nationality_code
    code = nationality_type&.code_1
    code != NATIONALITY_CODE || is_other_nationality? ? code : ""
  end

  def other_nationality_code
    return other_nationality if is_other_nationality?

    nationality_type&.code_1 == NATIONALITY_CODE ? NATIONALITY_CODE : ""
  end

  def birthday_non_separator_format
    birthday&.strftime Settings.date.non_separator
  end

  def residence_expiration_non_separator_format
    residence_expiration_date&.strftime Settings.date.non_separator
  end

  def hire_date_non_separator_format
    hire_date&.strftime Settings.date.non_separator
  end
end
