module OrderCaseWagesDecorator
  def special_offer_order_rate
    ((self.special_offer_fee.to_i + cal_billing_field_6) * OrderCase::OFFER_FEE_RATE).ceil
  end

  def payment_unit_price_addition
    return 0 if self.regular_order?
    return 0 unless period_rate

    period_rate.unit_rate
  end

  def total_unit_price_addition staff, time_season
    total = hourly_payment_unit_price_addition(staff, time_season)
    data_urgent_fee = self.is_urgent ? urgent_fee(staff, time_season) : 0
    total += data_urgent_fee > 0 ? data_urgent_fee : self.payment_bonus_unit_price
    total
  end

  def urgent_price staff
    payment_unit_price(staff).payment_urgent_unit_price
  end

  def staff_salary_before_apply staff
    base_price = basic_unit_price(staff).to_i
    urgent_price = urgent_price(staff).to_i
    base_price = urgent_price if self.is_urgent && urgent_price > 0
    base_price + payment_unit_price_addition.to_i
  end

  def staff_salary_after_apply staff
    base_price = basic_unit_price(staff).to_i
    urgent_price = urgent_price(staff).to_i
    base_price = urgent_price if self.is_urgent && urgent_price > base_price
    base_price + payment_unit_price_addition.to_i
  end

  def payment_urgent_price staff
    if self.regular_order?
      price = self.is_urgent ? ArrangePayment::DEFAULT_REGULAR_ORDER_URGENT_ADDITION : 0
    else
      price = urgent_price(staff).to_i - basic_unit_price(staff).to_i
    end
    price > 0 ? (price + payment_unit_price_addition.to_i) : payment_unit_price_addition.to_i
  end

  def urgent_fee staff, time_season = :all
    return ArrangePayment::DEFAULT_REGULAR_ORDER_URGENT_ADDITION if self.is_urgent && self.regular_order?

    unit_price = basic_unit_price_by_hour(staff, time_season)
    fee = urgent_price(staff).to_i - unit_price.to_i
    [fee, 0].max
  end

  def special_addition_flag staff
    if self.special_offer_order_rate.to_i > 0
      offer_flag_by_key self.special_offer_order_rate, :offer_fee_by_order_rate
    elsif @arrangement && (price = @arrangement.arrange_payment.total_unit_price_addition) > 0
      offer_flag_by_key price
    elsif self.is_urgent && (price = self.payment_urgent_price(staff)) > 0
      offer_flag_by_key price
    elsif self.payment_unit_price_addition.to_i > 0
      offer_flag_by_key self.payment_unit_price_addition
    end
  end

  def hourly_payment_unit_price_addition staff, time_season
    return 0 if payment_unit_price_addition.to_i <= 0 && !self.regular_order?

    unit_price = basic_unit_price_by_hour(staff, time_season)
    fee = payment_unit_price_addition.to_i - unit_price.to_i
    [fee, 0].max
  end

  def basic_unit_price_by_hour staff, time_season
    case time_season
    when :day
      unit_price = basic_unit_price(staff)
    when :night
      unit_price = night_unit_price(staff)
    else
      unit_price = basic_unit_price_by_wage(staff)
    end
    unit_price
  end

  def basic_unit_price staff
    return regular_order_arrange_payment.payment_basic_unit_price if self.regular_order?
    return unless payment_unit_price(staff)

    payment_unit_price(staff)[staff.payment_unit_price_field]
  end

  def basic_unit_price_by_wage staff
    self.is_night_working? ? night_unit_price(staff) : basic_unit_price(staff)
  end

  def night_unit_price staff
    return regular_order_arrange_payment.payment_night_unit_price if self.regular_order?
    return unless payment_unit_price(staff)

    payment_unit_price(staff)[staff.payment_unit_price_by_night_field]
  end

  def payment_bonus_unit_price
    self.arrangements.is_recruiting_jobs&.first&.payment_bonus_unit_price ||
      self.arrangements&.first&.payment_bonus_unit_price || 0
  end

  def cal_billing_field_6
    return 0 unless period_rate

    period_rate.order_rate.to_i
  end

  # Deprecated (TinhDT): Replace by DisplayUnitFeeCommand
  def display_unit_fee staff
    return 0 unless self.not_training?

    unit_fee = self.total_unit_price_addition(staff, :day)
    unit_fee = self.total_unit_price_addition(staff, :night) * ArrangePayment::NIGHT_SALARY_RATE if self.is_night_working? && !self.is_both_day_and_night_working?
    unit_fee.round
  end

  def special_offer_flag
    return unless self.offer_flag_key

    offer_fee_to_currency = ActionController::Base.helpers
      .number_to_currency self.send(offer_flag_key), unit: "", precision: 0
    I18n.t("staff.order_cases.special_offer_flag.#{offer_flag_key}", count: offer_fee_to_currency)
  end

  def special_offer_flags staff
    flags = []
    return flags unless self.not_training?

    unit_fee = self.display_unit_fee(staff)
    flags << self.offer_flag_by_key(unit_fee) if unit_fee > 0
    return flags if self.regular_order?

    order_fee = self.special_offer_order_rate.to_i
    arrangement = staff.current_arrangement_of(id)
    if !order.is_from_lawson? &&
      (arrangement&.arranged? || arrangement&.cancel_after_arrange_has_insurance?)
      order_fee += arrangement.payment_field_9.to_i
    end
    flags << self.offer_flag_by_key(order_fee, :offer_fee_by_order_rate) if order_fee > 0
    flags
  end

  def offer_flag_by_key price, key = :offer_fee_by_unit_rate
    price = ActionController::Base.helpers.number_to_currency price, unit: "", precision: 0
    I18n.t("staff.order_cases.special_offer_flag.#{key}", count: price)
  end

  def offer_fee_by_order_rate
    ((self.special_offer_fee.to_i + self.peak_period_order_rate.to_i) * OrderCase::OFFER_FEE_RATE).round
  end

  def offer_fee_by_unit_rate
    (self.peak_period_unit_rate.to_i * OrderCase::OFFER_FEE_RATE).round
  end

  def offer_flag_key
    return :offer_fee_by_order_rate if offer_fee_by_order_rate > 0

    :offer_fee_by_unit_rate if offer_fee_by_unit_rate > 0
  end

  def special_offer_flags_by_wages order_case_wages = {}
    unit_fee = unit_fee_by_wages order_case_wages
    order_fee = order_addition_by_wages order_case_wages
    flags = []
    flags << self.offer_flag_by_key(unit_fee) if unit_fee > 0
    flags << self.offer_flag_by_key(order_fee, :offer_fee_by_order_rate) if order_fee > 0
    flags
  end

  def unit_fee_by_wages order_case_wages = {}
    return order_case_wages[:day_addition] if self.is_day_working?

    order_case_wages[:night_addition]
  end

  def order_addition_by_wages order_case_wages = {}
    order_case_wages[:order_addition]
  end

  private

  def period_rate
    working_date = self.case_started_at.strftime(Settings.date.formats)
    period_rates = PeakPeriods::GetPeriodRatesByDatesQuery.execute(
      [working_date],
      self.location_prefecture_id,
      self.corporation_id,
      payment_rate.chain_id
    )

    period_rates[working_date]
  end
end
