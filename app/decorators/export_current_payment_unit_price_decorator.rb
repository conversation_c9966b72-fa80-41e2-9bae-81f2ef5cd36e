module ExportCurrentPaymentUnitPriceDecorator
  extend ActiveSupport::Concern
  ALL_ELEMENTS = 30
  TRUE_TO_INT = 1
  FALSE_TO_INT = 0

  def row_data label_keys
    data = label_keys.map{|method| send method}
    EncodingService.convert_encoding_to_shift_jis data
  end

  class_methods do
    def label_i18n
      EncodingService.convert_encoding_to_shift_jis(
        I18n.t("admin.payment_unit_price.export.label_name").values
      )
    end
  end

  private
  def name_prefecture
    prefecture&.name
  end

  def nationality_type_int
    bool_to_int nationality_type
  end

  def effective_start_date_format
    effective_start_date&.strftime Settings.date.formats
  end

  def bool_to_int bool
    bool ? TRUE_TO_INT : FALSE_TO_INT
  end
end
