module ExportOrderBranchUnitPriceDecorator
  extend ActiveSupport::Concern
  UNIT_PRICE_ALL_ELEMENTS = 77
  UNPAID_OVERTME_PAYMENT_RATE = 1.25
  PAYMENT_STATUTORY_UNIT_PRICE_RATE = 1.35
  BILLING_STATUTORY_UNIT_PRICE_RATE = 1.35
  UNIT_PRICE_FIELD_DATA = {
    0 => :order_no,
    1 => :work_system_no,
    2 => :payment_unit_price_classification,
    3 => :payment_basic_unit_price,
    4 => :unpaid_overtime_payment,
    5 => :midnight_payment_late_night_bid,
    6 => :payment_statutory_unit_price,
    7 => :unpaid_overtime_payment,
    8 => :midnight_payment_late_night_bid,
    9 => :midnight_payment_late_night_bid,
    10 => :payment_allowance_1,
    11 => :payment_allowance_2,
    12 => :payment_allowance_3,
    13 => :payment_allowance_4,
    14 => :payment_allowance_5,
    15 => :payment_allowance_6,
    16 => :payment_allowance_7,
    17 => :payment_allowance_8,
    18 => :payment_allowance_9,
    19 => :payment_allowance_10,
    20 => :payment_allowance_11,
    21 => :payment_allowance_12,
    22 => :payment_allowance_13,
    23 => :payment_allowance_14,
    24 => :payment_allowance_15,
    25 => :payment_allowance_16,
    26 => :payment_allowance_17,
    27 => :payment_allowance_18,
    28 => :payment_allowance_19,
    29 => :payment_allowance_20,
    30 => :deduction_item_1,
    31 => :deduction_item_2,
    32 => :deduction_item_3,
    33 => :deduction_item_4,
    34 => :deduction_item_5,
    35 => :deduction_item_6,
    36 => :deduction_item_7,
    37 => :deduction_item_8,
    38 => :deduction_item_9,
    39 => :deduction_item_10,
    40 => :deduction_item_11,
    41 => :deduction_item_12,
    42 => :deduction_item_13,
    43 => :deduction_item_14,
    44 => :deduction_item_15,
    45 => :deduction_item_16,
    46 => :deduction_item_17,
    47 => :deduction_item_18,
    48 => :deduction_item_19,
    49 => :deduction_item_20,
    50 => :billing_unit_classification,
    52 => :billing_basic_unit_price,
    53 => :overtime_charge_overtime,
    54 => :claim_late_night_price,
    55 => :billing_statutory_unit_price,
    56 => :billing_basic_unit_price,
    57 => :claim_item_1,
    58 => :claim_item_2,
    59 => :claim_item_3,
    60 => :claim_item_4,
    61 => :claim_item_5,
    62 => :claim_item_6,
    63 => :claim_item_7,
    64 => :claim_item_8,
    65 => :claim_item_9,
    66 => :claim_item_10,
    67 => :claim_item_11,
    68 => :claim_item_12,
    69 => :claim_item_13,
    70 => :claim_item_14,
    71 => :claim_item_15,
    72 => :claim_item_16,
    73 => :claim_item_17,
    74 => :claim_item_18,
    75 => :claim_item_19,
    76 => :claim_item_20
  }
  FIXED_0_VALUE = 0
  FIXED_1_VALUE = 1
  FIXED_0_VALUE_FIELDS = %i(payment_allowance_1
    payment_allowance_2 payment_allowance_3 payment_allowance_4 payment_allowance_5
    payment_allowance_6 payment_allowance_7 payment_allowance_8 payment_allowance_9
    payment_allowance_10 payment_allowance_11 payment_allowance_12 payment_allowance_13
    payment_allowance_14 payment_allowance_15 payment_allowance_16 payment_allowance_17
    payment_allowance_18 payment_allowance_19 payment_allowance_20 deduction_item_1
    deduction_item_2 deduction_item_3 deduction_item_4 deduction_item_5 deduction_item_6
    deduction_item_7 deduction_item_8 deduction_item_9 deduction_item_10 deduction_item_11
    deduction_item_12 deduction_item_13 deduction_item_14 deduction_item_15 deduction_item_16
    deduction_item_17 deduction_item_18 deduction_item_19 deduction_item_20
    claim_item_3 claim_item_4 claim_item_5 claim_item_6 claim_item_7
    claim_item_8 claim_item_9 claim_item_10 claim_item_11 claim_item_12 claim_item_13
    claim_item_14 claim_item_15 claim_item_16 claim_item_17 claim_item_18 claim_item_19
    claim_item_20)
  FIXED_1_VALUE_FIELDS = %i(work_system_no payment_unit_price_classification
    billing_unit_classification)

  MAPPING_SALARY = {
    13 => 1000,
    14 => 1000,
    11 => 1000,
    12 => 1000,
    1 => 850,
    4 => 800,
    16 => 950,
    23 => 1000,
    27 => 950,
    28 => 950,
    26 => 950,
    34 => 950,
    38 => 900,
    40 => 920
  }

  FIELD_TYPE = {string: [4, 5, 6, 7, 8, 9, 52, 53, 54, 55, 56, 57, 58]}
  CLAIM_MINUTES = 360

  def unit_price_row_data export_order_id
    @export_order_id = export_order_id
    row_array = Array.new(UNIT_PRICE_ALL_ELEMENTS, nil)
    UNIT_PRICE_FIELD_DATA.each do |key, method|
      row_array[key] = set_row_value method
    end
    row_array
  end

  class_methods do
    def unit_price_sheet_name
      I18n.t("admin.order.export_xlsx.unit_price.sheet_name")
    end

    def unit_price_label_i18n
      I18n.t("admin.order.export_xlsx.unit_price.label_name").values
    end

    def unit_price_column_data_types
      type_array = Array.new(UNIT_PRICE_ALL_ELEMENTS, nil)
      FIELD_TYPE.each do |type, columns|
        columns.each{|index| type_array[index] = type}
      end
      type_array
    end
  end

  private
  def payment_basic_unit_price
    prefecture_id = order.location_prefecture_id
    MAPPING_SALARY.key?(prefecture_id) ? MAPPING_SALARY[prefecture_id] : FIXED_0_VALUE
  end

  def unpaid_overtime_payment
    (payment_basic_unit_price * UNPAID_OVERTME_PAYMENT_RATE).floor
  end

  def midnight_payment_late_night_bid
    unpaid_overtime_payment - payment_basic_unit_price
  end

  def payment_statutory_unit_price
    (payment_basic_unit_price * PAYMENT_STATUTORY_UNIT_PRICE_RATE).floor
  end

  def billing_unit_price
    billing_price = BillingUnitPrice.find_by prefecture_id: order.location_prefecture_id
    return BillingUnitPrice.new if billing_price.blank?

    billing_price
  end

  def billing_basic_unit_price
    (in_campaign? ? billing_unit_price.day_campaign_price : billing_unit_price.day_price).to_i
  end

  def in_campaign?
    billing_unit_price.campaign_started_at.present? &&
      self.started_at.to_date <= billing_unit_price.campaign_ended_at.to_date &&
      self.started_at.to_date >= billing_unit_price.campaign_started_at.to_date
  end

  def overtime_charge_overtime
    (in_campaign? ? billing_unit_price.night_campaign_price : billing_unit_price.night_price).to_i
  end

  def claim_late_night_price
    overtime_charge_overtime - billing_basic_unit_price
  end

  def billing_statutory_unit_price
    (billing_basic_unit_price * BILLING_STATUTORY_UNIT_PRICE_RATE).floor
  end

  def claim_item_1
    billing_unit_price.transportation_fee
  end

  def claim_item_2
    short_allowance = billing_unit_price.short_allowance
    predetermined_except_break_time <= CLAIM_MINUTES ? short_allowance : FIXED_0_VALUE
  end

  def set_row_value method
    if FIXED_0_VALUE_FIELDS.include?(method)
      FIXED_0_VALUE
    elsif FIXED_1_VALUE_FIELDS.include?(method)
      FIXED_1_VALUE
    else
      send method
    end
  end

  def predetermined_except_break_time
    working_end_time_format - working_start_time_format
  end
end
