module ExportStaffPayrollInfosDecorator
  extend ActiveSupport::Concern
  DEFAULT_ZERO = 0
  DEFAULT_ONE = 1
  DEFAULT_TWO = 2
  DEFAULT_FOUR = 4
  FIXED_CODE = "300-021"
  TOTAL_COLS = 34

  FIELD_DATA = {
    0 => :staff_number,
    1 => :half_width_account_name_kana,
    2 => :account_name,
    3 => :set_default_four,
    4 => :export_staff_gender_id,
    5 => :birthday,
    6 => :hire_date,
    7 => :postal_code,
    8 => :staff_prefecture_name,
    9 => :city,
    10 => :street_name_house_number,
    11 => :building,
    12 => :tel,
    13 => :set_fixed_code,
    14 => :set_default_two,
    15 => :set_default_two,
    16 => :tax_type_code,
    17 => :year_end_ajustment_code,
    18 => :set_default_zero,
    19 => :salary_is_spouse,
    20 => :city_code,
    21 => :set_default_one,
    22 => :set_default_zero,
    23 => :bank_no,
    24 => :branch_no,
    25 => :account_number,
    26 => :account_transfer_name_kana,
    27 => :account_transfer_name,
    28 => :set_default_one,
    29 => :set_default_one,
    30 => :set_default_zero,
    31 => :retirement_date,
    32 => :export_nationality_other_than_japan?,
    33 => :staff_payroll_residence_status_export
  }

  STAFF_PAYROLL_RESIDENCE_STATUS_EXPORT_MAPPING = {
    13 => "010",
    20 => "001",
    24 => "009",
    25 => "002",
    26 => "003",
    27 => "005",
    28 => "007",
    29 => "008",
    30 => "006",
    31 => "004"
  }

  def staff_payroll_row_data
    row_array = Array.new(TOTAL_COLS, nil)
    FIELD_DATA.each do |key, method|
      row_array[key] = send method
    end
    row_array
  end

  class_methods do
    def staff_payroll_label_i18n
      Settings.payroll.export_csv.staff_info.label
    end
  end

  private
  def set_default_zero
    DEFAULT_ZERO
  end

  def set_default_one
    DEFAULT_ONE
  end

  def set_default_two
    DEFAULT_TWO
  end

  def set_default_four
    DEFAULT_FOUR
  end

  def set_fixed_code
    FIXED_CODE
  end

  def tax_type_code
    return self.staff_salary.tax_type_before_type_cast if self.staff_salary&.i_t_target?

    0
  end

  def year_end_ajustment_code
    return 1 if tax_type_code == 1

    0
  end

  def salary_is_spouse
    self.staff_salary&.is_spouse_before_type_cast.to_i
  end

  def get_insurance_subsection_type
    self.staff_salary&.insurance_subsection_type_before_type_cast
  end

  def employment_insurance_type
    self.staff_salary&.employment_insurance_type_before_type_cast
  end

  def bank_no
    self.staff_account_transfer.bank_code
  end

  def branch_no
    self.staff_account_transfer.bank_branch_code
  end

  def account_number
    self.staff_account_transfer.account_number
  end

  def account_transfer_name_kana
    self.staff_account_transfer.half_width_account_name_kana
  end

  def account_transfer_name
    self.staff_account_transfer.account_name
  end

  def export_staff_gender_id
    self.male? ? 0 : 1
  end

  def city_code
    return "" unless local_public_code

    code_int = local_public_code.to_i

    sum = (code_int % 10 * 2) + (code_int / 10 % 10 * 3) + (code_int / 100 % 10 * 4) +
      (code_int / 1000 % 10 * 5) + (code_int / 10_000 % 10 * 6)
    res = 11 - (sum % 11)
    "#{local_public_code.rjust(5, '0')}#{res.to_s.last}"
  end

  def export_nationality_other_than_japan?
    self.nationality.to_i == Type::JAPAN_ID ? 0 : 1
  end

  def half_width_account_name_kana
    Unicode::Japanese.z2h self.account_name_kana
  end

  def staff_payroll_residence_status_export
    STAFF_PAYROLL_RESIDENCE_STATUS_EXPORT_MAPPING[self.residence_status]
  end

  def street_name_house_number
    [self.street_number, self.house_number].join(" ")
  end
end
