module ExportArrangementDecorator
  extend ActiveSupport::Concern

  ATTRS_FROM_TO = %w(working_time work_achievement_rest1_time work_achievement_rest2_time
    work_achievement_rest3_time arrange_billing_rest1_time arrange_billing_rest2_time
    arrange_billing_rest3_time)
  FIELD_DATA = {
    0 => :order_created_date,
    1 => :order_id,
    2 => :order_case_id,
    3 => :current_location_type,
    4 => :order_real_pic_department_name,
    5 => :working_day,
    6 => :working_time_from,
    7 => :working_time_to,
    8 => :break_time,
    9 => :predetermined_except_break_time,
    10 => :corporation_id,
    11 => :location_id,
    12 => :order_location_code,
    13 => :staff_staff_number,
    14 => :staff_department_name_at_working_time,
    15 => :payment_adjusment_type_id,
    16 => :urgent_status,
    17 => :order_case_status_export_i18n,
    18 => :status_staff_evaluation_i18n,
    19 => :staff_account_name_kana,
    20 => :staff_gender_id_export_i18n,
    21 => :staff_total_work_experience,
    22 => :staff_avg_evaluation,
    23 => :order_corporation_full_name,
    24 => :location_name,
    25 => :order_location_stations_1_station_name,
    26 => :location_avg_evaluation,
    27 => :order_business_name,
    28 => :working_prepare_status,
    29 => :working_need_talking,
    30 => :arrived_status,
    31 => :working_has_problem,
    32 => :work_achievement_working_time_status_id_export_i18n,
    33 => :adjusment_type_id_export_i18n,
    34 => :payment_start_time,
    35 => :payment_end_time,
    36 => :work_achievement_rest1_time_from,
    37 => :work_achievement_rest1_time_to,
    38 => :work_achievement_rest2_time_from,
    39 => :work_achievement_rest2_time_to,
    40 => :work_achievement_rest3_time_from,
    41 => :work_achievement_rest3_time_to,
    42 => :payment_actual_working_time_format,
    43 => :payment_basic_time_format,
    44 => :payment_ot1_time_format,
    45 => :payment_ot2_time_format,
    46 => :payment_night_time_format,
    47 => :payment_basic_break_time_format,
    48 => :payment_night_break_time_format,
    49 => :payment_late_time_format,
    50 => :payment_leave_early_time_format,
    51 => :payment_basic_unit_price,
    52 => :payment_ot_unit_price,
    53 => :payment_night_unit_price,
    54 => :payment_unit_price_addition,
    55 => :payment_urgent_unit_price_addition,
    56 => :payment_bonus_unit_price,
    57 => :payment_total_amount,
    58 => :payment_basic_amount,
    59 => :payment_ot_amount,
    60 => :payment_ot2_amount,
    61 => :payment_night_amount,
    62 => :payment_field_1,
    63 => :search_yahoo_url,
    64 => :payment_field_4,
    65 => :payment_field_5,
    66 => :payment_subtraction_amount,
    67 => :arrange_billing_adjusment_type_id_export_i18n,
    68 => :billing_started_at,
    69 => :billing_ended_at,
    70 => :arrange_billing_rest1_time_from,
    71 => :arrange_billing_rest1_time_to,
    72 => :arrange_billing_rest2_time_from,
    73 => :arrange_billing_rest2_time_to,
    74 => :arrange_billing_rest3_time_from,
    75 => :arrange_billing_rest3_time_to,
    76 => :billing_actual_working_time_format,
    77 => :billing_basic_time_format,
    78 => :billing_ot_time_format,
    79 => :billing_night_time_format,
    80 => :billing_basic_break_time_format,
    81 => :billing_night_break_time_format,
    82 => :billing_late_time_format,
    83 => :billing_leave_early_time_format,
    84 => :billing_basic_unit_price,
    85 => :billing_unit_price_addition,
    86 => :billing_ot_unit_price,
    87 => :billing_night_unit_price,
    88 => :billing_total_amount,
    89 => :total_amount_not_taxable,
    90 => :billing_basic_amount,
    91 => :billing_ot_amount,
    92 => :billing_night_amount,
    93 => :billing_note,
    94 => :billing_field_1,
    95 => :billing_field_2,
    96 => :billing_field_3,
    97 => :billing_field_4,
    98 => :billing_field_5,
    99 => :billing_field_6,
    100 => :billing_field_7,
    101 => :billing_tax_exemption,
    102 => :store_note,
    103 => :arrange_comment,
    104 => :order_location_pos_type_id_export_i18n,
    105 => :order_location_is_store_parking_area_usable_export_i18n,
    106 => :staff_home_tel,
    107 => :staff_tel,
    108 => :staff_account_email,
    109 => :staff_age,
    110 => :staff_uniform_size,
    111 => :staff_display_workable_time,
    112 => :staff_is_working_car_export_i18n,
    113 => :staff_level_up_training_export_i18n,
    114 => :staff_new_pos_training_export_i18n,
    115 => :note,
    116 => :order_note,
    117 => :order_case_special_offer_note,
    118 => :payment_field_9,
    119 => :billing_other_addition_fee,
    120 => :is_store_computer_int,
    121 => :order_case_segment_name,
    122 => :is_time_changeable,
    123 => :is_staff_apply_changeable_time,
    124 => :location_evaluation_comment
  }
  ALL_ELEMENTS = 124
  FORMAT_CURRENCY_ROWS = [[51, 62], [64, 66], [84, 92], [94, 101], [118, 119]]
  ATTRS_I18N = {
    order_case_status: "model_status.order_portion.status",
    staff_gender_id: "admin.staff.search_detail",
    work_achievement_working_time_status_id:
      "enum_label.work_achievement.display_working_time_status_ids",
    adjusment_type_id: "enum_label.arrange_payment.adjusment_type_ids",
    arrange_billing_adjusment_type_id: "enum_label.arrange_payment.adjusment_type_ids",
    order_location_pos_type_id: "enum_label.location.pos_type_ids",
    order_location_is_store_parking_area_usable: "corporation.order.store_parking",
    staff_is_working_car: "corporation.order.store_parking",
    staff_level_up_training: "admin.staff.display_level_up_training",
    staff_new_pos_training: "admin.staff.display_level_up_training"
  }
  ARRANGED_BILLING_STATUSES = %w(arranged cancel_after_arrange_has_insurance
    cancel_after_arrange_no_insurance absence)
  STATUS_NOT_SHOW = %w(cancel_after_arrange_no_insurance absence absence_has_alternative)
  BILLING_ROWS = (84..101).to_a - [89, 93] + [119]
  PAYMENT_TIME_ROWS = (34..50)

  def row_data
    row_data = Array.new(ALL_ELEMENTS, nil)
    FIELD_DATA.each do |key, method|
      if key.in? BILLING_ROWS
        value = self.send(method) if is_show_billing_field?
      elsif key.in? PAYMENT_TIME_ROWS
        value = self.send(method) if is_show_working_time?
      elsif key == 0
        value = self.send(:order_created_full_date)
      elsif key == 3
        value = self.send(:current_location_type_text)
      elsif key == 5
        value = self.send(:working_day_full_date)
      elsif key == 19
        value = self.send(:account_name_kana_to_half_size)
      elsif key == 89
        value = self.send(:billing_tax_exemption) if is_show_billing_field?
      else
        value = self.send(method)
      end
      row_data[key] = value
    end
    row_data
  end

  def export_date_format
    I18n.t("admin.location_time_sheets.export_date_format",
      month: ServerTime.now.month, day: ServerTime.now.day)
  end

  def deadline_format
    moment = (working_started_at + 1.day).beginning_of_day + 12.hours
    day_name = I18n.t("date.abbr_day_names")[moment.wday]
    I18n.t("admin.location_time_sheets.deadline_format",
      month: moment.month,
      day: moment.day,
      week_day: day_name,
      moment: moment.strftime(Settings.time.formats))
  end

  def start_time_format
    moment = working_started_at
    day_name = I18n.t("date.abbr_day_names")[moment.wday]
    I18n.t("admin.location_time_sheets.start_time_format",
      month: moment.month,
      day: moment.day,
      week_day: day_name)
  end

  def location_pics_name_format
    pic = LocationPic.by_location_and_type(self.order_location_id,
      LocationPic::PIC_TYPE_IDS[1]).order(created_at: :desc).first
    return unless pic

    "#{pic.name} (#{pic.tel})"
  end

  def is_time_changeable
    self.order_case.is_time_changable ? 1 : 0
  end

  def is_staff_apply_changeable_time
    origin_order_case_id = self.arrange_logs.oc_changed_temp.last&.origin_order_case_id
    return 0 if origin_order_case_id.blank?

    origin_order_case = OrderCase.find_by(id: origin_order_case_id)
    return 0 if origin_order_case.blank?

    origin_order_case.case_started_at != self.working_started_at ||
      origin_order_case.case_ended_at != self.working_ended_at ? 1 : 0
  end

  def total_invoice_amount
    billing_total_amount.to_i + billing_tax_exemption.to_i
  end

  private
  ATTRS_FROM_TO.each do |arr_method|
    %w(from to).each do |time|
      define_method "#{arr_method}_#{time}" do
        arr_val = self.send(arr_method)
        return unless arr_val

        arr_val = arr_val.split("~")
        time == "from" ? arr_val[0] : arr_val[1]
      end
    end
  end

  def urgent_status
    label = I18n.t("admin.arrangements.index.labels")
    urgent_status = ""
    urgent_status = label[:high_priority] if order_case_is_special_offer
    return urgent_status unless order_case_is_urgent

    urgent_status.present? ? [urgent_status, label[:urgent]].join("、") : label[:urgent]
  end

  ATTRS_I18N.each do |arrange_attr, i18n_path|
    define_method "#{arrange_attr}_export_i18n" do
      return if self.send(arrange_attr).nil?

      I18n.t "#{i18n_path}.#{self.send(arrange_attr)}"
    end
  end

  def status_staff_evaluation_i18n
    return unless status_staff_evaluation == :has_staff

    level_name = I18n.t("enum_label.staff_level.levels.#{staff_level}")[0] if staff_level
    rank_name = I18n.t("enum_label.staff_rank.ranks.#{staff_rank}")[0] if staff_rank
    [staff_account_name, "(", level_name, rank_name, ")"].join
  end

  def staff_avg_evaluation
    return I18n.t("admin.arrangements.staff_evaluation.blank") unless portion_arranged

    if staff_evaluation
      staff_evaluation.avg_evaluation
    else
      I18n.t "admin.arrangements.staff_evaluation.not_yet"
    end
  end

  def location_avg_evaluation
    return I18n.t("admin.arrangements.staff_evaluation.blank") unless portion_arranged

    evaluation = location_evaluation_val
    evaluation == 0 ? I18n.t("admin.arrangements.staff_evaluation.not_yet") : evaluation
  end

  def location_evaluation_comment
    return "" unless portion_arranged

    location_evaluation&.evaluation_comment
  end

  def working_prepare_status
    return unless portion_arranged

    status = is_prepared ? :done : :not_yet
    I18n.t "admin.arrangements.working_prepare.#{status}"
  end

  def arrived_status
    return unless portion_arranged

    status = is_arrived ? :done : :not_yet
    I18n.t "admin.arrangements.working_prepare.#{status}"
  end

  def search_yahoo_url
    return unless staff_home_station_station_name

    start_time = order_portion.case_started_at
    minutes = start_time.strftime(Settings.date.minute).chars
    params = {
      from: staff_home_station_station_name,
      to: order_location_stations_1_station_name,
      y: start_time.strftime(Settings.date.year),
      m: start_time.strftime(Settings.date.month),
      d: start_time.strftime(Settings.date.day_date),
      hh: start_time.strftime(Settings.date.hour),
      m2: minutes[1],
      m1: minutes[0],
      type: 1,
      ticket: "normal",
      expkind: 1,
      ws: 3,
      s: 0,
      al: 1,
      shin: 1,
      ex: 1,
      hb: 1,
      lb: 1,
      sr: 1
    }
    params_list = []
    params.each do |key, value|
      params_list.push("#{key}=#{value}")
    end
    "https://transit.yahoo.co.jp/search/result?#{params_list.join('&')}"
  end

  def is_show_billing_field?
    self.display_status_id.in? ARRANGED_BILLING_STATUSES
  end

  def is_show_working_time?
    !self.display_status_id.in? STATUS_NOT_SHOW
  end

  class_methods do
    def label_i18n
      I18n.t("admin.arrangements.index.export.columns").values
    end

    def row_style style
      row_array = Array.new(ALL_ELEMENTS, nil)
      FORMAT_CURRENCY_ROWS.map{|row| row_array[row] = style}
      row_array
    end
  end
end
