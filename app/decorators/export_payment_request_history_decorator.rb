module ExportPaymentRequestHistoryDecorator
  extend ActiveSupport::Concern

  HISTORY_FIELD_DATA = %i(
    created_at_format
    staff_current_department_name
    staff_staff_number
    staff_account_name
    staff_name_kana
    request_amount
    transfer_fee
    usage_fee
    transfered_amount
    transfer_date_format
  )

  def history_row_data
    HISTORY_FIELD_DATA.map{|method| self.send(method)}
  end

  def transfer_date_format
    transfer_date&.strftime Settings.date.formats
  end
end
