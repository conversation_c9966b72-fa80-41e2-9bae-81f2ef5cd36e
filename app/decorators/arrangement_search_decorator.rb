module ArrangementSearchDecorator
  ARRANGE_BILLING_FORMAT_DATE_ATTRS = %w(billing_started_at billing_ended_at)
  TRUE_TO_INT = 1
  FALSE_TO_INT = 0
  REST_FIELDS = %w(started ended)
  MINUTES_TO_TIME_FORMAT = {
    arrange_payment: %w(payment_ot1_time payment_late_time payment_leave_early_time
      payment_basic_time payment_night_time payment_basic_break_time payment_night_break_time
      payment_actual_working_time payment_ot2_time payment_ot_night_time payment_ot_day_time),
    arrange_billing: %w(billing_ot_time billing_late_time billing_leave_early_time
      billing_actual_working_time billing_basic_time billing_night_time billing_basic_break_time
      billing_night_break_time)
  }

  MINUTES_TO_TIME_FORMAT.each do |model, model_attrs|
    model_attrs.each do |attr_name|
      define_method("#{attr_name}_format") do
        minutes_to_times(self.send(model).send(attr_name))
      end
    end
  end

  def working_day
    I18n.l working_started_at, format: Settings.date.month_date
  end

  ARRANGE_BILLING_FORMAT_DATE_ATTRS.each do |billing_attr|
    define_method(billing_attr) do
      arrange_billing.send(billing_attr)&.strftime(Settings.time.formats)
    end
  end

  def location_name
    order_location_name
  end

  def location_code
    order_location_code
  end

  def location_is_export_timesheet
    order_location_is_export_timesheet
  end

  def corporation_full_name
    order_corporation_full_name
  end

  def corporation_id
    order_corporation_id
  end

  def working_need_talking
    self.portion_arranged ? need_talking_count : ""
  end

  def working_has_problem
    self.portion_arranged ? has_problem_count : ""
  end

  def location_evaluation_val
    location_evaluation&.evaluation.to_i
  end

  def payment_start_time
    I18n.l work_achievement.working_started_at, format: Settings.time.formats
  end

  def payment_end_time
    I18n.l work_achievement.working_ended_at, format: Settings.time.formats
  end

  def working_date
    working_started_at.strftime Settings.datetime.formats
  end

  def order_created_date
    self.order.created_at.strftime Settings.date.formats
  end

  def order_created_day
    I18n.l self.order.created_at, format: Settings.date.day_and_date
  end

  def order_created_full_day
    I18n.l self.order.created_at, format: Settings.date.full_date_time
  end

  def display_status_before_cast
    read_attribute_before_type_cast(:display_status_id).to_i
  end

  def order_portion_status_before_cast
    order_portion.read_attribute_before_type_cast(:status_id).to_s
  end

  def working_started_date
    working_started_at.strftime Settings.datetime.formats
  end

  def working_ended_date
    working_ended_at.strftime Settings.datetime.formats
  end

  def payment_started_at
    I18n.l work_achievement.working_started_at, format: Settings.datetime.formats
  end

  WorkAchievement::REST_TIMES.each do |rest_field|
    REST_FIELDS.each do |time|
      define_method "work_achievement_rest#{rest_field}_#{time}_date" do
        self.send("work_achievement_rest#{rest_field}_#{time}_at")
          &.strftime(Settings.datetime.formats) || work_achievement.send("working_date", "")
      end
    end
  end

  def billing_started_date
    arrange_billing.billing_started_at || working_started_at.strftime(Settings.date.formats)
  end

  def chain_id
    order.corporation_group.chain_id
  end

  def department_id
    order.pic_department_id
  end

  def location_id
    order_location_id.to_s
  end

  def oc_segment_id
    order_case.segment_id_before_type_cast.to_s
  end

  def working_time_status_id
    work_achievement&.working_time_status_id_before_type_cast&.to_s
  end

  def payment_adjusment_type_id
    arrange_payment&.adjusment_type_id_before_type_cast&.to_s
  end

  def billing_adjusment_type_id
    arrange_billing&.adjusment_type_id_before_type_cast&.to_s
  end

  def custom_billing_payment
    billing_payment_template_id.present?
  end

  def nested_arrange_logs
    self.arrange_logs.map do |al|
      {
        action_type: al.action_type_before_type_cast,
        arrange_status_id: al.arrange_status_id,
        admin_id: al.admin_id || 0
      }
    end
  end

  def not_prepared
    !is_prepared
  end

  def not_arrived
    !is_arrived
  end

  def special_offer
    order_case_is_special_offer
  end

  def urgent
    order_case_is_urgent
  end

  def invoice_target
    order_case_invoice_target
  end

  def diff_working_time
    return unless work_achievement

    (working_started_at != work_achievement.working_started_at) ||
      (working_ended_at != work_achievement.working_ended_at) ||
      (self.break_time != work_achievement.break_time)
  end

  def estimate_working_time
    {
      working_start_time: order_case.case_started_at.strftime(Settings.datetime.formats),
      working_end_time: order_case.case_ended_at.strftime(Settings.datetime.formats)
    }
  end

  def estimate_working_end_time
    return Settings.time.end_of_day if order_case.is_ended_next_day?

    order_case.case_ended_at.strftime Settings.time.formats
  end

  def working_day_full_date
    I18n.l self.working_started_at, format: Settings.date.day_and_date
  end

  def order_created_full_date
    I18n.l self.order.created_at, format: Settings.date.day_and_date
  end

  def current_location_type_text
    if new_location?
      I18n.t "admin.arrangements.index.labels.is_location_new"
    elsif old_location?
      I18n.t "admin.arrangements.index.labels.is_location_comeback"
    end
  end

  def account_name_kana_to_half_size
    Unicode::Japanese.z2h(staff_account_name_kana)
  end

  def is_regular_order
    self.order.regular_order?
  end

  def is_training_first_round
    self.order.training_first_round?
  end

  def is_training_second_round
    self.order.training_second_round?
  end

  private
  def bool_to_int bool
    bool ? TRUE_TO_INT : FALSE_TO_INT
  end

  def minutes_to_times minutes
    return unless minutes

    [minutes / 60, minutes % 60].map{|t| t.to_s.rjust(2, "0")}.join(":")
  end
end
