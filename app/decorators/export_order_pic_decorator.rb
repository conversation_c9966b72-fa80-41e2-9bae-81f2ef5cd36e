module ExportOrderPicDecorator
  extend ActiveSupport::Concern

  ALL_ELEMENTS = 9
  FIELD_DATA = {
    0 => :order_no,
    1 => :order_type_id,
    2 => :pic_type_id,
    4 => :pic_position,
    5 => :location_pic_name,
    6 => :pic_tel
  }
  ORDER_CREATED_TYPE = {
    admin: 2,
    owner: 1
  }
  PIC_TYPE_IDS = {
    haken_destination: 1,
    mandator: 3,
    claim: 2,
    haken_source: 2,
    claim_process: 4
  }
  FIELD_TYPE = {string: [1, 2]}

  def pic_row_data field, order_by_admin, export_order_id
    row_array = Array.new(ALL_ELEMENTS, nil)
    @order_by_admin = order_by_admin
    @field = field
    @export_order_id = export_order_id
    FIELD_DATA.each do |key, method|
      row_array[key] = self.send(method)
    end
    row_array
  end

  class_methods do
    def order_pic_sheet_name
      I18n.t("admin.order.export_xlsx.order_pic.sheet_name")
    end

    def order_pic_label_i18n
      I18n.t("admin.order.export_xlsx.order_pic.label_name").values
    end

    def order_pic_column_data_types
      type_array = Array.new(ALL_ELEMENTS, nil)
      FIELD_TYPE.each do |type, columns|
        columns.each{|index| type_array[index] = type}
      end
      type_array
    end
  end

  private
  def order_type_id
    @order_by_admin ? ORDER_CREATED_TYPE[:admin] : ORDER_CREATED_TYPE[:owner]
  end

  def pic_type_id
    PIC_TYPE_IDS[@field.to_sym]
  end

  def pic_position
    method_name = is_mandator? ? "#{@field}_position" : "#{@field}_pic_position"
    self.order.send(method_name)
  end

  def location_pic_name
    method_name = is_mandator? ? "#{@field}_name" : "#{@field}_pic_name"
    self.order.send(method_name)
  end

  def pic_tel
    return "" if @order_by_admin

    method_name = is_mandator? ? "#{@field}_tel" : "#{@field}_pic_tel"
    self.order.send(method_name)
  end

  def is_mandator?
    @field == "mandator"
  end
end
