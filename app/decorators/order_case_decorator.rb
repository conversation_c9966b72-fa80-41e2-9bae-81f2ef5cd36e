module OrderCaseDecorator
  REST_TIMES = %w(1 2 3)
  MIDNIGHT = "00:00"

  def site_name
    self.order.location_name
  end

  def remain_day_note
    remain_time = self.case_started_at - ServerTime.now
    return I18n.t("corporation.order_case.order_case_list_page.note_less_0_day") if remain_time <= 0

    mm, ss = remain_time.divmod(60)
    hh, mm = mm.divmod(60)
    dd, hh = hh.divmod(24)
    return if (dd > 7) || (dd == 7 && (hh > 0 || mm > 0 || ss > 0))

    I18n.t("corporation.order_case.order_case_list_page.note_less_7_day",
      day: dd, hour: hh, minute: mm, second: ss.to_i)
  end

  def placeholder_transportation_fee
    key = self.is_urgent ? "urgent_transportation_fee" : "input_transportation_fee"
    I18n.t("staff.order_cases.#{key}")
  end

  def is_midnight
    self.case_started_at.strftime(Settings.time.formats) == MIDNIGHT
  end

  def order_portion_arranged
    approved_statuses = self.order_portions.pluck(:status_id)
    total_approved = approved_statuses.select do |status|
      status == OrderPortion::STATUSES[:arranged]
    end.size
    [total_approved, self.total_portion].join("/")
  end

  def break_time
    self.order_branch.break_time.to_i
  end

  def order_date
    I18n.l self.order.created_at, format: Settings.date.day_and_date
  end

  def started_at_format_month_day
    I18n.l self.case_started_at, format: Settings.date.month_day_and_date
  end

  def started_at
    I18n.l self.case_started_at, format: Settings.date.day_and_date
  end

  def started_at_format_month_date
    I18n.l self.case_started_at, format: Settings.date.month_date
  end

  def started_at_format_year_month_and_date
    I18n.l self.case_started_at, format: Settings.date.year_month_and_date
  end

  def started_date
    I18n.l self.case_started_at, format: Settings.date.formats
  end

  def working_date
    self.case_started_at.to_date
  end

  def required_time
    return unless is_time_changable && required_start_time && required_end_time

    next_day = I18n.t("staff.order_cases.next_day") if required_start_time > required_end_time
    "#{required_start_time.strftime(Settings.time.formats)}~#{next_day}#{required_end_time.strftime(Settings.time.formats)}"
  end

  def working_time
    next_day = I18n.t("staff.order_cases.next_day") if is_ended_next_day?
    "#{self.case_started_at.strftime(Settings.time.formats)}~#{next_day}#{self.case_ended_at.strftime(Settings.time.formats)}"
  end

  def applied_required_time
    return unless self.is_time_changable && self.required_start_time && self.required_end_time

    [self.required_start_time.strftime(Settings.time.formats),
      self.required_end_time.strftime(Settings.time.formats)].join("～")
  end

  def working_time_format
    rest_time_fortmat = REST_TIMES.map do |rest_time|
      rest_start = order_branch.send("rest#{rest_time}_started_at")
      rest_end = order_branch.send("rest#{rest_time}_ended_at")
      next unless rest_start

      start_at = rest_start.strftime(Settings.time.formats)
      end_at = rest_end.strftime(Settings.time.formats)
      "#{start_at}~#{end_at}"
    end.compact.join(", ")
    return working_time if rest_time_fortmat.blank?

    "#{working_time} (#{rest_time_fortmat})"
  end

  def working_date_time
    [self.case_started_at.strftime(Settings.date.formats), working_time].join(" ")
  end

  def working_date_and_time
    start_time = I18n.l(self.case_started_at, format: Settings.date.month_day_and_date)
    i18n_changable = I18n.t("staff.order_cases.detail.change_able")
    changable = self.order_branch.is_time_changable ? i18n_changable : ""
    [[start_time, working_time].join(" "), changable].join
  end

  def status_name
    self.case_started_at >= ServerTime.now ? self.status_id : self.working_status_id
  end

  def status_name_i18n
    I18n.t("corporation.order_case.statuses.#{status_name}")
  end

  def status_class_name
    OrderCase::FINISHED_STATUS.include?(status_name) ? "waiting" : "finished"
  end

  def working_status_i18n
    I18n.t("corporation.order_case.order_case_list_page.arragement_status.#{working_status_id}")
  end

  def status_id_i18n
    I18n.t("corporation.order_case.order_case_list_page.status_by_portion.#{status_id}")
  end

  def status
    I18n.t("corporation.order_case.order_case_list_page.status_ids.#{self.status_id}")
  end

  def arrangements_status_i18n
    FormatDisplay::OwnerHomepage.order_case_arrangements_status_i18n(self)
  end

  def status_by_portion_int
    OrderCase.status_ids[status_by_portion]
  end

  def job_time_info
    [month_day_week_with_text, working_time].join
  end

  def month_day_week_with_text
    I18n.l case_started_at, format: Settings.date.month_day_and_date
  end
end
