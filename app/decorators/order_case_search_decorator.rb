module OrderCaseSearchDecorator
  DEADLINE_APPLY = 2.hours
  def geo_location
    return if location_latitude.nil? || location_longitude.nil?

    [location_latitude, location_longitude].join(",")
  end

  def has_additional_fee
    self.is_special_offer || self.is_urgent || self.peak_period_order_rate.to_i > 0
  end

  private
  def start_date
    case_started_at.strftime Settings.date.formats
  end

  def end_date
    case_ended_at.strftime Settings.date.formats
  end

  def start_time
    case_started_at.strftime Settings.time.formats
  end

  def end_time
    case_ended_at.strftime Settings.time.formats
  end

  def violation_date
    order&.violation_day&.strftime Settings.date.formats
  end

  def order_day
    order&.created_at&.strftime Settings.date.formats
  end

  def case_start
    case_started_at.strftime Settings.datetime.formats
  end

  def deadline_apply_date
    base_time = self.is_urgent ? self.case_ended_at : self.case_started_at
    (base_time - DEADLINE_APPLY).strftime Settings.datetime.formats
  end

  def created_datetime
    created_at.strftime Settings.datetime.formats
  end

  def station_ids
    [location_station_1, location_station_2, location_station_3, location_station_4,
     location_station_5].compact.uniq.join(" ")
  end

  def status_id_int
    OrderCase.status_ids[status_id]
  end

  def confirming_actual_working
    arrangements.any?{|a| a&.work_achievement&.owner_confirming?}
  end

  def status_before_cast
    read_attribute_before_type_cast(:status_id).to_s
  end

  def working_status_before_cast
    read_attribute_before_type_cast(:working_status_id).to_s
  end

  def full_arranged
    full_arranged?
  end

  def public_type_id_before_cast
    read_attribute_before_type_cast(:public_type_id).to_s
  end

  def oc_public_staff
    order_case_public_staffs.map do |oc_public_staff|
      {
        staff_id: oc_public_staff.staff_id,
        is_public: oc_public_staff.is_public
      }
    end
  end

  def segment_id_before_cast
    read_attribute_before_type_cast(:segment_id).to_s
  end

  def offer_mail
    urgent_offer_mail_staffs.map do |offer_mail|
      {
        staff_id: offer_mail.staff_id,
        created_at: offer_mail.created_at.strftime(Settings.date.formats)
      }
    end
  end

  def keeping_job
    staff_keep_order_cases.map do |keep_order_case|
      {
        staff_id: keep_order_case.staff_id,
        created_at: keep_order_case.created_at.strftime(Settings.date.formats)
      }
    end
  end

  # (Deprecated by ESR)
  def is_from_lawson
    order&.is_from_lawson?
  end

  # (Deprecated by ESR)
  def is_haken_type_labor
    order&.is_haken_type_labor?
  end

  def is_auto_matching
    order.location.is_auto_matching?
  end
end
