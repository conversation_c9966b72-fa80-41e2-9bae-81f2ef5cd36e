module TrainingScheduleApplicantDecorator
  def staff_number_and_name
    "#{staff.staff_number} #{staff.name}"
  end

  def schedule_status_text
    I18n.t("admin.training_schedule.schedule_status_codes.#{schedule_status_code}")
  end

  def created_full_date
    I18n.l self.created_at, format: Settings.date.day_and_date
  end

  # Overrides the default as_json method to allow renaming of keys in the resulting JSON object.
  def as_json options = {}
    json = super(options)
    if options.key?(:rename)
      options[:rename].each do |old_key, new_key|
        json[new_key.to_s] = json.delete(old_key.to_s)
      end
    end
    json
  end

  # Date/time format
  def deleted_at_formatted
    return "" if deleted_at.blank?

    I18n.l deleted_at, format: Settings.date.formats
  end

  def detail_single_session_info
    {
      training_center_id: location&.id,
      prefecture_name: location&.prefecture_name,
      location_name: location&.name,
      location_full_address: location&.formatted_full_address,
      single_session: self.detail_applicant_info,
      absent_deadline_note: I18n.t("staff.training_schedule.details.schedule_absent_deadline_single_session",
        absent_deadline: formatted_absent_deadline)
    }
  end

  def detail_applicant_info
    {
      id: id,
      training_schedule_id: training_schedule_id,
      schedule_status_code: schedule_status_code,
      datetime: formatted_datetime
    }
  end

  def staff_histories_info
    {
      id: id,
      schedule_id: training_schedule_id,
      session: training_session_text(TrainingSchedule.training_session_codes.key(session)),
      schedule_start_time: schedule_start_time(ServerTime.parse(training_start_time.to_s)),
      status: schedule_status_text,
      status_code: schedule_status_code,
      is_deleted: self.deleted_at?
    }
  end

  private

  def training_session_text session
    I18n.t("admin.training_schedule.training_session_codes.#{session}")
  end

  def schedule_start_time time, format = Settings.date.day_and_date
    return "" unless time

    I18n.l time, format: format
  end

  class << self
    def applicant_details applicants
      return applicants.detail_single_session_info if applicants.is_a?(TrainingScheduleApplicant)

      first_session = applicants.first.training_schedule
      second_session = applicants.last.training_schedule
      location = first_session&.location || second_session&.location

      {
        training_center_id: location&.id,
        prefecture_name: location&.prefecture_name,
        location_name: location&.name,
        location_full_address: location&.formatted_full_address,
        absent_deadline_note: I18n.t("staff.training_schedule.details.schedule_absent_deadline",
          first_session_absent_deadline: first_session.formatted_absent_deadline,
          second_session_absent_deadline: second_session.formatted_absent_deadline),
        first_session: applicants.first.detail_applicant_info,
        second_session: applicants.last.detail_applicant_info
      }
    end

    def get_current_session applicants
      return unless applicants
      return applicants if applicants.is_a?(TrainingScheduleApplicant)

      first_session = applicants.first
      second_session = applicants.last

      return second_session if !second_session.is_unavailable? &&
        second_session.training_schedule.start_time > first_session.training_schedule.start_time

      first_session
    end

    def get_reschedule_data applicants
      data = {}
      return data unless applicants

      if applicants.is_a?(TrainingScheduleApplicant)
        data[:single_session] = applicants.detail_applicant_info
      else
        data[:first_session] = applicants.first.detail_applicant_info
        data[:second_session] = applicants.second.detail_applicant_info
      end

      data[:current_applied_datetime] = current_applied_datetime(applicants)
      data
    end

    def current_applied_datetime applicants
      if applicants.is_a?(TrainingScheduleApplicant)
        return I18n.t("staff.training_schedule.current_applied.unavailable") if applicants.is_unavailable?

        I18n.t("staff.training_schedule.current_applied.datetime", datetime: applicants.formatted_datetime)
      else
        return I18n.t("staff.training_schedule.current_applied.unavailable") if
          applicants.all?(&:is_unavailable?)

        I18n.t("staff.training_schedule.current_applied.datetime_two_rounds",
          first_session_datetime: applicants.first.formatted_datetime,
          second_session_datetime: applicants.last.formatted_datetime)
      end
    end
  end
end
