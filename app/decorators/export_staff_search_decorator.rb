module ExportStaffSearchDecorator
  extend ActiveSupport::Concern
  NON_EMPLOYMENT_TYPE = DEFAULT_ZERO = 0
  RETIRED_PEOPLE_OVER60 = HAKEN_EMPLOYMENT_TYPE = DEFAULT_ONE = DAY_STUDENT_ID = 1
  NOT_RETIRED_PEOPLE_OVER60 = INTRODUCTION_EMPLOYMENT_TYPE = 2
  BOTH_HAKEN_INTRODUCTION_TYPE = DEFAULT_EMPLOYMENT_INSURANCE_CLASSIFICATION = 3
  UNSPECIFIED = -1
  MAX_LENGTH_BANK_CODE = DEFAULT_FOUR = 4
  MAX_LENGTH_BANK_BRANCH_CODE = ANNUAL_INCOME_5_MILLION_YEN = 3
  MAX_LENGTH_ACCOUNT_NUMBER = 7
  MAX_LENGTH_STAFF_ID = 6
  TRUE_TO_INT = 3
  FALSE_TO_INT = 4
  ALL_ELEMENTS = 330
  TYPE_NO_OF_JAPAN = 99
  OVER_AGE_INSURANCE = 65
  SPECIAL_PERMANENT_RESIDENT_ID = 25
  JAPANESE_OR_SPOUSE_ID = 26
  EXPORT_I18N = I18n.t("admin.staff.export")
  # FIELD_DATA = {
  #   0 => :half_width_name_kana,
  #   4 => :current_department_id,
  #   5 => :registration_history_type_no,
  #   6 => :uniform_size_type_no,
  #   7 => :workable_time,
  #   8 => :payment_request_int,
  #   11 => :gender_id_int,
  #   15 => :birthday_format,
  #   16 => :created_date,
  #   41 => :set_default_zero,
  #   42 => :set_default_zero,
  #   43 => :set_default_zero,
  #   44 => :set_default_zero,
  #   45 => :set_default_zero,
  #   46 => :set_default_zero,
  #   47 => :set_default_zero,
  #   48 => :set_default_zero,
  #   49 => :set_default_zero,
  #   50 => :set_default_zero,
  #   51 => :set_default_zero,
  #   52 => :set_default_zero,
  #   53 => :set_default_zero,
  #   54 => :set_default_zero,
  #   55 => :set_default_zero,
  #   56 => :set_default_zero,
  #   57 => :set_default_zero,
  #   58 => :set_default_zero,
  #   59 => :set_default_zero,
  #   60 => :set_default_zero,
  #   61 => :set_default_zero,
  #   62 => :set_default_zero,
  #   63 => :set_default_zero,
  #   64 => :set_default_zero,
  #   65 => :set_default_zero,
  #   66 => :set_default_zero,
  #   67 => :set_default_zero,
  #   68 => :set_default_zero,
  #   69 => :set_default_zero,
  #   70 => :set_default_zero,
  #   71 => :postal_code,
  #   72 => :address1,
  #   73 => :building,
  #   76 => :tel,
  #   77 => :home_tel,
  #   81 => :email,
  #   91 => :home_station_id_name,
  #   92 => :nearest_station_transport,
  #   94 => :emergency_postal_code,
  #   95 => :emergency_address,
  #   96 => :emergency_building,
  #   98 => :emergency_tel,
  #   100 => :staff_emergency_name,
  #   101 => :nationality_type_no,
  #   104 => :residence_status,
  #   106 => :residence_validity_format,
  #   107 => :residence_expiration_date_format,
  #   108 => :residence_permission_int,
  #   109 => :residence_permission_validity_format,
  #   125 => :restriction_id_int,
  #   126 => :exception_check_int,
  #   127 => :retired_people_over60,
  #   128 => :created_date,
  #   150 => :set_default_uncpecified,
  #   151 => :set_default_uncpecified,
  #   182 => :set_default_zero,
  #   207 => :set_default_one,
  #   208 => :set_default_one,
  #   210 => :set_default_zero,
  #   211 => :set_default_zero,
  #   212 => :set_default_zero,
  #   213 => :set_default_zero,
  #   215 => :wage_bank_no,
  #   216 => :wage_branch_no,
  #   217 => :wage_account_classification,
  #   218 => :wage_account_number,
  #   219 => :wage_person_name,
  #   220 => :wage_person_name_kana,
  #   256 => :set_default_four,
  #   271 => :not_insurance_reason,
  #   278 => :not_insurance_reason,
  #   279 => :employment_insurance_classification,
  #   283 => :employment_insurance_reason,
  #   295 => :created_date,
  #   297 => :set_default_zero,
  #   298 => :set_default_zero,
  #   299 => :set_default_zero,
  #   303 => :set_default_one,
  #   304 => :expectations_employment_type_introduction_schedule_dispatch,
  #   327 => :staff_master_number,
  #   328 => :name,
  #   329 => :set_default_one
  # }

  FIELD_DATA = {
    0 => :staff_master_number,
    1 => :change_space_byte_in_name,
    2 => :half_width_name_kana,
    6 => :current_department_id,
    7 => :registration_history_type_no,
    8 => :uniform_size_type_no,
    9 => :workable_time,
    10 => :payment_request_int,
    13 => :gender_id_int,
    17 => :birthday_format,
    18 => :created_date,
    43 => :set_default_zero,
    44 => :set_default_zero,
    45 => :set_default_zero,
    46 => :set_default_zero,
    47 => :set_default_zero,
    48 => :set_default_zero,
    49 => :set_default_zero,
    50 => :set_default_zero,
    51 => :set_default_zero,
    52 => :set_default_zero,
    53 => :set_default_zero,
    54 => :set_default_zero,
    55 => :set_default_zero,
    56 => :set_default_zero,
    57 => :set_default_zero,
    58 => :set_default_zero,
    59 => :set_default_zero,
    60 => :set_default_zero,
    61 => :set_default_zero,
    62 => :set_default_zero,
    63 => :set_default_zero,
    64 => :set_default_zero,
    65 => :set_default_zero,
    66 => :set_default_zero,
    67 => :set_default_zero,
    68 => :set_default_zero,
    69 => :set_default_zero,
    70 => :set_default_zero,
    71 => :set_default_zero,
    72 => :set_default_zero,
    73 => :postal_code,
    74 => :address1,
    75 => :building,
    78 => :tel,
    79 => :home_tel,
    83 => :email,
    93 => :home_station_id_name,
    94 => :nearest_station_transport,
    96 => :emergency_postal_code,
    97 => :emergency_address,
    98 => :emergency_building,
    100 => :emergency_tel,
    102 => :staff_emergency_name,
    103 => :nationality_type_no,
    106 => :residence_status,
    108 => :residence_validity_format,
    109 => :residence_expiration_date_format,
    110 => :residence_permission_int,
    111 => :residence_permission_validity_format,
    127 => :created_date,
    145 => :set_default_uncpecified,
    146 => :set_default_uncpecified,
    177 => :set_default_zero,
    202 => :set_default_one,
    203 => :set_default_one,
    205 => :set_default_zero,
    206 => :set_default_one,
    207 => :set_default_zero,
    208 => :set_default_zero,
    209 => :set_default_zero,
    211 => :wage_bank_no,
    212 => :wage_branch_no,
    213 => :wage_account_classification,
    214 => :wage_account_number,
    215 => :wage_person_name,
    216 => :wage_person_name_kana,
    252 => :set_default_four,
    267 => :not_insurance_reason,
    274 => :not_insurance_reason,
    275 => :employment_insurance_classification,
    279 => :employment_insurance_reason,
    291 => :created_date,
    293 => :set_default_zero,
    294 => :set_default_zero,
    295 => :set_default_zero,
    299 => :set_default_one,
    300 => :expectations_employment_type_introduction_schedule_dispatch
  }

  FIELD_TYPE = {
    string: [0, 71, 76, 77, 94, 212, 213, 214, 215]
  }
  MAPPING_NATIONALITIES = {
    1 => 2,
    2 => 3,
    4 => 9,
    5 => 4,
    6 => 1,
    7 => 19,
    9 => 6,
    12 => 25,
    14 => 7,
    15 => 11,
    17 => 15,
    18 => 8,
    20 => 20,
    26 => 14,
    27 => 16,
    30 => 24,
    34 => 10,
    35 => 12,
    36 => 17,
    37 => 22,
    39 => 18,
    55 => 27,
    57 => 99
  }
  DEFAULT_NATIONALITY_TYPE_NO = 999

  def row_data
    row_array = Array.new(ALL_ELEMENTS, nil)
    FIELD_DATA.each do |key, method|
      row_array[key] = send method
    end
    row_array
  end

  class_methods do
    def label_i18n
      I18n.t("admin.staff.export_xlsx.label_name").values
    end

    def sheet_name
      I18n.t("admin.staff.export_xlsx.sheet_name")
    end

    def column_data_types
      type_array = Array.new(ALL_ELEMENTS, nil)
      FIELD_TYPE.each do |type, columns|
        columns.each{|index| type_array[index] = type}
      end
      type_array
    end
  end

  private
  def change_space_byte_in_name
    name.gsub(/\s/, "　")
  end

  def workable_time
    return bool_to_int(staff_expectation&.is_working_car) unless staff_check_item&.workable_time

    staff_check_item&.workable_time_type&.type_no
  end

  def birthday_format
    birthday&.strftime Settings.date.formats
  end

  def created_date
    created_at&.strftime Settings.date.formats
  end

  def address1
    [staff_prefecture&.name, city, street_number, house_number].compact.join
  end

  def emergency_address
    [staff_emergency_prefecture&.name, emergency_city, emergency_street_number, emergency_house_number].compact.join
  end

  def staff_emergency_name
    "#{emergency_name}(#{emergency_relationship_type&.name_1})"
  end

  def expectations_employment_type_introduction_schedule_dispatch
    return HAKEN_EMPLOYMENT_TYPE if staff_expectation.expectation_employment_type.in?(
      [INTRODUCTION_EMPLOYMENT_TYPE, BOTH_HAKEN_INTRODUCTION_TYPE]
    )

    NON_EMPLOYMENT_TYPE
  end

  def nearest_station_transport
    return UNSPECIFIED unless staff_expectation&.nearest_station_transportation

    staff_expectation&.transportation_type&.type_no
  end

  def registration_history_type_no
    registration_history_type&.type_no
  end

  def uniform_size_type_no
    staff_check_item&.uniform_size_type&.type_no
  end

  def payment_request_int
    Staff.payment_requests[payment_request]
  end

  def gender_id_int
    Staff.gender_ids[gender_id]
  end

  def nationality_type_no
    MAPPING_NATIONALITIES[nationality_type&.type_no] || DEFAULT_NATIONALITY_TYPE_NO
  end

  def residence_permission_int
    return UNSPECIFIED if nationality_type_no == TYPE_NO_OF_JAPAN || residence_permission.blank?

    Staff.residence_permissions[residence_permission]
  end

  def wage_bank_no
    leading_zero_for_enough_length staff_account_transfer.bank&.code, MAX_LENGTH_BANK_CODE
  end

  def wage_branch_no
    leading_zero_for_enough_length staff_account_transfer.bank_branch&.code,
      MAX_LENGTH_BANK_BRANCH_CODE
  end

  def wage_account_classification
    StaffAccountTransfer.account_types[staff_account_transfer&.account_type]
  end

  def wage_account_number
    leading_zero_for_enough_length staff_account_transfer&.account_number, MAX_LENGTH_ACCOUNT_NUMBER
  end

  def wage_person_name
    staff_account_transfer&.account_name
  end

  def wage_person_name_kana
    staff_account_transfer&.half_width_account_name_kana
  end

  def home_station_id_name
    home_station&.name
  end

  def residence_validity_format
    residence_validity&.strftime Settings.date.formats
  end

  def residence_expiration_date_format
    residence_expiration_date&.strftime Settings.date.formats
  end

  def residence_permission_validity_format
    residence_permission_validity&.strftime Settings.date.formats
  end

  def set_default_zero
    DEFAULT_ZERO
  end

  def set_default_uncpecified
    UNSPECIFIED
  end

  def set_default_one
    DEFAULT_ONE
  end

  def set_default_four
    DEFAULT_FOUR
  end

  def not_insurance_reason
    I18n.t("admin.staff.export.reason_have_not_insurance")
  end

  def employment_insurance_classification
    DEFAULT_EMPLOYMENT_INSURANCE_CLASSIFICATION
  end

  def employment_insurance_reason
    return EXPORT_I18N[:more_65_years_old_reason] if age > OVER_AGE_INSURANCE
    return EXPORT_I18N[:residence_status_is_specific_activity] if residence_status
      .in? [SPECIAL_PERMANENT_RESIDENT_ID, JAPANESE_OR_SPOUSE_ID]
    return EXPORT_I18N[:day_student] if Staff.social_attributes[social_attribute] == DAY_STUDENT_ID

    EXPORT_I18N[:less_than_20_hours_per_week]
  end

  def staff_id
    leading_zero_for_enough_length id.to_s, MAX_LENGTH_STAFF_ID
  end

  def leading_zero_for_enough_length str, max_length
    str.rjust(max_length, "0")
  end

  def bool_to_int bool
    bool ? TRUE_TO_INT : FALSE_TO_INT
  end

  def staff_master_number
    self.master_staff&.staff_number
  end
end
