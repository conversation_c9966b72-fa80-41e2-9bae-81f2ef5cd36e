module ExportStaffMasterDecorator
  extend ActiveSupport::Concern
  IS_CHECK = "◯"
  ALL_ELEMENTS = 135
  FIELD_DATA = {
    0 => :staff_number,
    1 => :account_email,
    2 => :created_at_format,
    3 => :account_name,
    4 => :account_name_kana,
    5 => :gender,
    6 => :birthday_format,
    7 => :staff_number,
    8 => :role,
    9 => :current_department_name,
    10 => :evaluation,
    11 => :rank,
    12 => :level,
    13 => :level_up_training_label,
    14 => :new_pos_training_label,
    15 => :occupation_label,
    16 => :position_label,
    17 => :staff_status_label,
    18 => :exp_is_working_car,
    19 => :registration_history_type_name,
    20 => :uniform_size,
    21 => :nationality_name,
    22 => :birthplace_name,
    23 => :social_attribute_name,
    24 => :sci_japanese_level,
    25 => :postal_code,
    26 => :staff_prefecture_name,
    27 => :city,
    28 => :street_number,
    29 => :house_number,
    30 => :building,
    31 => :address_kana,
    32 => :tel,
    33 => :home_tel,
    34 => :contact_memo,
    35 => :home_station_name,
    36 => :station1_use_name,
    37 => :station2_use_name,
    38 => :station3_use_name,
    39 => :hire_date_format,
    40 => :expired_start_date,
    41 => :expired_end_date,
    42 => :expired_type_label,
    43 => :absence_start_date_format,
    44 => :absence_end_date_format,
    45 => :absence_type_label,
    46 => :last_contract_indefinite_employment_flag_label,
    47 => :last_contract_start_date_format,
    48 => :last_contract_end_date_format,
    49 => :last_contract_type_label,
    50 => :last_contract_note,
    51 => :contract_status_label,
    52 => :working_start_date_format,
    53 => :working_end_date_format,
    54 => :retirement_date_format,
    55 => :retirement_reason_type_name,
    56 => :retirement_reason,
    57 => :maiden_name,
    58 => :maiden_name_kana,
    59 => :job_entry_date_format,
    60 => :debut_date_format,
    61 => :rank_up_training_date_format,
    62 => :paid_occupation_establishment_date_format,
    63 => :arrangement_type_lack_of_submission,
    64 => :arrangement_type_attendance_ng,
    65 => :arrangement_type_grooming_ng,
    66 => :arrangement_type_arrangement_stop,
    67 => :arrangement_type_automatic_matching_ng,
    68 => :emergency_postal_code,
    69 => :staff_emergency_prefecture_name,
    70 => :emergency_city,
    71 => :emergency_street_number,
    72 => :emergency_house_number,
    73 => :emergency_building,
    74 => :emergency_name_kana,
    75 => :emergency_tel,
    76 => :emergency_home_tel,
    77 => :emergency_name,
    78 => :emergency_relationship_type_name,
    79 => :residence_name,
    80 => :residence_card_name,
    81 => :residence_number,
    82 => :residence_validity_format,
    83 => :residence_expiration_date_format,
    84 => :residence_permission_label,
    85 => :residence_permission_validity,
    86 => :long_vacation_1_start_at,
    87 => :long_vacation_1_end_at,
    88 => :long_vacation_2_start_at,
    89 => :long_vacation_2_end_at,
    90 => :long_vacation_3_start_at,
    91 => :long_vacation_3_end_at,
    92 => :foreigner_note1,
    93 => :foreigner_note2,
    94 => :sci_list,
    95 => :sci_health_uninsured_reason,
    96 => :sci_welfare_uninsured_reason,
    97 => :sci_employment_uninsured_reason,
    98 => :sci_my_number_receipt,
    99 => :sci_my_number_receipt_date,
    100 => :sci_my_number_dunning_date,
    101 => :sci_labor_contract_sent_date,
    102 => :sci_labor_contract_dunning_date,
    103 => :sci_dunning_type,
    104 => :sat_bank,
    105 => :sat_bank_branch,
    106 => :sat_account_type,
    107 => :sat_account_number,
    108 => :sat_account_name,
    109 => :sat_account_name_kana,
    110 => :optional_item1,
    111 => :optional_item2,
    112 => :optional_item3,
    113 => :optional_item4,
    114 => :optional_item5,
    115 => :optional_item6,
    116 => :optional_item7,
    117 => :optional_item8,
    118 => :ssa_income_tax_type,
    119 => :ssa_tax_type,
    120 => :ssa_is_spouse,
    121 => :ssa_insurance_subsection_type,
    122 => :ssa_insurance_number,
    123 => :ssa_basic_pension_number,
    124 => :ssa_insurance_certificate_acquisition_date,
    125 => :ssa_insurance_lost_qualification_date,
    126 => :ssa_insurance_subsection_deadline,
    127 => :ssa_employment_insurance_type,
    128 => :ssa_employment_insurance_number,
    129 => :ssa_employment_certificate_acquisition_date,
    130 => :ssa_employment_lost_qualification_date,
    131 => :ssa_employment_insurance_deadline,
    132 => :haken_type_label,
    133 => :registration_type_label,
    134 => :registration_answer_label
  }

  FIELD_TYPE = {
    string: [0, 7, 106]
  }

  DATE_FORMAT = [:created_at, :birthday, :hire_date, :expired_start_date, :expired_end_date,
    :absence_start_date, :absence_end_date, :last_contract_start_date, :last_contract_end_date,
    :working_start_date, :working_end_date, :retirement_date, :job_entry_date, :debut_date,
    :rank_up_training_date, :paid_occupation_establishment_date, :residence_expiration_date,
    :residence_validity]

  def master_row_data retirement_reasons = {}, evaluation_staffs = {}, department_staffs = {}
    row_array = Array.new(ALL_ELEMENTS, nil)
    FIELD_DATA.each do |key, method|
      if department_staffs.present? && method.to_sym == :current_department_name
        row_array[key] = department_staffs[self.id]
        next
      end
      if evaluation_staffs.present? && method.to_sym == :evaluation
        row_array[key] = evaluation_staffs[self.id]
        next
      end
      if retirement_reasons.present? && method.to_sym == :retirement_reason_type_name
        row_array[key] = retirement_reasons[self.retirement_reason_type]
        next
      end
      row_array[key] = send method
    end
    row_array
  end

  class_methods do
    def master_label_i18n
      I18n.t("admin.master_staff.export_xlsx.label_name").values
    end

    def master_sheet_name
      I18n.t("admin.staff.export_xlsx.sheet_name")
    end

    def master_column_data_types
      type_array = Array.new(ALL_ELEMENTS, nil)
      FIELD_TYPE.each do |type, columns|
        columns.each{|index| type_array[index] = type}
      end
      type_array
    end
  end

  private
  DATE_FORMAT.each do |field|
    define_method("#{field}_format") do
      return if send(field.to_s).blank?

      I18n.l(send(field.to_s), format: Settings.date.formats)
    end
  end

  def last_contract_type_label
    return if last_contract_type.blank?

    I18n.t("enum_label.staff.contract_types.#{last_contract_type}")
  end

  def haken_type_label
    return if haken_type.blank?

    I18n.t("enum_label.staff.haken_types.#{haken_type}")
  end

  def registration_type_label
    return if registration_type.blank? || matchbox?

    I18n.t("enum_label.staff.registration_types.#{registration_type}")
  end

  def registration_answer_label
    return unless single_haken? && registration_answer.present?

    I18n.t("staff.registration_answer.answer_2.option_#{registration_answer.answer_2}")
  end

  def contract_status_label
    return if contract_status.blank?

    I18n.t("enum_label.staff.contract_statuses.#{contract_status}")
  end

  def expired_type_label
    return unless expired_type

    I18n.t("enum_label.staff.expired_types.#{expired_type}")
  end

  def absence_type_label
    return unless absence_type

    I18n.t("enum_label.staff.absence_types.#{absence_type}")
  end

  def last_contract_indefinite_employment_flag_label
    return unless last_contract_indefinite_employment_flag

    I18n.t("enum_label.staff.indefinite_employment_flags" \
      ".#{last_contract_indefinite_employment_flag}")
  end

  def residence_permission_label
    return if residence_permission.blank?

    I18n.t("staff.entry.step_1.residence_permissions.#{residence_permission}")
  end

  def level_up_training_label
    I18n.t("enum_label.staff.level_up_training.#{level_up_training}")
  end

  def new_pos_training_label
    I18n.t("enum_label.staff.new_pos_training.#{new_pos_training}")
  end

  def occupation_label
    return if occupation.blank?

    I18n.t("enum_label.staff.occupations.#{occupation}")
  end

  def position_label
    return if position.blank?

    I18n.t("enum_label.staff.positions.#{position}")
  end

  def staff_status_label
    return if staff_status.blank?

    I18n.t("enum_label.staff.staff_statuses.#{staff_status}")
  end

  def gender
    I18n.t("print.staff_info.genders.#{gender_id}")
  end

  def last_contract_note
    last_contract&.note
  end

  def arrangement_type_lack_of_submission
    IS_CHECK if arrangement_type&.include?(Staff::ARRANGEMENT_TYPES[:lack_of_submission])
  end

  def arrangement_type_attendance_ng
    IS_CHECK if arrangement_type&.include?(Staff::ARRANGEMENT_TYPES[:attendance_ng])
  end

  def arrangement_type_grooming_ng
    IS_CHECK if arrangement_type&.include?(Staff::ARRANGEMENT_TYPES[:grooming_ng])
  end

  def arrangement_type_arrangement_stop
    IS_CHECK if arrangement_type&.include?(Staff::ARRANGEMENT_TYPES[:arrangement_stop])
  end

  def arrangement_type_automatic_matching_ng
    IS_CHECK if arrangement_type&.include?(Staff::ARRANGEMENT_TYPES[:automatic_matching_ng])
  end

  def rank
    return unless current_rank&.rank

    I18n.t("enum_label.staff_rank.ranks.#{current_rank.rank}")
  end

  def level
    return unless current_level&.level

    I18n.t("enum_label.staff_level.levels.#{current_level.level}")
  end

  def exp_is_working_car
    return unless staff_expectation

    I18n.t("enum_label.staff.is_working_car.#{staff_expectation.is_working_car}")
  end

  def sci_japanese_level
    return unless staff_check_item&.japanese_level

    I18n.t("admin.staff_check_item.japanese_level.#{staff_check_item.japanese_level}")
  end

  def sci_list
    str = []
    [:is_residence_card, :is_mynumber_card, :is_health_insurance, :is_studen_card,
      :is_residence_status, :is_residence_period, :is_permission,
      :is_check_address, :is_request_seal].each do |item|
        str.push(I18n.t("activerecord.attributes.staff_check_item")[item]) if staff_check_item.try(item)
      end
    str.push("#{I18n.t('activerecord.attributes.staff_check_item.license')}(#{I18n.l(staff_check_item.license_date, format: Settings.date.formats)})") if staff_check_item&.license
    str.push("#{I18n.t('activerecord.attributes.staff_check_item.is_identity_other')}(#{staff_check_item.identity_other_name})") if staff_check_item&.is_identity_other
    str.join(", ")
  end

  def sci_health_uninsured_reason
    staff_check_item&.health_uninsured_reason_type_name
  end

  def sci_welfare_uninsured_reason
    staff_check_item&.welfare_uninsured_reason_type_name
  end

  def sci_employment_uninsured_reason
    staff_check_item&.employment_uninsured_reason_type_name
  end

  def sci_my_number_receipt
    return if staff_check_item&.my_number_receipt.blank?

    I18n.t("enum_label.staff_check_item.my_number_receipt.#{staff_check_item.my_number_receipt}")
  end

  def sci_my_number_receipt_date
    return unless staff_check_item&.my_number_receipt_date

    I18n.l(staff_check_item.my_number_receipt_date, format: Settings.date.formats)
  end

  def sci_my_number_dunning_date
    return unless staff_check_item&.my_number_dunning_date

    I18n.l(staff_check_item.my_number_dunning_date, format: Settings.date.formats)
  end

  def sci_labor_contract_sent_date
    return unless staff_check_item&.labor_contract_sent_date

    I18n.l(staff_check_item.labor_contract_sent_date, format: Settings.date.formats)
  end

  def sci_labor_contract_dunning_date
    return unless staff_check_item&.labor_contract_dunning_date

    I18n.l(staff_check_item.labor_contract_dunning_date, format: Settings.date.formats)
  end

  def sci_dunning_type
    return unless staff_check_item&.dunning_type

    I18n.t("enum_label.staff_check_item.dunning_type.#{staff_check_item.dunning_type}")
  end

  (1..3).each do |i|
    define_method("long_vacation_#{i}_start_at") do
      return if long_vacation_arr[i - 1].blank?

      date = long_vacation_arr[i - 1].start_date
      I18n.l(date, format: Settings.date.formats)
    end

    define_method("long_vacation_#{i}_end_at") do
      return if long_vacation_arr[i - 1].blank?

      date = long_vacation_arr[i - 1].end_date
      I18n.l(date, format: Settings.date.formats)
    end
  end

  def long_vacation_arr
    @long_vacation_arr ||= staff_long_vacations.to_a.sort_by(&:start_date).reverse
  end

  def sat_bank
    staff_account_transfer&.bank_name
  end

  def sat_bank_branch
    staff_account_transfer&.bank_branch_name
  end

  def sat_account_type
    return unless staff_account_transfer&.account_type

    I18n.t("admin.staff_account_transfer.account_type.#{staff_account_transfer.account_type}")
  end

  def sat_account_number
    staff_account_transfer&.account_number
  end

  def sat_account_name
    staff_account_transfer&.account_name
  end

  def sat_account_name_kana
    staff_account_transfer&.account_name_kana
  end

  def ssa_income_tax_type
    return if staff_salary&.income_tax_type.blank?

    I18n.t("enum_label.staff_salary.income_tax_types.#{staff_salary.income_tax_type}")
  end

  def ssa_tax_type
    return if staff_salary&.tax_type.blank?

    I18n.t("enum_label.staff_salary.tax_types.#{staff_salary.tax_type}")
  end

  def ssa_is_spouse
    return if staff_salary&.is_spouse.blank?

    I18n.t("enum_label.staff_salary.is_spouses.#{staff_salary.is_spouse}")
  end

  def ssa_insurance_subsection_type
    return if staff_salary&.insurance_subsection_type.blank?

    I18n.t("enum_label.staff_salary.insurance_subsection_types." \
      "#{staff_salary.insurance_subsection_type}")
  end

  def ssa_insurance_number
    staff_salary&.insurance_number
  end

  def ssa_basic_pension_number
    staff_salary&.basic_pension_number
  end

  def ssa_insurance_certificate_acquisition_date
    return unless staff_salary&.insurance_certificate_acquisition_date

    I18n.l(staff_salary.insurance_certificate_acquisition_date, format: Settings.date.formats)
  end

  def ssa_insurance_lost_qualification_date
    return unless staff_salary&.insurance_lost_qualification_date

    I18n.l(staff_salary.insurance_lost_qualification_date, format: Settings.date.formats)
  end

  def ssa_employment_insurance_type
    return unless staff_salary&.employment_insurance_type

    I18n.t "enum_label.staff_salary.employment_insurance_types." \
      "#{staff_salary.employment_insurance_type}"
  end

  def ssa_employment_insurance_number
    staff_salary&.employment_insurance_number
  end

  def ssa_employment_certificate_acquisition_date
    return unless staff_salary&.employment_certificate_acquisition_date

    I18n.l(staff_salary.employment_certificate_acquisition_date, format: Settings.date.formats)
  end

  def ssa_employment_lost_qualification_date
    return unless staff_salary&.employment_lost_qualification_date

    I18n.l(staff_salary.employment_lost_qualification_date, format: Settings.date.formats)
  end

  def ssa_insurance_subsection_deadline
    return unless staff_salary&.insurance_subsection_deadline

    I18n.l(staff_salary.insurance_subsection_deadline, format: Settings.date.formats)
  end

  def ssa_employment_insurance_deadline
    return unless staff_salary&.employment_insurance_deadline

    I18n.l(staff_salary.employment_insurance_deadline, format: Settings.date.formats)
  end
end
