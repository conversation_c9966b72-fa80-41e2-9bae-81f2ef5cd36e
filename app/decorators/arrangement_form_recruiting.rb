module ArrangementFormRecruiting
  PERCENTS = [25, 135, 100]
  FIELD_ALLOWANCES = 1..20
  SIXTY_YEAR_OLD = 60
  BILLING_FIELDS = 1..10

  # Ticket 133 - <PERSON><PERSON><PERSON><PERSON> needs to update Fukuoka address
  FUKUOKA_DEPT_ID = 11
  FUKUOKA_ADDRESS = "福岡県福岡市博多区博多駅前3-19-18ｱﾙﾃﾞｨｺﾝﾃ’21　401号室"
  FUKUOKA_LAST_VALID_DATE = "2023-07-29".in_time_zone.end_of_day
  FUKUOKA_PIC_SECTION = "ｵﾍﾟﾚｰｼｮﾝｾﾝﾀｰ部"
  FUKUOKA_PIC_POSITION = "職員"
  FUKUOKA_PIC_NAME = "中村 優介"
  FUKUOKA_TEL = "080-4837-2813"

  # AD-4777 - Store data in previous documents due to address updated from Niigata to Tokyo
  NIIGATA_DEPT_ID = [1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 16, 17, 19]
  NIIGATA_ADDRESS = "新潟県新潟市中央区女池上山3-14-10"
  NIIGATA_LAST_VALID_DATE = "2024-09-30".in_time_zone.end_of_day
  NIIGATA_PIC_SECTION = "ｵﾍﾟﾚｰｼｮﾝｾﾝﾀｰ部"
  NIIGATA_PIC_POSITION = "部長補佐"
  NIIGATA_PIC_NAME = "山田幸輝"
  NIIGATA_TEL = "************"

  def working_start_date
    working_started_at.strftime Settings.date.year_month_and_date
  end

  def working_end_date
    working_ended_at.strftime Settings.date.year_month_and_date
  end

  def work_location
    [order_corporation_full_name, order_location_name, order_location_code].join(" ")
  end

  def work_address
    "#{I18n.t('corporation.order.step3.postal_code')}#{order.location&.postal_code} #{order_full_address}"
  end

  def organization_name
    [order_organization_full_name, order_organization_position_name].join(" ")
  end

  def working_started_time
    working_started_at.strftime Settings.time.formats
  end

  def working_ended_time
    working_ended_at.strftime Settings.time.formats
  end

  def payment_night_unit_price_percent
    payment_night_unit_price.to_i * 1.25
  end

  PERCENTS.each do |percent|
    define_method "payment_unit_price_#{percent}" do
      (payment_basic_unit_price.to_i + payment_unit_price_addition.to_i) * percent / 100
    end
  end

  def allowancess
    arrange_payement_labels = I18n.t("activerecord.attributes.arrange_payment_no_prefix")
    FIELD_ALLOWANCES.map do |order|
      next if self.send("payment_field_#{order}").to_i <= 0

      "#{arrange_payement_labels["payment_field_#{order}".to_sym]}:#{self.send("payment_field_#{order}")}#{I18n.t('common.salary.yen')}"
    end.compact.join("、")
  end

  def insurance_subsection
    return if staff_insurance_subsection_type.blank?

    I18n.t("enum_label.staff_salary.insurance_subsection_types.#{staff_insurance_subsection_type}")
  end

  def basic_pension_number
    return I18n.t("staff.recruiting_form.not_joining") if staff_basic_pension_number.blank?

    I18n.t("staff.recruiting_form.join")
  end

  def staff_department_info
    department = staff&.recent_departments&.first&.department
    return {} if department.blank?

    default_pic = department.id == FUKUOKA_DEPT_ID && working_started_at <= FUKUOKA_LAST_VALID_DATE
    niigata_pic = NIIGATA_DEPT_ID.include?(department.id) && working_started_at <= NIIGATA_LAST_VALID_DATE

    if default_pic
      address = FUKUOKA_ADDRESS
      section = claim_section = consultation_section = FUKUOKA_PIC_SECTION
      position = claim_position = consultation_position = FUKUOKA_PIC_POSITION
      name = claim_name = consultation_name = FUKUOKA_PIC_NAME
      tel = FUKUOKA_TEL
    elsif niigata_pic
      address = NIIGATA_ADDRESS
      section = claim_section = consultation_section = NIIGATA_PIC_SECTION
      position = claim_position = consultation_position = NIIGATA_PIC_POSITION
      name = claim_name = consultation_name = NIIGATA_PIC_NAME
      tel = NIIGATA_TEL
    else
      department_pics = department.department_pics
      pic = department_pics.find{|p| p.department_pic_type_id == Settings.department.pic_types.haken}
      claim_pic = department_pics.find{|p| p.department_pic_type_id == Settings.department.pic_types.claim}
      consul_pic = department_pics.find{|p| p.department_pic_type_id == Settings.department.pic_types.consultation}
      address = [
        department.address_prefecture&.name,
        department.address_city,
        department.address_street_number,
        department.address_building
      ].join
      tel = department.address_tel
      section = pic&.section
      position = pic&.position
      name = pic&.name
      claim_section = claim_pic&.section
      claim_position = claim_pic&.position
      claim_name = claim_pic&.name
      consultation_section = consul_pic&.section
      consultation_position = consul_pic&.position
      consultation_name = consul_pic&.name
    end
    {
      department_name: department.name,
      department_address: address,
      department_tel: tel,
      pic_section: section,
      pic_position: position,
      pic_name: name,
      claim_pic_section: claim_section,
      claim_pic_position: claim_position,
      claim_pic_name: claim_name,
      consultation_section: consultation_section,
      consultation_position: consultation_position,
      consultation_name: consultation_name,
      department_haken_unit_price: department.haken_payment_unit
    }
  end

  def location_haken_pic_info
    pic = order.location.location_pics.find{|pic| pic.pic_type_id == Settings.location.pic_types.haken}
    common_location_pic_info(pic)
  end

  def location_claim_pic_info
    pic = order.location.location_pics.find{|pic| pic.pic_type_id == Settings.location.pic_types.claim}
    common_location_pic_info(pic)
  end

  def location_mandator_info
    pic = order.location.location_pics.find{|pic| pic.pic_type_id == Settings.location.pic_types.mandator}
    common_location_pic_info(pic)
  end

  def work_place_name
    [order_corporation_full_name, order_location_name].join(" ")
  end

  def working_address
    order_location = order.location
    full_address_location = [order_location.postal_code, order_location.prefecture_id,
      order_location.city, order_location.street_number]
    if full_address_location.length != full_address_location.compact.length
      order_corp = order.corporation
      return [I18n.t("corporation.order.step3.postal_code"), order_corp.address_postal_code,
        order_corp.address].join
    end
    [I18n.t("corporation.order.step3.postal_code"), order_location.postal_code,
      order_location_full_address].join
  end

  def basic_fee
    billing_basic_unit_price.to_i + billing_unit_price_addition.to_i
  end

  def pic_department_name
    [order_pic_department_name, order_claim_process_pic_name].join
  end

  def avg_haken_payment_department
    order.location.corporation_group.pic_department&.avg_haken_payment
  end

  def corporation_group_violation_date
    return I18n.t("staff.recruiting_form.not_apply_1") if staff_age.to_i >= SIXTY_YEAR_OLD || staff&.indefinite?

    I18n.t("staff.recruiting_form.violation_day_of_corporation_sent_1") +
      order.location&.corporation_group&.violation_day&.strftime(Settings.date.year_month_and_date).to_s
  end

  def staff_violation_date
    return I18n.t("staff.recruiting_form.not_apply_2") if staff_age.to_i >= SIXTY_YEAR_OLD || staff&.indefinite?

    staff_violation_date = StaffViolationDate.find_by(staff_id: staff.id, location_id: order.location_id)
    I18n.t("staff.recruiting_form.violation_day_of_corporation_sent_2") +
      staff_violation_date&.violation_day&.strftime(Settings.date.year_month_and_date).to_s
  end

  def basic_salary_basic_charge
    (payment_basic_unit_price.to_i + payment_unit_price_addition.to_i)
  end

  def employment_insurance_number
    return I18n.t("staff.recruiting_form.not_joining") if staff_employment_insurance_number.blank?

    I18n.t("staff.recruiting_form.join")
  end

  # TODO(Tinh): Deprecated and will be removed in the future
  # def staff_company_and_department_name
  #   [order_corporation_full_name, staff_current_department_name].join("")
  # end

  # def corporation_name_and_staff_department_name
  #   [I18n.t("print.staff_contract.name_corporation"), staff_current_department_name].join("")
  # end

  def billing_field
    BILLING_FIELDS.map do |order|
      next if self.send("billing_field_#{order}").to_i <= 0

      "#{ArrangeBilling.human_attribute_name("billing_field_#{order}")}:#{self.send("billing_field_#{order}")}#{I18n.t('common.salary.yen')}"
    end.compact.join("、")
  end

  def individual_contract_corporation
    I18n.t("print.staff_contract.corporation_info", corporation_full_name: order_corporation_full_name)
  end

  def staff_over_60
    if staff.age_over_60?
      I18n.t("print.staff_contract.over_60")
    else
      I18n.t("print.staff_contract.under_60")
    end
  end

  def arrangement_contract_date
    return if staff_id.blank?

    return [order_case_case_started_at.strftime(Settings.date.year_month_and_date),
      order_case_case_ended_at.strftime(Settings.date.year_month_and_date)].join("~") if staff.single_haken?
    contract = staff.active_contract_by_date(order_case_case_started_at)
    return if contract.blank?

    contract_start_date = contract.contract_start_date.strftime Settings.date.year_month_and_date
    if contract.contract_end_date
      contract_end_date = contract.contract_end_date.strftime Settings.date.year_month_and_date
    else
      contract_end_date = I18n.t("enum_label.staff.indefinite_employment_flags.indefinite")
    end
    [contract_start_date, contract_end_date].join("~")
  end

  def employment_period_status
    return if staff_id.blank?

    staff.employment_period_status_by_date(order_case_case_started_at)
  end

  def expired_contract_flag
    return "" if staff_id.blank? || staff.last_contract_start_date.blank? || staff.single_haken?

    expired = staff.last_contract_end_date && order_case_case_started_at.to_date > staff.last_contract_end_date
    expired ? I18n.t("enum_label.staff.indefinite_employment_flags.expired_contract") : ""
  end

  def location_pic_contact_info column
    location_pics = order.location.location_pics
    location_pics.find{|pic| pic.pic_type_id == Settings.location.pic_types.contact}&.send column
  end

  def arrange_date
    staff_apply_order_cases.default_unscope&.first&.arranged_at
      &.strftime(Settings.date.year_month_and_date)
  end

  def dispatch_worker_info
    "#{staff_account_name} #{dispatch_worker_age}"
  end

  private

  # ! Department here means the same as "section" in department_pics
  def department_name
    location_pics = order.location.location_pics
    location_pics.find{|pic| pic.id == order_haken_destination_pic_id}&.department
  end

  def dispatch_worker_age
    return staff_age if staff_age < 18
    return I18n.t("print.staff_contract.over_45") if staff_age.between?(45, 59)

    I18n.t("print.staff_contract.over_60") if staff_age >= 60
  end

  def common_location_pic_info pic
    return {} if pic.blank?

    {
      section: pic.department,
      name: pic.name,
      position: pic.position,
      tel: pic.tel
    }
  end
end
