module ExportTrainingScheduleApplicantDecorator
  extend ActiveSupport::Concern

  FIELD_DATA = %i(
    created_at
    working_date
    training_schedule_id
    training_session_code_export_i18n
    start_time_text
    end_time_text
    location_id
    location_name
    staff_number
    staff_name
    schedule_status_code_export_i18n
    staff_tel
    deleted_at
    cancel_reason
  )

  ATTRS_I18N = {
    training_session_code: "admin.training_schedule.training_session_codes",
    schedule_status_code: "admin.training_schedule.schedule_status_codes"
  }

  def row_data
    FIELD_DATA.map do |method|
      get_value_row_data(method)
    end
  end

  def export_date_format
    I18n.t("admin.location_time_sheets.export_date_format",
      month: ServerTime.now.month, day: ServerTime.now.day)
  end

  private
  ATTRS_I18N.each do |arrange_attr, i18n_path|
    define_method "#{arrange_attr}_export_i18n" do
      return if self.send(arrange_attr).nil?

      I18n.t "#{i18n_path}.#{self.send(arrange_attr)}"
    end
  end

  class_methods do
    def label_i18n
      I18n.t("admin.training_schedule.export.columns").values
    end
  end

  def get_value_row_data method
    return self.send(:created_full_date) if method == :created_at
    return self.send(:deleted_at_formatted) if method == :deleted_at
    return I18n.t("admin.training_schedule.export.deleted_value.staff") if self.staff.deleted? && method == :staff_tel

    self.send(method)
  end
end
