module ArrangeBillingSearchDecorator
  def start_time
    get_billing_started_at&.strftime Settings.time.formats
  end

  def end_time
    get_billing_ended_at&.strftime Settings.time.formats
  end

  def get_billing_started_at
    billing_started_at || arrangement&.working_started_at
  end

  def get_billing_ended_at
    billing_ended_at || arrangement&.working_ended_at
  end

  def start_date
    get_billing_started_at&.strftime Settings.date.formats
  end

  def end_date
    get_billing_ended_at&.strftime Settings.date.formats
  end

  private

  def location_id
    arrangement.order_location_id
  end

  def corporation_id
    arrangement.order_corporation_id
  end

  def working_time_int
    return 0 unless billing_started_at

    (billing_ended_at - billing_started_at).to_i / 60
  end

  def corporation_group_id
    arrangement.order_corporation_group_id
  end

  def corporation_group_tag_id
    arrangement.order.corporation_group.corporation_group_tag_id
  end

  def start_date_format
    get_billing_started_at&.strftime Settings.date.formats
  end

  def is_billing_locked
    arrangement.is_billing_locked
  end
end
