module ExportStaffEmploymentHistoriesDecorator
  extend ActiveSupport::Concern

  TOTAL_FIELD = 5
  EMPLOYEE_HISTORY_FIELD_DATA = {
    0 => :staff_number,
    1 => :account_name,
    2 => :staff_convenience_lawson_start,
    3 => :staff_convenience_lawson_end,
    4 => :staff_convenience_lawson_shop
  }
  DATE_FIELD = %i(staff_convenience_lawson_start staff_convenience_lawson_end)

  def employment_histories_row_data
    EMPLOYEE_HISTORY_FIELD_DATA.values.map{|method| self.send(method)}
  end

  class_methods do
    def employment_histories_label_i18n
      I18n.t("admin.staff_employment_histories.export_xlsx.label_name").values
    end

    def employment_histories_sheet_name
      I18n.t("admin.staff_employment_histories.export_xlsx.sheet_name")
    end

    def employment_histories_format_date date_style
      styles = Array.new(TOTAL_FIELD, nil)
      EMPLOYEE_HISTORY_FIELD_DATA.each do |key, val|
        styles[key] = date_style if DATE_FIELD.include? val
      end
      styles
    end
  end
end
