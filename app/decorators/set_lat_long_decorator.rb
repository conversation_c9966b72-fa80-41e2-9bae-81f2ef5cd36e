module SetLatLongDecorator
  def set_lat_long_value full_address, prefecture_name, postal_code
    search_result = search_by_geocoder(full_address)
    prefecture_and_postal_code = [prefecture_name, postal_code].compact.join(" ")
    search_result = search_by_geocoder(prefecture_and_postal_code) if search_result.blank?
    postal_code_format = postal_code&.gsub("-", "")
    search_result = search_by_geocoder(postal_code_format) if search_result.blank?
    return [] if search_result.blank?

    search_result.first.coordinates
  end

  private
  def search_by_geocoder search_options
    Geocoder.search(search_options)
  rescue StandardError
    []
  end
end
