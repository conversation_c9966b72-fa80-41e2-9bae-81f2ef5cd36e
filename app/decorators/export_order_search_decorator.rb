module ExportOrderSearchDecorator
  extend ActiveSupport::Concern
  MINUTE_OF_HOUR = 60
  HOUR_OF_DAY = 24
  NUMBER_DAY_OF_WEEK = 7
  DAY_OF_WEEKS = %w(is_sunday is_monday is_tuesday is_wednesday is_thursday is_friday is_saturday)
  ALL_ELEMENTS = 145
  LOCATION_ATTRS = %w(code postal_code prefecture_name city street_number tel fax station_1
    station_2 station_3 station_4 note)
  MIDNIGHT_START = 22
  MIDNIGHT_END = 5
  MINUTES_PER_HOUR = 60
  FIELD_DATA = {
    0 => :order_no,
    1 => :created_date,
    2 => :transaction_type_id,
    5 => :customer_no,
    6 => :customer_no,
    7 => :location_id,
    8 => :location_name,
    9 => :location_code_format,
    10 => :location_postal_code,
    11 => :export_location_address,
    12 => :location_building,
    14 => :location_tel,
    15 => :location_fax,
    16 => :station_2_code,
    17 => :station_3_code,
    18 => :station_4_code,
    19 => :station_1_name,
    20 => :meeting_place_note,
    21 => :old_assembly_time_note,
    26 => :conflict_date_category,
    27 => :location_email,
    29 => :business_id,
    30 => :occupation_no,
    32 => :pic_id,
    33 => :corporation_group_pic_department_id,
    35 => :started_date,
    36 => :ended_date,
    37 => :is_sunday_format,
    38 => :is_monday_format,
    39 => :is_tuesday_format,
    40 => :is_wednesday_format,
    41 => :is_thursday_format,
    42 => :is_friday_format,
    43 => :is_saturday_format,
    44 => :except_holiday_format,
    46 => :arrangement_method,
    47 => :capture_pattern_no,
    48 => :work_system_in_batch,
    49 => :unit_price_specified_in_batch,
    51 => :working_start_time_format,
    52 => :working_end_time_format,
    53 => :predetermined_fixed_time,
    54 => :predetermined_fixed_time,
    75 => :break_time,
    76 => :over_time_break_time,
    77 => :midnight_break_time,
    79 => :staff_count,
    80 => :note_format,
    81 => :shift_work,
    86 => :work_conditions_1,
    87 => :work_conditions_2,
    88 => :work_conditions_3,
    89 => :work_conditions_4,
    90 => :work_conditions_5,
    94 => :business_no_2,
    96 => :business_no_3,
    103 => :fixed_period_employment_contract,
    105 => :paid_leave_is_available,
    106 => :allowance_available,
    108 => :validity_period_employment_date,
    113 => :recruitment_fee
  }

  ORDER_FIELD_DATA = %i(transaction_type_id location_id location_name
    location_postal_code export_location_address location_building
    location_tel location_fax location_email location_code_format
    corporation_group_pic_department_id)

  FIXED_FIELDS = {
    conflict_date_category: -1,
    business_id: 200,
    occupation_no: 1,
    over_time_break_time: 0,
    work_conditions_1: -1,
    work_conditions_2: -1,
    work_conditions_3: -1,
    work_conditions_4: -1,
    work_conditions_5: -1,
    business_no_2: -1,
    business_no_3: -1,
    fixed_period_employment_contract: 0,
    paid_leave_is_available: 0,
    allowance_available: 0,
    validity_period_employment_date: 0,
    recruitment_fee: 0,
    arrangement_method: 2,
    capture_pattern_no: -1,
    work_system_in_batch: 1,
    unit_price_specified_in_batch: 1,
    shift_work: 1
  }

  FIELD_TYPE = {string: [10, 14, 15]}
  STATIONS = %w(2 3 4)
  FIXED_480 = 480
  MEETING_PLACE_NOTE_PREFIX = "旧集合場所"
  OLD_ASSEMPLY_TIME_NOTE_PREFIX = "旧集合時間"

  def order_branch_row_data holidays, export_order_id
    @holidays = holidays
    @export_order_id = export_order_id
    row_array = Array.new(ALL_ELEMENTS, nil)
    FIELD_DATA.each do |key, method|
      row_array[key] = ORDER_FIELD_DATA.include?(method) ? order.send(method) : send(method)
    end
    row_array
  end

  class_methods do
    def order_branch_sheet_name
      I18n.t("admin.order.export_xlsx.sheet_name")
    end

    def order_branch_label_i18n
      I18n.t("admin.order.export_xlsx.label_name").values
    end

    def order_branch_column_data_types
      type_array = Array.new(ALL_ELEMENTS, nil)
      FIELD_TYPE.each do |type, columns|
        columns.each{|index| type_array[index] = type}
      end
      type_array
    end
  end

  private
  def order_no
    "#{@export_order_id}-#{self.sequence_no.to_s.rjust(3, '0')}"
  end

  def created_date
    order.created_at&.strftime Settings.date.formats
  end

  def started_date
    self.started_at&.strftime Settings.date.formats
  end

  def ended_date
    return self.started_at&.strftime Settings.date.formats if order.individual?

    (self.started_at + ((NUMBER_DAY_OF_WEEK * week_count.to_i.days) - 1.day))
      &.strftime Settings.date.formats
  end

  DAY_OF_WEEKS.each do |day|
    define_method("#{day}_format") do
      return self.send(day) ? 1 : 0 if order.batch?

      DAY_OF_WEEKS[self.started_at.wday] == day ? 1 : 0
    end
  end

  FIXED_FIELDS.each{|key, value| define_method(key){value}}

  def except_holiday_format
    return self.is_except_holiday ? 0 : 1 if order.batch?

    @holidays.include?(self.started_at.to_date) ? 1 : 0
  end

  def working_start_time_format
    (self.working_start_time.hour * MINUTE_OF_HOUR) + self.working_start_time.min
  end

  def working_end_time_format
    end_time_minutes = (self.working_end_time.hour * MINUTE_OF_HOUR) + self.working_end_time.min
    return end_time_minutes unless working_start_time_format > end_time_minutes

    end_time_minutes + (HOUR_OF_DAY * MINUTE_OF_HOUR)
  end

  def predetermined_fixed_time
    FIXED_480
  end

  def note_format
    note = self.is_time_changable ? I18n.t("admin.order.export_xlsx.changeable_note") : ""
    [order.note, note].join
  end

  def midnight_break_time
    end_hour = working_end_time.hour
    start_hour = working_start_time.hour
    working_minutes = (start_hour * MINUTES_PER_HOUR) + working_start_time.min
    return if start_hour < MIDNIGHT_START && working_minutes > MIDNIGHT_END * MINUTES_PER_HOUR

    over_midnight_time = end_hour > MIDNIGHT_END && end_hour < MIDNIGHT_START
    end_time = over_midnight_time ? working_end_time.change(hour: MIDNIGHT_END) : working_end_time
    time_diff = ((end_time - working_start_time) / MINUTES_PER_HOUR).to_i
    break_time if time_diff > break_time
  end

  def customer_no
    "#{order.corporation_id}-#{order.corporation_group.sequence_no.to_s.rjust(3, '0')}"
  end

  def pic_id
    order.location.corporation_group.pic_id
  end

  STATIONS.each do |station_no|
    define_method("station_#{station_no}_code") do
      order.location.send("stations_#{station_no}")&.station_code
    end
  end

  def station_1_name
    order.location.stations_1&.name
  end

  def meeting_place_note
    return unless note = order.location_note.presence

    note = note.each_line.find{|line| line.include?(MEETING_PLACE_NOTE_PREFIX)}
    return unless note

    note = note.split(Settings.enter_character)[0]
      .gsub!(/.*?(?=#{MEETING_PLACE_NOTE_PREFIX})/im, "")
    note[(MEETING_PLACE_NOTE_PREFIX.length + 1)..-1]
  end

  def old_assembly_time_note
    return unless note = order.location_note.presence

    note = note.each_line.find{|line| line.include?(OLD_ASSEMPLY_TIME_NOTE_PREFIX)}
    return unless note

    note = note.split(Settings.enter_character)[0]
      .gsub!(/.*?(?=#{OLD_ASSEMPLY_TIME_NOTE_PREFIX})/im, "")
    note[(OLD_ASSEMPLY_TIME_NOTE_PREFIX.length + 1)..-1]
  end
end
