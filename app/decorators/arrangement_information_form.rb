module ArrangementInformationForm
  def information_form_basic_unit_price
    unit_price(:basic) + payment_unit_price_addition.to_i + bonus_price
  end

  def information_form_payment_night_unit_price
    ((unit_price(:night) + payment_unit_price_addition.to_i + bonus_price) * ArrangePayment::NIGHT_SALARY_RATE).round
  end

  def information_form_payment_ot_unit_price
    (information_form_basic_unit_price * Calculation::CalculateArrangePayment::OT_RATE).ceil
  end

  def information_form_payment_non_statutory_holiday_unit_price
    (information_form_basic_unit_price * ArrangementIndividualContractForm::PERCENT_135).round
  end

  def information_form_payment_statutory_holiday_unit_price
    information_form_basic_unit_price
  end

  private
  def is_oc_urgent?
    order_case.is_urgent
  end

  def unit_price type = :night
    return payment_basic_unit_price.to_i + payment_urgent_unit_price_addition.to_i if is_oc_urgent?
    return payment_night_unit_price.to_i if type == :night

    payment_basic_unit_price.to_i
  end

  def bonus_price
    return 0 if is_oc_urgent?

    payment_bonus_unit_price.to_i
  end
end
