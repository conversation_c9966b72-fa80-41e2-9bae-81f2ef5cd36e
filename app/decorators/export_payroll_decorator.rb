module ExportPayrollDecorator
  extend ActiveSupport::Concern

  DEFAULT_ZERO = 0
  FIELD_DATA = {
    0 => :staff_number,
    1 => :attendance_day_count,
    2 => :used_day_off_count,
    3 => :actual_working_time_export,
    4 => :ot_time_export,
    5 => :night_time_export,
    6 => :basic_salary_paid_amount,
    7 => :payment_field_1,
    8 => :payment_field_2,
    9 => :deduction_field_1_2_13,
    10 => :set_default_zero,
    11 => :set_default_zero,
    12 => :ot45_amount,
    13 => :ot60_amount,
    14 => :payment_field_4_5_7_8_9,
    15 => :payment_field_3,
    16 => :overtime_and_working_night_amount,
    17 => :payment_field_7
  }

  TOTAL_COLS = 17

  def row_data
    row_array = Array.new(TOTAL_COLS, nil)
    FIELD_DATA.each do |key, method|
      row_array[key] = send method
    end
    row_array
  end

  class_methods do
    def label_i18n
      Settings.payroll.export_csv.payroll.label
    end
  end

  private
  def deduction_field_1_2_13
    deduction_field_1.to_i + deduction_field_2.to_i + deduction_field_13.to_i
  end

  def set_default_zero
    DEFAULT_ZERO
  end

  def payment_field_4_5_7_8_9
    payment_field_4.to_i + payment_field_5.to_i + payment_field_7.to_i + payment_field_8.to_i +
      payment_field_9.to_i
  end

  def overtime_and_working_night_amount
    overtime_amount + working_night_amount
  end

  def staff_number
    staff.staff_number
  end

  def basic_salary_paid_amount
    basic_salary_price.to_i + paid_amount.to_i
  end

  def actual_working_time_export
    format_time_export actual_working_time
  end

  def ot_time_export
    format_time_export ot_time
  end

  def night_time_export
    format_time_export night_time
  end

  def inlate_leave_early_time
    format_time_export(late_time.to_i + leave_early_time.to_i)
  end

  def format_time_export time
    h = time / 60
    m = (time % 60).to_s.rjust(2, "0")
    "#{h}.#{m}"
  end
end
