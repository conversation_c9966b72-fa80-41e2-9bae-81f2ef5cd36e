module Jobs
  class OrderCasesListDecorator < Jobs::BaseDecorator
    def decorate order_case_ids
      response_data(order_case_ids)
    end

    private

    def response_data order_case_ids
      rejected_order_case_ids = get_rejected_order_case_ids(order_case_ids)
      keep_order_case_ids = get_keep_order_case_ids(order_case_ids)

      order_cases = OrderCase.where_keep_order(order_case_ids)
        .includes(:order_branch, arrangements: [:arrange_payment],
          order: [location: [stations_1: [:railway_line], corporation_group: [:corporation]]])
      wages_fees = get_wages_fees(order_cases)

      {
        status: true,
        order_cases: OrderCaseBlueprint.render_as_json(order_cases, view: :staff_jobs),
        keep_order_case_ids: keep_order_case_ids,
        rejected_order_case_ids: rejected_order_case_ids,
        wages_fees: wages_fees,
        sign_in_count: staff.sign_in_count,
        staff_number: staff.staff_number,
        no_limit_registration_apply: no_limit_registration_apply?
      }
    end

    def get_rejected_order_case_ids order_case_ids
      return [] if order_case_ids.blank?

      staff.staff_apply_order_cases.rejected
        .where(order_case_id: order_case_ids)
        .pluck(:order_case_id)
    end

    def get_keep_order_case_ids order_case_ids
      return [] if order_case_ids.blank?

      staff.staff_keep_order_cases
        .where(order_case_id: order_case_ids)
        .pluck(:order_case_id)
    end

    def get_wages_fees order_cases
      return [] if order_cases.blank?

      order_cases_options = {order_cases: order_cases, only_flags: true}
      JobsWage::CalculateWage.wages_fees(order_cases_options, staff)
    end
  end
end
