module Jobs
  class BaseDecorator
    attr_reader :staff

    def initialize staff
      @staff = staff
    end

    def decorate
      raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"
    end

    private

    def format_wages wages, json_ocs
      json_ocs.each do |oc|
        order_case_wages = wages.data_wages_by_order_case_id(oc["id"])
        oc["display_unit_price"] = order_case_wages[:display_unit_price]
        oc["display_total_price"] = order_case_wages[:display_total_price]
        oc["average_price"] = order_case_wages[:average_price]
        oc["has_unit_price_addition"] = order_case_wages[:has_unit_price_addition]
        oc["has_total_price_addition"] = order_case_wages[:has_total_price_addition]
      end
      json_ocs
    end

    def no_limit_registration_apply?
      staff.op_confirm? || ServerTime.now.to_date < Settings.release_staff_interview.to_date
    end

    def get_order_cases_wages order_case_ids, wage_types
      cmd = Wages::StaffWagesBaseCommand.new(staff, order_case_ids)
      cmd.perform(wage_types)
    end
  end
end
