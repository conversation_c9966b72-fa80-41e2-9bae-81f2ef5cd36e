module Jobs
  class HomePageDecorator < BaseDecorator
    def decorate hot_job_ids, keep_job_ids, offer_job_ids
      all_oc_ids = (hot_job_ids + keep_job_ids + offer_job_ids).uniq
      hashmap_all_jobs = get_hashmap_data_jobs(all_oc_ids)

      hot_order_cases = hashmap_all_jobs.values_at(*hot_job_ids)
      keep_order_cases = hashmap_all_jobs.values_at(*keep_job_ids)
      offer_order_cases = hashmap_all_jobs.values_at(*offer_job_ids)
      all_keep_oc_ids = staff.staff_keep_order_cases.where(order_case_id: all_oc_ids)
        .pluck(:order_case_id)

      order_cases_wages = get_order_cases_wages(all_oc_ids, display_wage_types)
        .index_by{|oc_wage| oc_wage[:id]}

      response_data(
        hot_order_cases: hot_order_cases,
        keep_order_cases: keep_order_cases,
        offer_order_cases: offer_order_cases,
        keep_order_case_ids: all_keep_oc_ids,
        order_case_wages: order_cases_wages
      )
    end

    private

    def response_data kargs = {}
      {
        order_cases: {
          hot_order_cases: kargs[:hot_order_cases],
          keep_order_cases: kargs[:keep_order_cases],
          offer_order_cases: kargs[:offer_order_cases]
        },
        keep_order_case_ids: kargs[:keep_order_case_ids],
        wages_fees: kargs[:order_case_wages]
      }
    end

    def get_hashmap_data_jobs order_case_ids
      order_cases = OrderCase.includes(
        :order_branch, arrangements: [:arrange_payment],
        order: [location: [stations_1: [:railway_line], corporation_group: [:corporation]]]
      ).where(id: order_case_ids)

      OrderCaseBlueprint.render_as_hash(order_cases, view: :staff_jobs).index_by{|oc| oc[:id]}
    end

    def display_wage_types
      %i(total_order_case_addition total_unit_price_addition)
    end
  end
end
