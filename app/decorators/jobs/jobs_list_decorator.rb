module Jobs
  class JobsListDecorator < Jobs::BaseDecorator
    def decorate order_case_ids
      {
        status: true,
        data: response_data(order_case_ids)
      }
    end

    private

    def response_data order_case_ids
      liked_location_ids = get_liked_location_ids
      liked_location_oc_ids = get_liked_location_oc_ids(order_case_ids, liked_location_ids)

      arranged_oc_ids = staff.arrangements.is_arranged
        .where(order_case_id: order_case_ids).pluck(:order_case_id)
      applied_oc_ids = staff.staff_apply_order_cases.not_processed
        .where(order_case_id: order_case_ids).pluck(:order_case_id)
      ended_oc_ids = staff.arrangements.is_finish_recruiting.arrived
        .where(order_case_id: order_case_ids).pluck(:order_case_id)
      absence_oc_ids = staff.arrangements.is_absence_statuses
        .where(order_case_id: order_case_ids).pluck(:order_case_id)
      arranged_oc_ids = arranged_oc_ids - ended_oc_ids - absence_oc_ids

      display_order_case_ids = arranged_oc_ids + applied_oc_ids + ended_oc_ids +
        liked_location_oc_ids - absence_oc_ids

      cache_data = load_order_case_with_cache_by_ids(display_order_case_ids.uniq)

      {
        all_ids: display_order_case_ids,
        liked_location_ids: liked_location_ids,
        locations: cache_data[:locations],
        total_count: cache_data[:total_count],
        arranged_oc_ids: arranged_oc_ids,
        ended_oc_ids: ended_oc_ids,
        applied_oc_ids: applied_oc_ids - arranged_oc_ids - ended_oc_ids,
        liked_location_oc_ids: liked_location_oc_ids - arranged_oc_ids - applied_oc_ids,
        order_cases: cache_data[:order_cases],
        is_notification_on: true,
        server_time: ServerTime.now.strftime(Settings.date.formats),
        sign_in_count: staff.sign_in_count,
        staff_number: staff.staff_number,
        no_limit_registration_apply: no_limit_registration_apply?
      }
    end

    def get_liked_location_ids
      return [] if staff.id.blank?

      StaffLikeLocation.like_locations_by_staff(staff.id).pluck(:location_id)
    end

    def get_liked_location_oc_ids order_case_ids, liked_location_ids
      liked_location_ocs = OrderCase.where(id: order_case_ids)
        .where(status_id: [:recruiting, :adjusting])
        .filter_by_location_id(liked_location_ids)

      liked_location_ocs = liked_location_ocs.where.not(segment_id: :training) unless
        staff.current_level&.before_debut?

      liked_location_ocs.pluck(:id)
    end

    def load_order_case_with_cache_by_ids order_case_ids
      order_cases = OrderCase.where(id: order_case_ids)
        .includes(:location_job_category, :order, :order_branch, arrangements: [:arrange_payment])
        .order(case_started_at: :asc)
      location_ids = order_cases.map(&:location_id).uniq
      locations = Location.categories_by_locations(location_ids)
      json_ocs = json_new_jobs(order_cases)
      order_cases_options = {order_cases: order_cases}
      wages = JobsWage::CacheForCalculation.new(order_cases_options, staff)
      wages.calculate
      total_count = wages.total_count
      {
        order_cases: format_wages(wages, json_ocs),
        locations: locations,
        total_count: total_count
      }
    end

    def json_new_jobs order_cases
      order_cases.as_json(
        only: OrderCase::NEW_JOB_SEARCH_ATTRS,
        methods: OrderCase::NEW_JOB_SEARCH_METHODS + OrderCase::IDENTIFY_KEY
      )
    end
  end
end
