module StaffSearchDecorator
  MAX_DATE = "9999/01/01"
  ARRANGED_ARRANGEMENT_STATUSES = [7, 8, 11]

  def prefecture_name
    self.staff_prefecture&.name
  end

  def haken_type_tag
    unless self.op_confirm?
      signup_device_name = I18n.t("admin.registration.main_search_form.signup_devices.#{self.signup_device}")
      status = ""
      status = I18n.t("admin.registration.main_search_form.status_ids.#{self.status_id}") if
        self.status_id.present?
      return "#{status}(#{signup_device_name})"
    end
    I18n.t("admin.staff.haken_type.#{self.haken_type}")
  end

  def avatar_url_path
    file_exists = self.avatar.avatar_custom.file&.exists?
    return self.avatar_url(:avatar_custom) if file_exists

    self.avatar_url || ActionController::Base.helpers.asset_path("avatar-default.svg")
  end

  def nationality_name
    self.nationality_type.name
  end

  def last_worked_date
    working_started_at = self.arrangements.is_arranged
      .where("arrangements.working_started_at <= ?", ServerTime.now)
      .order(working_started_at: :DESC)&.first&.working_started_at
    working_started_at&.strftime(Settings.date.formats) || "研修"
  end

  def actual_worked_hour_by_week
    week_start_date = ServerTime.now.beginning_of_week(:sunday).beginning_of_day
    week_end_date = ServerTime.now.end_of_week(:sunday).end_of_day
    week_work_achievements = self.work_achievements.by_week(week_start_date, week_end_date)
      .order(working_started_at: :DESC)
    week_work_achievements.sum(&:actual_working_time).to_f
  end

  def work_achievements_ratio
    working_time_status = self.work_achievements
      .pluck(:working_time_status_id)
    total_absence = 0
    working_time_status.each do |status|
      total_absence += 1 if status.to_sym == :absence
    end
    {
      total: working_time_status.size,
      total_absence: total_absence
    }
  end

  def total_ban_locations
    self.staff_messages.forbidden.by_working_date(ServerTime.now)
      .where.not(location_id: nil).count
  end

  def data_work_experiences
    self.staff_work_experiences.as_json(
      only: %i(office_name employment_type industry_type),
      methods: [:format_end_date, :notes, :employment_type_text]
    )
  end

  def total_staff_notes
    total = 0
    (1..8).each do |note_index|
      next if self.send("optional_item#{note_index}").blank?

      total += 1
    end
    total
  end

  private
  def retirement_date_search
    return MAX_DATE unless retirement_date

    retirement_date&.strftime Settings.datetime.formats
  end

  def register_date
    created_at.strftime Settings.datetime.formats
  end

  def birthday_format
    birthday.strftime Settings.datetime.formats
  end

  def contract_start_date_format
    contract_start_date&.strftime Settings.datetime.formats
  end

  def contract_end_date_format
    return MAX_DATE if indefinite?

    contract_end_date&.strftime Settings.datetime.formats
  end

  def expired_start_date_format
    expired_start_date&.strftime Settings.datetime.formats
  end

  def expired_end_date_format
    return MAX_DATE if expired_start_date && expired_end_date.nil?

    expired_end_date&.strftime Settings.datetime.formats
  end

  def absence_start_date_format
    absence_start_date&.strftime Settings.datetime.formats
  end

  def absence_end_date_format
    return MAX_DATE if absence_start_date && absence_end_date.nil?

    absence_end_date&.strftime Settings.datetime.formats
  end

  def residence_expiration_date_format
    residence_expiration_date&.strftime Settings.datetime.formats
  end

  def residence_validity_format
    residence_validity&.strftime Settings.date.formats
  end

  def retirement_day_format
    retirement_date&.strftime Settings.datetime.formats
  end

  def debut_date_format
    debut_date&.strftime Settings.date.formats
  end

  def evaluation_format
    evaluation
  end

  def level_up_training_format
    level_up_training ? "1" : "0"
  end

  def staff_no
    self.id.to_s
  end

  def account_name_lower
    account_name.to_s.downcase
  end

  def level_before_type_cast
    current_level&.level_before_type_cast
  end

  def hire_date_format
    hire_date&.strftime Settings.date.formats
  end

  def hire_date_with_day
    I18n.l(hire_date, format: Settings.date.day_and_date) if hire_date
  end

  def retirement_date_with_day
    I18n.l(retirement_date, format: Settings.date.day_and_date) if retirement_date
  end

  def residence_expiration_date_with_day
    return unless residence_expiration_date

    I18n.l residence_expiration_date, format: Settings.date.day_and_date
  end

  def foreign_id
    nationality
  end

  def arrangement_type_str
    arrangement_type.join(" ")
  end

  def oc_public_staff_ids
    public_order_case_public_staffs.pluck(:order_case_id).join(" ")
  end

  def nested_staff_messages
    forbidden_staff_messages.as_json(
      only: %i(corporation_id location_id),
      methods: %i(start_date_format end_date_format message_type_before_type_cast)
    )
  end

  def nested_contract_histories
    staff_contract_histories.map do |contract_history|
      next if contract_history.employment_flag_blank?

      {
        contract_start_date: contract_history.contract_start_date_format,
        contract_end_date: contract_history.contract_end_date_format || MAX_DATE
      }
    end.compact
  end

  def arranged_arrangements
    arrangements.map do |arrangement|
      next unless arrangement.display_status_before_cast.in?(ARRANGED_ARRANGEMENT_STATUSES)

      {
        order_case_id: arrangement.order_case_id,
        display_status_id_before_type_cast: arrangement.display_status_id_before_type_cast,
        working_started_at: arrangement.working_started_at.strftime(Settings.datetime.formats),
        working_ended_at: arrangement.working_ended_at.strftime(Settings.datetime.formats)
      }
    end.compact
  end

  def payrolls_date
    self.used_for_calculate_payrolls.map{|p| p.payroll_date.strftime(Settings.date.formats)}
  end

  def nested_payrolls
    used_for_calculate_payrolls.map do |payroll|
      {
        payroll_date: payroll.payroll_date.strftime(Settings.date.formats),
        notification_status: payroll.notification_status_before_type_cast
      }
    end
  end

  def nested_staff_notifications
    self.update_renew_contract.map do |staff_notification|
      {
        notification_type: staff_notification.notification_type_before_type_cast
      }
    end
  end
end
