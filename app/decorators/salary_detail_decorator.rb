module SalaryDetailDecorator
  def serialize staff, department_name
    {
      id: id,
      staff_id: staff_id,
      department_name: department_name,
      staff_number: staff.staff_number,
      staff_name: staff.account.name,
      paytime_pdf: paytime_pdf,
      working_day_count: without_zero_value(working_day_count),
      absence_day_count: without_zero_value(absence_day_count),
      used_day_off_count: without_zero_value(used_day_off_count),
      actual_working_time: formatted_duration_salary(actual_working_time),
      basic_time: formatted_duration_salary(basic_time),
      ot_time: formatted_duration_salary(ot_time),
      night_time: formatted_duration_salary(night_time),
      late_time: formatted_duration_salary(late_time),
      leave_early_time: formatted_duration_salary(leave_early_time),
      ot45_time: formatted_duration_salary(ot45_time),
      ot60_time: formatted_duration_salary(ot60_time),
      basic_salary_price: format_currency(basic_salary_price.to_i + paid_amount.to_i, false),
      overtime_amount: format_currency(overtime_amount, false),
      working_night_amount: format_currency(working_night_amount, false),
      ot45_amount: format_currency(ot45_amount, false),
      ot60_amount: format_currency(ot60_amount, false),
      payment_field_1: format_currency(payment_field_1, false),
      payment_field_2: format_currency(payment_field_2, false),
      payment_field_3: format_currency(payment_field_3, false),
      payment_field_4: format_currency(payment_field_4, false),
      payment_field_5: format_currency(payment_field_5, false),
      payment_field_8: format_currency(payment_field_8, false),
      payment_field_9: format_currency(payment_field_9, false),
      payment_field_10: format_currency(payment_field_10, false),
      deduction_field_1: format_currency(deduction_field_1, false),
      deduction_field_2: format_currency(deduction_field_2, false),
      deduction_field_3: format_currency(deduction_field_3, false),
      deduction_field_4: format_currency(deduction_field_4, false),
      deduction_field_5: format_currency(deduction_field_5, false),
      deduction_field_6: format_currency(deduction_field_6, false),
      deduction_field_7: format_currency(deduction_field_7, false),
      deduction_field_8: format_currency(deduction_field_8, false),
      deduction_field_9: format_currency(deduction_field_9, false),
      deduction_field_10: format_currency(deduction_field_10, false),
      deduction_field_11: format_currency(deduction_field_11, false),
      deduction_field_12: format_currency(deduction_field_12, false),
      deduction_field_13: format_currency(deduction_field_13, false),
      deduction_field_21: format_currency(deduction_field_21, false),
      payment_total_amount: format_currency(payment_total_amount, false),
      deduction_total_amount: format_currency(deduction_total_amount, true),
      payment_subtraction_amount: format_currency(payment_subtraction_amount, false),
      fixed_income_tax_reduction_max: format_currency(fixed_income_tax_reduction_max, false),
      fixed_income_tax_reduction_amount: format_currency(fixed_income_tax_reduction_amount, false),
      fixed_income_tax_reduction_unpaid: format_currency(fixed_income_tax_reduction_unpaid, false)
    }
  end

  private

  def format_currency amount, is_accept_zero_val
    ApplicationController.helpers.format_currency(amount, is_accept_zero_val)
  end

  def without_zero_value value
    ApplicationController.helpers.without_zero_value(value)
  end

  def formatted_duration_salary value
    ApplicationController.helpers.formatted_duration_salary(value)
  end
end
