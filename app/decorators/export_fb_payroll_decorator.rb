module ExportFBPayrollDecorator
  extend ActiveSupport::Concern

  FIELD_DATA_FB = {
    0 => :staff_staff_number,
    1 => :staff_account_name,
    2 => :staff_current_department_name,
    3 => :staff_bank_code,
    4 => :staff_bank_name,
    5 => :staff_bank_branch_code,
    6 => :staff_bank_branch_name,
    7 => :staff_account_type_before_type_cast,
    8 => :staff_transfer_account_number,
    9 => :convert_account_name_kana,
    10 => :staff_transfer_account_name,
    11 => :transfered_amount,
    12 => :request_amount,
    13 => :usage_fee,
    14 => :transfer_fee
  }
  FIXED_0 = 0

  def row_data_fb
    FIELD_DATA_FB.values.map{|method| self.send(method)}
  end

  class_methods do
    def label_i18n_fb
      I18n.t("admin.payroll_request.export_csv.label_name").values
    end
  end

  private
  def convert_account_name_kana
    Unicode::Japanese.z2h(staff_transfer_account_name_kana)
  end

  def usage_fee
    FIXED_0
  end

  def request_amount
    payment_subtraction_amount.to_i + transfer_fee.to_i
  end

  def transfered_amount
    payment_subtraction_amount.to_i
  end
end
