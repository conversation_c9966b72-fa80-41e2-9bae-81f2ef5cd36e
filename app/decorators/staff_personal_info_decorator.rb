module StaffPersonalInfoDecorator
  STAFF_PERSONAL_ATTRIBUTES = {
    staff: [:birthday, :postal_code, :street_number, :house_number,
    :building, :address_kana, :tel, :home_tel, :emergency_postal_code,
    :emergency_city, :emergency_street_number, :emergency_house_number, :emergency_building,
    :emergency_name_kana, :emergency_tel, :emergency_home_tel, :residence_number,
    :emergency_name, :residence_img_front, :residence_img_back,
    :certificate_img, :email],
    account: [:email],
    staff_education_backgrounds: [:entry_school_date, :graduation_date,
      :education_background_type, :graduation_type, :school_name, :study_department_name,
      :study_subject_name, :study_time_type, :note],
    staff_account_transfer: [:bank_branch_id, :bank_id, :account_type, :account_number,
      :account_name, :account_name_kana],
    staff_salary: [:is_spouse, :insurance_number, :basic_pension_number, :employment_insurance_number,
      :copied_employment_insurance_number],
    staff_mail_histories: [:destination_email]
  }
end
