module Arrangements::BaseFormatterRawQuery
  def working_time
    TimeRangeFormatting.hour_to_hour(self[:working_started_at], self[:working_ended_at])
  end

  def order_created_date
    DateTimeFormatting.full_date_and_day(self[:order_created_at]&.in_time_zone)
  end

  def order_created_full_day
    DateTimeFormatting.full_date_time(self[:order_created_at]&.in_time_zone)
  end

  def working_day_full_date
    DateTimeFormatting.full_date_and_day(self[:working_started_at])
  end

  def order_case_public_type_id
    OrderCase.public_type_ids.key(self[:public_type_id])
  end

  def order_case_segment_id
    OrderCase.segment_ids.key(self[:order_case_segment_id])
  end

  def order_case_invoice_target
    OrderCase.invoice_targets.key(self[:order_case_invoice_target])
  end

  def order_segment_id
    Order.order_segment_ids.key(self[:order_segment_id])
  end

  def arrange_payment_adjusment_type_id
    ArrangePayment.adjusment_type_ids.key(self[:arrange_payment_adjusment_type_id])
  end

  def arrange_billing_adjusment_type_id
    ArrangeBilling.adjusment_type_ids.key(self[:arrange_billing_adjusment_type_id])
  end

  def location_pos_type_id
    Location.pos_type_ids.key(self[:order_location_pos_type_id])
  end

  def work_achievement_working_time_status_id
    WorkAchievement.working_time_status_ids.key(self[:working_time_status_id])
  end

  def order_location_id
    self[:order_location_id]
  end

  def location_job_category_type
    LocationJobCategory.job_category_ids.key(self[:location_job_category_type]) if
      self[:location_job_category_type]
  end

  def job_category_name
    self[:job_category_name]
  end

  def order_case_status
    status_id = order_portion_status_id

    return status_id if status_id != "not_arrange"
    return "finish_recruiting" if self.finish_apply?
    return "has_apply" if order_case_remained_apply > 0

    order_case_public_type_id
  end

  def status_staff_evaluation
    return :has_staff if self.staff_id

    return :waiting_arrange if order_case_remained_apply > 0
    return :arrange if order_case_remained_apply == 0

    :has_staff
  end

  def portion_arranged?
    order_portion_status_id == "arranged"
  end

  def portion_canceled?
    order_portion_status_id == "cancel"
  end

  def portion_cancel_after_arrange_has_insurance?
    order_portion_status_id == "cancel_after_arrange_has_insurance"
  end

  def working_need_talking
    return "" unless portion_arranged?

    self.need_talking_count
  end

  def working_has_problem
    return "" unless portion_arranged?

    self.has_problem_count
  end

  def regular_order?
    order_segment_id == "regular_order"
  end

  def location_expired?
    return false if order_location_id.nil? || self[:location_closed_at].nil?

    closed_at_date = self[:location_closed_at].in_time_zone.to_date
    closed_at_date < self[:order_branch_started_at]&.in_time_zone&.to_date
  end

  def staff_birthday
    self[:staff_birthday]&.in_time_zone&.beginning_of_day
  end

  def staff_age
    return unless staff_birthday

    ((ServerTime.now - staff_birthday) / 1.year).floor
  end

  def staff_gender_id
    Staff.gender_ids.key(self[:staff_gender_id])
  end

  def staff_display_workable_time
    workable_time = self[:staff_workable_time]
    return unless workable_time

    is_foreign = self[:staff_residence_status].blank? && self[:staff_nationality] == Type::JAPAN_ID
    return if is_foreign

    I18n.t("admin.staff_check_item.in_workable_time", hours: workable_time)
  end

  def staff_is_working_car
    BooleanConverting.convert_from_raw_query(self[:staff_is_working_car])
  end

  def staff_new_pos_training
    BooleanConverting.convert_from_raw_query(self[:staff_new_pos_training])
  end

  def staff_info
    return unless self[:staff_id]

    {
      id: self[:staff_id],
      gender_id: staff_gender_id,
      tel: self[:staff_tel],
      home_tel: self[:staff_home_tel],
      email: self[:staff_email],
      level_up_training: staff_level_up_training,
      new_pos_training: staff_new_pos_training,
      total_work_experience: self[:staff_total_work_experience],
      account_email: self[:staff_account_email],
      account_name: self[:staff_account_name],
      account_name_kana: self[:staff_account_name_kana],
      age: staff_age,
      uniform_size: self[:staff_uniform_size],
      display_workable_time: staff_display_workable_time,
      is_working_car: staff_is_working_car
    }
  end

  def staff_avg_evaluation
    return unless self[:total_staff_evaluation]

    (self[:total_staff_evaluation].to_f / StaffEvaluation::REVIEW_INDEX.size).round(1)
  end

  def order_location_stations_1_station_name
    return unless self[:station_1_name]

    "#{self[:station_1_name]}#{I18n.t('staff.order_cases.station')}"
  end

  def business_name
    return job_category_name unless location_job_category_type

    I18n.t("admin.location_job_category.job_category_ids.#{location_job_category_type}")
  end

  def payment_start_time
    DateTimeFormatting.hour_minute(self[:work_achievements_working_started_at]&.in_time_zone)
  end

  def payment_end_time
    DateTimeFormatting.hour_minute(self[:work_achievements_working_ended_at]&.in_time_zone)
  end

  def order_portion_case_started_at
    self[:order_portion_case_started_at]&.in_time_zone
  end

  def order_portion_case_ended_at
    self[:order_portion_case_ended_at]&.in_time_zone
  end

  def billing_started_at
    DateTimeFormatting.hour_minute(self[:billing_started_at]&.in_time_zone)
  end

  def billing_ended_at
    DateTimeFormatting.hour_minute(self[:billing_ended_at]&.in_time_zone)
  end

  def location_is_store_parking_area_usable
    BooleanConverting.convert_from_raw_query(self[:is_store_parking_area_usable])
  end

  def order_location_code
    self[:order_location_code]
  end

  def location_name
    self[:location_name]
  end

  def order_corporation_id
    self[:order_corporation_id]
  end

  def order_corporation_full_name
    self[:order_corporation_full_name]
  end

  def order_real_pic_department_name
    self[:order_real_pic_department_name]
  end

  def staff_department_name_at_working_time
    self[:staff_department_name_at_working_time]
  end

  def order_note
    self[:order_note]
  end

  def order_case_special_offer_note
    self[:order_case_special_offer_note]
  end

  def location_evaluation_val
    self[:location_evaluation_val]
  end

  def staff_home_station_station_name
    return unless staff_home_station_name

    "#{staff_home_station_name}#{I18n.t('staff.order_cases.station')}"
  end

  def deleted_user_order_pic?
    self[:deleted_user_order_pic].present?
  end

  def is_lawson
    BooleanConverting.convert_from_raw_query(self[:is_lawson])
  end

  def payment_requested
    self[:payment_requested_arrangement_id].present?
  end

  private

  def order_portion_status_id
    OrderPortion.status_ids.key(self[:order_portion_status_id])
  end

  def staff_level_up_training
    BooleanConverting.convert_from_raw_query(self[:staff_level_up_training])
  end

  def staff_home_station_name
    self[:staff_home_station_name]
  end
end
