module Arrangements
  module AdminExportDecorator
    include Arrangements::BaseFormatterRawQuery

    STATUS_SHOW_ARRANGE_BILLING = %w(arranged cancel_after_arrange_has_insurance
      cancel_after_arrange_no_insurance absence)
    STATUS_NOT_SHOW_WORKING_TIME = %w(cancel_after_arrange_no_insurance absence absence_has_alternative)

    COLUMNS_ATTR = %i(staff_number staff_department_name_at_working_time staff_total_work_experience
      order_corporation_full_name location_name payment_basic_unit_price
      payment_ot_unit_price payment_night_unit_price
      payment_unit_price_addition payment_urgent_unit_price_addition payment_bonus_unit_price payment_total_amount
      payment_basic_amount payment_ot_amount payment_ot2_amount payment_night_amount payment_field_1
      payment_field_4 payment_field_5 payment_subtraction_amount billing_note staff_home_tel staff_tel
      staff_account_email staff_uniform_size store_note arrange_comment note order_note
      order_case_special_offer_note payment_field_9)

    PAYMENT_TIME_FIELDS = %i(payment_actual_working_time payment_basic_time payment_ot1_time payment_ot2_time
      payment_night_time payment_basic_break_time payment_night_break_time payment_late_time
      payment_leave_early_time)

    BILLING_TIME_FIELDS = %i(billing_actual_working_time billing_basic_time billing_ot_time billing_night_time
      billing_basic_break_time billing_night_break_time billing_late_time billing_leave_early_time)

    BILLING_REST_TIME_FIELDS = %i(billing_rest1_started_at billing_rest2_started_at billing_rest3_started_at
      billing_rest1_ended_at billing_rest2_ended_at billing_rest3_ended_at)

    BILLING_ATTRS = %i(billing_basic_unit_price billing_unit_price_addition billing_ot_unit_price
      billing_night_unit_price billing_total_amount billing_basic_amount billing_ot_amount
      billing_night_amount billing_field_1 billing_field_2 billing_field_3 billing_field_4
      billing_field_5 billing_field_6 billing_field_7 billing_tax_exemption billing_other_addition_fee)

    IGNORE_METHODS = %i(is_staff_apply_changeable_time)

    def row_data methods, additional_data = {}
      methods.map do |method|
        if COLUMNS_ATTR.include?(method)
          self[method]
        elsif PAYMENT_TIME_FIELDS.include?(method)
          show_payment_time(method)
        elsif BILLING_TIME_FIELDS.include?(method)
          show_billing_time(method)
        elsif BILLING_REST_TIME_FIELDS.include?(method)
          billing_rest_time_format(method)
        elsif BILLING_ATTRS.include?(method)
          show_billing_attributes(method)
        elsif IGNORE_METHODS.include?(method)
          self.send(method, additional_data)
        else
          self.send(method)
        end
      end
    end

    private

    def working_day
      self.working_day_full_date
    end

    def working_time_from
      return unless self.working_time

      self.working_time.split("~")[0]
    end

    def working_time_to
      return unless self.working_time

      self.working_time.split("~")[1]
    end

    def payment_adjusment_type_id
      self[:arrange_payment_adjusment_type_id]&.to_s
    end

    def urgent_status
      label = I18n.t("admin.arrangements.index.labels")
      urgent_status = ""
      urgent_status = label[:high_priority] if
        BooleanConverting.convert_from_raw_query(self[:order_case_is_special_offer])

      return urgent_status unless
        BooleanConverting.convert_from_raw_query(self[:order_case_is_urgent])

      urgent_status.present? ? [urgent_status, label[:urgent]].join("、") : label[:urgent]
    end

    def location_avg_evaluation
      return I18n.t("admin.arrangements.staff_evaluation.blank") unless portion_arranged?

      evaluation = self.location_evaluation&.evaluation.to_i
      return I18n.t("admin.arrangements.staff_evaluation.not_yet") if evaluation.zero?

      evaluation
    end

    def working_prepare_status
      return unless portion_arranged?

      status = self[:is_prepared] ? :done : :not_yet
      I18n.t("admin.arrangements.working_prepare.#{status}")
    end

    def arrived_status
      return unless portion_arranged?

      status = self[:is_arrived] ? :done : :not_yet
      I18n.t("admin.arrangements.working_prepare.#{status}")
    end

    def adjusment_type_id_export_i18n
      adjusment_type_id = arrange_payment_adjusment_type_id
      return unless adjusment_type_id

      I18n.t("enum_label.arrange_payment.adjusment_type_ids.#{adjusment_type_id}")
    end

    def work_achievement_rest1_time_from
      return if not_show_working_time?

      DateTimeFormatting.hour_minute(self[:work_achievements_rest1_started_at]&.in_time_zone)
    end

    def work_achievement_rest1_time_to
      return if not_show_working_time?

      DateTimeFormatting.hour_minute(self[:work_achievements_rest1_ended_at]&.in_time_zone)
    end

    def work_achievement_rest2_time_from
      return if not_show_working_time?

      DateTimeFormatting.hour_minute(self[:work_achievements_rest2_started_at]&.in_time_zone)
    end

    def work_achievement_rest2_time_to
      return if not_show_working_time?

      DateTimeFormatting.hour_minute(self[:work_achievements_rest2_ended_at]&.in_time_zone)
    end

    def work_achievement_rest3_time_from
      return if not_show_working_time?

      DateTimeFormatting.hour_minute(self[:work_achievements_rest3_started_at]&.in_time_zone)
    end

    def work_achievement_rest3_time_to
      return if not_show_working_time?

      DateTimeFormatting.hour_minute(self[:work_achievements_rest3_ended_at]&.in_time_zone)
    end

    def arrange_billing_adjusment_type_id_export_i18n
      adjusment_type_id = arrange_billing_adjusment_type_id
      return unless adjusment_type_id

      I18n.t("enum_label.arrange_payment.adjusment_type_ids.#{adjusment_type_id}")
    end

    def staff_gender_id_export_i18n
      return unless self[:staff_gender_id]

      I18n.t "admin.staff.search_detail.#{staff_gender_id}"
    end

    def order_location_is_store_parking_area_usable_export_i18n
      return unless self[:is_store_parking_area_usable]

      I18n.t("corporation.order.store_parking.#{location_is_store_parking_area_usable}")
    end

    def total_amount_not_taxable
      return unless is_show_billing_field?

      self[:billing_tax_exemption]
    end

    def order_location_pos_type_id_export_i18n
      return unless location_pos_type_id

      I18n.t("enum_label.location.pos_type_ids.#{location_pos_type_id}")
    end

    def is_store_computer_int
      is_store_computer_account = self[:pic_migration_location_id].present? ||
        (self[:pic_location_code].present? && self[:pic_email].blank?)

      is_store_computer_account ? 1 : 0
    end

    def order_case_segment_name
      return "" unless order_case_segment_id

      I18n.t("corporation.order.segment_ids.#{order_case_segment_id}")
    end

    def is_time_changable
      self[:order_branches_is_time_changable].to_i
    end

    def show_billing_attributes field_name
      return unless is_show_billing_field?

      self[field_name]
    end

    def show_payment_time field_name
      return if not_show_working_time?

      TimeConverting.number_to_hour_minute(self[field_name])
    end

    def show_billing_time field_name
      TimeConverting.number_to_hour_minute(self[field_name])
    end

    def billing_rest_time_format field_name
      return unless self[field_name]

      DateTimeFormatting.hour_minute(self[field_name].in_time_zone)
    end

    def not_show_working_time?
      STATUS_NOT_SHOW_WORKING_TIME.include?(self.display_status_id)
    end

    def is_show_billing_field?
      STATUS_SHOW_ARRANGE_BILLING.include?(self.display_status_id)
    end

    def location_evaluation_comment
      return "" unless portion_arranged?

      self.location_evaluation&.evaluation_comment
    end

    def staff_is_working_car_export_i18n
      return unless self.staff_id

      I18n.t("corporation.order.store_parking.#{staff_is_working_car}")
    end

    def staff_level_up_training_export_i18n
      return unless self.staff_id

      I18n.t("admin.staff.display_level_up_training.#{staff_level_up_training}")
    end

    def staff_new_pos_training_export_i18n
      return unless self.staff_id

      I18n.t("admin.staff.display_level_up_training.#{staff_new_pos_training}")
    end

    def order_case_status_export_i18n
      return unless order_case_status

      I18n.t "model_status.order_portion.status.#{order_case_status}"
    end

    def status_staff_evaluation_i18n
      return unless status_staff_evaluation == :has_staff

      if self[:recent_staff_level]
        staff_level = StaffLevel.levels.key(self[:recent_staff_level])
        level_name = I18n.t("enum_label.staff_level.levels.#{staff_level}")[0]
      end

      if self[:recent_staff_rank]
        staff_rank = StaffRank.ranks.key(self[:recent_staff_rank])
        rank_name = I18n.t("enum_label.staff_rank.ranks.#{staff_rank}")[0]
      end

      [self[:staff_account_name], "(", level_name, rank_name, ")"].join
    end

    def staff_account_name_kana
      Unicode::Japanese.z2h(self[:staff_account_name_kana])
    end

    def search_yahoo_url
      return unless self[:home_station_name]

      home_station_name = "#{self[:home_station_name]}#{I18n.t('staff.order_cases.station')}"
      start_time = order_portion_case_started_at
      minutes = start_time.strftime(Settings.date.minute).chars
      station_1_name = order_location_stations_1_station_name

      params = {
        from: home_station_name,
        to: station_1_name,
        y: start_time.strftime("%Y"),
        m: start_time.strftime("%m"),
        d: start_time.strftime("%d"),
        hh: start_time.strftime("%H"),
        m2: minutes[1],
        m1: minutes[0],
        type: 1,
        ticket: "normal",
        expkind: 1,
        ws: 3,
        s: 0,
        al: 1,
        shin: 1,
        ex: 1,
        hb: 1,
        lb: 1,
        sr: 1
      }
      params_list = []
      params.each do |key, value|
        params_list.push("#{key}=#{value}")
      end
      "https://transit.yahoo.co.jp/search/result?#{params_list.join('&')}"
    end

    def payment_end_time
      return if not_show_working_time?

      super
    end

    def payment_start_time
      return if not_show_working_time?

      super
    end

    def work_achievement_working_time_status_id_export_i18n
      I18n.t("enum_label.work_achievement.display_working_time_status_ids.#{work_achievement_working_time_status_id}")
    end

    def staff_avg_evaluation
      return I18n.t("admin.arrangements.staff_evaluation.blank") unless portion_arranged?

      avg = super
      return I18n.t "admin.arrangements.staff_evaluation.not_yet" unless avg

      avg
    end

    def is_staff_apply_changeable_time additional_data
      return 0 if additional_data[:ids_has_staff_changeable_time].blank?

      additional_data[:ids_has_staff_changeable_time].include?(self.id) ? 1 : 0
    end
  end
end
