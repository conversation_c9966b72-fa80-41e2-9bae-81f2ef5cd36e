module OrderDecorator
  CREATOR_TYPES = {admin: 1, owner: 2}

  def violation_day_has_changed
    return I18n.t("admin.order.details.no_data") if self.is_violation_day_unchanged.nil?
    return I18n.l(self.violation_day, format: Settings.date.formats) if self.is_violation_day_unchanged

    I18n.t("admin.order.details.changed")
  end

  def location_survey_has_changed
    return I18n.t("admin.order.details.no_data") if self.is_location_survey_unchanged.nil?
    return I18n.t("admin.order.details.unchanged") if self.is_location_survey_unchanged

    I18n.t("admin.order.details.changed")
  end

  def unchanged_confirmed_date
    return false if self.is_violation_day_unchanged.nil? &&
      self.is_location_survey_unchanged.nil?

    true
  end

  def has_confirmed_not_one_man_operation
    return I18n.t("admin.order.details.no_data") if self.is_not_one_man_operation.nil?

    self.is_not_one_man_operation? ? I18n.t("admin.order.details.confirmed") :
      I18n.t("admin.order.details.not_confirmed")
  end

  private
  def overall_start_date
    overall_started_at.strftime Settings.datetime.formats
  end

  def overall_end_date
    overall_ended_at.strftime Settings.date.formats
  end

  def order_date
    return if self.draft?

    created_at.strftime Settings.datetime.formats
  end

  def violation_date
    corporation_group.violation_day&.strftime Settings.date.formats
  end

  def status_before_cast
    read_attribute_before_type_cast(:status_id).to_s
  end

  def real_order_no
    self.draft? ? 0 : self.id
  end

  def export_status
    self.is_exported ? "1" : "0"
  end

  def order_segment_before_cast
    read_attribute_before_type_cast(:order_segment_id).to_s
  end

  def creator_type
    self.created_admin_id.present? ? CREATOR_TYPES[:admin] : CREATOR_TYPES[:owner]
  end

  alias real_order_date order_date
end
