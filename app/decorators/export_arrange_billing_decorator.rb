module ExportArrangeBillingDecorator
  extend ActiveSupport::Concern
  include ActionView::Helpers::Number<PERSON>elper

  ALL_ELEMENTS = 36
  REGISTRATION_ALL_ELEMENTS = 15
  DEFAULT_INITIAL_OPERATION = 10_000
  FORMAT_TIME_FIELDS = %i(billing_basic_break_time billing_ot_break_time billing_night_break_time
    billing_basic_time billing_ot_time billing_night_time billing_late_time
    billing_leave_early_time)
  ROW_CALCULATE = {
    6 => "G",
    7 => "H",
    8 => "I",
    22 => "W",
    23 => "X",
    24 => "Y",
    25 => "Z",
    26 => "AA",
    27 => "AB",
    28 => "AC",
    29 => "AD",
    30 => "AE",
    31 => "AF",
    32 => "AG",
    33 => "AH",
    34 => "AI"
  }
  ROW_CURRENCY = [4, 7] + (19..34).to_a
  FIELD_DATA = {
    0 => :order_location_code,
    1 => :address_prefecture_name,
    2 => :order_location_name,
    3 => :order_corporation_full_name,
    4 => :billing_started_date,
    5 => :staff_account_name,
    6 => :billing_total_amount,
    7 => :billing_tax_amount,
    8 => :consumption_tax,
    9 => :billing_started_time,
    10 => :billing_ended_time,
    11 => :rest1_duration,
    12 => :rest2_duration,
    13 => :rest3_duration,
    14 => :billing_basic_time,
    15 => :billing_ot_time,
    16 => :billing_night_time,
    17 => :billing_late_time,
    18 => :billing_leave_early_time,
    19 => :billing_basic_unit_price,
    20 => :billing_ot_unit_price,
    21 => :late_night_charge,
    22 => :billing_basic_amount,
    23 => :billing_ot_amount,
    24 => :billing_night_amount,
    25 => :billing_field_1,
    26 => :billing_field_2,
    27 => :billing_field_3,
    28 => :billing_field_4,
    29 => :billing_field_5,
    30 => :billing_field_6,
    31 => :billing_field_7,
    32 => :billing_tax_exemption,
    33 => :billing_other_addition_fee,
    34 => :consumption_tax,
    35 => :billing_note
  }
  REGISTRATION_FIELD_DATA = {
    0 => :order_location_name,
    1 => :billing_started_date,
    2 => :staff_number_export,
    3 => :staff_account_name,
    4 => :staff_account_name_kana,
    5 => :billing_started_time,
    6 => :billing_ended_time,
    7 => :rest1_duration,
    8 => :rest2_duration,
    9 => :rest3_duration,
    10 => :billing_basic_time,
    11 => :billing_ot_time,
    12 => :billing_night_time,
    13 => :billing_late_time,
    14 => :billing_leave_early_time
  }

  def row_data registration_history, is_haken
    count = registration_history ? REGISTRATION_ALL_ELEMENTS : ALL_ELEMENTS
    field_data = registration_history ? REGISTRATION_FIELD_DATA : FIELD_DATA
    row_array = Array.new(count, nil)
    field_data.each do |key, method|
      value = self.send(method)
      value = minutes_to_times(value) if FORMAT_TIME_FIELDS.include?(method)
      row_array[key] = value
    end
    if registration_history == Staff::REGIST_HISTORY_JPTIP
      row_array[15] = is_haken ? DEFAULT_INITIAL_OPERATION : nil
    end
    row_array
  end

  class_methods do
    def label_i18n registration_history
      i18n = I18n.t("admin.arrange_billings.export")
      i18n_export = registration_history ? i18n[:registration_label] : i18n[:label]
      header_label = i18n_export.values
      header_label.push(i18n[:initial_operation]) if registration_history == Staff::REGIST_HISTORY_JPTIP
      header_label
    end

    def sheet_name registration_history
      return I18n.t("admin.arrange_billings.export.sheet_name") unless registration_history

      I18n.t("admin.arrange_billings.registration_history.history_#{registration_history}")
    end

    def row_caculate_data size
      row_array = Array.new(ALL_ELEMENTS, nil)
      size += 1
      ROW_CALCULATE.each do |key, column_name|
        row_array[key] = "=SUM(#{column_name}2:#{column_name}#{size})"
      end
      row_array
    end

    def row_caculate_style highlight
      row_array = Array.new(ALL_ELEMENTS, nil)
      ROW_CALCULATE.map{|key, _v| row_array[key] = highlight}
      row_array
    end

    def row_style border_cell, currency_cell, registration_history
      if registration_history
        row_array = Array.new(REGISTRATION_ALL_ELEMENTS, border_cell)
        row_array[15] = currency_cell
      else
        row_array = Array.new(ALL_ELEMENTS, border_cell)
        ROW_CURRENCY.each{|num| row_array[num] = currency_cell}
      end
      row_array
    end
  end

  private

  def billing_tax_amount
    billing_tax_exemption
  end

  def staff_number_export
    " #{staff_number}"
  end

  def consumption_tax
    tax_rate = Tax.tax_rate_before_date(get_billing_started_at.to_date).first.tax_rate.to_f / 100
    (billing_total_amount.to_i * tax_rate).round
  end

  # def billing_started_date
  #   billing_started_at&.strftime(Settings.date.formats)
  # end

  def billing_started_time
    get_billing_started_at&.strftime(Settings.time.formats)
  end

  def billing_ended_time
    get_billing_ended_at&.strftime(Settings.time.formats)
  end

  def minutes_to_times minutes
    return unless minutes

    [minutes / 60, minutes % 60].map{|t| t.to_s.rjust(2, "0")}.join(":")
  end

  def address_prefecture_name
    corporation_group = arrangement.order.corporation_group
    corporation_group&.address_prefecture_name
  end

  def late_night_charge
    billing_night_unit_price.to_i - billing_basic_unit_price.to_i
  end

  Arrangement::REST_TIMES.each do |time|
    define_method "rest#{time}_duration" do
      rest_start = self["billing_rest#{time}_started_at"] || arrangement["rest#{time}_started_at"]
      rest_end = self["billing_rest#{time}_ended_at"] || arrangement["rest#{time}_ended_at"]
      return unless rest_start && rest_end

      Time.at(rest_end - rest_start).utc.strftime Settings.time.formats
    end
  end
end
