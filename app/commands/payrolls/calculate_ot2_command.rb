class Payrolls::CalculateOt2Command
  attr_reader :staff_ids, :start_date, :end_date

  def initialize staff_ids, start_date, end_date
    @staff_ids = staff_ids
    @start_date = start_date
    @end_date = end_date
  end

  def perform
    arr_ids = Arrangement.joins(:work_achievement).where(staff_id: staff_ids)
      .approved_work_achivement_for_payroll_calculation(start_date, end_date)
      .pluck(:id)
    arrange_payments = ArrangePayment.by_arrangement_ids(arr_ids)
    return if arrange_payments.blank?

    ActiveRecord::Base.transaction do
      arrange_payments.sort_by{|payment| payment.arrangement.working_started_at}.each do |payment|
        payment.calculate_trigger :arrange_payment, %i(ot2_field)
      end
    end
  end
end
