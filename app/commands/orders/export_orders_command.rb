module Orders
  class ExportOrdersCommand
    attr_reader :order_ids, :jid

    CSV_HEADERS = I18n.t("admin.order.export").values

    def initialize order_ids, jid
      @order_ids = order_ids
      @jid = jid
    end

    def perform
      return if order_ids.blank?

      order_cases = OrderCase.includes(order: [:corporation, :location])
        .select(:id, :order_id, :case_started_at, :case_ended_at, :total_portion)
        .where(order_id: order_ids)

      file_name = "order_export_#{jid}.csv"
      file_path = Rails.root.join("tmp", file_name)
      File.open(file_path, "w:shift_jis", invalid: :replace, undef: :replace, replace: "?") do |file|
        file_csv = CSV.generate do |csv|
          csv << CSV_HEADERS
          order_cases.find_each(batch_size: 2000) do |order_case|
            csv << get_row_data(order_case)
          end
        end
        file.write(file_csv)
      end

      return unless Settings.environment_can_use_aws.include? Rails.env

      s3_folder = Settings.aws.s3.folders.admin_exports
      S3_BUCKET.object("#{s3_folder}/#{file_name}").upload_file(file_path)
    end

    private

    def get_row_data order_case
      order = order_case.order
      unchanged_checked_date = order.unchanged_confirmed_date ? order.order_created_at : nil
      corporation_name = order.corporation.full_name
      location_name = order.location.name

      [
        order.corporation_id,
        corporation_name,
        order.location_id,
        location_name,
        order.id_with_leading_zeros,
        order_case.working_date_time,
        order_case.total_portion,
        order.order_created_at,
        unchanged_checked_date,
        order.violation_day_has_changed,
        order.location_survey_has_changed
      ]
    end
  end
end
