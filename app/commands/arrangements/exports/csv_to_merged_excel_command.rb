module Arrangements::Exports
  class CsvToMergedExcelCommand
    CURRENCY_FORMAT = "#,##0"
    FONT = {type: "Arial", size: 11}

    attr_reader :jid, :columns

    def initialize jid, columns
      @jid = jid
      @columns = columns
    end

    def perform
      tracking_export = RedisModels::TrackingExport.new
      final_job_progress = tracking_export.get_final(jid)
      return if final_job_progress["current"].to_i < final_job_progress["total"].to_i

      batches_cache_data = tracking_export.get_all_batch_data(jid)
      batches_cache_data = batches_cache_data.sort_by{|batch| batch[/data_batch_(\d+)/, 1].to_i}

      file_name = Rails.root.join("tmp", "arrangement_export_#{jid}.xlsx")

      xlsx_package = FastExcel.open(file_name,
        constant_memory: true)
      xlsx_package.default_format.set(
        font_size: FONT[:size],
        font_family: FONT[:type]
      )
      currency_format = xlsx_package.number_format CURRENCY_FORMAT
      worksheet = xlsx_package.add_worksheet(Settings.export.file_name.arrangement)

      headers = get_headers(columns)
      worksheet.write_row(0, headers)

      Arrangement::FORMAT_CURRENCY_ROWS.each do |row_range|
        worksheet.set_column row_range[0], row_range[1], nil, currency_format
      end

      batches_cache_data.each do |batch_data_key|
        data_in_batches = tracking_export.get_cache_data(batch_data_key)
        data_in_batches.each do |row|
          worksheet.append_row(JSON.parse(row))
        end
      end

      if Settings.environment_can_use_aws.include? Rails.env
        s3_folder = Settings.aws.s3.folders.admin_exports
        S3_BUCKET.object("#{s3_folder}/arrangement_export_#{jid}.xlsx")
          .put body: xlsx_package.read_string
      else
        xlsx_package.close
      end

      tracking_export.del_all(jid)
    rescue JSON::ParserError
      RedisModels::TrackingExport.new.del_all(jid)
    end

    private

    def get_headers chosen_columns
      I18n.t("admin.arrangements.index.export.columns").values.values_at(*chosen_columns)
    end
  end
end
