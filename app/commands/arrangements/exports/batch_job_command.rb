module Arrangements::Exports
  class BatchJobCommand
    attr_reader :columns, :batch_number, :jid

    def initialize columns, batch_number, jid
      @columns = columns
      @batch_number = batch_number
      @jid = jid
    end

    def perform
      tracking_export = RedisModels::TrackingExport.new
      arrangement_ids = tracking_export.get(jid, batch_number)
      return if arrangement_ids.empty? || columns.empty?

      export_query = Arrangements::ExportArrangementsQuery.new(columns)
      arrangements = export_query.execute(arrangement_ids).to_a

      additional_data = {
        ids_has_staff_changeable_time: Arrangements::GetStaffApplyChangeableTimeQuery.execute(arrangement_ids)
      }

      methods = get_methods(columns)

      row_data = arrangements.map do |arrangement|
        data = arrangement.extend(Arrangements::AdminExportDecorator).row_data(methods, additional_data)
        data.to_json
      end

      tracking_export.cache_data_export(jid, batch_number, row_data)

      tracking_export.increase_progress(jid, arrangement_ids.size)
      tracking_export.del(jid, batch_number)
      final_job_progress = tracking_export.get_final(jid)

      if final_job_progress["current"].to_i >= final_job_progress["total"].to_i
        ExportJobs::ArrangementMergedExcelWorker.perform_async(jid, columns)

        return
      end

      next_job = batch_number + 1
      ExportJobs::ExportArrangementBatchWorker.perform_async(columns, next_job, jid)
    end

    private

    def get_methods columns
      Arrangements::ExportArrangementsQuery::FIELD_DATA.slice(*columns).values
    end
  end
end
