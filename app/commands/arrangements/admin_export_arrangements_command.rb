module Arrangements
  class AdminExportArrangementsCommand
    LARGE_PART = 3_000
    MEDIUM_PART = 1_500
    SMALL_PART = 1_000
    FIRST_JOB = 1

    def initialize search_params, jid
      @search_params = search_params
      @jid = jid
    end

    def perform chosen_columns = (0..124).to_a
      formatted_params = Arrangements::AdminFilterParamsFormatter.new(@search_params).execute
      arrangements = Arrangements::AdminFilterQuery.new(**formatted_params).execute

      if chosen_columns.size <= 10
        batch_size = LARGE_PART
      elsif chosen_columns.size <= 50
        batch_size = MEDIUM_PART
      else
        batch_size = SMALL_PART
      end

      arrangement_ids = arrangements.pluck(:id)
      return if arrangement_ids.empty?

      batch_number = 0
      RedisModels::TrackingExport.new.set_final(@jid, arrangement_ids.size)

      arrangement_ids.each_slice(batch_size) do |batch_ids|
        next if batch_ids.empty?

        batch_number += 1
        RedisModels::TrackingExport.new.set(@jid, batch_number, batch_ids)
      end

      ExportJobs::ExportArrangementBatchWorker.perform_async(chosen_columns, FIRST_JOB, @jid)

      @jid
    end
  end
end
