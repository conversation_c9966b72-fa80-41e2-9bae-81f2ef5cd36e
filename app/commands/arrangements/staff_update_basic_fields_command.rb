module Arrangements
  class StaffUpdateBasicFieldsCommand
    attr_reader :staff, :arrangement_id, :update_params

    def initialize staff, arrangement_id, update_params
      @staff = staff
      @arrangement_id = arrangement_id
      @update_params = update_params.symbolize_keys
    end

    def perform
      validate_params!
      return false if update_params.blank?

      arrangement = staff.arrangements.find(arrangement_id)
      counter_field = Arrangement::COUNTER_FIELDS.find{|attr| update_params[attr]}
      return update_counter_field(arrangement, counter_field) if counter_field

      update_basic_fields(arrangement)
    end

    private

    def validate_params!
      update_params.slice!(*Arrangement::UPDATABLE_ATTRS)
    end

    def update_counter_field arrangement, counter_field
      Arrangement.increment_counter(counter_field, arrangement)

      true
    end

    def update_basic_fields arrangement
      ActiveRecord::Base.transaction do
        arrangement.update!(update_params)
        Staff.create_first_contract [arrangement] if update_params[:is_prepared]
      end

      true
    end
  end
end
