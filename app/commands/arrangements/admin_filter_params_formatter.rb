class Arrangements::AdminFilterParamsFormatter
  attr_reader :params

  def initialize params
    @params = params
  end

  def execute
    hash_params = ActiveSupport::HashWithIndifferentAccess.new(params)
    search_params = copy_raw_params(hash_params)
    search_params = search_params.merge format_array_params(hash_params)
    search_params = search_params.merge format_training_session_params(hash_params)
    search_params = search_params.merge format_prepared_params(hash_params)
    search_params = search_params.merge format_arrived_params(hash_params)
    search_params = search_params.merge format_billing_payment_template_params(hash_params)
    search_params = search_params.merge format_sort_params(hash_params)
    search_params.merge format_paginate_params(hash_params)
  end

  private

  def copy_raw_params hash_params
    [
      :order_created_from,
      :order_created_to,
      :staff_id,
      :auto_matching,
      :special_offer,
      :urgent,
      :diff_working_time,
      :order_id,
      :order_case_id,
      :working_started_at,
      :working_ended_at,
      :order_portion_status_id
    ].each_with_object(Hash.new) do |field, h|
      next if hash_params[field].blank?

      if field == :working_started_at
        h[:working_date_from] = hash_params[field]
        next
      end

      if field == :working_ended_at
        h[:working_date_to] = hash_params[field]
        next
      end

      h[field] = hash_params[field]
    end
  end

  def format_array_params hash_params
    [
      :department_id,
      :staff_department_id,
      :corporation_id,
      :location_id,
      :order_case_segment_id,
      :arrange_status_id,
      :working_time_status_id,
      :billing_adjusment_type_id,
      :payment_adjusment_type_id,
      :invoice_target,
      :current_location_type
    ].each_with_object(Hash.new) do |field, h|
      next if hash_params[field].blank?

      h[field] = hash_params[field].split(",")

      next if field.in? [:invoice_target, :current_location_type]

      h[field] = h[field].map(&:to_i)
    end
  end

  def format_training_session_params hash_params
    training_session_codes = []
    training_session_codes << :training_first_round if hash_params[:is_training_first_round]
    training_session_codes << :training_second_round if hash_params[:is_training_second_round]
    {training_session_code: training_session_codes}
  end

  def format_prepared_params hash_params
    # Only want to filter not_prepared arrangement
    return {} if hash_params[:not_prepared].blank? || !hash_params[:not_prepared].to_s.true?

    {is_prepared: false}
  end

  def format_arrived_params hash_params
    # Only want to filter not_arrived arrangement
    return {} if hash_params[:not_arrived].blank? || !hash_params[:not_arrived].to_s.true?

    {is_arrived: false}
  end

  def format_billing_payment_template_params hash_params
    has_template = hash_params[:has_billing_payment_template].to_s.true?
    no_template = hash_params[:has_no_billing_payment_template].to_s.true?
    return {} if has_template == no_template # both true or both false

    {has_billing_payment_template: has_template}
  end

  def format_sort_params hash_params
    desc = hash_params[:desc].nil? || hash_params[:desc].to_s.true?
    sort_order = desc ? :desc : :asc
    sort_key = hash_params[:order_key].presence&.to_sym || :created_at
    sort_key = :working_started_at if sort_key.in? [:working_date, :working_time, :working_started_time]

    {
      sort_key: sort_key,
      sort_order: sort_order
    }
  end

  def format_paginate_params hash_params
    {
      page: hash_params[:page].presence || Settings.page.default,
      per_page: hash_params[:per_page].presence || Settings.per_page.arrangement_search
    }
  end
end
