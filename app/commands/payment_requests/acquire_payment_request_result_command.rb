class PaymentRequests::AcquirePaymentRequestResultCommand
  BANK_TRANSACTION_ERROR_CODES = %w(5 6)

  def initialize payment_request_ids
    @payment_request_ids = payment_request_ids
    @log_file = "update_payment_requests_process.log"
    @logger = Logger.new Rails.root.join("log", @log_file)
  end

  def execute
    write_log "START at #{ServerTime.now}: acquire payment request result for ids: #{@payment_request_ids}"
    payment_requests = get_payment_requests
    if payment_requests.blank?
      write_log "FINISH at #{ServerTime.now}: no available payment_requests"
      return
    end
    acquire_payment_request_data
    write_log "FINISH at #{ServerTime.now}: finished acquire payment request result"
  end

  private

  def get_payment_requests
    return if @payment_request_ids.blank?

    PaymentRequest.by_ids(@payment_request_ids).available_to_acquire
  end

  def acquire_payment_request_data
    return unless can_send_request?

    payload = generate_payload
    response = send_api_request(payload)
    status = response[:status]
    body = response[:body]
    message = response[:message]

    if status == :success
      body["items"].each do |item|
        next if item["progress"] != 0

        if item["result"] == 0
          handle_success(item)
        else
          handle_failure(item)
          notify_failure(
            payment_request_id: item["transaction_id"],
            payload: payload,
            response: body,
            status: status,
            message: message
          )
        end
      end
    else
      update_api_status(status)
      notify_failure(
        payload: payload,
        response: body,
        status: status,
        message: message
      )
    end
  end

  def send_api_request payload
    uri = URI.parse(acquire_endpoint)
    request = Net::HTTP::Post.new(uri)
    request.content_type = Settings.payment_request_api.content_type
    request.body = payload
    req_options = {use_ssl: uri.scheme == "https"}

    write_log "BEGIN send request to #{Settings.payment_request_api.acquire_endpoint} with payload: #{payload}"
    begin
      res = Net::HTTP.start(uri.hostname, uri.port, req_options) do |http|
        http.request(request)
      end
      body = res&.body.present? ? JSON.parse(res.body) : nil
      write_log "END receive response body: #{body}"
      return {status: :success, body: body, message: body["message"]} if res.is_a?(Net::HTTPSuccess)

      {status: :failed, body: body, message: "request failed"}
    rescue *NetHttpTimeoutErrors.all => e
      write_log "ERROR occured, request failed due to Net::HTTP errors #{e} - \nPayload: #{payload}"
      {body: nil, status: :timeout, message: nil}
    end
  end

  def generate_payload
    items = @payment_request_ids.map do |id|
      {
        service: Settings.payment_request_api.service,
        transaction_id: id.to_s
      }
    end
    JSON.dump(items: items)
  end

  def update_api_status status
    api_status = "acquire_#{status}".to_sym
    payment_requests = get_payment_requests
    payment_requests.update_all(api_status: api_status)
  end

  def handle_success data
    payment_request_id = data["transaction_id"]
    payment_request = PaymentRequest.available_to_acquire.find_by(id: payment_request_id)
    if payment_request.blank?
      write_log "WARNING cannot update payment request result, payment request not found: #{payment_request_id}"
      return
    end
    status = payment_request.update(
      process_status: :trade_success,
      transfer_date: data["transfer_at"],
      transfer_scheduled_at: data["scheduled_at"],
      api_status: :acquire_success
    )

    if status
      PaymentRequestService.trigger_after_change_process_status(payment_request_id)
      notify_staff_on_success(payment_request)
    else
      notify_failure(
        payment_request_id: payment_request_id,
        errors: payment_request.errors.full_messages
      )
    end
  end

  def handle_failure data
    unless data["error_code"].in? BANK_TRANSACTION_ERROR_CODES
      write_log "WARNING receive API-related error codes, skip update - retry next iteration: #{data}"
      return
    end
    payment_request_id = data["transaction_id"]
    write_log "ERROR transaction failed for payment_request #{payment_request_id} - data: #{data}"
    payment_request = PaymentRequest.available_to_acquire.find_by(id: payment_request_id)
    if payment_request.blank?
      write_log "WARNING cannot update payment request result, payment request not found: #{payment_request_id}"
      return
    end
    status = payment_request.update(
      process_status: :trade_failed,
      transfer_date: nil,
      transfer_scheduled_at: nil,
      api_status: :acquire_success
    )
    if status
      update_staff_account_transfer(payment_request) if data["error_code"].to_s == "5"
    else
      notify_failure(
        payment_request_id: payment_request_id,
        errors: payment_request.errors.full_messages
      )
    end
  end

  # ! Deprecated: account_inquired_result, keep to maintain data integrity with inquiry_result
  def update_staff_account_transfer payment_request
    payment_request&.staff&.staff_account_transfer&.update(
      account_inquired_at: ServerTime.now,
      account_inquired_result: MizuhoAccountInquiry.account_inquired_results[:wrong_name_account],
      inquiry_result: SmbcAccountInquiry.inquiry_results[:confirmed_ng]
    )
  end

  def write_log message
    @logger.info message
  end

  def acquire_endpoint
    Settings.payment_request_api.acquire_endpoint
  end

  # Note: this method is for notifying failure on the API side
  # Error handling should be done separately (i.e. catched by ExceptionHandler)
  def notify_failure options
    opt = options.merge(
      uri: acquire_endpoint,
      log_file: @log_file
    )
    CustomNotifier::PaymentRequestFailureNotifier.new.call(opt)
  end

  def notify_staff_on_success payment_request
    AdminMailer.notify_staff_payment_request_success(payment_request).deliver_now
    app_notification_option = {
      staff_id: payment_request.staff_id,
      creator_type: :by_system,
      notification_type: :notify_payment_request_success,
      params: {
        transfer_amount: format_currency(payment_request.transfer_amount),
        created_at: payment_request.created_at_format,
        transfer_date: payment_request.transfer_date_only_date
      }
    }
    AppSendNotificationWorker.perform_async([app_notification_option])
  end

  def format_currency value
    ActionController::Base.helpers.number_to_currency(value, separator: ",", unit: "", precision: 0)
  end

  def can_send_request?
    Settings.payment_request_api&.enabled
  end
end
