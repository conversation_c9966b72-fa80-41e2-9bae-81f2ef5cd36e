module Insurances
  class EligibleJoinSubsectionInsuranceCommand
    BASIC_PAYROLL = 88000

    attr_reader :staff_social_attributes

    def initialize staff_social_attributes
      @staff_social_attributes = staff_social_attributes
    end

    def perform avg_working_time_per_week, payroll
      return false if excluding_staff || avg_working_time_per_week < 20
      return true if avg_working_time_per_week >= 30

      payroll >= BASIC_PAYROLL
    end

    private

    def excluding_staff
      %w(international_student day_student).include?(staff_social_attributes)
    end
  end
end
