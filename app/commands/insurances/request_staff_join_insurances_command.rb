module Insurances
  class RequestStaffJoinInsurancesCommand
    WORKING_MONTH_START_DATE = 16
    WORKING_MONTH_END_DATE = 15
    NUMBER_MINUTES_OF_HOUR = 60
    BASIC_PAYROLL = 88000

    attr_reader :staff, :order_case_id, :accepted_apply

    def initialize staff, order_case_id, accepted_apply = false
      @staff = staff
      @order_case_id = order_case_id
      @accepted_apply = accepted_apply
    end

    def perform
      return {status: false} if staff.blank?
      return if accepted_apply

      @staff_salary = staff.staff_salary
      return unless @staff_salary
      return if !@staff_salary.not_joining? && !@staff_salary.e_i_not_target?

      @order_case = OrderCase.find_by(id: order_case_id)
      return {status: false} unless @order_case

      job_month_start_date = job_month_start_date()
      current_month_start_date = current_month_start_date()
      return {status: false} if job_month_start_date < current_month_start_date
      return check_request_in_the_future(job_month_start_date) if job_month_start_date > current_month_start_date

      check_request_in_current_month(current_month_start_date)
    end

    private
    # Explication: month_2 is month start of applying job,  month_1 is previous month of month_2

    def check_request_in_current_month current_month_start_date
      current_month_end_date = month_end_date(current_month_start_date)
      average_working_time = average_estimate_working_time(current_month_start_date, current_month_end_date)

      status_subsection_month_2 = eligible_join_subsection_insurance(average_working_time, BASIC_PAYROLL)
      status_employment_month_2 = eligible_join_employment_insurance(average_working_time)
      return if !status_subsection_month_2 && !status_employment_month_2

      last_month_data = worked_time_last_month(current_month_start_date)
      avg_working_time_per_week_last_month = last_month_data[:avg_working_time_per_week]
      payroll_amount_last_month = last_month_data[:payroll_amount]

      status_subsection_month_1 = eligible_join_subsection_insurance(avg_working_time_per_week_last_month,
        payroll_amount_last_month)

      status_employment_month_1 = eligible_join_employment_insurance(avg_working_time_per_week_last_month)
      return if !status_subsection_month_1 && !status_employment_month_1

      eligible_subsections = status_subsection_month_1 && status_subsection_month_2 && @staff_salary.not_joining?
      eligible_employments = status_employment_month_1 && status_employment_month_2 && @staff_salary.e_i_not_target?

      return request_staff_join_only_subsection(current_month_start_date, current_month_end_date) if
        condition_request_join_subsection_only(eligible_subsections, eligible_employments)

      return request_staff_join_only_employment(current_month_start_date, current_month_end_date) if
        condition_request_join_employment_only(eligible_subsections, eligible_employments)

      request_staff_join_both(current_month_start_date, current_month_end_date) if
        condition_request_join_both(eligible_subsections, eligible_employments)
    end

    def check_request_in_the_future job_month_start_date
      job_month_end_date = month_end_date(job_month_start_date)
      average_working_time = average_estimate_working_time(job_month_start_date, job_month_end_date)

      status_subsection_month_2 = eligible_join_subsection_insurance(average_working_time, BASIC_PAYROLL)
      status_employment_month_2 = eligible_join_employment_insurance(average_working_time)
      return if !status_subsection_month_2 && !status_employment_month_2

      previous_month_start_date = job_month_start_date.prev_month
      average_working_time_previous_month = average_estimate_working_time(previous_month_start_date,
        month_end_date(previous_month_start_date))

      status_subsection_month_1 = eligible_join_subsection_insurance(average_working_time_previous_month,
        BASIC_PAYROLL)

      status_employment_month_1 = eligible_join_employment_insurance(average_working_time_previous_month)
      return if !status_subsection_month_1 && !status_employment_month_1

      eligible_subsections = status_subsection_month_1 && status_subsection_month_2 && @staff_salary.not_joining?
      eligible_employments = status_employment_month_1 && status_employment_month_2 && @staff_salary.e_i_not_target?

      return request_staff_join_only_subsection(job_month_start_date, job_month_end_date) if
        condition_request_join_subsection_only(eligible_subsections, eligible_employments)

      return request_staff_join_only_employment(job_month_start_date, job_month_end_date) if
        condition_request_join_employment_only(eligible_subsections, eligible_employments)

      request_staff_join_both(job_month_start_date, job_month_end_date) if
        condition_request_join_both(eligible_subsections, eligible_employments)
    end

    def eligible_join_subsection_insurance avg_working_time_per_week, payroll_amount
      cmd = Insurances::EligibleJoinSubsectionInsuranceCommand.new(staff.social_attribute)
      cmd.perform(avg_working_time_per_week, payroll_amount)
    end

    def eligible_join_employment_insurance avg_working_time_per_week
      cmd = Insurances::EligibleJoinEmploymentInsuranceCommand.new(staff.social_attribute)
      cmd.perform(avg_working_time_per_week)
    end

    def condition_request_join_subsection_only eligible_subsections, eligible_employments
      eligible_subsections && !eligible_employments
    end

    def condition_request_join_employment_only eligible_subsections, eligible_employments
      !eligible_subsections && eligible_employments
    end

    def condition_request_join_both eligible_subsections, eligible_employments
      eligible_subsections && eligible_employments
    end

    def job_month_start_date
      start_date = @order_case.case_started_at.change(day: WORKING_MONTH_START_DATE).beginning_of_day
      start_date = start_date.prev_month if start_date > @order_case.case_started_at

      start_date
    end

    def current_month_start_date
      current_start_date = ServerTime.now.change(day: WORKING_MONTH_START_DATE).beginning_of_day
      current_start_date = current_start_date.prev_month if current_start_date > ServerTime.now

      current_start_date
    end

    def month_end_date start_date
      start_date.next_month.change(day: WORKING_MONTH_END_DATE).end_of_day
    end

    def worked_time_last_month current_month_start_date
      last_month_start_date = current_month_start_date.prev_month
      last_month_end_date = month_end_date(last_month_start_date)

      worked_data = working_data_query(last_month_start_date, last_month_end_date).first
      worked_data = worked_data.extend(Insurances::CalculateWorkingData)

      {
        avg_working_time_per_week: worked_data.average_working_time_per_week(last_month_start_date,
          last_month_end_date),
        payroll_amount: worked_data.payroll_amount
      }
    end

    def working_data_query working_started_at, working_ended_et
      params = {
        staff_ids: staff.id,
        start_time: working_started_at,
        end_time: working_ended_et.end_of_day,
        segment_calc: :payroll
      }

      WorkAchievements::WorkingTimeQuery.execute(
        :sum_actual_working_time,
        params
      )
    end

    def average_estimate_working_time start_date, end_date
      total_days = (start_date.to_date..end_date.to_date).count
      total_minutes = total_arranged_time(start_date, end_date) + total_applied_time(start_date, end_date) +
        + applying_time

      CalculateAverageModule.hours_per_week(total_minutes, total_days)
    end

    def total_arranged_time start_date, end_date
      arranged_jobs = WorkAchievements::StaffArrangedTimeQuery.execute(
        staff.id,
        start_date,
        end_date.end_of_day
      )

      arranged_jobs.first.extend(Insurances::CalculateFutureWorkingTime).total_arranged_time
    end

    def total_applied_time start_date, end_date
      applied_case = staff.staff_apply_order_cases
        .joins(:order_branch)
        .not_processed
        .in_range(start_date, end_date.end_of_day)
        .select(:order_case_id, :break_time,
          "case_started_at AS order_case_started_at", "case_ended_at AS order_case_ended_at")

      applied_case.extend(Insurances::CalculateFutureWorkingTime).total_applied_time
    end

    def applying_time
      ((@order_case.case_ended_at - @order_case.case_started_at).to_f / NUMBER_MINUTES_OF_HOUR) - @order_case.break_time
    end

    def request_staff_join_only_subsection start_date, end_date
      mail_history = InsuranceMailHistory.where(
        staff_id: staff.id,
        from_date: start_date.to_date,
        to_date: end_date.to_date,
        mail_type: :insurance_subsection
      )

      raise(CustomExceptions::InsurancesNecessaryError, json_message("subsection", start_date.next_month)) unless
        mail_history.exists?
    end

    def request_staff_join_only_employment start_date, end_date
      mail_history = InsuranceMailHistory.where(
        staff_id: staff.id,
        from_date: start_date.to_date,
        to_date: end_date.to_date,
        mail_type: :employment_insurance
      )

      raise(CustomExceptions::InsurancesNecessaryError, json_message("employment", start_date.next_month)) unless
        mail_history.exists?
    end

    def request_staff_join_both start_date, end_date
      mail_history = InsuranceMailHistory.where(
        staff_id: staff.id,
        from_date: start_date.to_date,
        to_date: end_date.to_date,
        mail_type: [:insurance_subsection, :employment_insurance]
      ).pluck(:mail_type)

      raise(CustomExceptions::InsurancesNecessaryError, json_message("both", start_date.next_month)) if
        mail_history.empty?

      raise(CustomExceptions::InsurancesNecessaryError, json_message("employment", start_date.next_month)) unless
        mail_history.include?("employment_insurance")

      raise(CustomExceptions::InsurancesNecessaryError, json_message("subsection", start_date.next_month)) unless
        mail_history.include?("insurance_subsection")
    end

    def json_message type, next_month
      next_month = I18n.l(next_month, format: Settings.date.year_month_and_date)
      {
        status: false,
        errors: I18n.t("staff.errors.insurance_necessary.#{type}"),
        notification: I18n.t("staff.errors.insurance_cancel_notification.#{type}", next_month: next_month),
        type: type
      }.to_json
    end
  end
end
