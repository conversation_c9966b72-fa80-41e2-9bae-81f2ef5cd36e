module Insurances
  class EligibleJoinEmploymentInsuranceCommand
    attr_reader :staff_social_attribute

    def initialize staff_social_attribute
      @staff_social_attribute = staff_social_attribute
    end

    def perform avg_working_time_per_week
      return false if excluding_staff || avg_working_time_per_week < 20

      true
    end

    private

    def excluding_staff
      %w(international_student day_student wh).include?(staff_social_attribute)
    end
  end
end
