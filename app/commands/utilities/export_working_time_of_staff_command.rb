module Utilities
  class ExportWorkingTimeOfStaffCommand
    WORKING_MONTH_START_DATE = 16
    WORKING_MONTH_END_DATE = 15

    attr_reader :start_month, :end_month

    def initialize start_month, end_month
      @start_month = start_month
      @end_month = end_month
    end

    def perform
      start_time = working_month_start_date(start_month.to_date)
      end_time_start_date = working_month_start_date(end_month.to_date)
      end_time = working_month_end_date(end_time_start_date)

      staff_has_arrangement = Arrangement.in_range(start_time, end_time)
        .where.not(staff_id: nil)
        .distinct
        .pluck(:staff_id)

      all_months = get_month_in_range(start_time, end_time)

      all_rows = []

      staff_has_arrangement.each_slice(200) do |staff_ids|
        staffs = Staff.includes(:account, :staff_salary).where(id: staff_ids)

        working_data = all_months.map do |month|
          working_time_data_query(staff_ids, month[:working_started_at], month[:working_ended_at]).to_a
        end

        staffs.each do |staff|
          staff_working_time_data = staff_working_time_data(staff.id, working_data, all_months)
          all_rows << get_row_data(staff, staff_working_time_data)
        end
      end

      file_name = "working_time_of_staff_from_#{ServerTime.now.to_i}.csv"
      file_path = Rails.root.join("tmp", file_name)
      File.open(file_path, "w:shift_jis", invalid: :replace, undef: :replace, replace: "?") do |file|
        file_csv = CSV.generate do |csv|
          csv << csv_headers(all_months)
          all_rows.each do |row|
            csv << row
          end
        end
        file.write(file_csv)
      end

      file_name
    end

    private

    def working_month_start_date month
      month.change(day: WORKING_MONTH_START_DATE).beginning_of_day
    end

    def working_month_end_date start_date
      start_date.next_month.change(day: WORKING_MONTH_END_DATE).end_of_day
    end

    def get_month_in_range start_time, end_time
      months = []
      current_check = start_time

      while current_check <= end_time
        months << {working_started_at: current_check, working_ended_at: working_month_end_date(current_check)}
        current_check += 1.month
      end

      months
    end

    def working_time_data_query staff_ids, working_started_at, working_ended_at
      params = {
        staff_ids: staff_ids,
        start_time: working_started_at,
        end_time: working_ended_at,
        segment_calc: :payroll,
        group_by_staff: true
      }

      WorkAchievements::WorkingTimeQuery.execute(:sum_actual_working_time, params)
    end

    def staff_working_time_data staff_id, working_data, all_months
      staff_working_data = []

      working_data.each_with_index do |data, index|
        next staff_working_data << {working_time: 0, payroll_amount: 0, avg_working_time: 0} if data.empty?

        staff_data = data.find{|key| key.staff_id == staff_id}
        next staff_working_data << {working_time: 0, payroll_amount: 0, avg_working_time: 0} unless staff_data

        working_started_at = all_months[index][:working_started_at]
        working_ended_at = all_months[index][:working_ended_at]
        staff_data = staff_data.extend(Insurances::CalculateWorkingData)
        working_time = staff_data.total_hours
        payroll_amount = staff_data.payroll_amount
        avg_working_time = staff_data.average_working_time_per_week(working_started_at, working_ended_at)
        staff_working_data << {
          working_time: working_time,
          payroll_amount: payroll_amount,
          avg_working_time: avg_working_time
        }
      end

      staff_working_data
    end

    def csv_headers all_months
      csv_headers = [
        I18n.t("activerecord.attributes.staff.staff_number"),
        I18n.t("activerecord.attributes.staff.name"),
        I18n.t("activerecord.attributes.staff.social_attribute")
      ]

      all_months.each do |month|
        start_date = month[:working_started_at].to_date.to_s
        end_date = month[:working_ended_at].to_date.to_s

        csv_headers << "Working time #{start_date} ~ #{end_date}"
        csv_headers << "Payroll #{start_date} ~ #{end_date}"
      end

      csv_headers.push(
        "Subsection insurance current status",
        "Eligible join subsection insurance",
        "Employment insurance current status",
        "Eligible join employment insurance"
      )

      csv_headers
    end

    def get_row_data staff, staff_working_data
      row_data = [
        staff.staff_number,
        staff.account_name,
        staff.social_attribute
      ]

      staff_working_data.each do |data|
        row_data << data[:working_time]
        row_data << data[:payroll_amount]
      end

      row_data << staff.staff_salary&.insurance_subsection_type
      row_data << eligible_join_subsection_insurance(staff, staff_working_data.last(2))
      row_data << staff.staff_salary&.employment_insurance_type
      row_data << eligible_join_employment_insurance(staff, staff_working_data.last(2))

      row_data
    end

    def eligible_join_employment_insurance staff, staff_working_data
      return "Needed 2 months data" if staff_working_data.size < 2

      cmd = Insurances::EligibleJoinEmploymentInsuranceCommand.new(staff.social_attribute)

      status = staff_working_data.map do |data|
        cmd.perform(data[:avg_working_time])
      end

      status = status.uniq
      return if status.size > 1
      return "Join" if staff.staff_salary&.e_i_not_target? && status == [true]

      "Reject" if staff.staff_salary&.e_i_target? && status == [false]
    end

    def eligible_join_subsection_insurance staff, staff_working_data
      return "Needed 2 months data" if staff_working_data.size < 2

      cmd = Insurances::EligibleJoinSubsectionInsuranceCommand.new(staff.social_attribute)

      status = staff_working_data.map do |data|
        cmd.perform(data[:avg_working_time], data[:payroll_amount])
      end

      status = status.uniq
      return if status.size > 1
      return "Join" if staff.staff_salary&.not_joining? && status == [true]

      "Reject" if staff.staff_salary&.subscription? && status == [false]
    end
  end
end
