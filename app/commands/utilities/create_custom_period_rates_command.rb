module Utilities
  class CreateCustomPeriodRatesCommand
    attr_reader :custom_date

    def initialize custom_date
      @custom_date = custom_date
    end

    def perform method, data
      self.send(method, data)
    end

    private

    def create_peak_period data
      peak_period = PeakPeriod.find_or_initialize_by(
        chain_id: data[:chain_id],
        target_date: custom_date.to_date.beginning_of_day
      )

      peak_period.name = data[:peak_period_name]
      peak_period.save!
    end

    def create_custom_period_rate data
      peak_period = PeakPeriod.find_by(target_date: custom_date.to_date.beginning_of_day)
      raise(StandardError, "Not found peak period date by custom date") unless peak_period

      prefecture = Prefecture.find_by(name: data[:prefecture_name])
      raise(StandardError, "Not found prefecture") unless prefecture

      new_period_rate = PeriodRate.find_or_initialize_by(
        peak_period_id: peak_period.id,
        prefecture_id: prefecture.id,
        corporation_id: data[:corporation_id]
      )

      new_period_rate.chain_id = peak_period.chain_id
      new_period_rate.order_rate = data[:order_rate].to_i
      new_period_rate.unit_rate = data[:unit_rate].to_i
      new_period_rate.is_default = false
      new_period_rate.is_all = data[:corporation_id].nil?

      new_period_rate.save!
    end

    def increase_default_order_rate data
      peak_period = PeakPeriod.find_by(target_date: custom_date.to_date.beginning_of_day)
      raise(StandardError, "Not found peak period date by custom date") unless peak_period

      PeriodRate.where(chain_id: Settings.lawson_chain_id, is_default: true).find_each do |period_rate|
        new_period_rate = PeriodRate.new(
          period_rate.as_json(
            only: [:chain_id, :is_all, :prefecture_id, :corporation_id, :unit_rate]
          )
        )

        new_period_rate.order_rate = period_rate.order_rate + data[:increase_amount].to_i
        new_period_rate.peak_period_id = peak_period.id
        new_period_rate.save!
      end
    end
  end
end
