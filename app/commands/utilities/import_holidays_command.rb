module Utilities
  class ImportHolidaysCommand
    attr_reader :file_name

    def initialize file_name
      @file_name = file_name
    end

    def perform
      puts "--START IMPORT--"
      raise(StandardError, "File name doesn't exists") unless File.exist?(file_name)

      holidays = []

      CSV.foreach(file_name, headers: true) do |row|
        date = Date.parse(row["holiday"])
        name = row["name"]

        holiday = Holiday.new(holiday: date, name: name)
        raise(StandardError, "Error input data at #{row}") unless holiday.valid?

        holidays << holiday
      end

      holidays = holidays.sort_by(&:holiday)

      delete_existing_holidays(holidays.first.holiday, holidays.last.holiday)
      Holiday.import(holidays)

      puts "--END IMPORT--"

      puts "-- List arrangements ID in holidays -- \n
        #{list_arrangement_id_in_holiday(holidays)}"
    end

    private

    def delete_existing_holidays start_date, end_date
      existing_holidays = Holiday.by_range_date(start_date, end_date)
      existing_holidays.delete_all if existing_holidays.present?
    end

    def list_arrangement_id_in_holiday holidays
      list_arrangement_ids_in_holiday = {}
      holidays.each do |date|
        current_date = date.holiday.in_time_zone
        arrangement_ids = Arrangement.in_range(
          current_date.beginning_of_day,
          current_date.end_of_day
        ).pluck(:id)
        next if arrangement_ids.blank?

        list_arrangement_ids_in_holiday[current_date.strftime(Settings.date.formats)] = arrangement_ids
      end
      list_arrangement_ids_in_holiday
    end
  end
end
