module Utilities
  class CreateNewOrdersCommand
    attr_reader :organization_id, :location_id, :order_params, :order_branches_params

    def initialize organization_id, location_id, order_params, order_branches_params
      @organization_id = organization_id
      @location_id = location_id
      @order_params = order_params
      @order_branches_params = order_branches_params
    end

    def perform
      order = build_new_order
      order_branches = build_order_branches
      order.order_branches = order_branches
      data = OrderBranch.data_info(order_branches)
      default_invoice_target = order&.location&.default_invoice_target
      data[:no_reindex] = true
      order_branches.each do |ob|
        ob.data_info = data
        ob.calculate_relate_field_after_assign_attr data
        ob.invoice_target = default_invoice_target if default_invoice_target.present?
        ob.must_validate_required_time = true
      end
      add_sequence_no_order_branch(order)
      order.set_pic_department_id
      order.calculate_overall_attributes
      raise(ActiveModel::ValidationError, order) unless order.valid?

      ActiveRecord::Base.transaction do
        order.save!
      end
    end

    private

    def build_new_order
      new_order_params = OrderBlueprint.render_as_hash(
        order_params.merge(location_id: location_id, organization_id: organization_id),
        view: :build_new_order
      )

      Order.new(new_order_params)
    end

    def build_order_branches
      new_order_branches_params = OrderBranchBlueprint.render_as_hash(
        order_branches_params,
        view: :build_new_order_branch
      )

      new_order_branches_params.map do |order_branch_params|
        OrderBranch.new(order_branch_params)
      end
    end

    def add_sequence_no_order_branch order
      sequence_no = order.order_branches&.with_deleted&.max_by(&:sequence_no)&.sequence_no
      if sequence_no.nil?
        sequence_no = 0
      else
        sequence_no += 1
      end
      order.order_branches.each do |ob|
        if ob.sequence_no.nil?
          ob.sequence_no = sequence_no
          sequence_no += 1
        end
      end
    end
  end
end
