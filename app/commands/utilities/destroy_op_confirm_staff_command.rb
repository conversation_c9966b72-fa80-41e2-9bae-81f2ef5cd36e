class Utilities::DestroyOpConfirmStaffCommand
  attr_reader :staff_id

  HARD_DELETE = [StaffVersion, RegistrationAnswer, StaffEducationBackground,
    StaffWorkExperience, StaffAccountTransfer]
  SOFT_DELETE = [StaffCheckItem, StaffExpectation, DeviceToken]

  def initialize staff_id
    @staff_id = staff_id
  end

  def perform
    staff = Staff.op_confirm.find(staff_id)
    account = staff.account
    reviewing_staff = staff.reviewing_staff
    delete_staff_ids = [staff.id, reviewing_staff.id]

    staff_update_params = address_params.merge(emergency_params)
    staff_update_params = staff_update_params.merge(birthday)
    staff_update_params = staff_update_params.merge(dummy_self_info(staff.tel, staff.email))
    update_staff_img(staff)
    update_staff_img(reviewing_staff)
    reviewing_staff.assign_attributes(staff_update_params)
    staff.assign_attributes(staff_update_params)
    staff.marked_as_deleted = true
    relation_with_account = User.find_by(account_id: account.id) || Admin.find_by(account_id: account.id)

    ActiveRecord::Base.transaction do
      hard_delete_data(delete_staff_ids)
      soft_delete_data(delete_staff_ids)
      reviewing_staff.save!(validate: false)
      staff.save!(validate: false)
      account.update_columns(dummy_self_info(account.tel, account.name)) unless relation_with_account
    end

    create_delete_staff_log(staff, account&.name)
  end

  private

  def address_params
    {
      city: "",
      street_number: "",
      house_number: nil,
      building: nil
    }
  end

  def emergency_params
    {
      emergency_city: nil,
      emergency_street_number: nil,
      emergency_house_number: nil,
      emergency_building: nil,
      emergency_name: nil,
      emergency_relationship_id: nil,
      emergency_tel: nil
    }
  end

  def birthday
    {
      birthday: "01/01/1970"
    }
  end

  def dummy_self_info tel, email
    sha256 = Digest::SHA256.new

    {
      email: email.nil? ? nil : "deleted_#{sha256.hexdigest(email)}",
      tel: tel.nil? ? nil : "deleted_#{sha256.hexdigest(tel)}"
    }
  end

  def hard_delete_data staff_id
    HARD_DELETE.each do |model|
      model.where(staff_id: staff_id).delete_all
    end
  end

  def soft_delete_data staff_id
    SOFT_DELETE.each do |model|
      model.where(staff_id: staff_id).destroy_all
    end
  end

  def update_staff_img staff
    staff.remove_avatar = true
    staff.remove_residence_img_front = true
    staff.remove_residence_img_back = true
    staff.remove_certificate_img = true
  end

  def create_delete_staff_log staff, account_name
    DeleteStaffLog.create(
      staff_id: staff.id,
      staff_number: staff.staff_number,
      staff_name: staff.name || account_name,
      staff_status_id: Staff.status_ids[staff.status_id],
      deleter_type_id: :by_system_request,
      admin_id: nil,
      staff_deleted_at: ServerTime.now
    )
  end
end
