module Utilities
  class ExportStaffLongVacationsCommand
    CSV_HEADERS = [
      I18n.t("activerecord.attributes.staff.staff_number"),
      I18n.t("activerecord.attributes.staff.name"),
      I18n.t("admin.staff_long_vacation.start_date"),
      I18n.t("admin.staff_long_vacation.end_date")
    ]

    def perform
      file_name = "staff_long_vacations_export_#{ServerTime.now.to_i}.csv"
      file_path = Rails.root.join("tmp", file_name)

      File.open(file_path, "w:shift_jis", invalid: :replace, undef: :replace, replace: "?") do |file|
        file_csv = CSV.generate do |csv|
          csv << CSV_HEADERS
          StaffLongVacation.includes(staff: :account).find_each(batch_size: 2000) do |staff_long_vacation|
            next unless staff_long_vacation.staff.type_staff?

            csv << get_row_data(staff_long_vacation)
          end
        end
        file.write(file_csv)
      end

      return unless Settings.environment_can_use_aws.include? Rails.env

      s3_folder = Settings.aws.s3.folders.admin_exports
      S3_BUCKET.object("#{s3_folder}/#{file_name}").upload_file(file_path)
    end

    private

    def get_row_data staff_long_vacation
      staff = staff_long_vacation.staff

      [
        staff.staff_number,
        staff.account_name,
        staff_long_vacation.start_date,
        staff_long_vacation.end_date
      ]
    end
  end
end
