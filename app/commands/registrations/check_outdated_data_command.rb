class Registrations::CheckOutdatedDataCommand
  def initialize staff_id, compared_data
    @staff_id = staff_id
    @updated_profile = compared_data[:updated_profile].to_s.true?
    @updated_at = compared_data[:updated_at].to_datetime.in_time_zone
  end

  def execute
    staff = Staff.find_by(id: @staff_id)
    raise ActiveRecord::RecordInvalid if staff.blank?

    staff.updated_profile == @updated_profile && staff.updated_at == @updated_at
  end
end
