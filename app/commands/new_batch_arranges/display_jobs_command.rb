module NewBatchArranges
  class DisplayJobsCommand
    attr_reader :order_case_ids, :options

    def initialize order_case_ids, options
      @order_case_ids = order_case_ids
      @options = options
    end

    def perform
      order_cases = get_order_cases_data.to_a
      location_ids = order_cases.pluck(:location_id).uniq
      corporation_ids = order_cases.pluck(:corporation_id).uniq
      order_branch_ids = order_cases.pluck(:order_branch_id).uniq
      location_arranged_ratio, corporation_arranged_ratio = get_arranged_ratio(location_ids, corporation_ids)
      order_branches = get_order_branches_data(order_branch_ids)
      order_portions_ratio = get_order_portions_ratio
      staff_order_case_offers = get_staff_order_case_offers
      order_cases_status = get_order_cases_status
      arrangements_data = get_arrangements_data

      order_cases.map do |order_case|
        location_arranged_order_case = location_arranged_ratio[order_case[:location_id]]
        corporation_arranged_order_case = corporation_arranged_ratio[order_case[:corporation_id]]
        portion_ratio = order_portions_ratio[order_case.id]
        sum_offers_order_case = staff_order_case_offers[order_case.id]
        order_case_status = order_cases_status[order_case.id]
        arrangements = arrangements_data[order_case.id]
        order_branch = order_branches[order_case.order_branch_id]

        OrderCaseBlueprint.render_as_hash(
          order_case,
          view: :new_batch_arranges,
          location_ratio: location_arranged_order_case,
          corporation_ratio: corporation_arranged_order_case,
          portion_ratio: portion_ratio,
          sum_offers: sum_offers_order_case,
          order_case_status: order_case_status,
          arrangements: arrangements,
          order_branch: order_branch
        )
      end
    end

    private

    def get_order_cases_data
      OrderCase.select("order_cases.id, order_cases.case_started_at, order_cases.order_id,
          order_cases.case_ended_at, order_cases.is_urgent, order_cases.segment_id, order_cases.total_portion,
          order_cases.special_offer_note, order_cases.training_session_code, order_cases.order_branch_id,
          orders.location_id, orders.corporation_id, orders.note AS order_note,
          locations.name AS location_name, locations.note AS location_note,
          CONCAT_WS('', corporations.name_1, corporations.name_2, corporations.name_3) AS corporation_name,
          stations.name AS location_stations_1_station_name")
        .joins(order: [:corporation, :location])
        .joins("LEFT JOIN stations ON locations.station_1 = stations.id")
        .where(id: order_case_ids)
        .order_by_ids(order_case_ids)
        .distinct
    end

    def get_arranged_ratio location_ids, corporation_ids
      query_obj = OrderPortions::StatusRatioQuery.new(:arranged, options[:from], options[:to])

      by_location = query_obj.execute(:location, location_ids)
      by_corporation = query_obj.execute(:corporation, corporation_ids)

      [by_location.index_by(&:location_id), by_corporation.index_by(&:corporation_id)]
    end

    def get_order_branches_data order_branch_ids
      order_branches = OrderBranch.select(:id, :break_time, :is_time_changable,
        :required_start_time, :required_end_time)
        .where(id: order_branch_ids)

      order_branches.index_by(&:id).transform_values do |order_branch|
        {
          break_time: order_branch.break_time,
          is_time_changable: order_branch.is_time_changable,
          required_time: get_required_time(order_branch)
        }
      end
    end

    def get_order_portions_ratio
      portions_ratio = OrderPortions::StatusRatioPerOrderCasesQuery.execute(order_case_ids)
      portions_ratio.index_by(&:order_case_id)
    end

    def get_staff_order_case_offers
      staff_order_case_offers = StaffOrderCaseOffer.select("SUM(is_called = TRUE) AS total_called,
          SUM(is_offered = TRUE) AS total_offered,
          order_case_id")
        .by_order_case(order_case_ids)
        .group(:order_case_id)

      staff_order_case_offers.index_by(&:order_case_id)
    end

    def get_order_cases_status
      OrderCases::RecruitProcessQuery.execute(order_case_ids)
    end

    def get_arrangements_data
      arrangements = Arrangement.select(:order_case_id, :note, :current_location_type)
        .where(order_case_id: order_case_ids)

      arrangements.group_by(&:order_case_id).transform_values do |records|
        {
          current_location_type_text: records.first.current_location_type_text,
          total_note: records.count{|r| r.note.present?}
        }
      end
    end

    # TODO(Tinh): When deploy ESR arrangements, we can use common module
    def get_required_time order_branch
      required_start_time = order_branch.required_start_time
      required_end_time = order_branch.required_end_time
      return unless order_branch.is_time_changable && required_start_time && required_end_time

      next_day = I18n.t("staff.order_cases.next_day") if required_start_time > required_end_time
      "#{required_start_time.strftime(Settings.time.formats)}~#{next_day}#{required_end_time.strftime(Settings.time.formats)}"
    end
  end
end
