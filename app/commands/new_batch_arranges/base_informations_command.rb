class NewBatchArranges::BaseInformationsCommand
  SEARCH_ATTRS = %i(department_id location_id staff_id working_from_date
    working_to_date segment_ids corporation_id)

  attr_reader :admin_id, :options

  def initialize admin_id, options = {}
    @admin_id = admin_id
    @options = options
  end

  def perform
    order_cases = filter_by_attrs
    sumary_advance_options(order_cases)
  end

  private

  def filter_by_attrs
    order_cases = OrderCase.select(:id, :total_portion, :staff_apply_count)
    search_options = format_search_params(options)

    SEARCH_ATTRS.each do |attr|
      order_cases = order_cases.send("filter_by_#{attr}", search_options[attr]) if search_options[attr].present?
    end

    order_cases
  end

  def format_search_params options
    cmd = NewBatchArranges::FormatSearchParamsCommand.new(admin_id, options)
    cmd.perform
  end

  def sumary_advance_options order_cases
    order_cases_ids = order_cases.map(&:id)
    advance_options = {
      total_applying: 0,
      total_portions: 0,
      total_arranged: 0,
      total_arrangements: 0,
      confirming_total_approved: 0,
      confirming_total_need_approve: 0,
      approving_total_approved: 0,
      approving_total_need_approve: 0
    }

    OrderCase.by_ids(order_cases_ids).filter_by_tab_name(:applying).distinct.each do |oc|
      advance_options[:total_applying] += oc.staff_apply_count.to_i
      advance_options[:total_portions] += oc.total_portion.to_i
    end

    Arrangement.is_arranged.by_order_case_id(order_cases_ids).where.not(staff_id: nil)
      .includes(:work_achievement, :order_case).find_each do |arrangement|
      advance_options[:total_arranged] += 1
      advance_options[:total_arrangements] += arrangement.order_case.total_portion.to_i

      next unless Arrangement::BATCH_ARRANGED_DISPLAY_STATUS_TEXT.include?(arrangement.display_status_id.to_sym)

      working_time_status = arrangement.work_achievement.working_time_status_id.to_sym
      if arrangement.approved_by_admin_id.present?
        advance_options[:approving_total_need_approve] += 1
        advance_options[:approving_total_approved] += 1 if
          WorkAchievement::OP_APPROVED_STATUS.include?(working_time_status)
        next
      end
      advance_options[:confirming_total_need_approve] += 1
      advance_options[:confirming_total_approved] += 1 if
        WorkAchievement::OP_APPROVED_STATUS.include?(working_time_status)
    end

    advance_options
  end
end
