module NewBatchArranges
  class DisplayBaseCommand
    attr_accessor :order_cases, :tab_name, :time_options
    attr_reader :location, :corporation, :preload_options

    STAFF_TABLE_RELATIONSHIP = {
      applying: %i(account staff_salary home_station registration_answer),
      arranged: %i(account staff_salary),
      confirming: %i(account staff_salary),
      approving: %i(account staff_salary)
    }

    def initialize order_cases, tab_name, time_options
      @order_cases = order_cases.includes(:order_branch, :order_portions, order: [:location])
      @order_cases_ids = order_cases.map(&:id)
      @location_ids = @order_cases.map{|oc| oc.order.location_id}.uniq
      @corporation_ids = @order_cases.map{|oc| oc.order.corporation_id}.uniq
      @tab_name = tab_name
      @time_options = time_options
      @preload_options = {}
    end

    def perform
      tab_name == :applying ? applying_preload : tab_preload
      staff_ids = @master_data_by_oc.values.flatten.pluck(:staff_id).uniq.compact
      staffs = get_data_staffs(staff_ids, tab_name)
      insurance_labels = Staff.insurance_labels_by_staff(staff_ids)
      order_cases = @order_cases.index_by(&:id)
      display_jobs_cmd = NewBatchArranges::DisplayJobsCommand.new(@order_cases_ids, @time_options)
      order_cases_json = display_jobs_cmd.perform

      order_cases_json.each do |order_case_json|
        order_case_json[:staffs] = []
        data_by_oc = @master_data_by_oc[order_case_json[:id]]
        next order_case_json if data_by_oc.blank?

        order_case = order_cases[order_case_json[:id]]
        order_case_json[:staffs] = data_by_oc.map do |data_relation|
          staff = staffs.find{|s| s.id == data_relation.staff_id}
          formated_staff_by_order_case = formated_staff_by_order_case(order_case, staff, tab_name, preload_options)
          next formated_staff_by_order_case if staff.nil? || staff&.deleted?

          label = insurance_labels.find{|lab| lab[:staff_id] == staff.id}
          formated_staff_by_order_case[:insurance_flag] = label[:insurance_subsection_label] if label
          formated_staff_by_order_case
        end
      end
    end

    private

    def applying_preload
      staffs_apply_order_cases = StaffApplyOrderCase.default_unscope
        .where(order_case_id: @order_cases_ids, status_id: [:not_processed, :rejected])
        .includes(arrangement: [:order_portion, :order_case],
          order: [:corporation])

      staff_ids = staffs_apply_order_cases.map(&:staff_id).uniq
      @master_data_by_oc = staffs_apply_order_cases.group_by(&:order_case_id)

      ban_staffs = StaffMessage.forbidden.where(staff_id: staff_ids)
        .select(:start_date, :end_date, :id, :staff_id).group_by(&:staff_id)

      @preload_options[:priority_staffs_by_locations] = PriorityStaff.where(staff_id: staff_ids)
        .priority_for_location(@location_ids).group_by(&:location_id)

      @preload_options[:order_cases_arrangements] = Arrangement.where(order_case_id: @order_cases_ids)
        .select(:id, :note, :order_case_id, :order_portion_id, :working_ended_at, :display_status_id, :staff_id,
          :store_note, :arrange_comment)
        .includes(:order_portion, :staff)
        .group_by(&:order_case_id)
      @preload_options[:ban_staffs] = ban_staffs
      @preload_options[:staffs_apply_order_cases] = staffs_apply_order_cases
      @preload_options[:staffs_work_achevements] = WorkAchievement.by_staff(staff_ids)
        .select("work_achievements.id, work_achievements.arrangement_id, work_achievements.working_time_status_id,
          work_achievements.working_started_at, work_achievements.working_ended_at,
          work_achievements.working_ended_at, work_achievements.break_time")
        .includes(arrangement: [:staff, :order, :order_portion])
        .group_by{|wa| wa.arrangement.staff_id} || {}
    end

    def tab_preload
      arrangements = Arrangement.where(order_case_id: @order_cases_ids)
        .where.not(staff_id: nil).where.not(display_status_id: [:temporary_arrange])
      arrangements = arrangements.where(display_status_id: Arrangement::BATCH_ARRANGED_DISPLAY_STATUS,
          approved_by_admin_id: nil) if tab_name == :confirming
      arrangements = arrangements.where(display_status_id: Arrangement::BATCH_ARRANGED_DISPLAY_STATUS)
        .where.not(approved_by_admin_id: nil) if tab_name == :approving
      arrangements = arrangements.includes(:order, :work_achievement,
        admin: [:account],
        arrange_logs: [admin: [:account]])
      @master_data_by_oc = arrangements.group_by(&:order_case_id)
      @preload_options[:order_cases_arrangements] = @master_data_by_oc
      staff_ids = arrangements.pluck(:staff_id)
      @preload_options[:priority_staffs_by_locations] = PriorityStaff.where(staff_id: staff_ids)
        .priority_for_location(@location_ids).group_by(&:location_id)
    end

    def get_data_staffs staff_ids, tab_name
      Staff.with_deleted
        .includes(STAFF_TABLE_RELATIONSHIP[tab_name])
        .where(id: staff_ids)
    end

    def formated_staff_by_order_case order_case, staff, tab_name, preload_options
      staff_apply_order_cases = preload_options[:staffs_apply_order_cases]
      staff_onday_applied = staff_onday_apply_order_cases(staff, order_case, tab_name, staff_apply_order_cases).to_i
      staff_data = FormatDisplay::NewBatchArrangeJob.new(order_case, staff, tab_name, preload_options)
      staff_data.format_staff_by_order_case(staff_onday_applied)
    end

    def staff_onday_apply_order_cases staff, order_case, tab_name, staff_apply_order_cases
      return if staff.nil? || staff.deleted? || tab_name != :applying

      order_cases_staff_applied = staff_apply_order_cases.select{|saoc| saoc.staff_id == staff.id}
      order_case_ids = order_cases_staff_applied.map(&:order_case_id)

      onday_order_cases = @order_cases.select do |oc|
        order_case.case_started_at.between?(oc.case_started_at.beginning_of_day, oc.case_started_at.end_of_day) &&
        order_case_ids.include?(oc.id)
      end

      onday_order_cases.size
    end
  end
end
