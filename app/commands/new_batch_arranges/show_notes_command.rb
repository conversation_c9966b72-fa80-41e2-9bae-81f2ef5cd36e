class NewBatchArranges::ShowNotesCommand
  attr_reader :order_case_id, :note_type, :arrangement_id

  def initialize order_case_id, note_type, arrangement_id = nil
    @order_case_id = order_case_id
    @note_type = note_type
    @arrangement_id = arrangement_id
  end

  def perform
    note_types_list = note_type&.scan(/\w+/).reject(&:blank?)
    order_case = OrderCase.find_by(id: order_case_id)
    return json_response(false) unless order_case

    order = order_case.order
    notes_data = {}

    note_types_list.each do |type|
      note = get_note_data(type, order_case, order)
      notes_data[type] = note
    end

    json_response(true, notes_data)
  end

  private

  def json_response status, notes_data = {}
    {status: status}.merge(notes_data)
  end

  def get_note_data type, order_case, order
    case type
    when "special_offer_note"
      order_case.special_offer_note
    when "order_note"
      order.note
    when "location_note"
      order.location&.note
    when "arrangement_note"
      Arrangement.find_by(id: arrangement_id)&.note
    end
  end
end
