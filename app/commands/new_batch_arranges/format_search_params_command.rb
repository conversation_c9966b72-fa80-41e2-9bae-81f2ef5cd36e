class NewBatchArranges::FormatSearchParamsCommand
  attr_reader :admin_id, :options

  def initialize admin_id, options = {}
    @admin_id = admin_id
    @options = options
  end

  def perform
    options[:working_from_date] = ServerTime.parse(options[:working_from_date])&.beginning_of_day if
      options[:working_from_date].present?
    options[:working_to_date] = ServerTime.parse(options[:working_to_date])&.end_of_day if
      options[:working_to_date].present?
    if options[:department_id].present?
      options[:department_id] = options[:department_id].scan(/\w+/)
    else
      departments = Department.joins(:admin_departments).where("admin_departments.admin_id = ?", admin_id)
      options[:department_id] = departments.pluck(:id)
    end
    options[:prefecture_id] = options[:prefecture_id].scan(/\w+/) if options[:prefecture_id].present?
    options[:location_id] = options[:location_id].scan(/\w+/) if options[:location_id].present?
    options[:staff_id] = options[:staff_id].scan(/\w+/) if options[:staff_id].present?
    options[:corporation_id] = options[:corporation_id].scan(/\w+/) if options[:corporation_id].present?
    return options if options[:segment].blank?

    segments = options[:segment].scan(/\w+/)
    options[:segment_ids] = []
    options[:segment_ids] << OrderCase.segment_ids[:regular_order] if segments.include?("regular")
    options[:segment_ids] << OrderCase.segment_ids[:haken] if segments.include?("haken")
    options[:segment_ids] << OrderCase.segment_ids[:contract] if segments.include?("contract")
    options[:segment_ids] += OrderCase::TRAINING_SEGMENT_IDS if segments.include?("training")

    options
  end
end
