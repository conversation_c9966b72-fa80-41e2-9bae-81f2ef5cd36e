class OrderCases::ArrangeFullPortionCommand
  attr_reader :order_case_ids, :options

  def initialize order_case_ids, options = {}
    @order_case_ids = order_case_ids
    @options = options
  end

  def perform
    order_cases = OrderCase.where(id: order_case_ids)
    full_portion_oc_ids = order_cases.map do |order_case|
      next unless order_case.full_arranged?

      order_case.id
    end

    full_portion_oc_ids.compact!
    return if full_portion_oc_ids.blank?

    staff_apply_order_case_ids = StaffApplyOrderCase.default_unscope
      .not_processed
      .by_order_case_ids(full_portion_oc_ids)
      .pluck(:id)

    StaffApplyOrderCases::RejectAppliedWorker.perform_async(staff_apply_order_case_ids, options)

    staff_apply_order_case_ids
  end
end
