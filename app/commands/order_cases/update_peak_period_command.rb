module OrderCases
  class UpdatePeakPeriodCommand
    attr_reader :order_case, :data

    def initialize order_case, data = {}
      @order_case = order_case
      @data = data
    end

    def perform
      period_rate = get_period_rate
      return unless period_rate

      order_case.update_columns(peak_period_order_rate: period_rate.order_rate.to_i,
        peak_period_unit_rate: period_rate.unit_rate.to_i)
    end

    private

    def get_period_rate
      return data[:peak_periods][order_case.id] if data.present? && data[:peak_periods].present?

      working_date = order_case.working_date
      peak_period = PeakPeriod.find_by(target_date: working_date.beginning_of_day)
      return unless peak_period

      order = order_case.order
      PeakPeriods::GetCurrentPeriodRateQuery.execute(
        peak_period.id,
        order.location_prefecture_id,
        order.corporation_id
      )
    end
  end
end
