class OrderCases::UpdateApplyCountCommand
  attr_reader :order_case_ids

  def initialize order_case_ids
    @order_case_ids = order_case_ids
  end

  def perform
    return if order_case_ids.blank?

    order_cases = OrderCase.includes(unscope_staff_staff_apply_order_cases: [:staff])
      .where(id: order_case_ids)

    return if order_cases.blank?

    staff_apply_arranged = OrderPortion.joins(arrangement: :staff_apply_order_cases)
      .where(order_case_id: order_case_ids, status_id: :arranged,
        staff_apply_order_cases: {
          status_id: StaffApplyOrderCase.status_ids[:arranged],
          apply_type_id: StaffApplyOrderCase.apply_type_ids[:staff_apply]
        })

    order_cases.each do |order_case|
      staff_apply_order_cases = order_case.unscope_staff_staff_apply_order_cases
      remained_apply = staff_apply_order_cases.select(&:not_processed?)

      if order_case.training?
        staff_apply_ocs = staff_apply_order_cases.select(&:staff_apply?)
      else
        staff_apply_ocs = staff_apply_order_cases.select{|saoc| saoc.staff_apply? && saoc.staff.op_confirm?}
      end

      not_processed_apply = staff_apply_ocs.select(&:not_processed?)
      arranged_apply = staff_apply_arranged.select{|portion| portion.order_case_id == order_case.id}

      staff_apply_count = not_processed_apply.size + arranged_apply.size

      order_case.staff_apply_count = staff_apply_count
      order_case.remained_apply = remained_apply.size
    end

    OrderCase.import(
      order_cases.to_a,
      on_duplicate_key_update: [:staff_apply_count, :remained_apply]
    )
  end
end
