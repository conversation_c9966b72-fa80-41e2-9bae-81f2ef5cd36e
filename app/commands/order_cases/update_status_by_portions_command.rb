module OrderCases
  class UpdateStatusByPortionsCommand
    attr_reader :order_case_id

    def initialize order_case_id
      @order_case_id = order_case_id
    end

    def perform
      order_case = OrderCase.find(order_case_id)
      order_case_params = update_status_params(order_case)
      return if order_case_params.blank?

      order_case.update_columns(order_case_params)
    end

    private

    def update_status_params order_case
      oc_status_id = get_status_by_portions
      return if oc_status_id == order_case.status_id_before_type_cast

      {
        status_id: oc_status_id,
        arranged_at: oc_status_id == OrderCase.status_ids[:arranged] ? ServerTime.now : nil,
        canceled_at: oc_status_id == OrderCase.status_ids[:cancel] ? ServerTime.now : nil
      }.compact
    end

    def get_status_by_portions
      status = OrderCases::RecruitProcessQuery.execute(order_case_id)

      OrderCase.status_ids[status]
    end
  end
end
