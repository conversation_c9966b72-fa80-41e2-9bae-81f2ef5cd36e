module OrderCases
  class FormatOwnerFilterParamsCommand
    attr_reader :params, :user_location_ids, :user_corporation_id

    def initialize params, **kwargs
      @params = params
      @user_location_ids = kwargs[:user_location_ids]
      @user_corporation_id = kwargs[:user_corporation_id]
    end

    def execute
      hash_params = ActiveSupport::HashWithIndifferentAccess.new(params)
      search_params = format_corporation_id_params
      search_params = search_params.merge format_location_ids_params(hash_params)
      search_params = search_params.merge format_case_started_at_params(hash_params)
      search_params = search_params.merge format_start_time_window_params(hash_params)
      search_params = search_params.merge format_contained_time_window_params(hash_params)
      search_params = search_params.merge format_status_id_params(hash_params)
      search_params = search_params.merge format_working_status_ids_params(hash_params)
      search_params = search_params.merge format_violation_day_params(hash_params)
      search_params = search_params.merge format_free_word_params(hash_params)
      search_params = search_params.merge format_sort_params(hash_params)
      search_params.merge format_paginate_params(hash_params)
    end

    private

    def format_case_started_at_params hash_params
      case_started_at_params = {}
      if hash_params[:search_by_choose_date].to_s.true?
        case_started_at_params[:case_started_at_from] = hash_params[:from_start_date].in_time_zone.beginning_of_day
        case_started_at_params[:case_started_at_to] = hash_params[:to_start_date].in_time_zone.end_of_day

        return case_started_at_params
      end

      case_started_at_params[:case_started_at_from] = hash_params[:start_date].in_time_zone.beginning_of_day if
        hash_params[:start_date].present?
      case_started_at_params[:case_started_at_to] = hash_params[:end_date].in_time_zone.end_of_day if
        hash_params[:end_date].present?

      case_started_at_params
    end

    def format_start_time_window_params hash_params
      return {} unless hash_params[:search_by_start_time].to_s.true?

      working_times = hash_params[:working_time].split(",")
      return {} if working_times.blank? || working_times.first.split("-").blank?

      {start_time_window: working_times}
    end

    def format_contained_time_window_params hash_params
      return {} if hash_params[:search_by_start_time].to_s.true?
      return {} if hash_params[:start_time].blank? && hash_params[:end_time].blank?

      start_time = hash_params[:start_time].presence || Settings.time.start_of_day
      end_time = hash_params[:end_time].presence || Settings.time.end_of_day

      {contained_time_window: ["#{start_time}-#{end_time}"]}
    end

    def format_status_id_params hash_params
      return {} if hash_params[:status_before_cast].blank?

      {status_ids: hash_params[:status_before_cast].split(",")}
    end

    def format_working_status_ids_params hash_params
      return {} if hash_params[:working_status_before_cast].blank?

      {working_status_ids: hash_params[:working_status_before_cast].split(",")}
    end

    def format_violation_day_params hash_params
      violation_params = {}
      violation_params[:violation_day_from] = hash_params[:violation_start] if hash_params[:violation_start].present?
      violation_params[:violation_day_to] = hash_params[:violation_end] if hash_params[:violation_end].present?

      violation_params
    end

    def format_location_ids_params hash_params
      location_ids = hash_params[:location_id].presence || user_location_ids
      {location_ids: location_ids.split(",")}
    end

    def format_corporation_id_params
      {corporation_id: user_corporation_id}
    end

    def format_free_word_params hash_params
      return {} if hash_params[:free_word].blank?

      {free_word: hash_params[:free_word]}
    end

    def format_sort_params hash_params
      asc = hash_params[:asc].nil? || hash_params[:asc].to_s.true?
      sort_order = asc ? :asc : :desc
      sort_key = hash_params[:order_key].presence&.to_sym

      {
        sort_key: sort_key,
        sort_order: sort_order
      }
    end

    def format_paginate_params hash_params
      {
        page: hash_params[:page].presence || Settings.page.default,
        per_page: hash_params[:per_page].presence || Settings.per_page.order_case_list[0]
      }
    end
  end
end
