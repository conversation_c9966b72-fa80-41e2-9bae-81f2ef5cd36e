module StaffLongVacations
  class GetOtArrangedCommand
    MAXIMUM_WORKING_TIME_DURING_LONG_VACATION = 8.hours

    attr_reader :staff_id, :start_time, :end_time

    def initialize staff_id, start_time, end_time
      @staff_id = staff_id
      @start_time = start_time
      @end_time = end_time
    end

    def perform
      arrangements = Arrangement.arranged
        .select(:id, :working_started_at, :working_ended_at, :break_time)
        .where(staff_id: staff_id)
        .where("working_started_at >= ? AND working_started_at <= ?", start_time, end_time)

      return [] if arrangements.blank?

      arrangements = arrangements.group_by{|arrangement| DateTimeFormatting.full_date(arrangement.working_started_at)}

      data = arrangements.keys.map do |date|
        next handle_for_one_job_in_day(arrangements[date].first) if arrangements[date].size == 1

        handle_for_multiple_jobs_in_day(arrangements[date])
      end.compact

      data.flatten
    end

    private

    def handle_for_one_job_in_day arrangement
      working_time = calc_working_time(
        arrangement.working_started_at,
        arrangement.working_ended_at,
        arrangement.break_time
      )

      return unless ot_on_date?(working_time)

      arrangement.id
    end

    def handle_for_multiple_jobs_in_day arrangements
      working_times = arrangements.map do |arrangement|
        calc_working_time(arrangement.working_started_at, arrangement.working_ended_at, arrangement.break_time)
      end

      return unless ot_on_date?(working_times.sum)

      arrangements.pluck(:id)
    end

    def calc_working_time started_at, ended_at, break_time
      (ended_at - started_at).to_i - break_time.minutes.to_i
    end

    def ot_on_date? working_time
      return true if working_time.to_i > MAXIMUM_WORKING_TIME_DURING_LONG_VACATION.to_i

      false
    end
  end
end
