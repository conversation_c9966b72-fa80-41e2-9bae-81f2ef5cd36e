module StaffLongVacations
  class AlertAdminStaffHasOvertimeArrangedCommand
    attr_reader :staff_id, :staff_long_vacation_ids

    def initialize staff_id, staff_long_vacation_ids
      @staff_id = staff_id
      @staff_long_vacation_ids = staff_long_vacation_ids
    end

    def perform
      staff_review = Staff.find_by(original_staff_id: staff_id)
      return unless staff_review

      staff_long_vacations = StaffLongVacation.select(:id, :staff_id, :start_date, :end_date)
        .where(id: staff_long_vacation_ids, staff_id: staff_review.id)

      return if staff_long_vacations.blank?

      data_for_mail = get_data_to_send_mail(staff_long_vacations)
      return unless data_for_mail

      send_mail_to_admin(data_for_mail)
    end

    private

    def get_data_to_send_mail staff_long_vacations
      arrangement_over_time_ids = []
      staff_long_vacations_overlap = []

      staff_long_vacations.each do |staff_long_vacation|
        start_date = staff_long_vacation.start_date.beginning_of_day
        end_date = staff_long_vacation.end_date.end_of_day

        arrangement_ids = get_ot_arrangement_ids(start_date, end_date)
        next if arrangement_ids.blank?

        staff_long_vacations_overlap << overlap_long_vacation_format_date(start_date, end_date)
        arrangement_over_time_ids += arrangement_ids
      end

      return if arrangement_over_time_ids.blank?

      arrangements_data = data_arrangements_for_mail(arrangement_over_time_ids)
      return if arrangements_data.blank?

      staff = data_staff_for_mail

      {
        staff_name: staff[:staff_name],
        staff_number: staff.staff_number,
        long_vacation_dates: staff_long_vacations_overlap.join(", "),
        arrangements: arrangements_data
      }
    end

    def get_ot_arrangement_ids start_time, end_time
      StaffLongVacations::GetOtArrangedCommand.new(staff_id, start_time, end_time).perform
    end

    def data_arrangements_for_mail arrangement_ids
      arrangements = Arrangement.joins(order: :location)
        .select(:working_started_at, :working_ended_at, :order_case_id, "locations.name AS location_name")
        .where(id: arrangement_ids)

      arrangements.map do |arrangement|
        arrangement = arrangement.extend(Arrangements::BaseFormatterRawQuery)

        {
          order_case_id: arrangement.order_case_id,
          location_name: arrangement.location_name,
          working_date: I18n.l(arrangement.working_started_at, format: Settings.date.year_month_and_date),
          working_time: arrangement.working_time
        }
      end
    end

    def data_staff_for_mail
      Staff.joins(:account).select(:staff_number, "accounts.name AS staff_name").find_by(id: staff_id)
    end

    def overlap_long_vacation_format_date start_date, end_date
      TimeRangeFormatting.date_to_date(start_date, end_date)
    end

    def send_mail_to_admin data_for_mail
      receiver_emails = Settings.op_center_emails

      receiver_emails.each do |email|
        AdminMailer.alert_arranged_job_during_long_vacation_mail(email, data_for_mail).deliver_now
      end
    end
  end
end
