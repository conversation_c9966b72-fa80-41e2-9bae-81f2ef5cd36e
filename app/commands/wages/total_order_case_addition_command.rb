module Wages
  class TotalOrderCaseAdditionCommand
    attr_reader :order_case, :cached_params

    def initialize order_case, cached_params = {}
      @order_case = order_case
      @cached_params = cached_params
    end

    def perform
      ((order_case.special_offer_fee.to_i + peak_period_order_price_addition) * OrderCase::OFFER_FEE_RATE).ceil
    end

    private

    def peak_period_order_price_addition
      return cached_params[:peak_period_order_price_addition].to_i if
        cached_params[:peak_period_order_price_addition]

      WagesModule.peak_period_order_price_addition(period_rate)
    end

    def period_rate
      get_period_rate[order_case.id]
    end

    def get_period_rate
      Wages::PeriodRatesQuery.execute(order_case.id) || {}
    end
  end
end
