module Wages
  class UrgentUnitPriceAdditionCommand
    attr_reader :staff, :order_case, :cached_params

    def initialize staff, order_case, cached_params = {}
      @staff = staff
      @order_case = order_case
      @cached_params = cached_params
    end

    def perform
      return 0 unless order_case.is_urgent
      return ArrangePayment::DEFAULT_REGULAR_ORDER_URGENT_ADDITION if order_case.regular_order?
      return 0 if urgent_unit_price <= basic_unit_price.to_i

      urgent_unit_price - basic_unit_price.to_i
    end

    private

    def basic_unit_price
      return cached_params[:unit_price] if cached_params[:unit_price]

      cached_params[:unit_price] = get_basic_unit_price
    end

    def get_basic_unit_price
      params = {
        payment_unit_price: payment_unit_price,
        regular_payment_basic_price: cached_params[:regular_payment_basic_price]
      }

      Wages::BasicUnitPriceCommand.new(staff, order_case, params).perform
    end

    def urgent_unit_price
      return cached_params[:urgent_unit_price] if cached_params[:urgent_unit_price]

      cached_params[:urgent_unit_price] = get_urgent_unit_price
    end

    def get_urgent_unit_price
      payment_unit_price.payment_urgent_unit_price.to_i
    end

    def payment_unit_price
      return cached_params[:payment_unit_price] if cached_params[:payment_unit_price]

      cached_params[:payment_unit_price] = get_payment_unit_price || PaymentUnitPrice.new
    end

    def get_payment_unit_price
      Wages::PaymentUnitPricesQuery.fetch_individual(staff, order_case)
    end
  end
end
