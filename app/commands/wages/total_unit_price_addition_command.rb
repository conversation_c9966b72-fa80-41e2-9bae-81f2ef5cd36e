module Wages
  class TotalUnitPriceAdditionCommand
    attr_reader :staff, :order_case, :cached_params

    def initialize staff, order_case, cached_params = {}
      @staff = staff
      @order_case = order_case
      @cached_params = cached_params
    end

    def perform
      return 0 unless order_case.not_training?
      return total_unit_price_addition.round unless order_case_is_night_working?

      (total_unit_price_addition * ArrangePayment::NIGHT_SALARY_RATE).round
    end

    private

    def total_unit_price_addition
      peak_period_unit_price_addition.to_i + additional_amount_for_urgent_or_bonus.to_i
    end

    def additional_amount_for_urgent_or_bonus
      return urgent_addition_amount if order_case.is_urgent? && urgent_addition_amount.to_i > 0

      payment_bonus_unit_price
    end

    def urgent_addition_amount
      return ArrangePayment::DEFAULT_REGULAR_ORDER_URGENT_ADDITION if order_case.regular_order?

      unit_price = get_unit_price
      urgent_unit_price = get_urgent_unit_price
      return 0 if urgent_unit_price.to_i <= unit_price.to_i

      urgent_unit_price.to_i - unit_price.to_i
    end

    def get_unit_price
      return cached_params[:unit_price] if cached_params[:unit_price]

      cached_params[:unit_price] = order_case_is_night_working? ? night_unit_price : basic_unit_price
    end

    def basic_unit_price
      params = {
        payment_unit_price: payment_unit_price,
        regular_payment_basic_price: cached_params[:regular_payment_basic_price]
      }

      Wages::BasicUnitPriceCommand.new(staff, order_case, params).perform
    end

    def night_unit_price
      params = {
        payment_unit_price: payment_unit_price,
        regular_payment_basic_price: cached_params[:regular_payment_basic_price]
      }

      Wages::NightUnitPriceCommand.new(staff, order_case, params).perform
    end

    def get_urgent_unit_price
      return cached_params[:urgent_unit_price] if cached_params[:urgent_unit_price]

      cached_params[:urgent_unit_price] = payment_unit_price.payment_urgent_unit_price.to_i
    end

    def payment_unit_price
      return cached_params[:payment_unit_price] if cached_params[:payment_unit_price]

      cached_params[:payment_unit_price] = get_payment_unit_price || PaymentUnitPrice.new
    end

    def get_payment_unit_price
      Wages::PaymentUnitPricesQuery.fetch_individual(staff, order_case)
    end

    def payment_bonus_unit_price
      return cached_params[:payment_bonus_unit_price].to_i if cached_params[:payment_bonus_unit_price]

      order_case.arrangements.is_recruiting_jobs&.first&.payment_bonus_unit_price || 0
    end

    def order_case_is_night_working?
      order_case.is_night_working? && !order_case.is_both_day_and_night_working?
    end

    def peak_period_unit_price_addition
      return cached_params[:peak_period_unit_price_addition].to_i if
        cached_params[:peak_period_unit_price_addition]

      return 0 if order_case.regular_order?

      WagesModule.peak_period_unit_price_addition(period_rate)
    end

    def period_rate
      get_period_rate[order_case.id]
    end

    def get_period_rate
      Wages::PeriodRatesQuery.execute(order_case.id) || {}
    end
  end
end
