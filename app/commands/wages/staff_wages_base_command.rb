module Wages
  class StaffWagesBaseCommand
    attr_reader :staff, :order_case_ids

    WAGE_STRATEGIES = %i(
      peak_period_unit_price_addition
      peak_period_order_price_addition
      total_unit_price_addition
      total_order_case_addition
      urgent_unit_price_addition
      basic_unit_price
      night_unit_price
    )

    def initialize staff, order_case_ids
      @staff = staff
      @order_case_ids = order_case_ids
    end

    def perform methods
      raise(CustomExceptions::InvalidMethodError) if methods.blank? || (methods - WAGE_STRATEGIES).present?
      return [] if order_case_ids.blank?

      order_cases = OrderCase.where(id: order_case_ids)

      order_cases.find_each.map do |order_case|
        get_wages_by_order_case(order_case, methods)
      end
    end

    private

    def get_wages_by_order_case order_case, methods
      wages = {id: order_case.id}

      methods.each do |method|
        wages[method] = self.send(method, order_case)
      end

      wages
    end

    def load_period_rates
      @period_rates ||= Wages::PeriodRatesQuery.execute(order_case_ids)
      @period_rates ||= {}
      @period_rates
    end

    def load_staff_payment_unit_prices
      @payment_unit_prices ||= Wages::PaymentUnitPricesQuery.fetch_by_order_cases(staff, order_case_ids)
      @payment_unit_prices ||= {}
      @payment_unit_prices
    end

    def load_arrange_payment_prices
      @arrange_payment_prices ||= Wages::ArrangePaymentPricesQuery.execute(order_case_ids)
      @arrange_payment_prices ||= {}
      @arrange_payment_prices
    end

    def current_level
      @current_level ||= staff&.current_level || StaffLevel.new
    end

    def current_rank
      @current_rank ||= staff&.current_rank || StaffRank.new
    end

    def basic_unit_price order_case
      params = {
        payment_unit_price: load_staff_payment_unit_prices[order_case.id] || PaymentUnitPrice.new,
        regular_payment_basic_price: load_arrange_payment_prices[order_case.id].try(:payment_basic_unit_price).to_i,
        current_rank: current_rank,
        current_level: current_level
      }

      cmd = Wages::BasicUnitPriceCommand.new(staff, order_case, params)
      cmd.perform
    end

    def night_unit_price order_case
      params = {
        payment_unit_price: load_staff_payment_unit_prices[order_case.id] || PaymentUnitPrice.new,
        regular_payment_basic_price: load_arrange_payment_prices[order_case.id].try(:payment_basic_unit_price).to_i,
        current_rank: current_rank,
        current_level: current_level
      }

      cmd = Wages::NightUnitPriceCommand.new(staff, order_case, params)
      cmd.perform
    end

    def peak_period_unit_price_addition order_case
      return 0 if order_case.regular_order?

      period_rate = load_period_rates[order_case.id]
      WagesModule.peak_period_unit_price_addition(period_rate)
    end

    def peak_period_order_price_addition order_case
      return 0 if order_case.regular_order?

      period_rate = load_period_rates[order_case.id]
      WagesModule.peak_period_order_price_addition(period_rate)
    end

    def total_unit_price_addition order_case
      is_night_working = order_case.is_night_working? && !order_case.is_both_day_and_night_working?
      unit_price = is_night_working ? night_unit_price(order_case) : basic_unit_price(order_case)
      payment_unit_price = load_staff_payment_unit_prices[order_case.id] || PaymentUnitPrice.new
      payment_bonus_unit_price = load_arrange_payment_prices[order_case.id].try(:payment_bonus_unit_price).to_i
      regular_payment_basic_price = load_arrange_payment_prices[order_case.id].try(:payment_basic_unit_price).to_i

      params = {
        urgent_unit_price: payment_unit_price.payment_urgent_unit_price.to_i,
        unit_price: unit_price,
        payment_bonus_unit_price: payment_bonus_unit_price,
        peak_period_unit_price_addition: peak_period_unit_price_addition(order_case),
        regular_payment_basic_price: regular_payment_basic_price
      }

      cmd = Wages::TotalUnitPriceAdditionCommand.new(
        staff,
        order_case,
        params
      )

      cmd.perform
    end

    def total_order_case_addition order_case
      cached_params = {peak_period_order_price_addition: peak_period_order_price_addition(order_case)}

      cmd = Wages::TotalOrderCaseAdditionCommand.new(order_case, cached_params)
      cmd.perform
    end

    def urgent_unit_price_addition order_case
      payment_unit_price = load_staff_payment_unit_prices[order_case.id] || PaymentUnitPrice.new
      regular_payment_basic_price = load_arrange_payment_prices[order_case.id].try(:payment_basic_unit_price).to_i

      params = {
        urgent_unit_price: payment_unit_price.payment_urgent_unit_price.to_i,
        unit_price: basic_unit_price(order_case),
        regular_payment_basic_price: regular_payment_basic_price
      }

      cmd = Wages::UrgentUnitPriceAdditionCommand.new(staff, order_case, params)
      cmd.perform
    end
  end
end
