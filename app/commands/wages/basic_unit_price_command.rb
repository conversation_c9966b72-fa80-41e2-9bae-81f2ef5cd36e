module Wages
  class BasicUnitPriceCommand
    attr_reader :staff, :order_case, :cached_params

    def initialize staff, order_case, cached_params = {}
      @staff = staff
      @order_case = order_case
      @cached_params = cached_params
    end

    def perform
      return regular_payment_basic_price if order_case.regular_order?

      payment_unit_price[payment_unit_price_field].to_i
    end

    private

    def current_level
      return cached_params[:current_level] if cached_params[:current_level]

      cached_params[:current_level] = get_current_level
    end

    def get_current_level
      staff&.current_level || StaffLevel.new
    end

    def current_rank
      return cached_params[:current_rank] if cached_params[:current_rank]

      cached_params[:current_rank] = get_current_rank
    end

    def get_current_rank
      staff&.current_rank || StaffRank.new
    end

    def payment_unit_price
      return cached_params[:payment_unit_price] if cached_params[:payment_unit_price]

      get_payment_unit_price || PaymentUnitPrice.new
    end

    def get_payment_unit_price
      Wages::PaymentUnitPricesQuery.fetch_individual(staff, order_case)
    end

    def level_by_unit_price
      level_before_type_cast = current_level&.level_before_type_cast
      level_before_type_cast = StaffLevel.levels[:debut] if level_before_type_cast == StaffLevel.levels[:oboj] || level_before_type_cast.nil?
      "level#{level_before_type_cast}"
    end

    def rank_by_unit_price
      return :rank1 unless current_rank

      "rank#{current_rank.rank_before_type_cast}"
    end

    def payment_unit_price_field
      return :training_basic_wage if current_level&.before_debut?

      "#{level_by_unit_price}_#{rank_by_unit_price}_basic_wage"
    end

    def regular_payment_basic_price
      return cached_params[:regular_payment_basic_price].to_i if cached_params[:regular_payment_basic_price]

      get_regular_payment_basic_price
    end

    def get_regular_payment_basic_price
      order_case.regular_order_arrange_payment.payment_basic_unit_price.to_i
    end
  end
end
