module SmbcInquiry
  class CheckStatusCommand
    UNCONFIRMED_STATUS = SmbcAccountInquiry.inquiry_results[:unconfirmed]
    CONFIRMING_STATUS = SmbcAccountInquiry.inquiry_results[:confirming]
    NO_ERROR = 0

    def initialize
      @inquiries = SmbcAccountInquiry.confirming
      @inquired_items = []
      @logger = Logger.new Rails.root.join("log", "smbc_account_inquiry_status.log")
    end

    def perform
      return if @inquiries.blank?

      get_status
      update_inquired_results
    end

    private

    def get_status
      result = SmbcInquiry::ApiClient.new.status(@inquiries.pluck(:id))

      return handle_generic_error(result) if result[:error].present?
      return if result[:items].blank?

      @inquired_items = result[:items]
    end

    def update_inquired_results
      return if @inquired_items.blank?

      @inquired_items.each do |item|
        inquiry = SmbcAccountInquiry.find(item[:transaction_id])
        next if inquiry.blank?

        review_account = inquiry.staff_account_transfer
        staff = Staff.includes(:staff_account_transfer).find_by(id: review_account.staff.original_staff_id)

        update_inquiry(inquiry, item)
        handle_inquiry_error(item)
        update_review_account(review_account, item)
        process_staff_account(staff, review_account, item)
        send_notification(inquiry, staff)
      end
    end

    def update_inquiry inquiry, item
      inquiry.update(
        inquiry_result: item[:result].to_i == UNCONFIRMED_STATUS ? CONFIRMING_STATUS : item[:result],
        error_code: item[:err_code].to_i == NO_ERROR ? nil : item[:err_code]
      )
    end

    def update_review_account review_account, item
      result = item[:result] == UNCONFIRMED_STATUS ? CONFIRMING_STATUS : item[:result]
      review_account.update(
        account_inquired_at: ServerTime.now,
        inquiry_result: result
      )
    end

    def process_staff_account staff, review_account, item
      return if staff.nil?

      staff.staff_account_transfer.update(
        bank_id: review_account.bank_id,
        bank_branch_id: review_account.bank_branch_id,
        account_name: review_account.account_name,
        account_name_kana: review_account.account_name_kana,
        account_type: review_account.account_type,
        account_number: review_account.account_number,
        account_inquired_at: ServerTime.now,
        inquiry_result: item[:result]
      )
    end

    def send_notification inquiry, staff
      if inquiry.confirmed_ng?
        send_reminder(staff)
      elsif inquiry.confirmed_ok?
        send_confirmation(staff)
      end
    end

    def send_reminder staff
      AdminMailer.alert_staff_update_bank_account(staff).deliver_now
      AppSendNotificationWorker.perform_async(
        [app_notification_options(staff.id, :request_reupdate_bank_account)]
      )
    end

    def send_confirmation staff
      AdminMailer.staff_update_bank_account_confirmed(staff).deliver_now
      AppSendNotificationWorker.perform_async(
        [app_notification_options(staff.id, :notify_bank_account_confirmed)]
      )
    end

    def app_notification_options staff_id, notification_type
      {
        staff_id: staff_id,
        creator_type: :by_system,
        notification_type: notification_type
      }
    end

    def handle_generic_error result
      write_log "ERROR occured: #{result[:error]} with body #{result[:body]}"
      notify_error result
    end

    def handle_inquiry_error item
      return unless item[:result] == SmbcInquiry::ApiClient::API_ERROR

      write_log "Failed to check status for: #{item}"
      notify_item_error item
    end

    def notify_error result
      message = "*Failed to inquire bank account results*\n"
      message << "* Detailed log: smbc_account_inquiry_status.log\n"
      message << "* Error: #{result[:error]}\n"
      message << "* Status: #{result[:status]}\n" if result[:status].present?
      message << "* Response body: #{result[:body]}" if result[:body].present?
      CustomNotifier::SimpleNotifier.new.call(message)
    end

    def notify_item_error item
      message = "*Failed to inquire bank account results*\n"
      message << "* Detailed log: smbc_account_inquiry_status.log\n"
      message << "* Transaction ID: #{item[:transaction_id]}\n"
      message << "* Result: #{item[:result]}\n"
      message << "* Error Code: #{item[:err_code]}\n"
      message << "* Error Reason: #{item[:err_reason]}\n"
      message << "* Valiation details: #{item[:err_validation]}\n"
      CustomNotifier::SimpleNotifier.new.call(message)
    end

    def write_log message
      @logger.info message
    end
  end
end
