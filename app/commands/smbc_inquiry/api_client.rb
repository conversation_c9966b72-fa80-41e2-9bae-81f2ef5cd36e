require "net/http"
require "uri"

module SmbcInquiry
  class ApiClient
    API_ERROR = 99

    def initialize base_url = nil
      @base_url = base_url || Settings.smbc_account_inquiry_api.base_url
      @logger = Logger.new Rails.root.join("log", "smbc_account_inquiry_api.log")
    end

    def register inquiries
      return {} if inquiries.blank?

      payload = {
        service_code: Settings.smbc_account_inquiry_api.service,
        items: generate_inquiry_list(inquiries)
      }
      send_request("register", payload)
    end

    def status ids
      return {} if ids.blank?

      payload = {
        service_code: Settings.smbc_account_inquiry_api.service,
        items: ids.map{|id| {transaction_id: id.to_s}}
      }
      send_request("status", payload)
    end

    private

    def send_request path, payload = {}
      return {} unless can_send_request?

      uri = URI.parse("#{@base_url}/#{path}")
      write_log "Sending request to #{uri} with payload: #{payload}"

      request = Net::HTTP::Post.new(uri)
      request.content_type = "application/json"
      request.body = payload.to_json

      response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == "https") do |http|
        http.request(request)
      end

      write_log "Received response with code: #{response.code} & body: #{response.body}"
      handle_response(response)
    rescue *NetHttpTimeoutErrors.all => e
      handle_error("ERROR Request timed out: #{e.message}")
    rescue StandardError => e
      handle_error("ERROR Unexpected error: #{e.message}")
    end

    def handle_response response
      return JSON.parse(response.body, symbolize_names: true) if response.is_a?(Net::HTTPSuccess)

      write_log "Unexpected response: #{response.code} - #{response.body}"
      {error: "Unexpected response", status: response.code, body: response.body}
    end

    def handle_error message
      write_log message
      {error: message}
    end

    def generate_inquiry_list inquiries
      inquiries.map do |i|
        {
          transaction_id: i.id.to_s,
          account_code: i.account_code,
          account_name: i.account_name_kana
        }
      end
    end

    def write_log message
      @logger.info message
    end

    def can_send_request?
      Settings.smbc_account_inquiry_api&.enabled
    end
  end
end
