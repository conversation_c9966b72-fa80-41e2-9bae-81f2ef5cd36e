module SmbcInquiry
  class RegisterCommand
    CONFIRMING_STATUS = SmbcAccountInquiry.inquiry_results[:confirming]

    def initialize staff_id
      staff = Staff.find_by(id: staff_id)
      review_staff = staff.reviewing_staff
      @staff_account = staff.staff_account_transfer
      @review_account = review_staff.staff_account_transfer
      @inquiry = SmbcAccountInquiry.new(inquiry_params)
      @logger = Logger.new Rails.root.join("log", "smbc_account_inquiry_register.log")
    end

    def perform
      return if @staff_account.blank? || @review_account.blank?

      update_inquiry_status
      register_inquiry
    end

    private

    def update_inquiry_status
      ActiveRecord::Base.transaction do
        @staff_account.update!(
          account_inquired_at: ServerTime.now,
          inquiry_result: CONFIRMING_STATUS
        )
        @review_account.update!(
          account_inquired_at: ServerTime.now,
          inquiry_result: CONFIRMING_STATUS
        )
        @inquiry.save!
      end
    end

    def register_inquiry
      result = SmbcInquiry::ApiClient.new.register([@inquiry])
      raise "Error occurs while registering smbc_account_inquiry, #{result[:error]}" if result[:error].present?

      if result[:items].present? && result[:items].first[:result] == SmbcInquiry::ApiClient::API_ERROR
        handle_inquiry_error(result[:items].first)
      else
        write_log "SUCCESS Registered smbc_account_inquiry: #{@inquiry.id}"
      end
    end

    def inquiry_params
      {
        account_code: format_account_code,
        account_name_kana: format_name_kana,
        staff_account_transfer_id: @review_account.id,
        inquiry_result: :confirming
      }
    end

    def format_account_code
      bank_code = @review_account.bank&.code
      branch_code = @review_account.bank_branch&.code
      account_number = @review_account.account_number
      [bank_code, branch_code, Settings.workz_deposite_type, account_number].join
    end

    def format_name_kana
      JaConverter::BankFormat.convert_all(@review_account.account_name_kana)
    end

    def handle_inquiry_error item
      write_log "Failed to register inquiry, result code 99: #{item}"
      notify_error item
    end

    def notify_error item
      message = "*Failed to register smbc_account_inquiry*\n"
      message << "* Detailed log: smbc_account_inquiry_register.log\n"
      message << "* Transaction ID: #{item[:transaction_id]}\n"
      message << "* Result: #{item[:result]}\n"
      message << "* Error Code: #{item[:err_code]}\n"
      message << "* Error Reason: #{item[:err_reason]}\n"
      message << "* Valiation details: #{item[:err_validation]}\n"
      CustomNotifier::SimpleNotifier.new.call(message)
    end

    def write_log message
      @logger.info message
    end
  end
end
