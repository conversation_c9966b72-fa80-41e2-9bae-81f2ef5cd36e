module Offers
  class AdminOfferJobsToStaffsCommand
    attr_reader :admin_id, :staff_mapping_order_case, :note_data, :is_urgent_changed

    def initialize admin_id, staff_mapping_order_case = {}, note_data = {}, is_urgent_changed = false
      @admin_id = admin_id
      @staff_mapping_order_case = staff_mapping_order_case.transform_keys(&:to_i)
      @note_data = note_data
      @is_urgent_changed = is_urgent_changed
    end

    def perform
      return if staff_mapping_order_case.blank?

      staff_ids = staff_mapping_order_case.keys
      staff_order_case_offer_data = []

      Staff.where(id: staff_ids).find_each do |staff|
        order_case_ids = staff_mapping_order_case[staff.id]
        search_params = order_case_search_params(order_case_ids)
        available_oc_ids = get_order_cases_by_params(staff, search_params).pluck(:id)
        next if available_oc_ids.empty?

        staff_order_case_offer_data += build_data_staff_order_case_offers(staff.id, available_oc_ids)
        handle_update_data_order_cases(available_oc_ids) if is_urgent_changed
      end

      return if staff_order_case_offer_data.empty?

      create_staff_order_case_offers(staff_order_case_offer_data)

      mapped_available_offer_for_staff = mapping_available_offer_oc_by_staff(staff_order_case_offer_data)
      send_mail_and_notification_to_staff(mapped_available_offer_for_staff)
    end

    private

    def order_case_search_params order_case_ids
      {
        order_case_ids: order_case_ids,
        is_recruiting: true
      }
    end

    def get_order_cases_by_params staff, search_params
      SearchJobs::StaffFilterQuery.new(
        staff,
        search_params: search_params
      ).execute
    end

    def build_data_staff_order_case_offers staff_id, order_case_ids
      order_case_ids.map do |order_case_id|
        {
          staff_id: staff_id,
          order_case_id: order_case_id,
          is_offered: true,
          admin_id: admin_id
        }
      end
    end

    def create_staff_order_case_offers staff_order_case_offer_data
      StaffOrderCaseOffer.import(
        staff_order_case_offer_data,
        on_duplicate_key_update: [:is_offered, :admin_id]
      )
    end

    def handle_update_data_order_cases order_case_ids
      order_cases = OrderCase.where(id: order_case_ids, is_urgent: false)
      updated_order_case_ids = order_cases.pluck(:id)

      order_cases.update_all(is_urgent: true, updated_at: ServerTime.now)

      arrange_payments = ArrangePayment.joins(:arrangement)
        .where(arrangements: {order_case_id: updated_order_case_ids})

      arrange_payments.update_all(payment_bonus_unit_price: 0, updated_at: ServerTime.now)

      order_cases = OrderCase.where(id: updated_order_case_ids)
      clear_wages_cache(order_cases)
    end

    def clear_wages_cache order_cases
      cache_keys = []

      order_cases.includes(:order).each do |order_case|
        date = order_case.case_started_at.strftime(Settings.date.non_separator)

        cache_keys << "wages_#{date}"
        cache_keys << "location_#{order_case.order.location_id}"
      end.uniq

      return if cache_keys.empty?

      cache_keys.each do |key|
        RedisWorkerCachingService.delete_similar_caches(key)
      end
    end

    def mapping_available_offer_oc_by_staff staff_order_case_offer_data
      staff_order_case_offer_data.each_with_object({}) do |staff_data, hash|
        hash[staff_data[:staff_id]] ||= []

        hash[staff_data[:staff_id]] << staff_data[:order_case_id]
      end
    end

    def send_mail_and_notification_to_staff mapped_available_offer_for_staff
      mapped_available_offer_for_staff.each do |staff_id, order_case_ids|
        Notifications::SendOfferJobsToStaffWorker.perform_async(admin_id, staff_id, order_case_ids, note_data)
      end
    end
  end
end
