module Offers
  class SendNotificationToStaffCommand
    attr_reader :admin_id, :staff_id, :order_case_ids, :note_data

    def initialize admin_id, staff_id, order_case_ids, note_data = {}
      @admin_id = admin_id
      @staff_id = staff_id
      @order_case_ids = order_case_ids
      @note_data = note_data
    end

    def perform
      staff = Staff.find_by(id: staff_id)
      return unless staff

      offered_order_cases = OrderCase.joins(:staff_order_case_offers)
        .where(staff_order_case_offers: {staff_id: staff.id, order_case_id: order_case_ids})
        .order_by_started_at_asc

      urgent_order_cases = offered_order_cases.where(is_urgent: true)
      normal_order_cases = offered_order_cases.where(is_urgent: false)
      return if urgent_order_cases.blank? && normal_order_cases.blank?

      handle_for_urgent_oc(staff, urgent_order_cases) if urgent_order_cases.present?
      handle_for_normal_oc(staff, normal_order_cases) if normal_order_cases.present?
    end

    private

    def handle_for_urgent_oc staff, urgent_order_cases
      send_notification_to_staff(urgent_order_cases)
      send_mail_to_staff(staff, urgent_order_cases, true)
      create_urgent_offer_mail_records(urgent_order_cases)
    end

    def handle_for_normal_oc staff, normal_order_cases
      send_notification_to_staff(normal_order_cases)
      send_mail_to_staff(staff, normal_order_cases, false)
    end

    def send_mail_to_staff staff, order_cases, is_urgent
      order_cases = order_cases.includes(
        order: [location: [:prefecture,
          {
            stations_1: [:railway_line],
            stations_2: [:railway_line],
            stations_3: [:railway_line],
            stations_4: [:railway_line],
            stations_5: [:railway_line]
          }]]
      )

      options = {order_cases: order_cases, note_data: note_data, is_urgent: is_urgent}
      Notification::SentEmailToStaffService.new(staff, :offer_job_mail_for_staff, options).execute
    end

    def send_notification_to_staff order_cases
      notification_options = order_cases.size > 1 ? notification_multiple_jobs_options :
        notification_option_for_a_job(order_cases.first)

      AppSendNotificationWorker.perform_async(notification_options)
    end

    def notification_multiple_jobs_options
      {
        staff_id: staff_id,
        creator_type: :admin,
        creator_id: admin_id,
        notification_type: :offered_multiple_jobs
      }
    end

    def notification_option_for_a_job order_case
      {
        staff_id: staff_id,
        creator_type: :admin,
        creator_id: admin_id,
        order_case_id: order_case.id,
        notification_type: order_case.is_urgent ? :offered_urgent_job : :offered_job
      }
    end

    def create_urgent_offer_mail_records urgent_order_cases
      urgent_offer_params = urgent_order_cases.to_a.map do |order_case|
        [order_case.id, staff_id]
      end

      UrgentOfferMailStaff.import!(UrgentOfferMailStaff::ATTRS, urgent_offer_params)
    end
  end
end
