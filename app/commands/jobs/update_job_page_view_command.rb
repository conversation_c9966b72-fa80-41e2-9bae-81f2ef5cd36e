module Jobs
  class UpdateJobPageViewCommand
    attr_reader :order_case_id

    def initialize order_case_id
      @order_case_id = order_case_id
    end

    def perform
      job_page_view = JobPageView.find_by(order_case_id: order_case_id)
      return create_job_page_view unless job_page_view

      if job_page_view.updated_at.to_date == ServerTime.now.to_date
        increase_lastest_date_pv_amount(job_page_view)
      else
        reset_lastest_date_pv_amount(job_page_view)
      end
    end

    private

    def create_job_page_view
      JobPageView.create!(order_case_id: order_case_id, pv_total_amount: 1, latest_date_pv_amount: 1)
    end

    def increase_lastest_date_pv_amount job_page_view
      JobPageView.update_counters(job_page_view.id, latest_date_pv_amount: 1, pv_total_amount: 1)
    end

    def reset_lastest_date_pv_amount job_page_view
      pv_total_amount = job_page_view.pv_total_amount + 1

      job_page_view.update(latest_date_pv_amount: 1, pv_total_amount: pv_total_amount)
    end
  end
end
