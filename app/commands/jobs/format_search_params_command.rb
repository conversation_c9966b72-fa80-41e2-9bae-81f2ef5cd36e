module Jobs
  class FormatSearchParamsCommand
    attr_reader :search_params

    def initialize search_params
      @search_params = search_params.symbolize_keys
    end

    def perform
      formatted_params = Hash.new

      formatted_params[:from_date] = format_date_started_at
      formatted_params[:to_date] = format_date_ended_at
      formatted_params[:pic_department_ids] = format_prefecture_ids
      formatted_params[:station_ids] = search_params[:station_ids] if search_params[:station_selected]
      formatted_params[:start_time_window] = search_params[:working_time].split(",") if
        search_params[:working_time].present?

      formatted_params[:contained_time_window] = format_contained_time_window if has_designated_work_period?

      formatted_params[:is_recruiting] = format_is_recruiting_params
      formatted_params[:has_additional_fee] = format_has_additional_fee_params
      formatted_params[:only_liked_locations] = search_params[:search_by_location_liked].presence
      formatted_params[:has_parking_area_usable] = search_params[:location_is_store_parking_area_usable].presence
      formatted_params[:only_kept_order_cases] = search_params[:search_by_keep_order_case].presence
      formatted_params[:is_time_changable] = format_is_time_changable_params
      formatted_params[:location_ids] = search_params[:location_id]

      formatted_params.compact
    end

    private

    def format_prefecture_ids
      if search_params[:search_by_prefecture] && search_params[:location_prefecture_id].present?
        prefecture_ids = search_params[:location_prefecture_id].split(",")
      elsif search_params[:prefecture_id].present?
        prefecture_ids = search_params[:prefecture_id].split(",")
      else
        prefecture_ids = []
      end

      return if prefecture_ids.blank?

      Department.where(pic_prefecture_id: prefecture_ids).pluck(:id)
    end

    def format_date_started_at
      date = search_params[:start_date_from]&.to_date
      return date if date.present? && date > default_search_date

      default_search_date
    rescue ArgumentError
      default_search_date
    end

    def format_date_ended_at
      search_params[:start_date_to]&.to_date
    rescue ArgumentError
      default_search_date
    end

    def format_is_recruiting_params
      search_params[:is_open].presence || boolean_type_cast(search_params[:is_recruiting]).presence
    end

    def format_has_additional_fee_params
      boolean_type_cast(search_params[:has_additional_fee]).presence
    end

    def format_is_time_changable_params
      boolean_type_cast(search_params[:is_time_changable]).presence
    end

    def format_contained_time_window
      ["#{search_params[:start_time]}-#{search_params[:end_time]}"]
    end

    def has_designated_work_period?
      search_params[:designated_period_selected].present? && search_params[:start_time].present? &&
        search_params[:end_time].present?
    end

    def boolean_type_cast value
      ActiveModel::Type::Boolean.new.cast(value)
    end

    def default_search_date
      ServerTime.now.to_date
    end
  end
end
