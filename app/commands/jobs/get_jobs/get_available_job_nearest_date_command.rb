module Jobs
  module GetJobs
    class GetAvailableJobNearestDateCommand < Jobs::GetJobs::BaseCommand
      def perform date_query
        filter_params = build_filter_params(date_query)

        get_order_cases_by_params(filter_params)
      end

      private

      def build_filter_params date_query
        search_params = format_base_search_params
        next_date = date_query.next_day.beginning_of_day

        search_params[:excluded_order_case_ids] = get_excluded_oc_ids(next_date)
        search_params[:from_date] = next_date
        search_params[:to_date] = nil

        {
          search_params: search_params,
          sort_params: build_sort_params
        }
      end

      def build_sort_params
        {order_key: "case_start", order_type: "asc"}
      end

      def get_excluded_oc_ids next_date
        staff_arrangement_oc_ids = staff.arrangements
          .where("arrangements.working_started_at >= ?", next_date)
          .where.not(display_status_id: :temporary_arrange)
          .pluck(:order_case_id)

        staff_applied_oc_ids = staff.staff_apply_order_cases.not_processed
          .joins(:order_case)
          .where("order_cases.case_started_at >= ?", next_date)
          .pluck(:order_case_id)

        (staff_applied_oc_ids + staff_arrangement_oc_ids).uniq
      end
    end
  end
end
