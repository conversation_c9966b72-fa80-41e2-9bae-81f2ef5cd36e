module Jobs
  module GetJobs
    class GetRecruitingJobsCommand < Jobs::GetJobs::BaseCommand
      def perform additional_params = {}
        filter_params = build_filter_params(additional_params)

        get_order_cases_by_params(filter_params)
      end

      private

      def build_filter_params additional_params
        search_params = format_base_search_params

        search_params = add_arranged_order_case_ids_to_search_params(search_params) if
          additional_params[:is_included_arranged]

        search_params[:status_ids] = status_ids_not_cancel if additional_params[:is_display_not_cancel]

        {
          search_params: search_params,
          sort_params: build_sort_by_input_params
        }
      end

      def add_arranged_order_case_ids_to_search_params search_params
        return search_params unless staff.id

        from_date = search_params[:from_date]
        to_date = search_params[:to_date]
        search_params[:arranged_order_case_ids] = get_arranged_oc_ids(from_date, to_date)

        search_params
      end

      def get_arranged_oc_ids from_date, to_date
        arranged_order_cases = OrderCase.joins(:arrangements)
          .where(arrangements: {staff_id: staff.id})

        arranged_order_cases = arranged_order_cases.where(
          "order_cases.case_started_at >= ?",
          from_date.to_date.beginning_of_day
        ) if from_date.present?

        arranged_order_cases = arranged_order_cases.where(
          "order_cases.case_started_at <= ?",
          to_date.to_date.end_of_day
        ) if to_date.present?

        arranged_order_cases.pluck(:id)
      end
    end
  end
end
