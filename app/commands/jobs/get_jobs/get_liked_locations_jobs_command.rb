module Jobs
  module GetJobs
    class GetLikedLocationsJobsCommand < Jobs::GetJobs::BaseCommand
      def perform
        filter_params = build_filter_params
        return [] if filter_params.nil?

        get_order_cases_by_params(filter_params)
      end

      private

      def build_filter_params
        search_params = format_base_search_params
        liked_location_ids = get_liked_location_ids
        return if liked_location_ids.blank?

        search_params[:location_ids] = liked_location_ids

        {
          search_params: search_params,
          sort_params: build_sort_by_input_params
        }
      end

      def get_liked_location_ids
        StaffLikeLocation.like_locations_by_staff(staff.id).pluck(:location_id)
      end
    end
  end
end
