module Jobs
  module GetJobs
    class GetLocationJobsCommand < Jobs::GetJobs::BaseCommand
      def perform location_id
        filter_params = is_public_staff? ? build_public_staff_filter_params(location_id) :
          build_filter_params(location_id)

        get_order_cases_by_params(filter_params)
      end

      private

      def build_filter_params location_id
        search_params = format_base_search_params

        search_params[:location_ids] = location_id
        search_params[:status_ids] = status_ids_not_cancel

        {
          search_params: search_params,
          sort_params: build_sort_by_input_params
        }
      end

      def build_public_staff_filter_params location_id
        search_params = format_base_search_params

        search_params[:location_ids] = location_id

        {
          search_params: search_params
        }
      end

      def is_public_staff?
        staff.id.blank?
      end
    end
  end
end
