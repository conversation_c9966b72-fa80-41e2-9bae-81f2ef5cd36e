module Jobs
  module GetJobs
    class GetSimilarOrderCasesCommand < Jobs::GetJobs::BaseCommand
      def perform order_case_id
        order_case = OrderCase.find_by(id: order_case_id)
        return [] unless order_case

        filter_params = build_filter_params(order_case)
        get_order_cases_by_params(filter_params)
      end

      private

      def build_filter_params order_case
        search_params = build_params(order_case)
        sort_params = {order_key: "case_start", order_type: "asc"}

        {
          search_params: search_params,
          sort_params: sort_params
        }
      end

      def build_params order_case
        current_location = order_case.order.location

        locations_same_station = get_locations_have_same_station(current_location.station_1).pluck(:id)
        locations_nearby = get_locations_nearby(current_location, locations_same_station).pluck(:id)

        location_ids = (locations_same_station + locations_nearby).uniq - [current_location.id]

        {
          location_ids: location_ids,
          is_recruiting: true,
          from_date: ServerTime.now.to_date
        }
      end

      def get_locations_have_same_station station_id
        Location.joins(:stations_1).where(stations: {id: station_id})
      end

      def get_locations_nearby location, exclude_location_ids
        params = {
          lat: location.latitude,
          lng: location.longitude,
          exclude_location_ids: exclude_location_ids
        }

        SearchJobs::FindNearbyLocationQuery.new(params).execute
      end
    end
  end
end
