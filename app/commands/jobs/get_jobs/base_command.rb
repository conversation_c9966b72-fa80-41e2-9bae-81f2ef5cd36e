module Jobs
  module GetJobs
    # BaseCommand serves as the parent class for commands responsible for
    # fetching job-related data (OrderCases) for a staff.
    class BaseCommand
      NOT_CANCEL_STATUSES = %i(recruiting finish_recruiting adjusting arranged).freeze

      attr_reader :staff, :params

      def initialize staff, params = {}
        @staff = staff
        @params = params&.symbolize_keys || {}
      end

      private

      def get_order_cases_by_params filter_params
        SearchJobs::StaffFilterQuery.new(staff, **filter_params).execute
      end

      def format_base_search_params
        Jobs::FormatSearchParamsCommand.new(params).perform
      end

      def build_sort_by_input_params
        return {} if params[:order_key].blank? && params[:order_type].blank?

        {order_key: params[:order_key], order_type: params[:order_type]}
      end

      def status_ids_not_cancel
        NOT_CANCEL_STATUSES
      end
    end
  end
end
