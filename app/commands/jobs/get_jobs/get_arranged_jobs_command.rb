module Jobs
  module GetJobs
    class GetArrangedJobsCommand < Jobs::GetJobs::BaseCommand
      def perform state = :worked
        return [] unless staff.id

        filter_params = build_filter_params(state)
        return [] if filter_params.nil?

        get_order_cases_by_params(filter_params)
      end

      private

      def build_filter_params state
        search_params = format_base_search_params
        from_date = search_params[:from_date]
        to_date = search_params[:to_date]

        arrangements = staff_arrangements(from_date, to_date)
        arrangements = state == :worked ? get_worked_jobs(arrangements) : get_arranged_future_jobs(arrangements)
        order_case_ids = arrangements.pluck(:order_case_id).uniq
        return if order_case_ids.empty?

        search_params[:order_case_ids] = order_case_ids
        search_params[:arranged_order_case_ids] = order_case_ids
        search_params[:status_ids] = status_ids_not_cancel

        {
          search_params: search_params,
          sort_params: build_sort_by_input_params
        }
      end

      def staff_arrangements from_date, to_date
        arrangements = Arrangement.joins(:order_case).where(staff_id: staff.id)

        arrangements = arrangements.where(
          "order_cases.case_started_at >= ?",
          from_date.to_date.beginning_of_day
        ) if from_date.present?

        arrangements = arrangements.where(
          "order_cases.case_started_at <= ?",
          to_date.to_date.end_of_day
        ) if to_date.present?

        arrangements
      end

      def get_arranged_future_jobs arrangements
        arrangements.is_arranged.not_worked.where.not(display_status_id: Arrangement::ABSENCE_STATUSES)
      end

      def get_worked_jobs arrangements
        arrangements.is_finish_recruiting.arrived
      end
    end
  end
end
