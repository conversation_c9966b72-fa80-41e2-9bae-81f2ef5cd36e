module Jobs
  module GetJobs
    class GetAppliedJobsCommand < Jobs::GetJobs::BaseCommand
      def perform
        return [] unless staff.id

        filter_params = build_filter_params
        return [] if filter_params.nil?

        get_order_cases_by_params(filter_params)
      end

      private

      def build_filter_params
        search_params = format_base_search_params
        search_params[:order_case_ids] = get_applied_job_ids(
          search_params[:from_date],
          search_params[:to_date]
        )

        return if search_params[:order_case_ids].empty?

        {
          search_params: search_params,
          sort_params: {order_key: "case_start", order_type: "asc"}
        }
      end

      def get_applied_job_ids from_date, to_date
        staff_apply_oc = StaffApplyOrderCase.joins(:order_case)
          .not_processed
          .where(staff_id: staff.id)

        staff_apply_oc = staff_apply_oc.where(
          "order_cases.case_started_at >= ?",
          from_date.to_date.beginning_of_day
        ) if from_date.present?

        staff_apply_oc = staff_apply_oc.where(
          "order_cases.case_started_at <= ?",
          to_date.to_date.end_of_day
        ) if to_date.present?

        staff_apply_oc.pluck(:order_case_id)
      end
    end
  end
end
