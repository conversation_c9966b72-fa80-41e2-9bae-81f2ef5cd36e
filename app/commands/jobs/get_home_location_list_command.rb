module Jobs
  class GetHomeLocationListCommand
    attr_reader :staff

    def initialize staff
      @staff = staff
    end

    def perform
      recent_location_ids = get_recent_worked_location_ids
      liked_location_ids = get_liked_location_ids
      all_location_ids = (recent_location_ids + liked_location_ids).uniq
      return response_data([], [], {}) if all_location_ids.empty?

      all_locations = Location.includes(corporation_group: :corporation)
        .where(id: all_location_ids)

      data_locations = LocationBlueprint.render_as_hash(all_locations, view: :staff_basic_view)
      data_locations = data_locations.index_by{|location| location[:id]}
      liked_locations =  data_locations.values_at(*liked_location_ids)
      recent_locations = data_locations.values_at(*recent_location_ids)
      oc_per_location = count_oc_by_location_ids(all_location_ids)

      response_data(liked_locations, recent_locations, oc_per_location)
    end

    private

    def response_data liked_locations, recent_locations, oc_per_location
      {
        locations: {
          liked_locations: liked_locations,
          recent_locations: recent_locations,
          oc_per_location: oc_per_location.as_json
        }
      }
    end

    def get_recent_worked_location_ids
      WorkAchievement.approved.working_in_3_months
        .joins(arrangement: :order)
        .where(arrangements: {staff_id: staff.id})
        .distinct
        .limit(Settings.top_page.limit)
        .pluck(:location_id)
    end

    def get_liked_location_ids
      StaffLikeLocation.like_locations_by_staff(staff.id)
        .limit(Settings.top_page.limit)
        .pluck(:location_id)
    end

    def count_oc_by_location_ids location_ids
      all_oc = SearchJobs::StaffFilterQuery.new(
        staff,
        search_params: {
          from_date: ServerTime.now.to_date,
          is_recruiting: true,
          location_ids: location_ids
        }
      ).execute

      all_oc.group(:location_id).count
    end
  end
end
