module Jobs
  class GetHomeOrderCasesListCommand
    attr_reader :staff

    def initialize staff
      @staff = staff
    end

    def perform
      applied_ids = get_applied_job_ids

      hot_job_ids = get_hot_work_jobs_not_applied(applied_ids).pluck(:id)
      keep_job_ids = get_keep_jobs_not_applied(applied_ids).pluck(:id)
      offer_job_ids = get_offer_jobs_not_applied(applied_ids).pluck(:id)

      Jobs::HomePageDecorator.new(staff).decorate(hot_job_ids, keep_job_ids, offer_job_ids)
    end

    private

    def get_applied_job_ids
      staff.staff_apply_order_cases
        .joins(:order_case)
        .extending(SearchJobs::Scopes)
        .in_deadline_to_apply
        .pluck(:order_case_id)
    end

    def get_hot_work_jobs_not_applied applied_ids
      current_department_id = staff.current_department_id
      department_ids = NeighborDepartment.where(original_department_id: current_department_id)
        .pluck(:neighbor_department_id)

      department_ids << current_department_id
      department_ids = department_ids.compact

      hot_work_params = {
        is_recruiting: true,
        has_additional_fee: true,
        pic_department_ids: department_ids,
        excluded_order_case_ids: applied_ids
      }

      hot_work_filter_params = build_filter_params(hot_work_params)
      order_cases = get_order_cases(hot_work_filter_params)

      order_cases.limit(Settings.top_page.limit)
    end

    def get_keep_jobs_not_applied applied_ids
      keep_job_params = {
        is_recruiting: true,
        only_kept_order_cases: true,
        excluded_order_case_ids: applied_ids
      }

      keep_job_filter_params = build_filter_params(keep_job_params)
      order_cases = get_order_cases(keep_job_filter_params)

      order_cases.limit(Settings.top_page.limit)
    end

    def get_offer_jobs_not_applied applied_ids
      offer_job_params = {
        is_recruiting: true,
        has_offer_order_cases: true,
        excluded_order_case_ids: applied_ids
      }

      offer_job_filter_params = build_filter_params(offer_job_params, false)
      order_cases = get_order_cases(offer_job_filter_params)

      order_cases.order(is_urgent: :desc, case_started_at: :asc)
        .limit(Settings.top_page.limit)
    end

    def build_filter_params search_params, requires_sorting = true
      {
        search_params: search_params,
        sort_params: requires_sorting ? default_sorting_params : {}
      }
    end

    def get_order_cases filter_params
      SearchJobs::StaffFilterQuery.new(staff, filter_params).execute
    end

    def default_sorting_params
      {order_key: "case_start", order_type: "asc"}
    end
  end
end
