module WorkAchievements
  class GetWorkingDateCommand
    attr_reader :order_case_started_at, :input_started_at

    def initialize order_case_started_at, input_started_at
      @order_case_started_at = order_case_started_at
      @input_started_at = input_started_at
    end

    def perform
      date = order_case_started_at
      return DateTimeFormatting.full_date(date) unless input_started_at

      start_time_str = DateTimeFormatting.hour_minute(order_case_started_at)
      input_time_str = DateTimeFormatting.hour_minute(input_started_at)

      if input_time_str > start_time_str && (start_time_str.to_time - input_time_str.to_time.yesterday) <= 2.hours
        date -= 1.day
      elsif input_time_str < start_time_str && (start_time_str.to_time - input_time_str.to_time) > 2.hours
        date += 1.day
      end

      DateTimeFormatting.full_date(date)
    end
  end
end
