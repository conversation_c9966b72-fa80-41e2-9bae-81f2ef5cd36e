class Interviews::StaffApplyInterviewCommand
  attr_reader :interview_id, :prefecture_id, :staff_id, :booking_by_admin

  def initialize interview_id, prefecture_id, staff_id, booking_by_admin = false
    @interview_id = interview_id
    @prefecture_id = prefecture_id
    @staff_id = staff_id
    @booking_by_admin = booking_by_admin
  end

  def perform
    staff = Staff.type_staff.find_by(id: staff_id)
    return unless staff

    interview = Interview.find_by(id: interview_id)
    raise(CustomExceptions::UnavailableInteviewError) unless interview&.is_open?

    current_apply = StaffApplyInterview.find_by(staff_id: staff_id)
    is_prefecture_change = is_prefecture_change?(current_apply, interview)
    return update_prefecture_id(current_apply) if is_prefecture_change

    room = get_available_room
    raise(CustomExceptions::UnavailableInteviewError) unless room

    ActiveRecord::Base.transaction do
      if booking_by_admin
        admin_remove_applied_past_interviews(current_apply)
      else
        staff_remove_applied_interviews
      end

      create_staff_apply_interview
    end
  end

  private

  def admin_remove_applied_past_interviews current_apply
    return unless current_apply

    current_interview = current_apply.interviews_room.interview
    raise(CustomExceptions::StaffAlreadyAppliedInterviewError) if
      current_interview.interview_date > ServerTine.now

    staff_apply_interview.destroy!
  end

  def staff_remove_applied_interviews
    StaffApplyInterview.where(staff_id: staff_id).destroy_all
  end

  def is_prefecture_change? current_apply, new_interview
    return false unless current_apply

    current_apply.interviews_room.interview_id == new_interview.id
  end

  def update_prefecture_id current_apply
    current_apply.update_columns(prefecture_id: prefecture_id, updated_at: ServerTime.now)

    current_apply
  end

  def create_staff_apply_interview
    room = get_available_room
    raise(CustomExceptions::UnavailableInteviewError) unless room

    apply = StaffApplyInterview.new(
      staff_id: staff_id,
      prefecture_id: prefecture_id
    )

    until apply.persisted? || room.nil?
      apply.interviews_room_id = room.id
      staff_apply_count = room.staff_apply_interviews.size
      next apply.save! if staff_apply_count < room.max_candidate

      room = get_available_room
    end

    raise(CustomExceptions::UnavailableInteviewError) unless apply.persisted?

    re_check_valid_apply(room, apply)

    apply
  end

  def get_available_room
    InterviewsRoom.find_by("max_candidate > staff_apply_interviews_count AND interview_id = ?", interview_id)
  end

  def re_check_valid_apply interviews_room, apply
    interviews_room.reload
    return if interviews_room.max_candidate >= interviews_room.staff_apply_interviews_count

    apply.really_destroy!
    create_staff_apply_interview
  end
end
