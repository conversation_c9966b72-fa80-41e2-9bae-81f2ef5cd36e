module Interviews
  class StaffCancelInterviewCommand
    attr_reader :staff_id, :staff_apply_interview_id

    def initialize staff_id, staff_apply_interview_id
      @staff_id = staff_id
      @staff_apply_interview_id = staff_apply_interview_id
    end

    def perform
      staff = Staff.find_by(id: staff_id)
      return unless staff

      staff_apply_interview = staff.staff_apply_interviews.find_by(id: staff_apply_interview_id)
      return unless staff_apply_interview

      current_interview_room = InterviewsRoom.find_by(id: staff_apply_interview.interviews_room_id)
      return unless current_interview_room

      current_interview = Interview.find_by(id: current_interview_room.interview_id)
      return if current_interview.nil? || !current_interview.is_open?

      ActiveRecord::Base.transaction do
        staff_apply_interview.destroy!
        update_recruitment_process(staff)
      end
    end

    private

    def update_recruitment_process staff
      staff_recruitment_process = staff.staff_recruitment_process

      staff_recruitment_process.update!(
        interview_process_code: :deleted_interview_schedule
      )
    end
  end
end
