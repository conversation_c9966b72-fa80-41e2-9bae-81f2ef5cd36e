class Interviews::CreateScheduledMailerToPicCommand
  attr_reader :interview_ids

  def initialize interview_ids = []
    @interview_ids = interview_ids
  end

  def perform
    return if interview_ids.blank?

    interviews = Interview.where(id: interview_ids)
    return if interviews.blank?

    interviews.each do |interview|
      jid_time = interview.start_time - 2.hours
      next if jid_time < ServerTime.now

      jid = Interviews::SendNotificationToPicWorker.perform_at(jid_time, interview.id)
      redis = RedisModels::NotificationInterview.new
      redis.set(interview.id, jid)
    end
  end
end
