class Interviews::DestroyScheduledMailerToPicCommand
  attr_reader :interview_ids

  def initialize interview_ids = []
    @interview_ids = interview_ids
  end

  def perform
    return if interview_ids.blank?

    redis = RedisModels::NotificationInterview.new
    jids = interview_ids.map{|interview_id| redis.get(interview_id)}

    scheduler = Sidekiq::ScheduledSet.new
    jobs = scheduler.select do |schedule|
      schedule.klass == "Interviews::SendNotificationToPicWorker" && jids.include?(schedule.jid)
    end

    jobs.map(&:delete)
    interview_ids.map{|interview_id| redis.del(interview_id)}
  end
end
