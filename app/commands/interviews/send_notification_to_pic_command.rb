class Interviews::SendNotificationToPicCommand
  attr_reader :interview_id

  def initialize interview_id
    @interview_id = interview_id
  end

  def perform
    interview = Interview.find_by(id: interview_id)
    return unless interview

    interview_date = interview.of_date_with_day
    interview_time = interview.from_hour

    interview_rooms = InterviewsRoom.includes(:room, admin: :account, staff_apply_interviews: [staff: :account])
      .applied
      .where(interview_id: interview.id)

    interview_rooms.each do |interview_room|
      admin_email = interview_room.admin&.email
      next unless admin_email

      notification_data = serialize_data(interview_room, interview_date, interview_time)
      next unless notification_data

      notification_to_pic(admin_email, notification_data)
    end
  end

  private

  def notification_to_pic email, notification_data
    AdminMailer.interview_remind_mail(email, notification_data).deliver_now
  end

  def serialize_data interview_room, interview_date, interview_time
    room = interview_room.room
    staffs_info = format_staff_info(interview_room.staff_apply_interviews)
    return if staffs_info.blank?

    {
      pic_name: interview_room.admin&.name,
      staff_apply_count: interview_room.staff_apply_interviews_count,
      interview_date: interview_date,
      interview_time: interview_time,
      room_name: room.name,
      room_url: room.url,
      staffs_info: staffs_info
    }
  end

  def format_datetime datetime, format
    return "" unless datetime

    I18n.l(datetime, format: format)
  end

  def format_staff_info staff_apply_interviews
    staff_apply_interviews.map.with_index do |staff_apply, index|
      {
        index: index + 1,
        staff_id: staff_apply.staff_id,
        staff_name: staff_apply.staff&.account&.name
      }
    end
  end
end
