module Staffs
  class CreateStaffVersionCommand
    attr_reader :admin_id, :staff_id

    def initialize admin_id, staff_id
      @admin_id = admin_id
      @staff_id = staff_id
    end

    def perform
      staff = Staff.confirmed_staff.not_marked_as_deleted.find_by(id: staff_id)
      return unless staff

      staff.staff_versions.create!(staff_version_data(staff))
    end

    private

    def staff_version_data staff
      {
        admin_id: admin_id,
        version_type: :admin_edit,
        contents: staff.to_json(
          only: Staff::STAFF_VERSION_ATTRS,
          methods: [:account_name, :account_name_kana, :account_email],
          include: {
            staff_account_transfer: {},
            staff_salary: {},
            staff_check_item: {},
            staff_departments: {
              methods: :affiliation_date_format
            },
            staff_long_vacations: {}
          }
        )
      }
    end
  end
end
