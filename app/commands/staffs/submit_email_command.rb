class Staffs::SubmitEmailCommand
  attr_reader :staff_id, :email

  def initialize staff_id, email
    @staff_id = staff_id
    @email = email
  end

  def perform
    staff = Staff.find(staff_id)
    account = staff.account
    account.assign_attributes(email: email, skip_password_validation: true, is_email_present: true)

    return {status: false, errors: account.errors.details} unless account.valid? && account.errors.blank?

    token = staff.set_reset_password_token
    send_otp_to_email(staff, email)
    {status: true, login_id: email, token: token}
  end

  private

  def send_otp_to_email staff, email
    otp_service = OtpVerificationService.new(email)
    otp_code = otp_service.generate_otp
    return if otp_code.blank?

    Mailers::SendVerificationOtpToStaffWorker.perform_async(staff.id, otp_code, email)
  end
end
