class TrainingSchedules::StaffAbsentTrainingScheduleCommand
  attr_reader :training_schedule_applicant_ids, :staff_id, :consider_dropping_out

  def initialize training_schedule_applicant_ids, staff_id, consider_dropping_out = nil
    @training_schedule_applicant_ids = training_schedule_applicant_ids
    @staff_id = staff_id
    @consider_dropping_out = consider_dropping_out
  end

  def perform
    staff = Staff.find(staff_id)

    applicants = TrainingScheduleApplicant.includes(:training_schedule)
      .where(id: training_schedule_applicant_ids, staff_id: staff.id)
    raise(CustomExceptions::RecordNotFoundError) if applicants.empty?

    absent_applicants = []
    ActiveRecord::Base.transaction(isolation: :serializable) do
      applicants.each do |applicant|
        raise(CustomExceptions::UnavailableAbsentError) unless applicant.able_to_absent?
        next if applicant.absent_with_notice?

        applicant.schedule_status_code = :absent_with_notice
        applicant.save!
        absent_applicants << applicant
      end

      recruitment_process_params = {
        training_process_code: set_training_process_code,
        consider_dropping_out: consider_dropping_out
      }

      staff.update_recruitment_process!(recruitment_process_params.compact)
    end

    absent_applicants
  end

  private

  def set_training_process_code
    TrainingScheduleApplicants::GetTrainingProcessCodeQuery.execute(staff_id)
  end
end
