class TrainingSchedules::SendAbsentNotificationToStaffsCommand
  attr_reader :staff_id, :training_schedule_id

  def initialize staff_id, training_schedule_id
    @staff_id = staff_id
    @training_schedule_id = training_schedule_id
  end

  def perform
    staff = Staff.find_by(id: staff_id)
    return unless staff

    account = staff.account
    return unless account

    training_schedule = TrainingSchedule.with_deleted.find_by(id: training_schedule_id)
    return unless training_schedule

    if account.email.present?
      send_absent_email(account, training_schedule)
    else
      send_absent_sms(account, training_schedule)
    end
  end

  private
  def send_absent_sms account, training_schedule
    MessageSenderService.notify_training_schedule_absent(
      account.tel,
      date_format: training_schedule.training_time_with_day,
      user_name: account.name
    )
  end

  def send_absent_email account, training_schedule
    StaffMailer.notify_training_schedule_absent(
      account,
      training_schedule.location&.name,
      training_schedule.formatted_datetime,
      training_schedule.training_date_weekday
    ).deliver_now
  end
end
