class TrainingSchedules::FormatAutoRestoreReasonCommand
  attr_reader :training_schedule_id, :reason, :restored_schedule_ids

  DEFAULT_REASON = %w(staff_absent change_pic delete_staff delete_schedule admin_absent)

  def initialize training_schedule_id, reason, restored_schedule_ids = []
    @training_schedule_id = training_schedule_id
    @reason = reason
    @restored_schedule_ids = restored_schedule_ids
  end

  def perform
    raise(CustomExceptions::AutoRestoreReason) if DEFAULT_REASON.exclude?(reason)
    return {} if restored_schedule_ids.empty?

    training_schedule = TrainingSchedule.with_deleted.find_by(id: training_schedule_id)
    return {} unless training_schedule

    pic_name = training_schedule.person_in_charge_name
    restored_schedules = TrainingSchedule.where(id: restored_schedule_ids)
      .includes(TrainingSchedule::INCLUDE_MODELS)
      .map(&:deleted_format)

    return {} if restored_schedule_ids.empty?

    case reason
    when "staff_absent"
      data_with_reason = format_when_staff_absent(training_schedule, restored_schedules, pic_name)
      heading_content = I18n.t("admin.training_schedule.auto_restore_mailer.staff_absent.heading")
    when "change_pic"
      data_with_reason = format_when_change_pic(training_schedule, restored_schedules, pic_name)
      heading_content = I18n.t("admin.training_schedule.auto_restore_mailer.change_pic.heading")
    when "delete_staff"
      data_with_reason = format_when_delete_staff(training_schedule, restored_schedules, pic_name)
      heading_content = I18n.t("admin.training_schedule.auto_restore_mailer.delete_staff.heading")
    when "delete_schedule"
      data_with_reason = format_when_delete_schedule(training_schedule, restored_schedules, pic_name)
      heading_content = I18n.t("admin.training_schedule.auto_restore_mailer.delete_schedule.heading")
    else
      data_with_reason = format_when_admin_absent(training_schedule, restored_schedules, pic_name)
      heading_content = I18n.t("admin.training_schedule.auto_restore_mailer.admin_absent.heading")
    end

    auto_restore_mail_data = training_schedule.auto_restore_mail_data(data_with_reason)
    auto_restore_mail_data[:heading_content] = heading_content

    auto_restore_mail_data
  end

  private

  def format_when_staff_absent training_schedule, restored_schedules, pic_name
    restored_schedules.each do |schedule|
      if schedule[:person_in_charge_id] == training_schedule.person_in_charge_id
        reason = I18n.t(
          "admin.training_schedule.auto_restore_mailer.staff_absent.reason.available_trainer",
          schedule_id: training_schedule.id,
          trainer_name: pic_name
        )
      else
        reason = I18n.t(
          "admin.training_schedule.auto_restore_mailer.staff_absent.reason.available_location_session",
          schedule_id: training_schedule.id
        )
      end

      schedule[:auto_restore_reason] = reason
    end
  end

  def format_when_change_pic training_schedule, restored_schedules, pic_name
    restored_schedules.each do |schedule|
      schedule[:auto_restore_reason] = I18n.t(
        "admin.training_schedule.auto_restore_mailer.change_pic.reason",
        schedule_id: training_schedule.id,
        trainer_name_old: schedule[:person_in_charge],
        trainer_name_new: pic_name
      )
    end
  end

  def format_when_delete_staff training_schedule, restored_schedules, pic_name
    restored_schedules.each do |schedule|
      if schedule[:person_in_charge_id] == training_schedule.person_in_charge_id
        reason = I18n.t(
          "admin.training_schedule.auto_restore_mailer.delete_staff.reason.available_trainer",
          schedule_id: training_schedule.id,
          trainer_name: pic_name
        )
      else
        reason = I18n.t(
          "admin.training_schedule.auto_restore_mailer.delete_staff.reason.available_location_session",
          schedule_id: training_schedule.id
        )
      end

      schedule[:auto_restore_reason] = reason
    end
  end

  def format_when_delete_schedule training_schedule, restored_schedules, pic_name
    restored_schedules.each do |schedule|
      if schedule[:person_in_charge_id] == training_schedule.person_in_charge_id
        reason = I18n.t(
          "admin.training_schedule.auto_restore_mailer.delete_schedule.reason.available_trainer",
          schedule_id: training_schedule.id,
          trainer_name: pic_name
        )
      else
        reason = I18n.t(
          "admin.training_schedule.auto_restore_mailer.delete_schedule.reason.available_location_session",
          schedule_id: training_schedule.id
        )
      end

      schedule[:auto_restore_reason] = reason
    end
  end

  def format_when_admin_absent training_schedule, restored_schedules, pic_name
    restored_schedules.each do |schedule|
      if schedule[:person_in_charge_id] == training_schedule.person_in_charge_id
        reason = I18n.t(
          "admin.training_schedule.auto_restore_mailer.admin_absent.reason.available_trainer",
          schedule_id: training_schedule.id,
          trainer_name: pic_name
        )
      else
        reason = I18n.t(
          "admin.training_schedule.auto_restore_mailer.admin_absent.reason.available_location_session",
          schedule_id: training_schedule.id
        )
      end

      schedule[:auto_restore_reason] = reason
    end
  end
end
