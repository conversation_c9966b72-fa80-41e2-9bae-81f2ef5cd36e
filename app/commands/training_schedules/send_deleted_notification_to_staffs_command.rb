class TrainingSchedules::SendDeletedNotificationToStaffsCommand
  attr_reader :staff_ids, :training_schedule_id

  def initialize staff_ids, training_schedule_id
    @staff_ids = staff_ids
    @training_schedule_id = training_schedule_id
  end

  def perform
    return if staff_ids.empty?

    staffs = Staff.where(id: staff_ids).includes(:account)
    return if staffs.empty?

    training_schedule = TrainingSchedule.with_deleted.find_by(id: training_schedule_id)
    return unless training_schedule

    staffs.each do |staff|
      account = staff.account
      next unless account

      if account&.email.present?
        send_deleted_email(account, training_schedule)
      else
        send_deleted_sms(account, training_schedule)
      end
    end
  end

  private

  def send_deleted_sms account, training_schedule
    MessageSenderService.notify_training_schedule_deleted(
      account.tel,
      account.name,
      training_schedule.training_time_with_day
    )
  end

  def send_deleted_email account, training_schedule
    StaffMailer.notify_training_schedule_deleted(
      account,
      training_schedule.location&.name,
      training_schedule.formatted_datetime,
      training_schedule.training_date_weekday
    ).deliver_now
  end
end
