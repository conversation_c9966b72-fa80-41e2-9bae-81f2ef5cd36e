class TrainingSchedules::DeleteTrainingScheduleCommand
  attr_reader :current_admin_id, :training_schedule_ids, :trigger_schedule_id

  def initialize current_admin_id, training_schedule_ids, trigger_schedule_id = nil
    @current_admin_id = current_admin_id
    @training_schedule_ids = training_schedule_ids
    @trigger_schedule_id = trigger_schedule_id
  end

  def perform
    deleted_training_schedule_ids = TrainingSchedule.where(id: training_schedule_ids).pluck(:id)
    raise(CustomExceptions::InvalidTrainingSchedules) if deleted_training_schedule_ids.blank?

    training_schedule_applicants = TrainingScheduleApplicant.where(training_schedule_id: deleted_training_schedule_ids)
      .group_by(&:training_schedule_id)

    ActiveRecord::Base.transaction do
      TrainingSchedule.where(id: deleted_training_schedule_ids)
        .update_all(deleted_at: ServerTime.now, deleted_by: trigger_schedule_id)

      TrainingScheduleApplicant.where(training_schedule_id: deleted_training_schedule_ids)
        .update_all(deleted_at: ServerTime.now)
    end

    staff_ids = []
    training_schedules = TrainingSchedule.only_deleted.where(id: deleted_training_schedule_ids)

    training_schedules.each do |training_schedule|
      applicants = training_schedule_applicants[training_schedule.id]
      staff_ids += applicants.pluck(:staff_id)
      notify_staff_schedule_deleted(training_schedule, applicants)

      TrainingSchedules::SendNotificationToTrainersWorker.perform_async(training_schedule.id, "delete")
      TrainingScheduleLoggingWorker.perform_async(
        "admin",
        {
          admin_id: current_admin_id,
          target_id: training_schedule.id,
          action_type: "delete"
        }.as_json
      )
    end

    handle_staff_recruitment_process_code(staff_ids)
    auto_restore_training_schedule(deleted_training_schedule_ids)
  end

  private

  def notify_staff_schedule_deleted training_schedule, applicants
    applied_staff_ids = applicants.select{|applicant| applicant.schedule_status_code == "booked"}
      .map(&:staff_id)

    return if applied_staff_ids.blank? || !can_send_notify?(training_schedule)

    notification_data = []

    TrainingSchedules::SendDeletedNotificationToStaffsWorker.perform_async(
      applied_staff_ids,
      training_schedule.id
    )

    applied_staff_ids.each do |staff_id|
      opt = {
        staff_id: staff_id,
        creator_type: :by_system,
        notification_type: :deleted_training_schedule,
        params: {
          job_date_time: training_schedule.training_time_with_day
        }
      }

      if current_admin_id
        opt[:creator_type] = :admin
        opt[:creator_id] = current_admin_id
      end

      notification_data << opt
    end
    send_notifications(notification_data)
  end

  def can_send_notify? training_schedule
    training_schedule.training_time_with_day.present? &&
    !training_schedule.has_training_time_passed?
  end

  def send_notifications data
    jids = []
    data.in_groups_of(Settings.app_notification.batch_size).each do |data|
      jids << AppSendNotificationWorker.perform_async(data.reject(&:blank?).as_json)
    end
  end

  def handle_staff_recruitment_process_code staff_ids
    return if staff_ids.blank?

    Staff.where(id: staff_ids).find_each do |staff|
      training_process_code = set_training_process_code(staff.id)
      staff.update_recruitment_process!(
        training_process_code: training_process_code
      )
    end
  end

  def auto_restore_training_schedule deleted_training_schedule_ids
    return if trigger_schedule_id

    deleted_training_schedule_ids.each do |training_schedule_id|
      auto_restore_cmd = TrainingSchedules::AutoRestoreTrainingScheduleCommand.new(training_schedule_id)
      restored_schedule_ids = auto_restore_cmd.perform

      TrainingSchedules::SendAutoRestoreMailerWorker.perform_async(
        training_schedule_id,
        "delete_schedule",
        restored_schedule_ids
      )
    end
  end

  def set_training_process_code staff_id
    TrainingScheduleApplicants::GetTrainingProcessCodeQuery.execute(staff_id)
  end
end
