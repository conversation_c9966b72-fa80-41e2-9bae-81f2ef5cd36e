class TrainingSchedules::AutoRestoreTrainingScheduleCommand
  attr_reader :training_schedule_id, :update_pic

  def initialize training_schedule_id, update_pic = false
    @training_schedule_id = training_schedule_id
    @update_pic = update_pic
  end

  def perform
    training_schedule_absent = TrainingSchedule.with_deleted.find_by(id: training_schedule_id)
    return [] unless can_trigger?(training_schedule_absent)

    restore_schedule_ids = list_training_available_restore(training_schedule_absent)
    return [] if restore_schedule_ids.empty?

    restore_schedules = TrainingSchedule.only_deleted.where(id: restore_schedule_ids)
    restored_ids_cached = restore_schedules.pluck(:id)
    restore_schedules.update_all(TrainingSchedule::RESTORE_PARAMS)
    save_admin_action_log(restored_ids_cached)

    restored_ids_cached
  end

  private

  def save_admin_action_log schedule_ids
    schedule_ids.each do |schedule_id|
      TrainingScheduleLoggingWorker.perform_async(:admin,
        admin_id: nil,
        target_id: schedule_id,
        action_type: "auto_restore")
    end
  end

  def list_training_available_restore training_schedule_absent
    schedules_is_active = TrainingSchedule.joins(:training_schedule_applicants)
      .where(start_time: training_schedule_absent.start_time.all_day)
      .where("training_schedule_applicants.schedule_status_code IN (?)", TrainingScheduleApplicant::NOT_ABSENT_STATUSES)
      .uniq

    unavailable_trainer = schedules_is_active.pluck(:person_in_charge_id)
    unavailable_location_and_session = schedules_is_active.pluck(:location_id, :training_session_code)

    draft_restore_schedules = TrainingSchedule.only_deleted
      .where(start_time: training_schedule_absent.start_time.all_day)
      .where.not(deleted_by: nil)
      .where.not(person_in_charge_id: unavailable_trainer)
      .where.not(training_session_code: [:training_first_round, :training_second_round])

    available_schedule_ids = draft_restore_schedules.map do |schedule|
      next if unavailable_location_and_session.include?(
        [schedule.location_id, schedule.training_session_code]
      )

      schedule.id
    end

    available_schedule_ids.compact
  end

  def can_trigger? trigger_schedule
    return false if trigger_schedule.nil? || trigger_schedule.start_time <= ServerTime.now

    current_portion = trigger_schedule.training_schedule_applicants.not_absent.size
    return true if update_pic || !current_portion.positive? || trigger_schedule.deleted?

    false
  end
end
