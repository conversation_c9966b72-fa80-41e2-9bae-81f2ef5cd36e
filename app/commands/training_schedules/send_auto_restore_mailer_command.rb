class TrainingSchedules::SendAutoRestoreMailerCommand
  attr_reader :restored_schedule_data, :exclude_pic

  def initialize restored_schedule_data, exclude_pic = false
    @restored_schedule_data = restored_schedule_data
    @exclude_pic = exclude_pic
  end

  def perform
    return if restored_schedule_data.empty?

    send_to_op_center
    send_to_trainers
  end

  private

  def send_to_op_center
    return if exclude_pic

    receiver_emails = Settings.op_center_emails

    receiver_emails.each do |email|
      AdminMailer.auto_restore_mail(email, restored_schedule_data).deliver_now
    end
  end

  def send_to_trainers
    notification_trainers_data = restored_schedule_data

    if exclude_pic
      exclude_pic_id = notification_trainers_data[:person_in_charge_id]
      notification_trainers_data[:restored_schedules].reject! do |schedule|
        schedule[:person_in_charge_id] == exclude_pic_id
      end
    end

    notification_trainers_data[:to_trainer] = true

    pic_ids = notification_trainers_data[:restored_schedules].pluck(:person_in_charge_id).uniq

    accounts = Account.joins(:admin)
      .select("admins.id AS pic_id, accounts.email, accounts.name")
      .where("admins.id IN (?)", pic_ids)

    accounts.each do |account|
      next if account.email.blank?

      data_to_trainer = data_to_trainer(account.pic_id, account.name, notification_trainers_data)
      AdminMailer.auto_restore_mail(account.email, data_to_trainer).deliver_now
    end
  end

  def data_to_trainer pic_id, trainer_name, notification_trainers_data
    restored_schedules = notification_trainers_data[:restored_schedules].select do |schedule|
      schedule[:person_in_charge_id] == pic_id
    end

    data_to_trainer = notification_trainers_data.except(:restored_schedules)
    data_to_trainer[:restored_schedules] = restored_schedules
    data_to_trainer[:trainer_name] = trainer_name

    data_to_trainer
  end
end
