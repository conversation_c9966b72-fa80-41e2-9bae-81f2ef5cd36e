class TrainingSchedules::RescheduleTrainingScheduleCommand
  LOG_ACTION_TYPES = {
    cancel: "staff_cancel_training_schedule",
    reschedule: "staff_reschedule_training_schedule"
  }
  attr_reader :staff_id, :removed_ids, :added_ids,
    :survey_answer_id, :survey_answer_response,
    :removed_applicants, :added_applicants

  def initialize **kwargs
    @staff_id =     kwargs[:staff_id]
    @removed_ids =  kwargs[:removed_ids]
    @added_ids =    kwargs[:added_ids]
    @survey_answer_id = kwargs[:survey_answer_id]
    @survey_answer_response = kwargs[:survey_answer_response]
  end

  def perform
    find_or_initialize_applicants
    reschedule
    handle_absent_applicants
    handle_added_applicants
  end

  private

  def find_or_initialize_applicants
    @removed_applicants = TrainingScheduleApplicant.includes(:training_schedule)
      .not_absent
      .where(
        staff_id: staff_id,
        training_schedule_id: removed_ids
      )

    raise CustomExceptions::CannotRescheduleTrainingError unless @removed_applicants.all?(&:able_to_absent?)

    @added_applicants = added_ids.map do |added_id|
      schedule = TrainingSchedule.find_by(id: added_id)
      raise CustomExceptions::InvalidTrainingScheduleError if schedule.nil? ||
        schedule.start_time < ServerTime.now + 24.hours
      raise CustomExceptions::TrainingScheduleIsFullError if schedule.is_full?

      TrainingScheduleApplicant.find_or_initialize_by(
        staff_id: staff_id,
        training_schedule_id: added_id
      )
    end
  end

  def reschedule
    ActiveRecord::Base.transaction(isolation: :serializable) do
      removed_applicants.each do |applicant|
        next if applicant.absent_with_notice?

        applicant.schedule_status_code = :absent_with_notice
        applicant.cancel_reason = survey_answer_response
        applicant.save!

        create_survey_response(applicant.id)
        create_training_schedule_log(
          applicant.training_schedule_id, applicant.id, LOG_ACTION_TYPES[:cancel]
        )
      end

      added_applicants.each do |applicant|
        applicant.schedule_status_code = :booked
        applicant.save!
        create_training_schedule_log(
          applicant.training_schedule_id, applicant.id, LOG_ACTION_TYPES[:reschedule]
        )
      end
    end
  end

  def handle_absent_applicants
    removed_applicants.each do |applicant|
      TrainingSchedules::HandleAbsentActionWorker.perform_async(
        applicant.training_schedule_id,
        applicant.staff_name,
        true
      )
    end
  end

  def handle_added_applicants
    added_applicants.each(&:remove_other_schedules_and_notify_trainer)
  end

  def create_survey_response applicant_id
    TrainingSchedules::CreateSurveyResponsesWorker.perform_async(
      survey_answer_id,
      survey_answer_response,
      applicant_id,
      staff_id
    )
  end

  def create_training_schedule_log training_schedule_id, applicant_id, action_type
    TrainingScheduleLoggingWorker.perform_async(
      :staff,
      staff_id: staff_id,
      target_type: TrainingSchedule.name,
      target_id: training_schedule_id,
      action_type: action_type,
      params: {special_info: {training_applicant_id: applicant_id}}
    )
  end
end
