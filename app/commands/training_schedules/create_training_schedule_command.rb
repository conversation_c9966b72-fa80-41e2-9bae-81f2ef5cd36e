class TrainingSchedules::CreateTrainingScheduleCommand
  DEFAULT_SCHEDULE_SESSION_CODE = TrainingSchedule.training_session_codes[:single_session]
  attr_reader :admin_id, :training_schedules_params, :date_params

  def initialize admin_id, training_schedules_params, date_params
    @admin_id = admin_id
    @training_schedules_params = training_schedules_params
    @date_params = date_params
  end

  def perform
    raise CustomExceptions::MissingArgumentsError if training_schedules_params.empty? || date_params.empty?

    errors = []
    training_schedules = []

    date_params.each do |date|
      training_schedules_params.each do |schedule_params|
        new_schedule = build_training_schedule_params(schedule_params, date)
        training_schedule = TrainingSchedule.new(new_schedule)

        unless training_schedule.valid?
          errors << training_schedule.errors.messages.values
          raise(CustomExceptions::CreateTrainingScheduleError, errors.flatten.to_json)
        end

        training_schedules << new_schedule
      end
    end

    TrainingSchedule.create!(training_schedules)
  end

  private

  def build_training_schedule_params schedule_params, date
    start_time = ServerTime.parse("#{date} #{schedule_params[:start_time]}")
    end_time = ServerTime.parse("#{date} #{schedule_params[:end_time]}")
    training_session_code = DEFAULT_SCHEDULE_SESSION_CODE

    {
      start_time: start_time,
      end_time: end_time,
      location_id: schedule_params[:location_id].to_i,
      training_session_code: training_session_code,
      total_portion: schedule_params[:total_portion],
      person_in_charge_id: schedule_params[:person_in_charge_id].presence,
      creator_id: admin_id
    }
  end
end
