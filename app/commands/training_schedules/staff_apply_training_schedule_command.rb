class TrainingSchedules::StaffApplyTrainingScheduleCommand
  attr_reader :training_schedule_id, :staff_id, :admin_assign

  def initialize training_schedule_id, staff_id, admin_assign = false
    @training_schedule_id = training_schedule_id
    @staff_id = staff_id
    @admin_assign = admin_assign
  end

  def perform
    @staff = Staff.find_by(id: staff_id)
    raise(CustomExceptions::MissingStaffSelectedError) if @staff.nil?

    @training_schedule = TrainingSchedule.find_by(id: training_schedule_id)
    raise(CustomExceptions::InvalidTrainingScheduleError) if @training_schedule.nil? || invalid_apply?
    raise(CustomExceptions::TrainingScheduleIsFullError) if @training_schedule.is_full?

    applicant = TrainingScheduleApplicant.find_or_initialize_by(
      training_schedule_id: training_schedule_id,
      staff_id: staff_id
    )

    ActiveRecord::Base.transaction(isolation: :serializable) do
      applicant.schedule_status_code = :booked
      applicant.save!

      recruitment_process_params = {
        registration_process_code: :completed_registration,
        training_process_code: set_training_process_code
      }

      @staff.update_recruitment_process!(recruitment_process_params.compact)
    end

    @training_schedule.remove_other_schedules_and_notify_trainer

    applicant
  end

  private

  def set_training_process_code
    return :arranged_training if @training_schedule.single_session?

    handle_training_process_code_old_session
  end

  def handle_training_process_code_old_session
    single_session = @staff.single_session
    return if single_session

    if @training_schedule.training_first_round?
      other_session = @staff.second_session
    else
      other_session = @staff.first_session
    end

    return :no_training_schedule unless other_session
    return :absent_from_training if other_session.is_unavailable?

    :arranged_training
  end

  def invalid_apply?
    return false if admin_assign
    return false if @training_schedule.single_session? &&
      @training_schedule.start_time > ServerTime.now + 24.hours

    true
  end
end
