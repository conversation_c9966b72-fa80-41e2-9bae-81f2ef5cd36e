class TrainingSchedules::SendNotificationToTrainersCommand
  attr_reader :training_schedule_id, :notification_type, :data

  def initialize training_schedule_id, notification_type, data = {}
    @training_schedule_id = training_schedule_id
    @notification_type = notification_type
    @data = data
  end

  def perform
    @training_schedule = TrainingSchedule.with_deleted.find(training_schedule_id)
    person_in_charge_id = @training_schedule.person_in_charge_id
    person_in_charge_email = @training_schedule.person_in_charge_email
    return if person_in_charge_id.blank? || person_in_charge_email.blank?

    case notification_type
    when "booked"
      deleted_schedules = data.map(&:symbolize_keys)
      notification_data = @training_schedule.booked_mailer_data(deleted_schedules)
      AdminMailer.notify_staff_booked_training_schedule(person_in_charge_email, notification_data).deliver_now
    when "absent"
      data[:schedule_id] = training_schedule_id
      restored_schedules = get_restored_schedules
      notify_to_pic_trainer(restored_schedules)
      notify_to_op_center(data.merge(@training_schedule.absent_mailer_data(restored_schedules))) if
        restored_schedules.present?
    when "delete"
      AdminMailer.notify_delete_schedule(
        person_in_charge_email,
        @training_schedule.delete_mailer_data
      ).deliver_now
    else
      raise(CustomExceptions::InvalidNotificationType)
    end
  end

  private

  def notify_to_pic_trainer restored_schedules
    schedules_related_pic = schedules_related_pic(@training_schedule.person_in_charge_id, restored_schedules)
    notification_data = data.merge(@training_schedule.absent_mailer_data(schedules_related_pic))

    AdminMailer.notify_staff_cancel_schedule(
      @training_schedule.person_in_charge_email,
      notification_data
    ).deliver_now
  end

  def get_restored_schedules
    return [] if data[:restored_schedule_ids].blank?

    TrainingSchedule.where(id: data[:restored_schedule_ids])
      .includes(TrainingSchedule::INCLUDE_MODELS)
      .map(&:deleted_format)
  end

  def schedules_related_pic person_in_charge_id, restored_schedules
    return [] if restored_schedules.empty?

    restored_schedules.select{|schedule| schedule[:person_in_charge_id] == person_in_charge_id}
  end

  def notify_to_op_center notification_data
    receiver_emails = Settings.op_center_emails

    receiver_emails.each do |email|
      AdminMailer.alert_admin_staff_cancel_schedule(email, notification_data).deliver_now
    end
  end
end
