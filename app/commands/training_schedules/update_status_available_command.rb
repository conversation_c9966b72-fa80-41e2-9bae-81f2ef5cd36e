class TrainingSchedules::UpdateStatusAvailableCommand
  attr_reader :training_schedule_ids

  NOT_ABSENT_STATUSES = %w(booked joined)

  def initialize training_schedule_ids
    @training_schedule_ids = training_schedule_ids
  end

  def perform
    return if training_schedule_ids.blank?

    training_schedules = TrainingSchedule.with_deleted
      .includes(:training_schedule_applicants)
      .where(id: training_schedule_ids)

    raise ActiveRecord::RecordInvalid if training_schedules.blank?

    training_schedules.each do |schedule|
      valid_applicants = schedule.training_schedule_applicants.select do |applicant|
        NOT_ABSENT_STATUSES.include?(applicant.schedule_status_code)
      end

      next if schedule.total_portion < valid_applicants.size

      updated_params = schedule.total_portion == valid_applicants.size
      next if schedule.is_full == updated_params

      schedule.update_columns(is_full: updated_params)
    end
  end
end
