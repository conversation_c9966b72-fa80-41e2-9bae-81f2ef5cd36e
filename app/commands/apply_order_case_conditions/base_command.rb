module ApplyOrderCaseConditions
  class BaseCommand
    VALIDATION_STRATEGIES = %i(
      validate_ot_day
      validate_long_vacation
      validate_ot_week
    ).freeze

    MERGED_WARNING_MESSAGE = %i(ot_day staff_long_vacation).freeze

    attr_reader :order_case, :staff, :apply_order_case, :request_data

    def initialize order_case, staff, apply_order_case, request_data: {}
      @order_case = order_case
      @staff = staff
      @apply_order_case = apply_order_case
      @request_data = request_data
    end

    def perform
      warnings_and_errors = VALIDATION_STRATEGIES.map do |strategy|
        send(strategy)
      end.compact

      return {status: true} if warnings_and_errors.blank?

      warnings_and_errors = warnings_and_errors.group_by{|value| value[:type]}
      raise(CustomExceptions::ApplyOrderCaseConditionError, warnings_and_errors[:error].to_json) if
        warnings_and_errors[:error].present?

      warnings = warnings_and_errors[:warning].index_by{|value| value[:key]}

      {
        status: false,
        warning_message: get_warning_messages(warnings, MERGED_WARNING_MESSAGE),
        warning_in_week_message: warnings.dig(:ot_in_week, :message),
        note: I18n.t("staff_notification.messages.staff_apply_order_case.note"),
        op_center_tel: I18n.t("staff_notification.messages.staff_apply_order_case.op_center_tel")
      }
    end

    private

    def working_time
      @working_time ||= ApplyOrderCaseConditions::CalcWorkingTimeCommand.new(
        order_case,
        staff,
        apply_order_case,
        request_data: request_data
      ).perform
    end

    def validate_ot_day
      cmd = ApplyOrderCaseConditions::ValidateOtDayCommand.new(
        order_case,
        staff,
        working_time[:arranged_time_after_apply_in_day]
      )

      result = cmd.perform
      format_response_validate_command(:ot_day, result) if result
    end

    def validate_ot_week
      cmd = ApplyOrderCaseConditions::ValidateOtWeekCommand.new(
        order_case,
        staff,
        working_time[:arranged_time_after_apply_in_week]
      )

      result = cmd.perform
      format_response_validate_command(:ot_in_week, result) if result
    end

    def validate_long_vacation
      cmd = ApplyOrderCaseConditions::ValidateLongVacationCommand.new(
        order_case,
        staff,
        working_time[:arranged_time_after_apply_in_day]
      )

      result = cmd.perform
      format_response_validate_command(:staff_long_vacation, result) if result
    end

    def format_response_validate_command key, response_validate
      {
        key: key,
        type: response_validate.first,
        message: response_validate.last
      }
    end

    def get_warning_messages warnings, keys
      keys.map do |key|
        warnings.dig(key, :message)
      end.join("\n\n")
    end
  end
end
