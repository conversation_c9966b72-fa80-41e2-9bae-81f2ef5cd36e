module ApplyOrderCaseConditions
  class CalcWorkingTimeCommand
    SECONDS_OF_MINUTE = 60

    attr_reader :order_case, :staff, :apply_oc, :request_data

    def initialize order_case, staff, apply_oc, request_data: {}
      @order_case = order_case
      @staff = staff
      @apply_oc = apply_oc
      @request_data = request_data
    end

    def perform
      working_date = order_case.case_started_at.to_date
      apply_working_time = calculate_apply_time(request_data[:start_at], request_data[:end_at]).to_i

      {
        arranged_time_after_apply_in_day: calculate_arranged_time_in_day(working_date).to_i + apply_working_time,
        arranged_time_after_apply_in_week: calculate_arranged_time_in_week(working_date).to_i + apply_working_time
      }
    end

    private

    def calculate_apply_time requested_started_at, requested_ended_at
      break_time = order_case.order_branch.break_time * SECONDS_OF_MINUTE
      apply_time = 0
      if requested_started_at.nil? && requested_ended_at.nil?
        apply_time = order_case.case_ended_at - order_case.case_started_at
      else
        apply_oc.update_requested_date
        apply_time = apply_oc.requested_ended_at - apply_oc.requested_started_at if apply_oc.requested_started_at && apply_oc.requested_ended_at
      end

      apply_time - break_time
    end

    def calculate_arranged_time_in_day working_date
      start_date = working_date.beginning_of_day
      end_date = working_date.end_of_day

      working_time_arranged(start_date, end_date)
    end

    def calculate_arranged_time_in_week working_date
      week_start = working_date.beginning_of_week(:sunday).beginning_of_day
      week_end = working_date.end_of_week(:sunday).end_of_day

      working_time_arranged(week_start, week_end)
    end

    def working_time_arranged start_date, end_date
      work_achievements = staff.work_achievements.portion_arranged_in_range(start_date, end_date)
      return 0 if work_achievements.blank?

      working_time = work_achievements.map do |wk|
        ((wk.working_ended_at - wk.working_started_at) - (wk.break_time * SECONDS_OF_MINUTE))
      end.compact

      working_time.sum
    end
  end
end
