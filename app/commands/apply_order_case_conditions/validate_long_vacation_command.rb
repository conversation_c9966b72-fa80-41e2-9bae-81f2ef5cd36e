module ApplyOrderCaseConditions
  class ValidateLongVacationCommand
    MAXIMUM_HOUR_PER_DAY_DURING_VACATION = 8

    attr_reader :order_case, :staff, :total_second_working_time_after_arranged

    def initialize order_case, staff, total_second_working_time_after_arranged
      @order_case = order_case
      @staff = staff
      @total_second_working_time_after_arranged = total_second_working_time_after_arranged
    end

    def perform
      return unless staff.is_overseas_student?
      return if total_second_working_time_after_arranged <= MAXIMUM_HOUR_PER_DAY_DURING_VACATION.hours.to_i
      return [:warning, warning_message] if is_oc_not_in_long_vacation?

      [:error, error_message]
    end

    private

    def is_oc_not_in_long_vacation?
      working_date = order_case.case_started_at.to_date

      staff_long_vacation = staff.staff_long_vacations
        .where("start_date <= ? AND end_date >= ?", working_date, working_date)

      staff_long_vacation.empty?
    end

    def warning_message
      I18n.t("staff_notification.messages.staff_apply_order_case.warning_confirm_not_in_long_vacation")
    end

    def error_message
      I18n.t("staff_notification.messages.staff_apply_order_case.error_overtime_during_long_vacation")
    end
  end
end
