module ApplyOrderCaseConditions
  class ValidateOtWeekCommand
    OT_HOUR_THRESHOLD_PER_WEEK = 40

    attr_reader :staff, :order_case, :total_second_working_time_after_arranged

    def initialize staff, order_case, total_second_working_time_after_arranged
      @staff = staff
      @order_case = order_case
      @total_second_working_time_after_arranged = total_second_working_time_after_arranged
    end

    def perform
      return if total_second_working_time_after_arranged <= OT_HOUR_THRESHOLD_PER_WEEK.hours.to_i

      [:warning, warning_message]
    end

    private

    def warning_message
      ot_time = total_second_working_time_after_arranged - OT_HOUR_THRESHOLD_PER_WEEK.hours.to_i
      ot_message = FormatOtTime.for_staff(ot_time)

      I18n.t("staff_notification.messages.staff_apply_order_case.over_40_hours", ot_time_message: ot_message)
    end
  end
end
