# Sync updated data (diffs only)
class Zengin::UpdateMasterDataCommand
  def initialize used_version = "current"
    @need_update = false
    @api_client = Zengin::ApiClient.new
    @bank_diffs_file = ""
    @branch_diffs_file = ""
    @used_version = used_version
    @current_version = ""
    @logger = Logger.new Rails.root.join("log", "zengin_update_master_data.log")
  end

  def perform
    write_log("START update master data #{ServerTime.now.strftime(Settings.datetime.full_formats)}")

    check_for_update

    write_log("WITH processed version: #{@current_version} - Need Update? #{@need_update}")

    fetch_bank_diffs
    fetch_branch_diffs
    process_bank_diffs
    process_branch_diffs
    update_last_applied_version

    write_log("FINISH update master data #{ServerTime.now.strftime(Settings.datetime.full_formats)}")
  end

  private

  def check_for_update
    if @used_version == "current"
      res = @api_client.current_version
      @current_version = res["version"]
    else
      @current_version = @used_version
    end

    return @need_update = false if @current_version.blank?

    @need_update = ZenginMasterVersion.last_version_is_behind? @current_version
  end

  def fetch_bank_diffs
    return unless @need_update

    @bank_diffs_file = @api_client.get_bank_diffs(@used_version)
    write_log("GET bank diffs file: #{@bank_diffs_file}")
  end

  def fetch_branch_diffs
    return unless @need_update

    @branch_diffs_file = @api_client.get_branch_diffs(@used_version)
    write_log("GET branch diffs file: #{@branch_diffs_file}")
  end

  def process_bank_diffs
    return if @bank_diffs_file.blank?

    write_log("BEGIN process bank diffs from: #{@bank_diffs_file}")
    file_path = Rails.root.join("tmp", @bank_diffs_file)
    Zlib::GzipReader.open(file_path) do |gz|
      CSV.new(gz, headers: true).each do |row|
        data = row.to_h
        create_or_update_bank(data) if data["diff_type"].in? %w(created updated)
        delete_bank(data) if data["diff_type"] == "deleted"
      end
    end
    write_log("END process bank diffs from: #{@bank_diffs_file}")
  end

  def process_branch_diffs
    return if @branch_diffs_file.blank?

    write_log("BEGIN process branch diffs from: #{@branch_diffs_file}")
    file_path = Rails.root.join("tmp", @branch_diffs_file)
    Zlib::GzipReader.open(file_path) do |gz|
      CSV.new(gz, headers: true).each do |row|
        data = row.to_h
        create_or_update_branch(data) if data["diff_type"].in? %w(created updated)
        delete_branch(data) if data["diff_type"] == "deleted"
      end
    end
    write_log("END process branch diffs from: #{@branch_diffs_file}")
  end

  def create_or_update_bank data
    bank = Bank.find_or_initialize_by(code: data["bank_code"])
    bank.name = data["name"]
    bank.name_kana = data["halfkana"]
    bank.save!
    write_log("UPSERT bank with code: #{data['bank_code']}")
  end

  def delete_bank data
    bank = Bank.find_by(code: data["bank_code"])
    return if bank.blank?

    branches = BankBranch.where(bank_id: bank.id)

    write_log("DELETE bank with id-code: #{bank.id} - #{bank.code}")
    write_log("DELETE branch with id-code: #{branches.pluck(:id, :bank_code, :code)}")

    bank.really_destroy!
    branches.delete_all
  end

  def create_or_update_branch data
    bank_id = Bank.find_by(code: data["bank_code"])&.id
    raise "Cannot find bank by code #{data['bank_code']} to create bank branch" if bank_id.blank?

    branch = BankBranch.find_or_initialize_by(
      bank_id: bank_id, bank_code: data["bank_code"], code: data["bank_branch_code"]
    )
    branch.name = data["name"]
    branch.name_kana = data["halfkana"]
    branch.save!

    write_log("UPSERT branch with code: #{data['bank_code']} #{data['bank_branch_code']}")
  end

  def delete_branch data
    branch = BankBranch.find_by(bank_code: data["bank_code"], code: data["bank_branch_code"])
    return if branch.blank?

    write_log("DELETE branch with id-code: #{branch.id} #{branch.bank_code} #{branch.code}")

    branch.really_destroy!
  end

  def update_last_applied_version
    return if !@need_update || @current_version.blank?

    ZenginMasterVersion.create(
      version: @current_version,
      import_type: :diff,
      applied_at: ServerTime.now
    )
  end

  def write_log message
    @logger.info(message)
  end
end
