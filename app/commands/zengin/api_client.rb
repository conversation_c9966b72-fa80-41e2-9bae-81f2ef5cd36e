require "net/http"
require "uri"

class Zengin::ApiClient
  def initialize base_url = nil
    @base_url = base_url || Settings.zengin_master_api.base_url
    @auth_user = Settings.zengin_master_api.auth_user
    @auth_pass = Settings.zengin_master_api.auth_pass
    @logger = Logger.new Rails.root.join("log", "zengin_master_api.log")
  end

  def list_version
    get_json("list_version")
  end

  def current_version
    get_json("current_version")
  end

  def get_banks version = "current"
    get_file("banks", version)
  end

  def get_bank_diffs version = "current"
    get_file("bank_diffs", version)
  end

  def get_branches version = "current"
    get_file("branches", version)
  end

  def get_branch_diffs version = "current"
    get_file("branch_diffs", version)
  end

  private

  def get_json path
    uri = URI("#{@base_url}/#{path}")
    response = send_request(uri)
    JSON.parse(response.body)
  rescue StandardError => e
    write_log "ERROR occured, request to #{uri} failed: #{e}"
    {}
  end

  def get_file path, version
    uri = URI("#{@base_url}/#{path}/#{version}")
    response = send_request(uri)
    filename = ""
    if response.is_a?(Net::HTTPRedirection)
      filename = "zengin_#{path}_#{version}.csv.gz"
      download_file(response["location"], filename)
    else
      write_log "Unexpected response: #{response.code} - #{response.body}"
    end
    filename
  rescue StandardError => e
    write_log "ERROR occured, request to #{uri} failed: #{e}"
    ""
  end

  def send_request uri
    write_log "Sending request to #{uri}"
    request = Net::HTTP::Get.new(uri)
    request.basic_auth(@auth_user, @auth_pass)
    Net::HTTP.start(uri.host, uri.port, use_ssl: true) do |http|
      http.request(request)
    end
  end

  def download_file url, filename
    uri = URI(url)
    filepath = Rails.root.join("tmp", filename)
    File.binwrite(filepath, URI.parse(uri).open.read)
  end

  def write_log message
    @logger.info message
  end
end
