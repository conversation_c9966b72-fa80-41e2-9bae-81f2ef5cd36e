# Sync all data
class Zengin::ImportMasterDataCommand
  def initialize used_version = "current"
    @used_version = used_version
    @current_version = ""
    @api_client = Zengin::ApiClient.new
    @banks_file = ""
    @branches_file = ""
    @logger = Logger.new Rails.root.join("log", "zengin_import_master_data.log")
  end

  def perform
    write_log("START import master data at #{ServerTime.now.strftime(Settings.datetime.full_formats)}")

    fetch_current_version

    write_log("WITH processed version: #{@current_version}")

    fetch_banks
    fetch_branches
    process_banks
    process_branches
    update_last_applied_version

    write_log("FINISH import master data #{ServerTime.now.strftime(Settings.datetime.full_formats)}")
  end

  private

  def fetch_current_version
    unless @used_version == "current"
      @current_version = @used_version
      return
    end

    res = @api_client.current_version
    @current_version = res["version"]
  end

  def fetch_banks
    @banks_file = @api_client.get_banks(@used_version)
    write_log("GET banks file: #{@banks_file}")
  end

  def fetch_branches
    @branches_file = @api_client.get_branches(@used_version)
    write_log("GET branches file: #{@branches_file}")
  end

  def process_banks
    return if @banks_file.blank?

    write_log("BEGIN process banks from: #{@banks_file}")
    bank_ids = []
    file_path = Rails.root.join("tmp", @banks_file)
    Zlib::GzipReader.open(file_path) do |gz|
      CSV.new(gz, headers: true).each do |row|
        bank_ids << create_or_update_bank(row.to_h)
      end
    end

    deleted_bank_ids = Bank.where.not(id: bank_ids).pluck(:id)
    delete_banks(deleted_bank_ids)
    write_log("END process banks from: #{@banks_file}")
  end

  def process_branches
    return if @branches_file.blank?

    write_log("BEGIN process branches from: #{@branches_file}")
    branch_ids = []
    file_path = Rails.root.join("tmp", @branches_file)
    Zlib::GzipReader.open(file_path) do |gz|
      CSV.new(gz, headers: true).each do |row|
        branch_ids << create_or_update_branch(row.to_h)
      end
    end

    deleted_branch_ids = BankBranch.where.not(id: branch_ids).pluck(:id)
    delete_branches(deleted_branch_ids)
    write_log("END process branches from: #{@banks_file}")
  end

  def create_or_update_bank data
    bank = Bank.find_or_initialize_by(code: data["code"])
    bank.name = data["name"]
    bank.name_kana = data["halfkana"]
    is_changed = bank.changed?

    bank.save!

    write_log("UPSERT bank with code: #{data['code']}") if is_changed

    bank.id
  end

  def delete_banks bank_ids
    return if bank_ids.blank?

    banks = Bank.where(id: bank_ids).select(:id, :code)
    write_log("DELETE banks with ids-codes: #{banks.pluck(:id, :code)}")

    banks.delete_all
  end

  def create_or_update_branch data
    bank_id = Bank.find_by(code: data["bank_code"])&.id
    raise "Cannot find bank by code #{data['bank_code']} to create bank branch" if bank_id.blank?

    branch = BankBranch.find_or_initialize_by(
      bank_id: bank_id, bank_code: data["bank_code"], code: data["bank_branch_code"]
    )
    branch.name = data["name"]
    branch.name_kana = data["halfkana"]
    is_changed = branch.changed?

    branch.save!

    write_log("UPSERT branch with code: #{data['bank_code']} #{data['bank_branch_code']}") if is_changed

    branch.id
  end

  def delete_branches branch_ids
    return if branch_ids.blank?

    branches = BankBranch.where(id: branch_ids).select(:id, :bank_code, :code)
    write_log("DELETE branches with ids-codes: #{branches.pluck(:id, :bank_code, :code)}")

    branches.delete_all
  end

  def update_last_applied_version
    return if @current_version.blank?

    ZenginMasterVersion.create(
      version: @current_version,
      import_type: :full,
      applied_at: ServerTime.now
    )
  end

  def write_log message
    @logger.info message
  end
end
